import React, { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import { Package, Trash2, Edit, Plus, X, AlertCircle, Check, Search } from 'lucide-react';
import useDebounce from '../../hooks/useDebounce';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  pages: string[];
  specifications: string[];
  theme_color?: string;
  stripe_monthly_price_id?: string;
  stripe_yearly_price_id?: string;
  stripe_product_id?: string;
  monthly_discount?: number;
  yearly_discount?: number;
  created_at: string;
}



export default function ProductManagement() {
  const [products, setProducts] = useState<Product[]>([]);
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [searchInput, setSearchInput] = useState('');
  const debouncedSearchTerm = useDebounce(searchInput, 200);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10); // Default to 10 products per page
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    image_url: '',
    category: '',
    pages: [] as string[], // Array of page names where the product will be displayed
    specifications: [''], // Initialize with one empty specification
    theme_color: 'blue', // Default theme color
    stripe_monthly_price_id: '',
    stripe_yearly_price_id: '',
    stripe_product_id: '',
    monthly_discount: '0',
    yearly_discount: '0'
  });
  const [editingId, setEditingId] = useState<string | null>(null);

  // Fetch all products (without pagination)
  const fetchAllProducts = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setProducts([]);
      setAllProducts([]);
      setAllProducts(data || []);
      setProducts(data || []);
      setTotalCount(data?.length || 0);
      setTotalPages(Math.ceil((data?.length || 0) / pageSize));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, []);



  useEffect(() => {
    fetchAllProducts();
  }, [fetchAllProducts]);

  // Client-side filtered products
  const filteredProducts = allProducts.filter(product => {
    const term = debouncedSearchTerm.toLowerCase();
    return (
      product.name.toLowerCase().includes(term) ||
      product.description.toLowerCase().includes(term) ||
      product.category.toLowerCase().includes(term)
    );
  });

  // Get paginated results from filtered products
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Update total count and pages based on filtered results
  useEffect(() => {
    setTotalCount(filteredProducts.length);
    setTotalPages(Math.ceil(filteredProducts.length / pageSize));
  }, [filteredProducts, pageSize]);

  // Function to handle escape key press
  const handleEscapeKey = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape' && showForm) {
      setShowForm(false);
      setEditingId(null);
    }
  }, [showForm]);

  // Add event listener for escape key
  useEffect(() => {
    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [handleEscapeKey]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      // Filter out empty specifications
      const filteredSpecs = formData.specifications.filter(spec => spec.trim() !== '');
      console.log('Filtered specifications:', filteredSpecs);

      // Ensure specifications is a valid JSON array
      let specificationsValue;
      try {
        // First, try to stringify and parse to ensure it's a valid JSON array
        specificationsValue = JSON.parse(JSON.stringify(filteredSpecs));
        console.log('Specifications as JSON array:', specificationsValue);

        // Log the type to help with debugging
        console.log('Specifications value type:', typeof specificationsValue);
        console.log('Is array:', Array.isArray(specificationsValue));

        // Make sure it's actually an array of strings
        if (!Array.isArray(specificationsValue)) {
          specificationsValue = [];
        }
      } catch (jsonError) {
        console.error('Error converting specifications to JSON:', jsonError);
        // Fallback to empty array if there's an error
        specificationsValue = [];
      }

      // Ensure pages is a valid JSON array
      const pagesValue = formData.pages;
      console.log('Pages value:', pagesValue);

      // The specifications column exists (as we saw in the response)
      // Create the product data object
      const productData = {
        name: formData.name,
        description: formData.description,
        price: parseFloat(formData.price),
        image_url: formData.image_url || null,
        category: formData.category,
        pages: pagesValue,
        // Always include specifications
        specifications: specificationsValue,
        theme_color: formData.theme_color,
        stripe_monthly_price_id: formData.stripe_monthly_price_id || null,
        stripe_yearly_price_id: formData.stripe_yearly_price_id || null,
        stripe_product_id: formData.stripe_product_id || null,
        monthly_discount: parseFloat(formData.monthly_discount) || 0,
        yearly_discount: parseFloat(formData.yearly_discount) || 0
      };

      // Log the final product data for debugging
      console.log('Final product data:', JSON.stringify(productData));

      // Log a message about the specifications
      if (specificationsValue.length > 0) {
        console.log(`Adding ${specificationsValue.length} specifications to the product`);
      } else {
        console.log('No specifications to add');
      }

      console.log('Product data being sent:', productData);

      if (editingId) {
        console.log('Updating product with ID:', editingId);
        const { data, error } = await supabase
          .from('products')
          .update(productData)
          .eq('id', editingId)
          .select();

        console.log('Update response:', { data, error });

        if (error) throw error;
        setSuccess('Product updated successfully!');
        setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
      } else {
        const { error } = await supabase
          .from('products')
          .insert([productData]);

        if (error) throw error;
        setSuccess('Product created successfully!');
        setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
      }

      setFormData({
        name: '',
        description: '',
        price: '',
        image_url: '',
        category: '',
        pages: [],
        specifications: [''], // Reset to one empty specification
        theme_color: 'blue',
        stripe_monthly_price_id: '',
        stripe_yearly_price_id: '',
        stripe_product_id: '',
        monthly_discount: '0',
        yearly_discount: '0'
      });
      setShowForm(false);
      setEditingId(null);
      fetchAllProducts();
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setTimeout(() => setError(null), 5000); // Auto-dismiss after 5 seconds
    }
  };

  const handleEdit = (product: Product) => {
    // Handle specifications field carefully
    let specifications = [''];

    if (product.specifications) {
      // Check if specifications is an array
      if (Array.isArray(product.specifications) && product.specifications.length > 0) {
        specifications = product.specifications;
      } else {
        // Try to parse it if it's a string (sometimes JSON is stored as a string)
        try {
          const parsedSpecs = typeof product.specifications === 'string'
            ? JSON.parse(product.specifications)
            : product.specifications;

          if (Array.isArray(parsedSpecs) && parsedSpecs.length > 0) {
            specifications = parsedSpecs;
          }
        } catch (e) {
          console.error('Error parsing specifications:', e);
        }
      }
    }

    // Handle pages field carefully
    let pages: string[] = [];

    if (product.pages) {
      // Check if pages is an array
      if (Array.isArray(product.pages)) {
        pages = product.pages;
      } else {
        // Try to parse it if it's a string (sometimes JSON is stored as a string)
        try {
          const parsedPages = typeof product.pages === 'string'
            ? JSON.parse(product.pages)
            : product.pages;

          if (Array.isArray(parsedPages)) {
            pages = parsedPages;
          }
        } catch (e) {
          console.error('Error parsing pages:', e);
        }
      }
    }

    setFormData({
      name: product.name,
      description: product.description,
      price: product.price.toString(),
      image_url: product.image_url || '',
      category: product.category,
      pages: pages,
      specifications: specifications,
      theme_color: product.theme_color || 'blue',
      stripe_monthly_price_id: product.stripe_monthly_price_id || '',
      stripe_yearly_price_id: product.stripe_yearly_price_id || '',
      stripe_product_id: product.stripe_product_id || '',
      monthly_discount: (product.monthly_discount || 0).toString(),
      yearly_discount: (product.yearly_discount || 0).toString()
    });
    setEditingId(product.id);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this product?')) return;
    console.log('Deleting product with ID:', id);
    try {
      const { data, error, count } = await supabase
        .from('products')
        .delete()
        .eq('id', id)
        .select();

      console.log('Delete response:', { data, error, count });

      if (error) throw error;
      if (!data || data.length === 0) {
        console.warn('No rows were deleted. Product may not exist or you lack permission.');
        setError('Failed to delete product. It may not exist or you lack permission.');
        return;
      }

      await fetchAllProducts();
      setSuccess('Product deleted successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: unknown) {
      console.error('Delete error:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setTimeout(() => setError(null), 5000);
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-2xl font-bold">Products</h2>

        <div className="flex flex-col md:flex-row gap-3 items-center">
          {/* Search input */}
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Search products..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>

          <button
            onClick={() => {
              setShowForm(true);
              setEditingId(null);
              setFormData({
                name: '',
                description: '',
                price: '',
                image_url: '',
                category: '',
                pages: [],
                specifications: [''],
                theme_color: 'blue',
                stripe_monthly_price_id: '',
                stripe_yearly_price_id: '',
                stripe_product_id: '',
                monthly_discount: '0',
                yearly_discount: '0'
              });
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <Plus className="h-5 w-5 mr-2" />
            New Product
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg flex items-start mb-6">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
          <button onClick={() => setError(null)} className="ml-auto">
            <X className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg flex items-start">
          <Check className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
          <button onClick={() => setSuccess(null)} className="ml-auto">
            <X className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Product Form Modal */}
      {showForm && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fadeIn"
          onClick={(e) => {
            // Close modal when clicking outside
            if (e.target === e.currentTarget) {
              setShowForm(false);
              setEditingId(null);
            }
          }}
        >
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold">
                  {editingId ? 'Edit Product' : 'Add New Product'}
                </h3>
                <button
                  onClick={() => {
                    setShowForm(false);
                    setEditingId(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product Name
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    required
                    rows={4}
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price
                    </label>
                    <input
                      type="number"
                      required
                      min="0"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.category}
                      onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., Products, GrantReady, Service, etc."
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Common categories: Products, GrantReady, Service, etc. This field is for categorization purposes.
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Display Pages <span className="text-sm text-gray-500">(Where this product will appear)</span>
                    </label>
                    <div className="grid grid-cols-2 gap-3 mb-3">
                      {/* Products Page */}
                      <div
                        className={`border rounded-lg p-3 cursor-pointer transition-all ${formData.pages.includes('Products')
                          ? 'bg-blue-50 border-blue-300 shadow-sm'
                          : 'border-gray-200 hover:border-blue-200 hover:bg-blue-50/30'}`}
                        onClick={() => {
                          const newPages = formData.pages.includes('Products')
                            ? formData.pages.filter(p => p !== 'Products')
                            : [...formData.pages, 'Products'];
                          setFormData({ ...formData, pages: newPages });
                        }}
                      >
                        <div className="flex items-center">
                          <div className={`w-5 h-5 flex-shrink-0 rounded border ${formData.pages.includes('Products')
                            ? 'bg-blue-600 border-blue-600 flex items-center justify-center'
                            : 'border-gray-300'}`}>
                            {formData.pages.includes('Products') && (
                              <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                              </svg>
                            )}
                          </div>
                          <div className="ml-3">
                            <h4 className="font-medium text-gray-900">Products</h4>
                            <p className="text-xs text-gray-500">Main products page</p>
                          </div>
                        </div>
                      </div>

                      {/* GrantReady Page */}
                      <div
                        className={`border rounded-lg p-3 cursor-pointer transition-all ${formData.pages.includes('GrantReady')
                          ? 'bg-purple-50 border-purple-300 shadow-sm'
                          : 'border-gray-200 hover:border-purple-200 hover:bg-purple-50/30'}`}
                        onClick={() => {
                          const newPages = formData.pages.includes('GrantReady')
                            ? formData.pages.filter(p => p !== 'GrantReady')
                            : [...formData.pages, 'GrantReady'];
                          setFormData({ ...formData, pages: newPages });
                        }}
                      >
                        <div className="flex items-center">
                          <div className={`w-5 h-5 flex-shrink-0 rounded border ${formData.pages.includes('GrantReady')
                            ? 'bg-purple-600 border-purple-600 flex items-center justify-center'
                            : 'border-gray-300'}`}>
                            {formData.pages.includes('GrantReady') && (
                              <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                              </svg>
                            )}
                          </div>
                          <div className="ml-3">
                            <h4 className="font-medium text-gray-900">GrantReady</h4>
                            <p className="text-xs text-gray-500">Grant management solutions</p>
                          </div>
                        </div>
                      </div>

                      {/* SOAR Page */}
                      <div
                        className={`border rounded-lg p-3 cursor-pointer transition-all ${formData.pages.includes('SOAR')
                          ? 'bg-green-50 border-green-300 shadow-sm'
                          : 'border-gray-200 hover:border-green-200 hover:bg-green-50/30'}`}
                        onClick={() => {
                          const newPages = formData.pages.includes('SOAR')
                            ? formData.pages.filter(p => p !== 'SOAR')
                            : [...formData.pages, 'SOAR'];
                          setFormData({ ...formData, pages: newPages });
                        }}
                      >
                        <div className="flex items-center">
                          <div className={`w-5 h-5 flex-shrink-0 rounded border ${formData.pages.includes('SOAR')
                            ? 'bg-green-600 border-green-600 flex items-center justify-center'
                            : 'border-gray-300'}`}>
                            {formData.pages.includes('SOAR') && (
                              <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                              </svg>
                            )}
                          </div>
                          <div className="ml-3">
                            <h4 className="font-medium text-gray-900">SOAR</h4>
                            <p className="text-xs text-gray-500">Disease outbreak prediction</p>
                          </div>
                        </div>
                      </div>

                      {/* Elenor Page */}
                      <div
                        className={`border rounded-lg p-3 cursor-pointer transition-all ${formData.pages.includes('Elenor')
                          ? 'bg-yellow-50 border-yellow-300 shadow-sm'
                          : 'border-gray-200 hover:border-yellow-200 hover:bg-yellow-50/30'}`}
                        onClick={() => {
                          const newPages = formData.pages.includes('Elenor')
                            ? formData.pages.filter(p => p !== 'Elenor')
                            : [...formData.pages, 'Elenor'];
                          setFormData({ ...formData, pages: newPages });
                        }}
                      >
                        <div className="flex items-center">
                          <div className={`w-5 h-5 flex-shrink-0 rounded border ${formData.pages.includes('Elenor')
                            ? 'bg-yellow-600 border-yellow-600 flex items-center justify-center'
                            : 'border-gray-300'}`}>
                            {formData.pages.includes('Elenor') && (
                              <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                              </svg>
                            )}
                          </div>
                          <div className="ml-3">
                            <h4 className="font-medium text-gray-900">Elenor</h4>
                            <p className="text-xs text-gray-500">Public health solutions</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Click on the pages where you want this product to be displayed.
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Image URL
                  </label>
                  <input
                    type="url"
                    value={formData.image_url}
                    onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Theme Color
                  </label>
                  <div className="grid grid-cols-4 gap-3">
                    {/* Blue Theme */}
                    <div
                      className={`border rounded-lg p-3 cursor-pointer transition-all ${formData.theme_color === 'blue'
                        ? 'bg-blue-50 border-blue-300 shadow-sm'
                        : 'border-gray-200 hover:border-blue-200 hover:bg-blue-50/30'}`}
                      onClick={() => setFormData({ ...formData, theme_color: 'blue' })}
                    >
                      <div className="flex items-center">
                        <div className={`w-5 h-5 flex-shrink-0 rounded-full bg-blue-600 border ${formData.theme_color === 'blue'
                          ? 'ring-2 ring-blue-600 ring-offset-2'
                          : 'border-gray-300'}`}>
                        </div>
                        <div className="ml-3">
                          <h4 className="font-medium text-gray-900">Blue</h4>
                        </div>
                      </div>
                    </div>

                    {/* Orange Theme */}
                    <div
                      className={`border rounded-lg p-3 cursor-pointer transition-all ${formData.theme_color === 'orange'
                        ? 'bg-orange-50 border-orange-300 shadow-sm'
                        : 'border-gray-200 hover:border-orange-200 hover:bg-orange-50/30'}`}
                      onClick={() => setFormData({ ...formData, theme_color: 'orange' })}
                    >
                      <div className="flex items-center">
                        <div className={`w-5 h-5 flex-shrink-0 rounded-full bg-orange-600 border ${formData.theme_color === 'orange'
                          ? 'ring-2 ring-orange-600 ring-offset-2'
                          : 'border-gray-300'}`}>
                        </div>
                        <div className="ml-3">
                          <h4 className="font-medium text-gray-900">Orange</h4>
                        </div>
                      </div>
                    </div>

                    {/* Purple Theme */}
                    <div
                      className={`border rounded-lg p-3 cursor-pointer transition-all ${formData.theme_color === 'purple'
                        ? 'bg-purple-50 border-purple-300 shadow-sm'
                        : 'border-gray-200 hover:border-purple-200 hover:bg-purple-50/30'}`}
                      onClick={() => setFormData({ ...formData, theme_color: 'purple' })}
                    >
                      <div className="flex items-center">
                        <div className={`w-5 h-5 flex-shrink-0 rounded-full bg-purple-600 border ${formData.theme_color === 'purple'
                          ? 'ring-2 ring-purple-600 ring-offset-2'
                          : 'border-gray-300'}`}>
                        </div>
                        <div className="ml-3">
                          <h4 className="font-medium text-gray-900">Purple</h4>
                        </div>
                      </div>
                    </div>

                    {/* Yellow Theme */}
                    <div
                      className={`border rounded-lg p-3 cursor-pointer transition-all ${formData.theme_color === 'yellow'
                        ? 'bg-yellow-50 border-yellow-300 shadow-sm'
                        : 'border-gray-200 hover:border-yellow-200 hover:bg-yellow-50/30'}`}
                      onClick={() => setFormData({ ...formData, theme_color: 'yellow' })}
                    >
                      <div className="flex items-center">
                        <div className={`w-5 h-5 flex-shrink-0 rounded-full bg-yellow-600 border ${formData.theme_color === 'yellow'
                          ? 'ring-2 ring-yellow-600 ring-offset-2'
                          : 'border-gray-300'}`}>
                        </div>
                        <div className="ml-3">
                          <h4 className="font-medium text-gray-900">Yellow</h4>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Select a theme color for this product's display card.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Specifications
                  </label>
                  {formData.specifications.map((spec, index) => (
                    <div key={`spec-${index}`} className="flex items-center space-x-2 mb-2">
                      <input
                        type="text"
                        value={spec}
                        onChange={(e) => {
                          const newSpecs = [...formData.specifications];
                          newSpecs[index] = e.target.value;
                          setFormData({ ...formData, specifications: newSpecs });
                        }}
                        onBlur={() => {
                          // Trim the value when the input loses focus
                          const newSpecs = [...formData.specifications];
                          newSpecs[index] = newSpecs[index].trim();
                          setFormData({ ...formData, specifications: newSpecs });
                        }}
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., Up to 5 users, 24/7 support, etc."
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newSpecs = formData.specifications.filter((_, i) => i !== index);
                          // Ensure there's always at least one input field
                          setFormData({ ...formData, specifications: newSpecs.length ? newSpecs : [''] });
                        }}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={() => {
                      setFormData({ ...formData, specifications: [...formData.specifications, ''] });
                    }}
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Add Specification
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Monthly Discount (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={formData.monthly_discount}
                      onChange={(e) => setFormData({ ...formData, monthly_discount: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter the discount percentage for monthly billing (0-100).
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Yearly Discount (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={formData.yearly_discount}
                      onChange={(e) => setFormData({ ...formData, yearly_discount: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter the discount percentage for yearly billing (0-100).
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Stripe Monthly Price ID <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.stripe_monthly_price_id}
                      onChange={(e) => setFormData({ ...formData, stripe_monthly_price_id: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="price_1RECFcef6Q1C2PErRkUF9am"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter the Stripe Price ID for this product (starts with 'price_').
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Stripe Yearly Price ID <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.stripe_yearly_price_id}
                      onChange={(e) => setFormData({ ...formData, stripe_yearly_price_id: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="price_1RECFcef6Q1C2PErRkUF9am"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter the Stripe Price ID for this product (starts with 'price_').
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Stripe Product ID <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.stripe_product_id}
                      onChange={(e) => setFormData({ ...formData, stripe_product_id: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="prod_1RECYCef6Q1C2PEZ4SmS8"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter the Stripe Product ID for this product (starts with 'prod_').
                    </p>
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700"
                  >
                    {editingId ? 'Update Product' : 'Add Product'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
      <div className="relative min-h-[200px]">
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gray-50 rounded-lg p-8 max-w-md mx-auto">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-2 text-gray-700">No Products Found</h3>
              <p className="text-gray-500 mb-4">
                There are currently no products matching your search criteria.
              </p>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pages
                      </th>
                      <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price & Discounts
                      </th>
                      <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stripe IDs
                      </th>
                      <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 bg-gray-50"></th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paginatedProducts.map((product) => (
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <Package className="h-5 w-5 text-gray-400 mr-3" />
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {product.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {product.description.substring(0, 50)}...
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.category === 'Products' ? 'bg-blue-100 text-blue-800' : product.category === 'GrantReady' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}`}>
                            {product.category}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex flex-wrap gap-1">
                            {product.pages && product.pages.length > 0 ? (
                              product.pages.map((page, index) => (
                                <span key={index} className={`px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${page === 'Products' ? 'bg-blue-100 text-blue-800' :
                                  page === 'GrantReady' ? 'bg-purple-100 text-purple-800' :
                                    page === 'SOAR' ? 'bg-green-100 text-green-800' :
                                      page === 'Elenor' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-gray-100 text-gray-800'
                                  }`}>
                                  {page}
                                </span>
                              ))
                            ) : (
                              <span className="text-xs text-gray-500">No pages selected</span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex flex-col">
                            <span>${product.price.toFixed(2)}</span>
                            {((product.monthly_discount ?? 0) > 0 || (product.yearly_discount ?? 0) > 0) && (
                              <div className="text-xs mt-1">
                                {(product.monthly_discount ?? 0) > 0 && (
                                  <span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full mr-1">
                                    Monthly: {product.monthly_discount}% off
                                  </span>
                                )}
                                {(product.yearly_discount ?? 0) > 0 && (
                                  <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                                    Yearly: {product.yearly_discount}% off
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex flex-col">
                            {product.stripe_yearly_price_id ? (
                              <span className="text-xs mb-1 truncate max-w-[150px]"
                                title={"Stripe yearly price ID: " + product.stripe_yearly_price_id}>
                                S.Y.Price_ID: {product.stripe_yearly_price_id}
                              </span>
                            ) : (
                              <span className="text-xs mb-1 text-red-500">No Yearly Price ID</span>
                            )}
                            {product.stripe_monthly_price_id ? (
                              <span className="text-xs mb-1 truncate max-w-[150px]"
                                title={"Stripe monthly price ID: " + product.stripe_monthly_price_id}>
                                S.M.Price: {product.stripe_monthly_price_id}
                              </span>
                            ) : (
                              <span className="text-xs mb-1 text-red-500">No Monthly Price ID</span>
                            )}
                            {product.stripe_product_id ? (
                              <span className="text-xs truncate max-w-[150px]" title={product.stripe_product_id}>
                                S.Product: {product.stripe_product_id}</span>
                            ) : (
                              <span className="text-xs text-red-500">No Stripe Product ID</span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(product.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={() => handleEdit(product)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Edit className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => handleDelete(product.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 className="h-5 w-5" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination Controls */}
              {!loading && products.length > 0 && (
                <div className="border-t border-gray-200 bg-white px-4 py-3 mt-4">
                  {/* Results info and page size selector */}
                  <div className="flex flex-wrap items-center justify-between mb-4">
                    <div className="text-sm text-gray-700 mb-2 sm:mb-0 flex items-center">
                      Showing <span className="font-medium mx-1">{products.length > 0 ? (currentPage - 1) * pageSize + 1 : 0}</span> to{' '}
                      <span className="font-medium mx-1">{Math.min(currentPage * pageSize, totalCount)}</span> of{' '}
                      <span className="font-medium mx-1">{totalCount}</span> results
                    </div>

                    <div className="flex items-center">
                      <span className="mr-2 text-sm text-gray-700">Rows per page:</span>
                      <div className="relative">
                        <select
                          value={pageSize}
                          onChange={(e) => {
                            const newSize = Number(e.target.value);
                            setPageSize(newSize);
                            // Adjust current page to maintain position in data as much as possible
                            const firstItemIndex = (currentPage - 1) * pageSize;
                            const newPage = Math.floor(firstItemIndex / newSize) + 1;
                            setCurrentPage(Math.min(newPage, Math.ceil(totalCount / newSize) || 1));
                          }}
                          className="appearance-none h-full rounded-md border-gray-300 bg-white py-0 pl-3 pr-7 text-gray-500 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value={5}>5</option>
                          <option value={10}>10</option>
                          <option value={20}>20</option>
                          <option value={50}>50</option>
                        </select>
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                          <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Simplified Pagination controls - "Page X of Y" style */}
                  <div className="flex items-center justify-center">
                    <nav className="inline-flex rounded-md shadow-sm border border-gray-300" aria-label="Pagination">
                      {/* Previous page button */}
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage <= 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Previous</span>
                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </button>

                      {/* Page X of Y */}
                      <div className="relative inline-flex items-center px-4 py-2 bg-gray-100 text-sm font-medium text-gray-700 border-l border-r border-gray-300">
                        Page <span className="font-semibold mx-1 text-blue-600">{currentPage}</span> of <span className="font-semibold mx-1">{totalPages}</span>
                      </div>

                      {/* Next page button */}
                      <button
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage >= totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Next</span>
                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </nav>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
