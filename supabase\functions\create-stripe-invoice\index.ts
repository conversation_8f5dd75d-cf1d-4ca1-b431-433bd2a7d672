import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
// Using the import from your current file version
import Stripe from 'https://esm.sh/stripe@11.16.0?target=deno';
import { corsHeaders } from '../_shared/cors.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.0.0?target=deno';

// Interface for items intended for a subscription
interface SubscriptionItemInput {
  priceId: string; // Stripe Price ID (e.g., price_xxxx) - MUST BE RECURRING
  quantity: number;
}

// Updated payload interface
interface CreateSubscriptionInvoicePayload {
  customerId: string; // Stripe Customer ID (can be empty if creating new customer)
  userId?: string; // Supabase user ID (required if creating new customer)
  userEmail?: string; // User email (required if creating new customer)
  // Renamed 'items' to 'subscriptionItems' for clarity
  subscriptionItems: SubscriptionItemInput[];
  description?: string; // Optional invoice description
  daysUntilDue?: number; // Optional days until the invoice is due
}

// Initialize Stripe client (using your current setup)
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY')!, {
  apiVersion: '2023-10-16',
  // httpClient: Stripe.createFetchHttpClient(), // Keep commented out as per previous step
});

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // --- 1. Parse and Validate Input ---
    const payload: CreateSubscriptionInvoicePayload = await req.json();
    console.log("Received payload for subscription invoice:", payload);

    // Remove this validation check that's causing the error
    // if (!payload.customerId) {
    //   throw { message: 'Missing required field: customerId', statusCode: 400 };
    // }

    // Instead, check if we have enough info to either use existing customer or create new one
    if (!payload.customerId && (!payload.userId || !payload.userEmail)) {
      throw {
        message: 'Either a valid customerId or both userId and userEmail are required',
        statusCode: 400
      };
    }

    // Check for subscriptionItems instead of items
    if (!Array.isArray(payload.subscriptionItems) || payload.subscriptionItems.length === 0) {
      throw { message: 'Missing or empty required field: subscriptionItems', statusCode: 400 };
    }
    if (!payload.subscriptionItems.every(item => item.priceId && typeof item.quantity === 'number' && item.quantity > 0)) {
      throw { message: 'Invalid subscriptionItems array. Each item must have a priceId (string) and quantity (positive number).', statusCode: 400 };
    }

    // --- 1.5. Check and Create Stripe Customer if needed ---
    let customerId: string;
    if (!payload.customerId || payload.customerId.trim() === '') {
      // If no customer ID provided, we need user email to create a customer
      if (!payload.userEmail) {
        throw { message: 'Missing user email required to create a Stripe customer', statusCode: 400 };
      }

      console.log(`No valid Stripe customer ID provided. Creating new customer for email: ${payload.userEmail}`);
      try {
        const customer = await stripe.customers.create({
          email: payload.userEmail,
          metadata: { supabase_user_id: payload.userId },
        });

        customerId = customer.id;
        console.log(`Created new Stripe customer: ${customerId}`);

        // Update the user's profile with the new Stripe customer ID
        // This requires Supabase client with admin privileges
        const supabaseAdmin = createClient(
          Deno.env.get('SUPABASE_URL') || '',
          Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '',
          { auth: { persistSession: false } }
        );

        const { error: updateError } = await supabaseAdmin
          .from('profiles')
          .update({ stripe_customer_id: customerId })
          .eq('id', payload.userId);

        if (updateError) {
          console.error(`Failed to update user profile with new Stripe customer ID: ${updateError.message}`);
          // Continue anyway since we have the customer ID for this operation
        }
      } catch (error) {
        console.error('Error creating Stripe customer:', error);
        throw { message: 'Failed to create Stripe customer', statusCode: 500 };
      }
    } else {
      // Use the provided customer ID
      customerId = payload.customerId;
    }

    // Use subscriptionItems
    const subscriptionItemsInput = payload.subscriptionItems.map(item => ({
      price: item.priceId,
      quantity: item.quantity,
    }));
    const description = payload.description;
    const daysUntilDue = payload.daysUntilDue ?? 30; // Default to 30 days

    // --- 2. Create the Subscription ---
    // We create the subscription and tell Stripe to generate an invoice for the first payment.
    console.log(`Creating subscription for customer ${customerId}...`);
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: subscriptionItemsInput,
      // 'send_invoice' collection method for the subscription's invoices
      collection_method: 'send_invoice',
      days_until_due: daysUntilDue,
      // Expand the latest invoice object so we can return it
      expand: ['latest_invoice'],
      description: description, // Optional description for the subscription itself
      // You might add trial periods, metadata, etc. here if needed
    });
    console.log(`Successfully created subscription ${subscription.id}.`);

    // --- 3. Check for the Invoice ---
    // The invoice is automatically created by Stripe when using collection_method: 'send_invoice'
    // and should be included in the 'latest_invoice' expansion.
    if (!subscription.latest_invoice || typeof subscription.latest_invoice === 'string') {
      // This shouldn't happen with the expand parameter, but good to check.
      console.error('Error: Subscription created, but latest_invoice was not expanded or found.');
      throw { message: 'Failed to retrieve the invoice associated with the new subscription.', statusCode: 500 };
    }

    const invoice = subscription.latest_invoice; // This is the Stripe Invoice object
    console.log(`Invoice ${invoice.id} generated for subscription. Status: ${invoice.status}`);

    // Note: Stripe automatically finalizes and sends the invoice when the subscription
    // is created with collection_method: 'send_invoice' and days_until_due.
    // Manual finalization/sending is usually not required here.

    // --- 4. Return the Created Invoice ---
    // Return the invoice object, not the subscription object
    return new Response(
      JSON.stringify(invoice),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 201, // 201 Created
      }
    );

  } catch (error) {
    // --- Enhanced Error Logging ---
    console.error('!!! Create Subscription Invoice Function Error !!!'); // Updated log prefix
    console.error('Error Object:', JSON.stringify(error, null, 2));

    let errorMessage = 'An unexpected error occurred.';
    let statusCode = 500;

    if (error && typeof error === 'object' && 'type' in error) { // Stripe error
      console.error('Stripe Error Type:', error.type);
      console.error('Stripe Error Message:', error.message);
      errorMessage = `Stripe Error (${error.type}): ${error.message || 'No message provided.'}`;
      statusCode = typeof error.statusCode === 'number' ? error.statusCode : 500;
    } else if (error instanceof Error) { // Standard JS error
      errorMessage = error.message;
      if ('statusCode' in error && typeof error.statusCode === 'number') {
        statusCode = error.statusCode;
      }
    } else { // Other types
      errorMessage = String(error);
    }
    // --- End Enhanced Error Logging ---

    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    });
  }
});
