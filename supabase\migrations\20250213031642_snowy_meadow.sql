-- First ensure the user exists in standalone_admins
INSERT INTO standalone_admins (username, role)
VALUES (
  '<EMAIL>',
  'admin'
)
ON CONFLICT (username) 
DO UPDATE SET
  role = 'admin',
  last_login = NULL;

-- Function to check admin status with better error handling
CREATE OR REPLACE FUNCTION is_admin(email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check both auth.users and standalone_admins
  RETURN EXISTS (
    SELECT 1 
    FROM standalone_admins a
    JOIN auth.users u ON u.email = a.username
    WHERE a.username = email
    AND a.role = 'admin'
  );
END;
$$;

-- Function to verify admin access
CREATE OR REPLACE FUNCTION verify_admin_access(user_email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM standalone_admins 
    WHERE username = user_email
    AND role = 'admin'
  );
END;
$$;

-- <PERSON> execute permissions
GRANT EXECUTE ON FUNCTION is_admin TO authenticated;
GRANT EXECUTE ON FUNCTION verify_admin_access TO authenticated;