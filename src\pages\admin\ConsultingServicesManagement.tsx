import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { Plus, Edit, Trash2, AlertCircle, CheckCircle } from 'lucide-react';

interface ConsultingService {
  id: string;
  title: string;
  description: string;
  image_url: string | null;
  created_at: string;
  updated_at: string;
}

export default function ConsultingServicesManagement() {
  const [services, setServices] = useState<ConsultingService[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('ConsultingServicesManagement mounted');
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      console.log('Fetching consulting services...');
      setLoading(true);
      const { data, error } = await supabase
        .from('consulting_services')
        .select('*')
        .order('created_at', { ascending: false });

      console.log('Supabase response:', { data, error });

      if (error) throw error;
      setServices(data || []);
      console.log('Services set:', data);
    } catch (err: any) {
      console.error('Error fetching services:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  console.log('Rendering with:', { services, loading, error });

  if (loading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Consulting Services</h2>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Consulting Services</h2>
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Consulting Services</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
          <Plus className="h-5 w-5 mr-2" />
          Add Service
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="p-4">
          <p>Services count: {services.length}</p>
          {services.length === 0 ? (
            <p className="text-gray-500">No consulting services found. The table might not exist or be empty.</p>
          ) : (
            <div className="space-y-2">
              {services.map((service) => (
                <div key={service.id} className="border p-3 rounded">
                  <h3 className="font-semibold">{service.title}</h3>
                  <p className="text-sm text-gray-600">{service.description}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

