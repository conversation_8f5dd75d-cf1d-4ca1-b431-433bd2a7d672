import React from 'react';
import { Link } from 'react-router-dom';
import { FileText, Lock, Zap, ArrowRight } from 'lucide-react';

function Solutions() {
  const solutions = [
    {
      id: 'soar',
      title: 'SOAR',
      description: 'STLT Outbreak Analytics & Response - Disease outbreak prediction and response.',

      icon: Lock,
      link: '/solutions/soar',
      buttonText: 'Learn About Technology',
      // SOAR color schema
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #F3AE7A 0%, #EE8149 100%)',
      iconColor: 'text-white',
      textColor: 'text-white'
    },
    {
      id: 'grantready',
      title: 'GrantReady™',
      description: 'Streamline your grant management process with our comprehensive solution.',
      icon: FileText,
      link: '/solutions/grantready',
      buttonText: 'Discover Our Solution',
      // GrantReady color schema
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #41AC90 0%, #7681E6 100%)',
      iconColor: 'text-white',
      textColor: 'text-white',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80'
    },
    {
      id: 'elenor',
      title: 'ELENOR',
      description: 'Advanced emergency response and coordination platform.',
      icon: Zap,
      link: '/solutions/elenor',
      buttonText: 'Learn About Sustainability',
      // ELENOR color schema
      bgColor: 'radial-gradient(50% 55% at 0% 0%, #3082F5 0%, #313791 100%)',
      iconColor: 'text-white',
      textColor: 'text-white'
    }
  ];

  return (
    <section className=" py-8 sm:py-12 md:py-16 lg:py-20 xl:py-24 bg-white relative overflow-hidden">
      {/* Background decorative shapes */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Large background shapes behind cards */}
        <div className="absolute top-52 left-20 w-72 h-32 bg-indigo-100 rounded-3xl transform rotate-[4deg] opacity-60"></div>
        <div className="absolute bottom-14 right-20 w-72 h-32 bg-orange-200 rounded-3xl transform rotate-[4deg] opacity-60"></div>

        {/* Top left stars */}
        <div className="absolute top-16 left-8 text-indigo-200 transform rotate-12">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 0l2.5 5.5L16 8l-5.5 2.5L8 16l-2.5-5.5L0 8l5.5-2.5L8 0z" />
          </svg>
        </div>
        <div className="absolute top-24 left-16 text-indigo-200 transform -rotate-12">
          <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 0l2.5 5.5L16 8l-5.5 2.5L8 16l-2.5-5.5L0 8l5.5-2.5L8 0z" />
          </svg>
        </div>

        {/* Top right stars */}
        <div className="absolute top-12 right-12 text-orange-200 transform rotate-45">
          <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 0l2.5 5.5L16 8l-5.5 2.5L8 16l-2.5-5.5L0 8l5.5-2.5L8 0z" />
          </svg>
        </div>
        <div className="absolute top-20 right-8 text-orange-200 transform -rotate-30">
          <svg width="10" height="10" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 0l2.5 5.5L16 8l-5.5 2.5L8 16l-2.5-5.5L0 8l5.5-2.5L8 0z" />
          </svg>
        </div>

        {/* Bottom geometric shapes */}
        <div className="absolute bottom-16 left-12 text-indigo-100 transform rotate-45">
          <div className="w-6 h-6 border-2 border-current"></div>
        </div>
        <div className="absolute bottom-24 right-16 text-orange-100 transform -rotate-12">
          <div className="w-4 h-4 bg-current"></div>
        </div>

        {/* Additional scattered elements */}
        <div className="absolute top-1/3 left-4 text-gray-200 transform rotate-12">
          <div className="w-3 h-3 bg-current rounded-full"></div>
        </div>
        <div className="absolute top-2/3 right-6 text-gray-200 transform -rotate-45">
          <div className="w-2 h-8 bg-current"></div>
        </div>
      </div>

      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-8 sm:mb-10 md:mb-12 lg:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-2 sm:mb-3 md:mb-4">Our Platforms</h2>
          <p className="text-base sm:text-lg md:text-xl text-gray-700 max-w-3xl mx-auto px-2 sm:px-4">
            Comprehensive platforms designed for public health excellence
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 max-w-7xl mx-auto">
          {solutions.map((solution) => {
            const IconComponent = solution.icon;
            return (
              <div
                key={solution.id}
                className={`rounded-xl overflow-hidden ${solution.bgColor} h-full relative w-full`}
                style={{
                  background: `${solution.bgColor}`,
                  boxShadow: '-8px -8px 16px rgba(255, 255, 255, 0.1), 4px 4px 12px rgba(0, 0, 0, 0.1)',
                  minWidth: '280px'
                }}
              >
                {solution.id === 'grantready' ? (
                  // Special layout for GrantReady with large image
                  <div className="relative h-full min-h-[320px] sm:min-h-[380px] md:min-h-[420px]">
                    {/* Background image */}
                    <img
                      src={solution.image}
                      alt={solution.title}
                      className="absolute inset-0 w-full h-full object-cover"
                    />
                    {/* Dark overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-40"></div>
                    {/* Content overlay */}
                    <div className="relative z-10 p-6 sm:p-7 md:p-8 h-full flex flex-col justify-between">
                      {/* Header with icon and title */}
                      <div className="flex items-center mb-4 sm:mb-5">
                        <IconComponent className={`h-6 w-6 sm:h-7 sm:w-7 mr-3 sm:mr-4 ${solution.iconColor}`} />
                        <h3 className={`text-xl sm:text-2xl font-bold ${solution.textColor}`}>{solution.title}</h3>
                      </div>

                      {/* Bottom content */}
                      <div>
                        {/* Description */}
                        <p className={`${solution.textColor} mb-5 sm:mb-7 text-sm sm:text-base leading-relaxed`}>
                          {solution.description}
                        </p>

                        {/* Button */}
                        <Link
                          to={solution.link}
                          className="inline-flex items-center bg-white text-gray-800 pl-5 pr-1 py-2 rounded-md font-medium text-sm sm:text-base hover:bg-gray-100 transition-colors duration-200 self-start"
                        >
                          {solution.buttonText}
                          <span className='bg-black ml-4 flex items-center px-3 py-2 justify-center rounded-md'>
                            <ArrowRight className="text-white w-4 h-4" />
                          </span>
                        </Link>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Standard layout for SOAR and ELENOR
                  <div className="relative p-6 sm:p-7 md:p-8 h-full flex flex-col min-h-[320px] sm:min-h-[380px] md:min-h-[420px]">
                    {/* Header with icon and title */}
                    <div className="flex items-center mb-4 sm:mb-5">
                      <IconComponent className={`h-6 w-6 sm:h-7 sm:w-7 mr-3 sm:mr-4 ${solution.iconColor}`} />
                      <h3 className={`text-xl sm:text-2xl font-bold ${solution.textColor}`}>{solution.title}</h3>
                    </div>

                    {/* Content area */}
                    <div className="absolute bottom-6 sm:bottom-7 md:bottom-8 left-6 sm:left-7 md:left-8 right-6 sm:right-7 md:right-8 flex-grow flex flex-col justify-between">
                      {/* Description */}
                      <p className={`${solution.textColor} mb-5 sm:mb-7 text-sm sm:text-base leading-relaxed`}>
                        {solution.description}
                      </p>

                      {/* Button */}
                      <Link
                        to={solution.link}
                        className="inline-flex items-center bg-white text-gray-800 pl-5 pr-1 py-2 rounded-md font-medium text-sm sm:text-base hover:bg-gray-100 transition-colors duration-200 self-start"
                      >
                        {solution.buttonText}
                        <span className='bg-black ml-4 flex items-center px-3 py-2 justify-center rounded-md'>
                          <ArrowRight className="text-white w-4 h-4" />
                        </span>
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}

export default Solutions;