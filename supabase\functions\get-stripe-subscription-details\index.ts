// supabase/functions/get-stripe-subscription-details/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import <PERSON><PERSON> from 'https://esm.sh/stripe@11.1.0?target=deno'

// Define allowed origins. '*' is permissive, but for development localhost is fine.
// For production, replace '*' or add your production frontend URL.
const allowedOrigins = [
  'http://localhost:5173', // Your local dev environment
  // Add your production frontend URL here, e.g., 'https://your-app.com'
];

// Helper function to create CORS headers
const createCorsHeaders = (origin: string | null) => {
  const headers = new Headers();
  // Check if the request origin is allowed
//   const isAllowed = origin && allowedOrigins.includes(origin);
  headers.set('Access-Control-Allow-Origin','*' ); // Allow specific origin or fall back
  headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS'); // Allow POST and OPTIONS methods
  headers.set('Access-Control-Allow-Headers', '*'); // Crucial headers for Supabase client
  headers.set('Content-Type', 'application/json'); // Default content type for responses
  return headers;
};


// Get Stripe key from environment variables (set via Supabase secrets)
const stripe = Stripe(Deno.env.get('STRIPE_SECRET_KEY')!, {
  httpClient: Stripe.createFetchHttpClient(), // Required for Deno runtime
  apiVersion: '2023-10-16', // Use a specific API version
})

serve(async (req) => {
  const requestOrigin = req.headers.get('origin');
  const corsHeaders = createCorsHeaders(requestOrigin);

  // Handle OPTIONS preflight request
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 204, headers: corsHeaders }); // Respond with 204 No Content and CORS headers
  }

  // 1. Ensure it's a POST request
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), { status: 405, headers: corsHeaders }); // Add CORS headers to error response
  }

  let stripeSubscriptionId: string | null = null;
  try {
    const body = await req.json();
    stripeSubscriptionId = body.stripeSubscriptionId;
    if (!stripeSubscriptionId) throw new Error("Missing stripeSubscriptionId in request body");
  } catch (e: any) { // Added type annotation for 'e'
     return new Response(JSON.stringify({ error: 'Invalid request body', details: e.message }), { status: 400, headers: corsHeaders }); // Add CORS headers
  }


  try {
    // 2. Fetch Subscription details from Stripe with more expansions
    const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId, {
      expand: [
        'default_payment_method',
        'latest_invoice',
        'customer', // Expand customer details if needed
        'schedule', // Expand any associated subscription schedule
        'pending_update', // Expand details about pending updates (e.g., plan changes)
        'items', // Expand the subscription items directly from Stripe
        'items.data.price.product' // Expand product details within items
      ],
    });

    const invoices = await stripe.invoices.list({
        subscription: stripeSubscriptionId,
        limit: 5, // Get the last 5 invoices for this subscription
    });

    // 4. Format the response data
    const paymentMethod = subscription.default_payment_method as Stripe.PaymentMethod | null;
    const paymentMethodDetails = paymentMethod?.card
        ? `${paymentMethod.card.brand?.toUpperCase()} ending in ${paymentMethod.card.last4}`
        : paymentMethod?.type // Show type if card details aren't available
        ? `Type: ${paymentMethod.type}`
        : 'Payment method details unavailable';

    // Extract expanded Stripe items (these might differ slightly from your DB items)
    const stripeItems = subscription.items.data.map(item => ({
        id: item.id,
        stripe_price_id: item.price.id,
        product_name: (item.price.product as Stripe.Product)?.name || 'Unknown Product',
        product_id: (item.price.product as Stripe.Product)?.id || null, // Stripe Product ID
        quantity: item.quantity,
        // Add other relevant fields from Stripe's SubscriptionItem if needed
    }));

    const responseData = {
      // Core subscription details from Stripe object
      status: subscription.status,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      cancel_at_period_end: subscription.cancel_at_period_end,
      canceled_at: subscription.canceled_at,
      created: subscription.created, // Stripe uses 'created' timestamp
      // Expanded details
      payment_method_details: paymentMethodDetails,
      customer_email: (subscription.customer as Stripe.Customer)?.email || null, // Example: get customer email
      schedule: subscription.schedule, // Include the schedule object if present
      pending_update: subscription.pending_update, // Include pending update details if present
      stripe_items: stripeItems, // Include items fetched directly from Stripe
      // Invoice details
      latest_invoice_id: (subscription.latest_invoice as Stripe.Invoice)?.id || null,
      recent_invoices: invoices.data.map(inv => ({
          id: inv.id,
          created: inv.created,
          total: inv.total,
          status: inv.status,
          invoice_pdf: inv.invoice_pdf,
          due_date: inv.due_date,
      })),
    };


    // 5. Return the data
    return new Response(JSON.stringify(responseData), {
      headers: corsHeaders, // Use the CORS headers here
      status: 200,
    })
  } catch (error: any) {
    console.error("Stripe API Error:", error);
    // Return a generic error or more specific based on Stripe error type
    return new Response(JSON.stringify({ error: 'Failed to fetch data from Stripe', details: error.message }), {
      headers: corsHeaders, // Use the CORS headers here
      status: 500, // Or appropriate error code
    })
  }
})

// Deploy command: supabase functions deploy get-stripe-subscription-details --no-verify-jwt