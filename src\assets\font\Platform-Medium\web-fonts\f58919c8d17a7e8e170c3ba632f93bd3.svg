<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20200314 at Wed Sep  1 13:56:37 2010
 By Unknown
Copyright (c) 2010 <PERSON><PERSON>, under exclusive license to Schwartzco Inc. d/b/a Commercial Type.
</metadata>
<defs>
<font id="Platform-Medium" horiz-adv-x="400" >
  <font-face 
    font-family="Platform Medium"
    font-weight="500"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 6 4 3 2 2 2 2 4"
    ascent="825"
    descent="-175"
    x-height="535"
    cap-height="692"
    bbox="-58.129 -259 1156 1073"
    underline-thickness="50"
    underline-position="-200"
    unicode-range="U+0020-FB02"
  />
<missing-glyph horiz-adv-x="151" 
 />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="540" 
d="M314 535v24q0 84 39 128.5t131 44.5q30 0 43 -1v-112h-25q-54 0 -54 -61v-23h79v-113h-79v-422h-134v422h-118v-422h-134v422h-50v113h50v24q0 84 39 128.5t131 44.5q30 0 43 -1v-112h-25q-54 0 -54 -61v-23h118z" />
    <glyph glyph-name=".notdef" horiz-adv-x="151" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="151" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="236" 
d="M186 691v-501h-136v502zM119 -10q-35 0 -60.5 25t-25.5 60t25.5 59.5t60.5 24.5t59 -24.5t24 -59.5t-24.5 -60t-58.5 -25z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="358" 
d="M31 692h122v-256h-122v256zM205 692h122v-256h-122v256z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="611" 
d="M533 172h-105l-42 -172h-118l41 172h-95l-42 -172h-119l42 172h-79v109h105l37 151h-80v109h106l37 151h120l-37 -151h94l37 151h120l-37 -151h76v-109h-103l-37 -151h79v-109zM277 432l-37 -151h96l36 151h-95z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="498" 
d="M287 760v-58q88 -11 128 -66.5t37 -137.5h-111q0 73 -54 95v-181q18 -6 50 -22q67 -31 102 -78t35 -122q0 -81 -51 -135t-136 -65v-59h-77v58q-53 5 -91.5 26.5t-58.5 54t-28.5 68t-7.5 75.5h119q-2 -95 67 -114v205l-51 22q-62 30 -94 75.5t-32 116.5t48 122.5t129 61.5
v58h77zM348 182q0 31 -15 49.5t-46 36.5v-168q61 18 61 82zM155 523q0 -28 13.5 -45t41.5 -32v150q-55 -15 -55 -73z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="794" 
d="M534 164q0 -31 19 -52t50 -21q33 0 51 20.5t18 52.5t-19 52t-51 20t-50 -20.5t-18 -51.5zM429 163q0 74 48 122.5t124 48.5q78 0 127 -48.5t49 -122.5q0 -72 -48.5 -120.5t-125.5 -48.5q-78 0 -126 47.5t-48 121.5zM713 692l-512 -692h-116l513 692h115zM122 529
q0 -31 19 -51.5t50 -20.5q33 0 51.5 20.5t18.5 51.5q0 32 -19.5 52.5t-51.5 20.5t-50 -21t-18 -52zM18 529q0 73 48 121.5t123 48.5q79 0 127.5 -48t48.5 -122q0 -73 -48.5 -121.5t-125.5 -48.5q-78 0 -125.5 48t-47.5 122z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="664" 
d="M516 0l-44 45q-77 -56 -182 -56q-125 0 -197.5 60t-72.5 154q0 62 29 111t82 76q-55 52 -55 130t55 130.5t147 52.5q96 0 150.5 -51t54.5 -132q0 -68 -39 -113t-98 -54l113 -116q21 61 18 134h130q7 -141 -53 -236l130 -135h-168zM293 110q53 0 90 25l-173 176
q-48 -29 -48 -90q0 -49 36.5 -80t94.5 -31zM204 516q0 -33 22.5 -55t55.5 -22q34 0 55.5 22t21.5 57q-1 33 -23 54t-56 21q-33 0 -54.5 -22t-21.5 -55z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="184" 
d="M31 692h122v-256h-122v256z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="268" 
d="M40 28v565q0 203 204 203h8v-119h-7q-44 0 -61 -21t-17 -62v-567q0 -42 17 -63t61 -21h7v-118h-8q-204 0 -204 203z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="268" 
d="M228 593v-565q0 -203 -204 -203h-8v118h7q44 0 61 21t17 63v567q0 41 -17 62t-61 21h-7v119h8q204 0 204 -203z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="404" 
d="M28 564l45 78l84 -49v99h90v-99l84 49l44 -77l-84 -49l85 -49l-45 -78l-84 50v-100h-90v100l-84 -50l-45 77l84 49z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="470" 
d="M27 281v112h148v153h120v-153h147v-112h-147v-154h-120v154h-148z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="208" 
d="M23 -111l55 109q-26 7 -42 28t-17 50q-1 36 23.5 60.5t62.5 24.5q37 0 60.5 -24.5t23.5 -61.5q0 -32 -21 -77l-53 -109h-92z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="306" 
d="M31 223v126h244v-126h-244z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="208" 
d="M104 -11q-36 0 -60.5 25t-24.5 61q0 37 24 61.5t61 24.5t61 -24.5t24 -61.5q0 -36 -24.5 -61t-60.5 -25z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="464" 
d="M466 731l-345 -906h-123l345 906h123z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="527" 
d="M349 476q0 44 -21.5 71t-63.5 27q-85 0 -85 -98v-260q0 -97 85 -97q42 0 63.5 26.5t21.5 70.5v260zM488 476v-258q0 -106 -59.5 -168.5t-166.5 -62.5q-112 0 -167 61t-55 170v258q0 107 56.5 168t167.5 61q107 0 165.5 -62t58.5 -167z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="261" 
d="M216 692v-692h-139v552l-65 -24v131l75 33h129z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="495" 
d="M471 0h-443v122l222 232q37 38 54.5 68.5t17.5 66.5q0 39 -21 62t-59 23q-42 0 -59.5 -26.5t-17.5 -60.5h-137q-1 30 4.5 59t20.5 58.5t38.5 51.5t62.5 35.5t88 13.5q108 0 166 -58.5t58 -150.5q0 -65 -26 -112.5t-79 -100.5l-152 -158h262v-125z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="497" 
d="M144 299v108l139 160h-241v125h402v-124l-143 -154q78 -10 126 -59.5t48 -143.5q0 -100 -64 -162t-167 -62q-63 0 -109.5 19.5t-71 52.5t-35.5 69.5t-11 77.5h137q2 -39 24.5 -64t65.5 -25q40 0 64 25.5t24 65.5q0 43 -25 67t-71 24h-92z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="538" 
d="M520 197h-74v-197h-136v197h-297v115l296 380h137v-375h74v-120zM310 513l-154 -196h154v196z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="504" 
d="M35 206h137q5 -87 84 -87q42 0 64 27t22 72v20q0 96 -84 96q-56 0 -76 -43h-135v401h393v-125h-259v-137q13 13 42 23.5t66 10.5q190 0 190 -222v-25q0 -100 -56.5 -165t-166.5 -65q-114 0 -167.5 60t-53.5 159z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="515" 
d="M463 640l-77 -103q-44 37 -97 37q-113 0 -111 -118v-28q13 15 44 28t71 13q194 0 194 -225v-30q0 -99 -59 -163t-166 -64q-111 0 -166.5 60.5t-55.5 167.5v238q0 112 59 182t185 70q114 0 179 -65zM264 340q-44 0 -65 -25.5t-21 -67.5v-33q0 -44 20.5 -70.5t64.5 -26.5
q43 0 64.5 27t21.5 70v31q0 95 -85 95z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="467" 
d="M444 601l-226 -601h-155l217 567h-264v125h428v-91z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="508" 
d="M172 500q0 -37 23 -60t60 -23t59.5 23t22.5 60t-23 60t-60 23t-59.5 -23t-22.5 -60zM33 506q0 82 57 140.5t164 58.5q108 0 165 -58t57 -141q0 -99 -74 -149q86 -52 86 -160q0 -88 -61 -149t-172 -61q-114 0 -174 60t-60 150q0 107 85 160q-73 52 -73 149zM164 202
q0 -40 25.5 -65.5t65.5 -25.5q41 0 66 25.5t25 65.5t-25.5 65.5t-65.5 25.5q-41 0 -66 -25.5t-25 -65.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="514" 
d="M51 50l76 106q51 -38 104 -38q109 0 105 118v28q-11 -15 -41 -28t-67 -13q-200 0 -200 225v30q0 100 58 163.5t164 63.5q112 0 168.5 -60.5t56.5 -166.5v-238q0 -116 -58 -184.5t-183 -68.5q-113 0 -183 63zM251 352q43 0 64 25.5t22 67.5v33q0 44 -21.5 70.5t-65.5 26.5
q-43 0 -64 -26.5t-21 -70.5v-30q0 -96 86 -96z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="208" 
d="M104 -11q-36 0 -60.5 25t-24.5 61q0 37 24 61.5t61 24.5t61 -24.5t24 -61.5q0 -36 -24.5 -61t-60.5 -25zM104 338q-36 0 -60.5 25t-24.5 61q0 37 24 61.5t61 24.5t61 -24.5t24 -61.5q0 -36 -24.5 -61t-60.5 -25z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="208" 
d="M23 -111l55 109q-26 7 -42 28t-17 50q-1 36 23.5 60.5t62.5 24.5q37 0 60.5 -24.5t23.5 -61.5q0 -32 -21 -77l-53 -109h-92zM104 338q-36 0 -60.5 25t-24.5 61q0 37 24 61.5t61 24.5t61 -24.5t24 -61.5q0 -36 -24.5 -61t-60.5 -25z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="417" 
d="M27 279v115l362 161v-119l-233 -97l233 -97v-120z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="474" 
d="M30 361v107h415v-107h-415zM30 195v107h415v-107h-415z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="410" 
d="M383 394v-115l-355 -157v120l226 97l-226 97v119z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="473" 
d="M292 326v-136h-133v234h73q39 0 62 20.5t23 59.5q0 36 -22.5 59t-58.5 23q-45 0 -63 -28.5t-18 -64.5h-135q-1 30 7 62t28.5 67t67.5 57.5t113 22.5q102 0 159 -55.5t57 -141.5q0 -76 -45 -125t-115 -54zM226 -10q-35 0 -60 24.5t-25 60.5q0 35 25 59.5t60 24.5
t59.5 -24.5t24.5 -59.5q0 -36 -24.5 -60.5t-59.5 -24.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="955" 
d="M610 134q-21 -26 -58.5 -44t-80.5 -18q-98 0 -157.5 59.5t-59.5 152.5q0 89 59 151t149 62q71 0 107 -36v31h117v-257q0 -61 53 -61q37 0 59.5 45t22.5 116q0 144 -89 223.5t-247 79.5t-252.5 -101t-94.5 -250q0 -158 102.5 -253t268.5 -95q144 0 245 87l61 -74
q-49 -50 -130.5 -82t-177.5 -32q-221 0 -352.5 126t-131.5 318q0 201 130 328t335 127q207 0 326 -111t119 -284q0 -122 -57.5 -195.5t-149.5 -73.5q-86 0 -116 61zM378 285q0 -43 25.5 -70.5t67.5 -27.5q43 0 69 27t26 71t-26.5 71.5t-68.5 27.5t-67.5 -28t-25.5 -71z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="648" 
d="M506 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM318 516l-75 -208h151z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="503" 
d="M482 203q0 -94 -57 -148.5t-152 -54.5h-227v692h213q101 0 154 -49t53 -138q0 -46 -20 -84t-53 -56q89 -43 89 -162zM253 295h-70v-174h70q41 0 63.5 24t22.5 64q0 41 -22.5 63.5t-63.5 22.5zM247 572h-64v-159h64q37 0 57 21t20 59q0 79 -77 79z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="627" 
d="M533 175l81 -97q-93 -91 -229 -91q-164 0 -263.5 101t-99.5 257q0 155 100 257.5t260 102.5q139 0 230 -81l-82 -104q-64 56 -147 56q-98 0 -156.5 -65t-58.5 -165q0 -99 60.5 -164t156.5 -65q81 0 148 58z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="572" 
d="M185 569v-446h8q99 -1 155 57.5t56 166.5q0 107 -55.5 165t-155.5 57h-8zM46 692h140q170 0 266.5 -92t96.5 -253q0 -160 -97 -253.5t-266 -93.5h-140v692z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="398" 
d="M366 0h-320v692h317v-130h-178v-144h173v-128h-173v-160h181v-130z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="396" 
d="M361 290h-176v-290h-139v692h320v-130h-181v-144h176v-128z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="727" 
d="M685 372v-372h-134v41q-66 -53 -173 -53q-159 0 -257.5 99t-98.5 260q0 151 99.5 254.5t256.5 103.5q195 0 284 -137l-108 -80q-65 88 -176 88q-94 0 -152.5 -64t-58.5 -165q0 -104 61 -167.5t156 -63.5q106 0 165 61v79h-113v116h249z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="646" 
d="M600 692v-692h-139v289h-276v-289h-139v692h139v-271h276v271h139z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="231" 
d="M185 692v-692h-139v692h139z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="288" 
d="M243 692v-526q0 -82 -36 -124.5t-130 -42.5q-42 0 -59 1v128h39q47 0 47 53v511h139z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="558" 
d="M185 692v-283l214 283h161l-258 -331l262 -361h-163l-216 297v-297h-139v692h139z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="380" 
d="M360 0h-314v692h139v-562h175v-130z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="867" 
d="M687 517l-190 -517h-133l-190 517v-517h-128v692h207l183 -511l183 511h202v-692h-134v517z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="668" 
d="M484 0l-303 456v-456h-135v692h144l297 -464v464h135v-692h-138z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="764" 
d="M22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5q-163 0 -261.5 101t-98.5 257zM168 346q0 -100 59 -164.5t155 -64.5q98 0 156 64t58 165q0 100 -59 165t-156 65q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="484" 
d="M472 493q0 -93 -58 -148t-154 -55h-76v-290h-138v692h214q103 0 157.5 -51.5t54.5 -147.5zM249 572h-65v-162h65q38 0 58.5 22.5t20.5 59.5t-20.5 58.5t-58.5 21.5z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="764" 
d="M22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5q0 -114 -61 -205l58 -50l-86 -101l-66 57q-89 -61 -205 -61q-163 0 -261.5 101t-98.5 257zM166 346q0 -101 60 -166.5t156 -65.5q57 0 103 23l-55 48l85 101l60 -53q23 51 23 113q0 101 -59.5 166.5
t-157.5 65.5q-99 0 -157 -65t-58 -167z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="531" 
d="M472 493q0 -120 -96 -171l164 -322h-149l-147 296h-60v-296h-138v692h214q103 0 157.5 -51.5t54.5 -147.5zM249 572h-65v-162h65q38 0 58.5 22.5t20.5 59.5t-20.5 58.5t-58.5 21.5z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="498" 
d="M172 517q0 -52 65 -79q11 -5 46.5 -19t57.5 -24q133 -59 133 -193q0 -98 -63 -156.5t-165 -58.5q-53 0 -93.5 13.5t-65 35t-40 52t-20.5 61t-3 64.5h132q-1 -46 20.5 -76t69.5 -30q38 0 61.5 21t23.5 59q0 58 -74 89q-10 4 -53.5 22t-66.5 31q-103 54 -103 181
q0 86 58 140.5t154 54.5q110 0 160 -57.5t46 -149.5h-128q0 39 -20.5 63.5t-57.5 24.5q-35 0 -54.5 -19.5t-19.5 -49.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="425" 
d="M408 562h-126v-562h-139v562h-126v130h391v-130z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="636" 
d="M317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="661" 
d="M255 0l-260 692h156l183 -517l184 517h148l-261 -692h-150z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="989" 
d="M635 0l-139 480l-140 -480h-157l-201 692h152l135 -507l140 507h149l140 -507l134 507h143l-196 -692h-160z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="628" 
d="M-5 0l236 358l-225 334h169l143 -214l140 214h163l-223 -337l235 -355h-161l-161 238l-161 -238h-155z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="510" 
d="M184 346l-193 346h155l112 -210l114 210h147l-196 -344v-348h-139v346z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="582" 
d="M556 0h-530v94l333 470h-329v128h524v-94l-337 -471h339v-127z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="268" 
d="M40 -175v971h212v-119h-85v-733h85v-119h-212z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="464" 
d="M343 -175l-345 906h124l344 -906h-123z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="268" 
d="M228 796v-971h-212v119h85v733h-85v119h212z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="493" 
d="M315 692l164 -307h-137l-96 180l-97 -180h-135l165 307h136z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="619" 
d="M30 -138v107h559v-107h-559z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="476" 
d="M102 710h131l76 -138h-126z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55
t-56 21t-55.5 -21.5t-21.5 -54.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="617" 
d="M169 268q0 -69 41 -113t107 -44q67 0 107 44t40 113t-41 113t-107 44q-67 0 -107 -44t-40 -113zM166 51v-51h-127v731h134v-249q24 26 66.5 44t93.5 18q120 0 192.5 -76t72.5 -200q0 -120 -76 -198.5t-198 -78.5q-49 0 -91 17.5t-67 42.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="488" 
d="M297 112q59 0 112 44l74 -90q-71 -75 -187 -75q-125 0 -201 78t-76 197q0 118 78.5 198t197.5 80q118 0 188 -72l-77 -93q-46 44 -110 44q-65 0 -104 -44t-39 -112q0 -70 42 -112.5t102 -42.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="617" 
d="M453 0v53q-25 -27 -67 -44.5t-91 -17.5q-124 0 -200 76.5t-76 198.5q0 118 76 198t195 80q97 0 154 -52v239h134v-731h-125zM153 268q0 -68 41.5 -112.5t106.5 -44.5q66 0 106.5 44.5t40.5 112.5t-41 112.5t-106 44.5q-66 0 -107 -44.5t-41 -112.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="560" 
d="M542 224h-382q5 -53 40.5 -83.5t92.5 -30.5q84 0 135 80l99 -60q-28 -59 -86.5 -99t-147.5 -40q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 191 -72t71 -193q0 -32 -2 -55zM281 431q-50 0 -81.5 -29.5t-36.5 -76.5h242q-5 47 -38 76.5t-86 29.5z
" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="288" 
d="M196 558v-23h79v-113h-79v-422h-134v422h-50v113h50v24q0 84 39 128.5t131 44.5q30 0 43 -1v-112h-25q-54 0 -54 -61z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="615" 
d="M451 485v50h126v-445q0 -273 -286 -273q-115 0 -199 48l43 106q69 -39 157 -39q67 0 109 28t43 84v7q-51 -60 -148 -60q-124 0 -200.5 76.5t-76.5 198.5q0 118 76.5 198t195.5 80q104 0 160 -59zM153 267q0 -68 41.5 -112.5t106.5 -44.5q66 0 106.5 44.5t40.5 112.5
t-41 113t-106 45q-67 0 -107.5 -44.5t-40.5 -113.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="490" 
d="M319 0v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v731h134v-236q40 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="212" 
d="M173 535v-535h-134v535h134zM106 569q-33 0 -56.5 23t-23.5 56q0 34 23.5 57.5t56.5 23.5q34 0 57 -23.5t23 -57.5q0 -33 -23 -56t-57 -23z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="212" 
d="M173 535v-535q0 -175 -171 -175h-39v116h24q31 0 41.5 16t10.5 45v533h134zM106 569q-34 0 -57 23t-23 56q0 34 23 57.5t57 23.5q33 0 56.5 -23.5t23.5 -57.5q0 -33 -23.5 -56t-56.5 -23z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="496" 
d="M489 535l-193 -247l200 -288h-162l-161 236v-236h-134v731h134v-406l159 210h157z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="212" 
d="M173 731v-731h-134v731h134z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="766" 
d="M451 337v-337h-134v337q0 39 -18.5 62t-52.5 23q-35 0 -54 -23t-19 -64v-335h-134v535h134v-40q38 46 115 46q86 0 127 -58q50 58 134 58q93 0 136.5 -52t43.5 -136v-353h-134v337q0 40 -18.5 62.5t-53.5 22.5t-53.5 -23.5t-18.5 -61.5z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="490" 
d="M319 0v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v535h134v-40q37 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="597" 
d="M153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79q-124 0 -201.5 77.5t-77.5 197.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="617" 
d="M169 267q0 -69 41 -113t107 -44q67 0 107 44t40 113t-41 113.5t-107 44.5q-67 0 -107 -44.5t-40 -113.5zM173 43v-218h-134v710h127v-51q60 60 164 60q121 0 194.5 -76.5t73.5 -198.5q0 -119 -74.5 -198.5t-199.5 -79.5q-90 0 -151 52z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="617" 
d="M444 -175v221q-20 -22 -62 -38.5t-89 -16.5q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-710h-134zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z
" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="293" 
d="M277 537v-140h-34q-38 0 -54 -17.5t-16 -53.5v-326h-134v535h134v-43q32 45 104 45z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="421" 
d="M156 393q0 -37 47 -50q11 -3 35 -9t38 -10q67 -20 97.5 -57.5t30.5 -107.5q0 -78 -56 -122t-139 -44q-103 0 -148.5 51t-42.5 129h128q0 -72 65 -72q27 0 45 12.5t18 35.5t-12.5 35t-40.5 20q-65 18 -81 24q-111 40 -111 158q0 68 51.5 112t131.5 44q86 0 133 -43
t48 -126h-123q-2 65 -61 65q-24 0 -38.5 -12.5t-14.5 -32.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="300" 
d="M196 619v-84h82v-113h-82v-249q0 -30 11.5 -43.5t39.5 -13.5h31v-114l-9 -2q-9 -2 -26.5 -3.5t-37.5 -1.5q-143 0 -143 140v287h-50v113h50v84h134z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="487" 
d="M171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="527" 
d="M147 535l121 -367l121 367h139l-189 -535h-147l-193 535h148z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="826" 
d="M490 535l99 -363l96 363h140l-156 -535h-148l-105 343l-103 -343h-151l-161 535h151l96 -363l99 363h143z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="536" 
d="M387 0l-121 168l-122 -168h-145l194 273l-184 262h155l108 -152l108 152h147l-183 -258l193 -277h-150z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="530" 
d="M261 -175h-130l72 196l-204 514h148l125 -332l121 332h138z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="434" 
d="M413 438l-233 -327h233v-111h-392v97l227 327h-223v111h388v-97z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="304" 
d="M203 27q0 -41 17.5 -62t60.5 -21h8v-119h-9q-204 0 -204 203v156q0 36 -11.5 51.5t-47.5 15.5h-1v124h1q36 0 47.5 15.5t11.5 51.5v151q0 203 204 203h9v-119h-8q-43 0 -60.5 -21t-17.5 -62v-177q0 -88 -68 -105q68 -15 68 -105v-180z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="243" 
d="M178 731v-906h-113v906h113z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="304" 
d="M101 27v180q0 90 69 105q-69 17 -69 105v177q0 41 -17.5 62t-60.5 21h-7v119h8q204 0 204 -203v-151q0 -36 11.5 -51.5t47.5 -15.5h2v-124h-2q-36 0 -47.5 -15.5t-11.5 -51.5v-156q0 -203 -204 -203h-8v119h7q43 0 60.5 21t17.5 62z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="476" 
d="M234 238q-44 24 -63 24q-39 0 -41 -47h-112q-2 76 37 125.5t108 49.5q39 0 80 -24q5 -3 16.5 -9.5t17 -9t14 -5t16.5 -2.5q37 0 39 50h112q2 -79 -36 -127.5t-106 -48.5q-42 0 -82 24z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="151" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="236" 
d="M186 403v-508h-136v508h136zM117 599q35 0 60 -24.5t25 -59.5t-25 -60t-60 -25t-59.5 25t-24.5 60t24.5 59.5t59.5 24.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="495" 
d="M328 604v-63q96 -10 160 -74l-74 -90q-35 37 -86 46v-312q48 9 88 47l73 -89q-64 -66 -161 -75v-63h-74v64q-107 13 -171 88.5t-64 182.5q0 106 65.5 183t169.5 91v64h74zM150 267q0 -58 28.5 -98t75.5 -54v305q-49 -13 -76.5 -54.5t-27.5 -98.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="543" 
d="M222 483v-89h137v-118h-137v-152h300v-124h-498v124h59v152h-59v118h59v98q0 95 57 154t162 59q62 0 107 -20.5t67.5 -54.5t32.5 -69.5t9 -73.5h-137q-3 87 -79 87q-41 0 -60.5 -25t-19.5 -66z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="525" 
d="M56 412h132l-186 280h161l104 -162l103 162h149l-186 -280h133v-100h-136v-62h136v-100h-136v-150h-138v150h-136v100h136v62h-136v100z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="243" 
d="M178 255v-430h-113v430h113zM178 731v-419h-113v419h113z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="576" 
d="M355 3q0 27 -16 44t-53 30l-136 49q-127 46 -127 169q0 53 27 89.5t70 52.5q-35 45 -35 111q0 80 55 134.5t155 54.5q61 0 103.5 -18.5t63 -50.5t28.5 -64t8 -69h-133q0 32 -17 56t-55 24q-36 0 -53 -17.5t-17 -43.5q0 -50 68 -73l125 -43q135 -47 135 -173
q0 -51 -26.5 -90.5t-65.5 -55.5q34 -38 34 -111q0 -80 -57 -135t-157 -55q-109 0 -158.5 56.5t-48.5 150.5h135q0 -36 17.5 -60.5t56.5 -24.5q37 0 55.5 17.5t18.5 45.5zM373 305l-131 48q-35 13 -59 -1.5t-24 -43.5q0 -41 43 -55l131 -48q31 -11 56.5 1t25.5 45
q0 39 -42 54z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="476" 
d="M152 573q-32 0 -53 21.5t-21 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM335 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="772" 
d="M23 345q0 153 100.5 256.5t258.5 103.5q166 0 267 -101t101 -256q0 -154 -100.5 -257.5t-262.5 -103.5q-164 0 -264 101.5t-100 256.5zM110 345q0 -122 75 -201t202 -79t201 78.5t74 204.5q0 125 -75 202t-205 77q-122 0 -197 -79t-75 -203zM528 498l-63 -82
q-25 25 -67 25q-40 0 -63.5 -26.5t-23.5 -68.5q0 -41 25 -67.5t65 -26.5t65 23l62 -75q-49 -49 -126 -49q-91 0 -145 55t-54 139q0 85 54.5 141t142.5 56q77 0 128 -44z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="358" 
d="M20 420q0 60 37.5 96.5t100.5 36.5q45 0 69 -18v23q0 57 -47 57q-46 0 -48 -44h-97q2 60 38 93.5t107 33.5q150 0 150 -153v-256h-87v23q-30 -28 -83 -28q-61 0 -100 37t-40 99zM119 422q0 -24 15 -39t40 -15q24 0 39.5 15t15.5 39t-15.5 39t-40.5 15q-24 0 -39 -15
t-15 -39z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="391" 
d="M209 99h-115l-80 169l80 171h115l-82 -171zM377 99h-114l-81 169l81 171h115l-82 -171z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="444" 
d="M221 510h-16v-83h-59v220h87q36 0 55 -18t19 -49q0 -36 -30 -57l38 -96h-61zM225 597h-20v-39h20q21 0 21 20q0 19 -21 19zM15 534q0 87 57 145.5t147 58.5q94 0 152 -58t58 -145q0 -86 -57.5 -145t-149.5 -59t-149.5 57.5t-57.5 145.5zM67 534q0 -68 42 -112t112 -44
q72 0 113 43.5t41 113.5t-41.5 112.5t-114.5 42.5q-69 0 -110.5 -44t-41.5 -112z" />
    <glyph glyph-name="macron" unicode="&#xaf;" 
d="M79 585v98h242v-98h-242z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="357" 
d="M19 545q0 67 44.5 112.5t113.5 45.5q73 0 117 -44.5t44 -111.5q0 -69 -44 -113.5t-116 -44.5t-115.5 44.5t-43.5 111.5zM111 545q0 -29 19 -49t49 -20t48 20t18 49q0 30 -19 50.5t-48 20.5q-30 0 -48.5 -20.5t-18.5 -50.5z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="323" 
d="M300 335h-278v74l127 100q24 18 33.5 31t9.5 30q0 16 -9 26t-26 10q-37 0 -37 -41h-97q-3 50 30 91.5t104 41.5q66 0 102.5 -32.5t36.5 -89.5q0 -60 -62 -106l-58 -49h124v-86z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="320" 
d="M97 490v64l67 55h-124v83h237v-78l-66 -55q34 -5 60 -32.5t26 -75.5q0 -54 -37.5 -88t-105.5 -34q-66 0 -101 33.5t-36 90.5h99q0 -20 10.5 -31t29.5 -11q18 0 28.5 11t10.5 29q0 39 -44 39h-54z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="476" 
d="M256 710h131l-81 -138h-126z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="464" 
d="M258 0h-102v410q-62 2 -102.5 41t-40.5 102q0 62 41.5 100.5t109.5 38.5h94v-692zM418 692v-692h-103v692h103z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="223" 
d="M111 242q-36 0 -60.5 25t-24.5 61q0 37 24 61.5t61 24.5t61 -24.5t24 -61.5q0 -36 -24.5 -61t-60.5 -25z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" 
d="M147 -259h-20v76h21q44 2 44 40t-44 38h-21v120h76v-51q78 -22 78 -107q0 -55 -35.5 -85.5t-98.5 -30.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="192" 
d="M154 692v-357h-102v269l-39 -15v81l49 22h92z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="442" 
d="M119 491q0 -47 28.5 -77.5t74.5 -30.5q45 0 73 30t28 78t-29 78.5t-73 30.5q-46 0 -74 -30.5t-28 -78.5zM13 490q0 91 59 149.5t149 58.5q94 0 151.5 -58t57.5 -148t-58.5 -148.5t-150.5 -58.5q-93 0 -150.5 57.5t-57.5 147.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="391" 
d="M14 439h114l81 -170l-81 -170h-114l81 170zM183 439h114l81 -170l-81 -170h-115l82 170z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="681" 
d="M649 70h-38v-70h-98v70h-169v87l168 203h99v-200h38v-90zM513 248l-73 -88h73v88zM185 692v-357h-102v269l-39 -15v81l49 22h92zM625 692l-515 -692h-107l514 692h108z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="697" 
d="M666 0h-278v75l126 99q24 18 33.5 31t9.5 30q0 16 -9 26t-26 10q-37 0 -37 -41h-97q-3 50 30 91.5t104 41.5q66 0 103 -32.5t37 -89.5q0 -62 -63 -107l-58 -48h125v-86zM625 692l-515 -692h-107l514 692h108zM185 692v-357h-102v269l-39 -15v81l49 22h92z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="754" 
d="M723 70h-38v-70h-98v70h-169v87l168 203h99v-200h38v-90zM587 248l-74 -88h74v88zM110 490v64l67 55h-124v83h237v-78l-66 -55q34 -5 60 -32.5t26 -75.5q0 -54 -37.5 -88t-105.5 -34q-66 0 -101 33.5t-36 90.5h99q0 -20 10.5 -31t29.5 -11q18 0 28.5 11t10.5 29
q0 39 -44 39h-54zM701 692l-515 -692h-107l514 692h108z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="473" 
d="M181 261v142h133v-240h-73q-39 0 -62 -20.5t-23 -59.5q0 -36 22 -59t59 -23q45 0 63 28.5t18 64.5h134q1 -30 -7 -62t-28.5 -67t-67.5 -57.5t-113 -22.5q-101 0 -158 55t-57 141q0 75 45 123.5t115 56.5zM247 600q35 0 60 -24.5t25 -59.5q0 -36 -25 -60.5t-60 -24.5
t-59.5 24.5t-24.5 60.5q0 35 24.5 59.5t59.5 24.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="648" 
d="M505 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM171 854h131l73 -130h-127zM317 516l-75 -208h151z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="648" 
d="M505 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM329 854h133l-77 -130h-129zM317 516l-75 -208h151z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="648" 
d="M505 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM247 854h139l103 -130h-123l-49 54l-50 -54h-123zM317 516l-75 -208h151z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="648" 
d="M505 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM378 728q-28 0 -60.5 17t-44.5 17q-29 0 -29 -35h-95q-3 58 27.5 96t81.5 38q30 0 61 -17t43 -17q27 0 27 35h96q2 -61 -27.5 -97.5t-79.5 -36.5zM317 516l-75 -208h151z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="648" 
d="M506 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM415 722q-32 0 -53 21t-21 52t21.5 52t52.5 21q32 0 53.5 -21t21.5 -52t-21.5 -52t-53.5 -21zM217 722q-31 0 -52 21t-21 52t21 52t52 21q32 0 54 -21t22 -52t-22 -52t-54 -21zM318 516l-75 -208h151z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="648" 
d="M506 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM207 819q0 46 31 76.5t78 30.5q51 0 81.5 -29.5t30.5 -75.5q0 -47 -30.5 -77t-80.5 -30q-48 0 -79 30t-31 75zM318 516l-75 -208h151zM275 820q0 -20 11.5 -32t31.5 -12t31 12t11 33q0 19 -11.5 31
t-31.5 12q-19 0 -30.5 -12t-11.5 -32z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="781" 
d="M748 0h-319v169h-195l-90 -169h-155l381 692h376v-130h-178v-144h172v-128h-172v-160h180v-130zM296 287h133v249z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="627" 
d="M317 -259h-20v76h21q44 2 44 40t-44 38h-21v101q-127 26 -200.5 121t-73.5 228q0 155 100 257.5t260 102.5q139 0 230 -81l-82 -104q-64 56 -147 56q-98 0 -156.5 -65t-58.5 -165q0 -99 60.5 -164t156.5 -65q81 0 148 58l81 -97q-93 -91 -229 -91h-13v-23q78 -22 78 -107
q0 -55 -35.5 -85.5t-98.5 -30.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="398" 
d="M77 854h131l73 -130h-127zM366 0h-320v692h317v-130h-178v-144h173v-128h-173v-160h181v-130z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="398" 
d="M204 854h133l-77 -130h-129zM366 0h-320v692h317v-130h-178v-144h173v-128h-173v-160h181v-130z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="398" 
d="M134 854h139l103 -130h-123l-49 54l-50 -54h-123zM366 0h-320v692h317v-130h-178v-144h173v-128h-173v-160h181v-130z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="398" 
d="M366 0h-320v692h317v-130h-178v-144h173v-128h-173v-160h181v-130zM109 722q-31 0 -52 21t-21 52t21 52t52 21q32 0 54 -21t22 -52t-22 -52t-54 -21zM307 722q-32 0 -53 21t-21 52t21.5 52t52.5 21q32 0 53.5 -21t21.5 -52t-21.5 -52t-53.5 -21z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="231" 
d="M185 692v-692h-139v692h139zM-26 854h131l73 -130h-127z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="231" 
d="M185 692v-692h-139v692h139zM125 854h133l-77 -130h-129z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="231" 
d="M45 854h139l100 -130h-122l-47 55l-47 -55h-122zM185 692v-692h-139v692h139z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="231" 
d="M20 722q-32 0 -53 21t-21 52t21.5 52t52.5 21q32 0 53.5 -21t21.5 -52t-21.5 -52t-53.5 -21zM209 722q-31 0 -52 21t-21 52t21 52t52 21q32 0 54 -21t22 -52t-22 -52t-54 -21zM185 692v-692h-139v692h139z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="587" 
d="M60 692h140q170 0 266.5 -92t96.5 -253q0 -160 -97 -253.5t-266 -93.5h-140v286h-52v130h52v276zM199 569v-153h98v-130h-98v-163h8q99 -1 155 57.5t56 166.5q0 107 -55.5 165t-155.5 57h-8z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="668" 
d="M405 728q-28 0 -60.5 17t-44.5 17q-29 0 -29 -35h-95q-3 58 27.5 96t81.5 38q30 0 61 -17t43 -17q27 0 27 35h96q2 -61 -27.5 -97.5t-79.5 -36.5zM483 0l-303 456v-456h-135v692h144l297 -464v464h135v-692h-138z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="764" 
d="M243 854h131l73 -130h-127zM22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5q-163 0 -261.5 101t-98.5 257zM168 346q0 -100 59 -164.5t155 -64.5q98 0 156 64t58 165q0 100 -59 165t-156 65q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="764" 
d="M385 854h133l-77 -130h-129zM22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5q-163 0 -261.5 101t-98.5 257zM168 346q0 -100 59 -164.5t155 -64.5q98 0 156 64t58 165q0 100 -59 165t-156 65q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="764" 
d="M312 854h139l103 -130h-123l-49 54l-50 -54h-123zM22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5q-163 0 -261.5 101t-98.5 257zM168 346q0 -100 59 -164.5t155 -64.5q98 0 156 64t58 165q0 100 -59 165t-156 65
q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="764" 
d="M442 728q-28 0 -60.5 17t-44.5 17q-29 0 -29 -35h-95q-3 58 27.5 96t81.5 38q30 0 61 -17t43 -17q27 0 27 35h96q2 -61 -27.5 -97.5t-79.5 -36.5zM22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5q-163 0 -261.5 101t-98.5 257z
M168 346q0 -100 59 -164.5t155 -64.5q98 0 156 64t58 165q0 100 -59 165t-156 65q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="764" 
d="M282 722q-31 0 -52 21t-21 52t21 52t52 21q32 0 54 -21t22 -52t-22 -52t-54 -21zM480 722q-32 0 -53 21t-21 52t21.5 52t52.5 21q32 0 53.5 -21t21.5 -52t-21.5 -52t-53.5 -21zM22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5
q-163 0 -261.5 101t-98.5 257zM168 346q0 -100 59 -164.5t155 -64.5q98 0 156 64t58 165q0 100 -59 165t-156 65q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="422" 
d="M17 451l78 79l116 -117l116 116l78 -78l-116 -116l116 -116l-77 -78l-117 116l-116 -116l-78 78l116 116z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="764" 
d="M22 345q0 154 100 256.5t255 102.5q116 0 193 -52l50 57l89 -78l-48 -55q77 -92 77 -229q0 -153 -99.5 -255.5t-258.5 -102.5q-113 0 -190 51l-52 -60l-89 77l50 58q-77 92 -77 230zM165 346q0 -70 29 -123l289 330q-47 25 -104 25q-98 0 -156 -65.5t-58 -166.5zM594 346
q0 65 -29 121l-288 -328q48 -24 102 -24q98 0 156.5 65t58.5 166z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="636" 
d="M186 854h131l73 -130h-127zM317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="636" 
d="M322 854h133l-77 -130h-129zM317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="636" 
d="M249 854h139l103 -130h-123l-49 54l-50 -54h-123zM317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="636" 
d="M317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5zM217 722q-31 0 -52 21t-21 52t21 52t52 21q32 0 54 -21t22 -52t-22 -52t-54 -21zM415 722q-32 0 -53 21t-21 52
t21.5 52t52.5 21q32 0 53.5 -21t21.5 -52t-21.5 -52t-53.5 -21z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="510" 
d="M184 346l-193 346h155l112 -210l114 210h147l-196 -344v-348h-139v346zM268 854h133l-77 -130h-129z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="491" 
d="M472 354q0 -96 -57.5 -151t-154.5 -55h-76v-148h-138v692h138v-139h76q103 0 157.5 -51.5t54.5 -147.5zM249 432h-65v-161h65q38 0 58.5 22t20.5 60q0 37 -20.5 58t-58.5 21z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="522" 
d="M227 0v112h12q55 1 91.5 35t37.5 95q1 60 -37 94.5t-93 34.5h-11v109h16q33 0 53.5 21t20.5 52q0 33 -19.5 52.5t-53.5 19.5q-37 0 -54 -23t-17 -56v-546h-134v535q0 91 50 146.5t159 55.5q94 0 147 -47t53 -128q0 -40 -18 -71t-42 -45q114 -62 114 -205
q0 -114 -71.5 -179t-188.5 -62h-15z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55
t-56 21t-55.5 -21.5t-21.5 -54.5zM94 710h131l76 -138h-126z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55
t-56 21t-55.5 -21.5t-21.5 -54.5zM250 710h131l-81 -138h-126z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55
t-56 21t-55.5 -21.5t-21.5 -54.5zM167 712h134l96 -142h-117l-46 62l-46 -62h-117z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55
t-56 21t-55.5 -21.5t-21.5 -54.5zM292 572q-27 0 -58 17t-44 17q-26 0 -26 -34h-95q-2 58 27 95.5t81 37.5q29 0 59 -17t42 -17q25 0 25 35h95q2 -61 -26.5 -97.5t-79.5 -36.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55
t-56 21t-55.5 -21.5t-21.5 -54.5zM140 573q-32 0 -53 21.5t-21 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM323 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55
t-56 21t-55.5 -21.5t-21.5 -54.5zM122 664q0 46 31 76.5t77 30.5q50 0 80.5 -30t30.5 -75q0 -46 -30.5 -76t-79.5 -30q-48 0 -78.5 29.5t-30.5 74.5zM187 666q0 -20 12.5 -33.5t32.5 -13.5q19 0 31.5 13.5t12.5 33.5t-12 32.5t-33 12.5q-19 0 -31.5 -12.5t-12.5 -32.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="831" 
d="M566 -8q-126 0 -197 79q-49 -80 -152 -80q-87 0 -141.5 49t-54.5 131q-1 79 49.5 128.5t134.5 49.5q61 0 94 -25v32q0 79 -69 79q-33 0 -48.5 -18.5t-15.5 -43.5h-125q2 78 50 123t140 45q108 0 151 -70q60 72 173 72q119 0 189.5 -71.5t71.5 -192.5q0 -21 -2 -55h-381
q2 -48 38.5 -81t94.5 -33q88 0 131 81l103 -58q-27 -60 -86 -100.5t-148 -40.5zM554 431q-51 0 -82 -32t-36 -78h240q-3 49 -36 79.5t-86 30.5zM147 177q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55t-56 21t-55.5 -21.5t-21.5 -54.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="488" 
d="M232 -259h-20v76h21q44 2 44 40t-44 38h-21v107q-89 25 -141 96t-52 168q0 118 78.5 198t197.5 80q118 0 188 -72l-77 -93q-46 44 -110 44q-65 0 -104 -44t-39 -112q0 -70 42 -112.5t102 -42.5q59 0 112 44l74 -90q-71 -75 -187 -75h-8v-27q78 -22 78 -107
q0 -55 -35.5 -85.5t-98.5 -30.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="560" 
d="M542 224h-382q5 -53 40.5 -83.5t92.5 -30.5q84 0 135 80l99 -60q-28 -59 -86.5 -99t-147.5 -40q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 191 -72t71 -193q0 -32 -2 -55zM281 431q-50 0 -81.5 -29.5t-36.5 -76.5h242q-5 47 -38 76.5t-86 29.5z
M144 710h131l76 -138h-126z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="560" 
d="M542 224h-382q5 -53 40.5 -83.5t92.5 -30.5q84 0 135 80l99 -60q-28 -59 -86.5 -99t-147.5 -40q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 191 -72t71 -193q0 -32 -2 -55zM281 431q-50 0 -81.5 -29.5t-36.5 -76.5h242q-5 47 -38 76.5t-86 29.5z
M297 710h131l-81 -138h-126z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="560" 
d="M542 224h-382q5 -53 40.5 -83.5t92.5 -30.5q84 0 135 80l99 -60q-28 -59 -86.5 -99t-147.5 -40q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 191 -72t71 -193q0 -32 -2 -55zM213 712h134l96 -142h-117l-46 62l-46 -62h-117zM281 431
q-50 0 -81.5 -29.5t-36.5 -76.5h242q-5 47 -38 76.5t-86 29.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="560" 
d="M542 224h-382q5 -53 40.5 -83.5t92.5 -30.5q84 0 135 80l99 -60q-28 -59 -86.5 -99t-147.5 -40q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 191 -72t71 -193q0 -32 -2 -55zM281 431q-50 0 -81.5 -29.5t-36.5 -76.5h242q-5 47 -38 76.5t-86 29.5z
M373 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5zM190 573q-32 0 -53 21.5t-21 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="212" 
d="M173 535v-535h-134v535h134zM-38 710h131l76 -138h-126z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="212" 
d="M121 710h131l-81 -138h-126zM173 535v-535h-134v535h134z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="212" 
d="M39 712h134l93 -142h-117l-43 62l-43 -62h-116zM173 535v-535h-134v535h134z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="212" 
d="M17 573q-31 0 -52.5 21.5t-21.5 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM194 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5zM173 535v-535h-134v535h134z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="598" 
d="M285 731l33 -37l104 28l25 -93l-57 -16l72 -80q58 -65 87 -126t29 -133q0 -122 -76.5 -202.5t-198.5 -80.5q-125 0 -204.5 76.5t-79.5 197.5q0 122 78 194t189 74q7 0 13 -2l-40 47l-119 -32l-25 93l77 21l-59 71h152zM153 266q0 -67 41 -110.5t105 -43.5q66 0 105.5 43
t39.5 111q0 67 -40.5 111t-105.5 44t-105 -43.5t-40 -111.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="490" 
d="M319 0v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v535h134v-40q37 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134zM301 572q-27 0 -58 17t-44 17q-26 0 -26 -34h-95q-2 58 27 95.5t81 37.5q29 0 59 -17t42 -17q25 0 25 35h95q2 -61 -26.5 -97.5
t-79.5 -36.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="597" 
d="M153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79q-124 0 -201.5 77.5t-77.5 197.5zM161 710h131l76 -138h-126z
" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="597" 
d="M311 710h131l-81 -138h-126zM153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79q-124 0 -201.5 77.5t-77.5 197.5
z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="597" 
d="M231 712h134l96 -142h-117l-46 62l-46 -62h-117zM153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79
q-124 0 -201.5 77.5t-77.5 197.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="597" 
d="M153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79q-124 0 -201.5 77.5t-77.5 197.5zM357 572q-27 0 -58 17
t-44 17q-26 0 -26 -34h-95q-2 58 27 95.5t81 37.5q29 0 59 -17t42 -17q25 0 25 35h95q2 -61 -26.5 -97.5t-79.5 -36.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="597" 
d="M205 573q-31 0 -52.5 21.5t-21.5 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM388 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5zM153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5
t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79q-124 0 -201.5 77.5t-77.5 197.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="470" 
d="M235 84q-32 0 -54.5 22.5t-22.5 54.5t22.5 54.5t54.5 22.5q33 0 55 -22.5t22 -54.5t-22.5 -54.5t-54.5 -22.5zM235 433q-32 0 -54.5 23t-22.5 55t22.5 54t54.5 22q33 0 55 -22t22 -54t-22.5 -55t-54.5 -23zM27 281v112h415v-112h-415z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="597" 
d="M19 267q0 119 79 198t200 79q79 0 143 -37l38 44l74 -62l-37 -43q62 -75 62 -178q0 -119 -78.5 -198t-201.5 -79q-86 0 -143 37l-41 -47l-72 62l39 45q-62 72 -62 179zM448 267q0 45 -18 80l-196 -227q30 -14 65 -14q68 0 108.5 45t40.5 116zM149 267q0 -44 17 -80
l196 228q-30 14 -64 14q-68 0 -108.5 -45.5t-40.5 -116.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="487" 
d="M171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134zM109 710h131l76 -138h-126z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="487" 
d="M251 710h131l-81 -138h-126zM171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="487" 
d="M176 712h134l96 -142h-117l-46 62l-46 -62h-117zM171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="487" 
d="M148 573q-32 0 -53 21.5t-21 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM331 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5zM171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5
v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="530" 
d="M261 -175h-130l72 196l-204 514h148l125 -332l121 332h138zM277 710h131l-81 -138h-126z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="617" 
d="M169 267q0 -69 40.5 -113t106.5 -44q67 0 107 44t40 113t-40.5 113.5t-107.5 44.5q-66 0 -106 -44.5t-40 -113.5zM173 42v-218h-134v906h134v-244q60 58 158 58q122 0 194.5 -76.5t72.5 -198.5q0 -119 -75.5 -198.5t-200.5 -79.5q-89 0 -149 51z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="530" 
d="M261 -175h-130l72 196l-204 514h148l125 -332l121 332h138zM179 573q-31 0 -52.5 21.5t-21.5 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM362 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5
t-54 -21.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="648" 
d="M506 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM189 731v99h256v-99h-256zM318 516l-75 -208h151z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55
t-56 21t-55.5 -21.5t-21.5 -54.5zM112 585v98h242v-98h-242z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="648" 
d="M506 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM317 723q-73 0 -110.5 40t-37.5 94h102q0 -18 12 -30.5t35 -12.5t35 13t12 30h101q0 -54 -38 -94t-111 -40zM318 516l-75 -208h151z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55
t-56 21t-55.5 -21.5t-21.5 -54.5zM230 564q-73 0 -109.5 43t-36.5 100h100q0 -22 12 -37t35 -15t35 15t12 37h99q0 -57 -37 -100t-110 -43z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="648" 
d="M507 -2l-70 192h-237l-68 -190h-137l250 692h146l262 -692q-48 -10 -48 -45t48 -37v-93q-85 -5 -129 24t-44 77q0 39 27 72zM318 516l-75 -208h151z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="469" 
d="M21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-338q-24 -4 -35.5 -16.5t-11.5 -28.5q0 -36 47 -36v-94q-85 -4 -128.5 23t-43.5 74q0 56 60 84v29q-38 -41 -113 -41
q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55t-56 21t-55.5 -21.5t-21.5 -54.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="627" 
d="M380 854h133l-77 -130h-129zM533 175l81 -97q-93 -91 -229 -91q-164 0 -263.5 101t-99.5 257q0 155 100 257.5t260 102.5q139 0 230 -81l-82 -104q-64 56 -147 56q-98 0 -156.5 -65t-58.5 -165q0 -99 60.5 -164t156.5 -65q81 0 148 58z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="488" 
d="M299 710h131l-81 -138h-126zM297 112q59 0 112 44l74 -90q-71 -75 -187 -75q-125 0 -201 78t-76 197q0 118 78.5 198t197.5 80q118 0 188 -72l-77 -93q-46 44 -110 44q-65 0 -104 -44t-39 -112q0 -70 42 -112.5t102 -42.5z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="627" 
d="M294 854h139l103 -130h-123l-49 54l-50 -54h-123zM533 175l81 -97q-93 -91 -229 -91q-164 0 -263.5 101t-99.5 257q0 155 100 257.5t260 102.5q139 0 230 -81l-82 -104q-64 56 -147 56q-98 0 -156.5 -65t-58.5 -165q0 -99 60.5 -164t156.5 -65q81 0 148 58z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="488" 
d="M209 712h134l96 -142h-117l-46 62l-46 -62h-117zM297 112q59 0 112 44l74 -90q-71 -75 -187 -75q-125 0 -201 78t-76 197q0 118 78.5 198t197.5 80q118 0 188 -72l-77 -93q-46 44 -110 44q-65 0 -104 -44t-39 -112q0 -70 42 -112.5t102 -42.5z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="627" 
d="M533 175l81 -97q-93 -91 -229 -91q-164 0 -263.5 101t-99.5 257q0 155 100 257.5t260 102.5q139 0 230 -81l-82 -104q-64 56 -147 56q-98 0 -156.5 -65t-58.5 -165q0 -99 60.5 -164t156.5 -65q81 0 148 58zM366 730q-31 0 -53.5 22t-22.5 54q0 31 21.5 53t54.5 22
q32 0 55 -22t23 -53q0 -32 -22.5 -54t-55.5 -22z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="488" 
d="M297 112q59 0 112 44l74 -90q-71 -75 -187 -75q-125 0 -201 78t-76 197q0 118 78.5 198t197.5 80q118 0 188 -72l-77 -93q-46 44 -110 44q-65 0 -104 -44t-39 -112q0 -70 42 -112.5t102 -42.5zM278 572q-32 0 -54 21.5t-22 54.5q0 32 21.5 53.5t54.5 21.5t55.5 -21.5
t22.5 -53.5q0 -33 -22 -54.5t-56 -21.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="627" 
d="M441 724h-139l-103 130h124l49 -55l49 55h123zM533 175l81 -97q-93 -91 -229 -91q-164 0 -263.5 101t-99.5 257q0 155 100 257.5t260 102.5q139 0 230 -81l-82 -104q-64 56 -147 56q-98 0 -156.5 -65t-58.5 -165q0 -99 60.5 -164t156.5 -65q81 0 148 58z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="488" 
d="M341 571h-134l-96 142h117l46 -62l46 62h117zM297 112q59 0 112 44l74 -90q-71 -75 -187 -75q-125 0 -201 78t-76 197q0 118 78.5 198t197.5 80q118 0 188 -72l-77 -93q-46 44 -110 44q-65 0 -104 -44t-39 -112q0 -70 42 -112.5t102 -42.5z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="572" 
d="M337 724h-139l-103 130h124l49 -55l49 55h123zM185 569v-446h8q99 -1 155 57.5t56 166.5q0 107 -55.5 165t-155.5 57h-8zM46 692h140q170 0 266.5 -92t96.5 -253q0 -160 -97 -253.5t-266 -93.5h-140v692z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="617" 
d="M622 731h108l-14 -159h-106zM453 0v53q-25 -27 -67 -44.5t-91 -17.5q-124 0 -200 76.5t-76 198.5q0 118 76 198t195 80q97 0 154 -52v239h134v-731h-125zM153 268q0 -68 41.5 -112.5t106.5 -44.5q66 0 106.5 44.5t40.5 112.5t-41 112.5t-106 44.5q-66 0 -107 -44.5
t-41 -112.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="587" 
d="M60 692h140q170 0 266.5 -92t96.5 -253q0 -160 -97 -253.5t-266 -93.5h-140v286h-52v130h52v276zM199 569v-153h98v-130h-98v-163h8q99 -1 155 57.5t56 166.5q0 107 -55.5 165t-155.5 57h-8z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="627" 
d="M318 579v94h126v58h134v-58h46v-94h-46v-579h-125v53q-25 -27 -67 -44.5t-91 -17.5q-124 0 -200 76.5t-76 198.5q0 118 76 198t195 80q97 0 154 -52v87h-126zM153 268q0 -68 41.5 -112.5t106.5 -44.5q66 0 106.5 44.5t40.5 112.5t-41 112.5t-106 44.5q-66 0 -107 -44.5
t-41 -112.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="398" 
d="M366 0h-320v692h317v-130h-178v-144h173v-128h-173v-160h181v-130zM81 731v99h256v-99h-256z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="560" 
d="M542 224h-382q5 -53 40.5 -83.5t92.5 -30.5q84 0 135 80l99 -60q-28 -59 -86.5 -99t-147.5 -40q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 191 -72t71 -193q0 -32 -2 -55zM162 585v98h242v-98h-242zM281 431q-50 0 -81.5 -29.5t-36.5 -76.5h242
q-5 47 -38 76.5t-86 29.5z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="398" 
d="M366 0h-320v692h317v-130h-178v-144h173v-128h-173v-160h181v-130zM207 723q-73 0 -110.5 40t-37.5 94h102q0 -18 12 -30.5t35 -12.5t35 13t12 30h101q0 -54 -38 -94t-111 -40z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="560" 
d="M542 224h-382q5 -53 40.5 -83.5t92.5 -30.5q84 0 135 80l99 -60q-28 -59 -86.5 -99t-147.5 -40q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 191 -72t71 -193q0 -32 -2 -55zM282 564q-73 0 -109.5 43t-36.5 100h100q0 -22 12 -37t35 -15t35 15
t12 37h99q0 -57 -37 -100t-110 -43zM281 431q-50 0 -81.5 -29.5t-36.5 -76.5h242q-5 47 -38 76.5t-86 29.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="398" 
d="M202 730q-31 0 -53.5 22t-22.5 54q0 31 21.5 53t54.5 22q32 0 55 -22t23 -53q0 -32 -22.5 -54t-55.5 -22zM366 0h-320v692h317v-130h-178v-144h173v-128h-173v-160h181v-130z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="560" 
d="M542 224h-382q5 -53 40.5 -83.5t92.5 -30.5q84 0 135 80l99 -60q-28 -59 -86.5 -99t-147.5 -40q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 191 -72t71 -193q0 -32 -2 -55zM281 431q-50 0 -81.5 -29.5t-36.5 -76.5h242q-5 47 -38 76.5t-86 29.5z
M282 572q-32 0 -54 21.5t-22 54.5q0 32 21.5 53.5t54.5 21.5t55.5 -21.5t22.5 -53.5q0 -33 -22 -54.5t-56 -21.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="398" 
d="M366 0h-36q-24 -4 -35.5 -16.5t-11.5 -28.5q0 -35 47 -37v-93q-85 -5 -128.5 22.5t-43.5 74.5q0 50 48 78h-160v692h317v-130h-178v-144h173v-128h-173v-160h181v-130z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="560" 
d="M428 190l99 -60q-23 -50 -73 -90q-55 -44 -55 -82q0 -40 59 -40v-93q-93 -6 -138 20.5t-45 71.5t41 75q-7 -1 -23 -1q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 190.5 -72t71.5 -193q0 -32 -2 -55h-382q5 -53 40.5 -83.5t92.5 -30.5
q84 0 135 80zM281 431q-50 0 -81.5 -29.5t-36.5 -76.5h242q-5 47 -38 76.5t-86 29.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="398" 
d="M276 724h-139l-103 130h124l49 -55l49 55h123zM366 0h-320v692h317v-130h-178v-144h173v-128h-173v-160h181v-130z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="560" 
d="M542 224h-382q5 -53 40.5 -83.5t92.5 -30.5q84 0 135 80l99 -60q-28 -59 -86.5 -99t-147.5 -40q-123 0 -198.5 74.5t-75.5 200.5q0 121 73.5 199.5t189.5 78.5q120 0 191 -72t71 -193q0 -32 -2 -55zM350 571h-134l-96 142h117l46 -62l46 62h117zM281 431
q-50 0 -81.5 -29.5t-36.5 -76.5h242q-5 47 -38 76.5t-86 29.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="727" 
d="M308 854h139l103 -130h-123l-49 54l-50 -54h-123zM685 372v-372h-134v41q-66 -53 -173 -53q-159 0 -257.5 99t-98.5 260q0 151 99.5 254.5t256.5 103.5q195 0 284 -137l-108 -80q-65 88 -176 88q-94 0 -152.5 -64t-58.5 -165q0 -104 61 -167.5t156 -63.5q106 0 165 61v79
h-113v116h249z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="615" 
d="M239 712h134l96 -142h-117l-46 62l-46 -62h-117zM451 485v50h126v-445q0 -273 -286 -273q-115 0 -199 48l43 106q69 -39 157 -39q67 0 109 28t43 84v7q-51 -60 -148 -60q-124 0 -200.5 76.5t-76.5 198.5q0 118 76.5 198t195.5 80q104 0 160 -59zM153 267
q0 -68 41.5 -112.5t106.5 -44.5q66 0 106.5 44.5t40.5 112.5t-41 113t-106 45q-67 0 -107.5 -44.5t-40.5 -113.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="727" 
d="M372 723q-73 0 -110.5 40t-37.5 94h102q0 -18 12 -30.5t35 -12.5t35 13t12 30h101q0 -54 -38 -94t-111 -40zM685 372v-372h-134v41q-66 -53 -173 -53q-159 0 -257.5 99t-98.5 260q0 151 99.5 254.5t256.5 103.5q195 0 284 -137l-108 -80q-65 88 -176 88q-94 0 -152.5 -64
t-58.5 -165q0 -104 61 -167.5t156 -63.5q106 0 165 61v79h-113v116h249z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="615" 
d="M451 485v50h126v-445q0 -273 -286 -273q-115 0 -199 48l43 106q69 -39 157 -39q67 0 109 28t43 84v7q-51 -60 -148 -60q-124 0 -200.5 76.5t-76.5 198.5q0 118 76.5 198t195.5 80q104 0 160 -59zM153 267q0 -68 41.5 -112.5t106.5 -44.5q66 0 106.5 44.5t40.5 112.5
t-41 113t-106 45q-67 0 -107.5 -44.5t-40.5 -113.5zM298 564q-73 0 -109.5 43t-36.5 100h100q0 -22 12 -37t35 -15t35 15t12 37h99q0 -57 -37 -100t-110 -43z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="727" 
d="M380 730q-31 0 -53.5 22t-22.5 54q0 31 21.5 53t54.5 22q32 0 55 -22t23 -53q0 -32 -22.5 -54t-55.5 -22zM685 372v-372h-134v41q-66 -53 -173 -53q-159 0 -257.5 99t-98.5 260q0 151 99.5 254.5t256.5 103.5q195 0 284 -137l-108 -80q-65 88 -176 88q-94 0 -152.5 -64
t-58.5 -165q0 -104 61 -167.5t156 -63.5q106 0 165 61v79h-113v116h249z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="615" 
d="M451 485v50h126v-445q0 -273 -286 -273q-115 0 -199 48l43 106q69 -39 157 -39q67 0 109 28t43 84v7q-51 -60 -148 -60q-124 0 -200.5 76.5t-76.5 198.5q0 118 76.5 198t195.5 80q104 0 160 -59zM153 267q0 -68 41.5 -112.5t106.5 -44.5q66 0 106.5 44.5t40.5 112.5
t-41 113t-106 45q-67 0 -107.5 -44.5t-40.5 -113.5zM294 572q-32 0 -54 21.5t-22 54.5q0 32 21.5 53.5t54.5 21.5t55.5 -21.5t22.5 -53.5q0 -33 -22 -54.5t-56 -21.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="727" 
d="M316 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM685 372v-372h-134v41q-66 -53 -173 -53q-159 0 -257.5 99t-98.5 260q0 151 99.5 254.5t256.5 103.5q195 0 284 -137l-108 -80q-65 88 -176 88
q-94 0 -152.5 -64t-58.5 -165q0 -104 61 -167.5t156 -63.5q106 0 165 61v79h-113v116h249z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="615" 
d="M349 785l-33 -74q54 -14 54 -69q0 -31 -21.5 -53.5t-56.5 -22.5q-34 0 -55 22.5t-21 53.5q0 27 19 67l37 76h77zM451 485v50h126v-445q0 -273 -286 -273q-115 0 -199 48l43 106q69 -39 157 -39q67 0 109 28t43 84v7q-51 -60 -148 -60q-124 0 -200.5 76.5t-76.5 198.5
q0 118 76.5 198t195.5 80q104 0 160 -59zM153 267q0 -68 41.5 -112.5t106.5 -44.5q66 0 106.5 44.5t40.5 112.5t-41 113t-106 45q-67 0 -107.5 -44.5t-40.5 -113.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="646" 
d="M600 692v-692h-139v289h-276v-289h-139v692h139v-271h276v271h139zM252 854h139l103 -130h-123l-49 54l-50 -54h-123z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="490" 
d="M319 0v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v731h134v-236q40 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134zM170 893h139l103 -130h-123l-49 54l-50 -54h-123z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="666" 
d="M609 692v-78h54v-101h-54v-513h-139v289h-276v-289h-139v513h-52v101h52v78h139v-78h276v78h139zM194 421h276v92h-276v-92z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="500" 
d="M2 579v94h46v58h134v-58h120v-94h-120v-84q40 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v579h-46z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="231" 
d="M185 692v-692h-139v692h139zM177 728q-28 0 -60.5 17t-44.5 17q-29 0 -29 -35h-95q-3 58 27.5 96t81.5 38q30 0 61 -17t43 -17q27 0 27 35h96q2 -61 -27.5 -97.5t-79.5 -36.5z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="212" 
d="M173 535v-535h-134v535h134zM165 572q-27 0 -58 17t-44 17q-26 0 -26 -34h-95q-2 58 27 95.5t81 37.5q29 0 59 -17t42 -17q25 0 25 35h95q2 -61 -26.5 -97.5t-79.5 -36.5z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="231" 
d="M185 692v-692h-139v692h139zM-11 731v99h256v-99h-256z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="212" 
d="M-10 585v98h232v-98h-232zM173 535v-535h-134v535h134z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="231" 
d="M185 692v-692h-139v692h139zM116 723q-73 0 -110.5 40t-37.5 94h102q0 -18 12 -30.5t35 -12.5t35 13t12 30h101q0 -54 -38 -94t-111 -40z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="212" 
d="M173 535v-535h-134v535h134zM106 564q-73 0 -109.5 43t-36.5 100h100q0 -22 12 -37t35 -15t35 15t12 37h99q0 -57 -37 -100t-110 -43z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="231" 
d="M185 692v-692q-44 -10 -44 -45t47 -37v-93q-85 -5 -128.5 23t-43.5 75q0 43 30 77v692h139z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="212" 
d="M173 535v-536q-24 -4 -35.5 -16.5t-11.5 -27.5q0 -35 47 -37v-93q-85 -5 -128.5 22t-43.5 75t38 80v533h134zM106 569q-33 0 -56.5 23t-23.5 56q0 34 23.5 57.5t56.5 23.5q34 0 57 -23.5t23 -57.5q0 -33 -23 -56t-57 -23z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="231" 
d="M185 692v-692h-139v692h139zM115 730q-31 0 -53.5 22t-22.5 54q0 31 21.5 53t54.5 22q32 0 55 -22t23 -53q0 -32 -22.5 -54t-55.5 -22z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="212" 
d="M173 535v-535h-134v535h134z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="518" 
d="M185 692v-692h-139v692h139zM473 692v-526q0 -82 -36 -124.5t-130 -42.5q-42 0 -59 1v128h39q47 0 47 53v511h139z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="424" 
d="M384 535v-535q0 -175 -171 -175h-39v116h24q31 0 41.5 16t10.5 45v533h134zM173 535v-535h-134v535h134zM317 569q-34 0 -57 23t-23 56q0 34 23 57.5t57 23.5q33 0 56.5 -23.5t23.5 -57.5q0 -33 -23.5 -56t-56.5 -23zM106 569q-33 0 -56.5 23t-23.5 56q0 34 23.5 57.5
t56.5 23.5q34 0 57 -23.5t23 -57.5q0 -33 -23 -56t-57 -23z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="288" 
d="M95 854h140l99 -130h-122l-47 55l-47 -55h-122zM236 692v-526q0 -82 -36 -124.5t-130 -42.5q-42 0 -59 1v128h39q47 0 47 53v511h139z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="212" 
d="M174 535v-535q0 -176 -169 -176q-25 0 -42 1v114h25q31 1 41 16.5t10 44.5v535h135zM39 712h134l96 -142h-117l-46 62l-46 -62h-117z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="558" 
d="M219 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM185 692v-283l214 283h161l-258 -331l262 -361h-163l-216 297v-297h-139v692h139z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="496" 
d="M198 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM489 535l-193 -247l200 -288h-162l-161 236v-236h-134v731h134v-406l159 210h157z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="380" 
d="M127 854h133l-77 -130h-129zM360 0h-314v692h139v-562h175v-130z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="212" 
d="M115 892h133l-77 -130h-129zM173 731v-731h-134v731h134z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="380" 
d="M140 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM360 0h-314v692h139v-562h175v-130z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="212" 
d="M47 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM173 731v-731h-134v731h134z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="380" 
d="M230 692h108l-14 -159h-106zM360 0h-314v692h139v-562h175v-130z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="212" 
d="M218 731h108l-14 -159h-106zM173 731v-731h-134v731h134z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="432" 
d="M331 267q-37 0 -62 25t-25 62t25 62t62 25t62 -25t25 -62t-25 -62t-62 -25zM360 0h-314v692h139v-562h175v-130z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="365" 
d="M204 354q0 35 24 59.5t60 24.5q35 0 59.5 -24.5t24.5 -59.5t-24.5 -59t-59.5 -24q-36 0 -60 24t-24 59zM173 731v-731h-134v731h134z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="381" 
d="M369 0h-314v201l-48 -26v123l48 27v367h139v-288l110 63v-132l-110 -59v-146h175v-130z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="243" 
d="M188 731v-226l49 30v-132l-49 -29v-374h-134v294l-47 -28v128l47 29v308h134z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="668" 
d="M334 854h133l-77 -130h-129zM483 0l-303 456v-456h-135v692h144l297 -464v464h135v-692h-138z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="490" 
d="M257 710h131l-81 -138h-126zM319 0v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v535h134v-40q37 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="667" 
d="M283 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM483 0l-303 456v-456h-135v692h144l297 -464v464h135v-692h-138z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="490" 
d="M187 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM319 0v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v535h134v-40q37 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="668" 
d="M411 724h-139l-103 130h124l49 -55l49 55h123zM483 0l-303 456v-456h-135v692h144l297 -464v464h135v-692h-138z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="490" 
d="M312 571h-134l-96 142h117l46 -62l46 62h117zM319 0v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v535h134v-40q37 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="634" 
d="M3 433l51 107q-57 18 -57 76q0 36 23 60t61 24q37 0 60 -24t23 -60q0 -31 -20 -76l-50 -107h-91zM462 0v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v535h134v-40q37 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="667" 
d="M484 2l-304 454v-456h-134v692h143l298 -461v461h134v-707q-1 -82 -35.5 -123.5t-127.5 -41.5q-7 0 -25.5 0.5t-24.5 0.5v128h32q39 0 44 40v13z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="490" 
d="M453 353v-353q0 -176 -169 -176q-25 0 -41 1v114h25q31 1 41 16.5t10 44.5v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v535h134v-40q37 46 113 46q88 0 127.5 -51.5t39.5 -136.5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="764" 
d="M252 731v99h256v-99h-256zM22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5q-163 0 -261.5 101t-98.5 257zM168 346q0 -100 59 -164.5t155 -64.5q98 0 156 64t58 165q0 100 -59 165t-156 65q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="597" 
d="M177 585v98h242v-98h-242zM153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79q-124 0 -201.5 77.5t-77.5 197.5z
" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="764" 
d="M381 723q-73 0 -110.5 40t-37.5 94h102q0 -18 12 -30.5t35 -12.5t35 13t12 30h101q0 -54 -38 -94t-111 -40zM22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5q-163 0 -261.5 101t-98.5 257zM168 346q0 -100 59 -164.5t155 -64.5
q98 0 156 64t58 165q0 100 -59 165t-156 65q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="597" 
d="M153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79q-124 0 -201.5 77.5t-77.5 197.5zM298 564q-73 0 -109.5 43
t-36.5 100h100q0 -22 12 -37t35 -15t35 15t12 37h99q0 -57 -37 -100t-110 -43z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="764" 
d="M296 853h128l-61 -129h-123zM470 853h127l-65 -129h-122zM22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5q-163 0 -261.5 101t-98.5 257zM168 346q0 -100 59 -164.5t155 -64.5q98 0 156 64t58 165q0 100 -59 165t-156 65
q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="597" 
d="M212 710h128l-65 -138h-122zM386 710h125l-68 -138h-120zM153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79
q-124 0 -201.5 77.5t-77.5 197.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="731" 
d="M379 130v432q-99 1 -155 -55t-56 -160t56.5 -161t154.5 -56zM386 692h310v-130h-178v-144h173v-128h-173v-160h181v-130h-313q-169 0 -266 94.5t-97 252.5q0 157 97.5 251t265.5 94z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="982" 
d="M153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5t-41 112t-105 44q-65 0 -105 -44t-40 -112zM704 431q-51 0 -82 -32t-36 -78h240q-3 49 -36 79.5t-86 30.5zM716 -8q-135 0 -209 94q-79 -95 -209 -95q-124 0 -201.5 77.5t-77.5 197.5t79.5 199t199.5 79
q135 0 211 -97q73 96 196 96q119 0 189.5 -71.5t71.5 -192.5q0 -21 -2 -55h-381q2 -48 38.5 -81t94.5 -33q88 0 131 81l103 -58q-27 -60 -86 -100.5t-148 -40.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="531" 
d="M472 493q0 -120 -96 -171l164 -322h-149l-147 296h-60v-296h-138v692h214q103 0 157.5 -51.5t54.5 -147.5zM249 572h-65v-162h65q38 0 58.5 22.5t20.5 59.5t-20.5 58.5t-58.5 21.5zM245 854h133l-77 -130h-129z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="293" 
d="M166 710h131l-81 -138h-126zM277 537v-140h-34q-38 0 -54 -17.5t-16 -53.5v-326h-134v535h134v-43q32 45 104 45z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="531" 
d="M472 493q0 -120 -96 -171l164 -322h-149l-147 296h-60v-296h-138v692h214q103 0 157.5 -51.5t54.5 -147.5zM208 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM249 572h-65v-162h65q38 0 58.5 22.5t20.5 59.5
t-20.5 58.5t-58.5 21.5z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="293" 
d="M50 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM277 537v-140h-34q-38 0 -54 -17.5t-16 -53.5v-326h-134v535h134v-43q32 45 104 45z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="531" 
d="M472 493q0 -120 -96 -171l164 -322h-149l-147 296h-60v-296h-138v692h214q103 0 157.5 -51.5t54.5 -147.5zM321 724h-139l-103 130h124l49 -55l49 55h123zM249 572h-65v-162h65q38 0 58.5 22.5t20.5 59.5t-20.5 58.5t-58.5 21.5z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="293" 
d="M223 571h-134l-96 142h117l46 -62l46 62h117zM277 537v-140h-34q-38 0 -54 -17.5t-16 -53.5v-326h-134v535h134v-43q32 45 104 45z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="498" 
d="M172 517q0 -52 65 -79q11 -5 46.5 -19t57.5 -24q133 -59 133 -193q0 -98 -63 -156.5t-165 -58.5q-53 0 -93.5 13.5t-65 35t-40 52t-20.5 61t-3 64.5h132q-1 -46 20.5 -76t69.5 -30q38 0 61.5 21t23.5 59q0 58 -74 89q-10 4 -53.5 22t-66.5 31q-103 54 -103 181
q0 86 58 140.5t154 54.5q110 0 160 -57.5t46 -149.5h-128q0 39 -20.5 63.5t-57.5 24.5q-35 0 -54.5 -19.5t-19.5 -49.5zM254 854h133l-77 -130h-129z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="421" 
d="M228 710h131l-81 -138h-126zM154 393q0 -37 47 -50q11 -3 35 -9t38 -10q67 -20 97.5 -57.5t30.5 -107.5q0 -78 -56 -122t-139 -44q-103 0 -148.5 51t-42.5 129h128q0 -72 65 -72q27 0 45 12.5t18 35.5t-12.5 35t-40.5 20q-65 18 -81 24q-111 40 -111 158q0 68 51.5 112
t131.5 44q86 0 133 -43t48 -126h-123q-2 65 -61 65q-24 0 -38.5 -12.5t-14.5 -32.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="498" 
d="M176 854h139l103 -130h-123l-49 54l-50 -54h-123zM172 517q0 -52 65 -79q11 -5 46.5 -19t57.5 -24q133 -59 133 -193q0 -98 -63 -156.5t-165 -58.5q-53 0 -93.5 13.5t-65 35t-40 52t-20.5 61t-3 64.5h132q-1 -46 20.5 -76t69.5 -30q38 0 61.5 21t23.5 59q0 58 -74 89
q-10 4 -53.5 22t-66.5 31q-103 54 -103 181q0 86 58 140.5t154 54.5q110 0 160 -57.5t46 -149.5h-128q0 39 -20.5 63.5t-57.5 24.5q-35 0 -54.5 -19.5t-19.5 -49.5z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="421" 
d="M142 712h134l96 -142h-117l-46 62l-46 -62h-117zM154 393q0 -37 47 -50q11 -3 35 -9t38 -10q67 -20 97.5 -57.5t30.5 -107.5q0 -78 -56 -122t-139 -44q-103 0 -148.5 51t-42.5 129h128q0 -72 65 -72q27 0 45 12.5t18 35.5t-12.5 35t-40.5 20q-65 18 -81 24
q-111 40 -111 158q0 68 51.5 112t131.5 44q86 0 133 -43t48 -126h-123q-2 65 -61 65q-24 0 -38.5 -12.5t-14.5 -32.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="498" 
d="M190 -259h-20v76h21q44 2 44 40t-44 38h-21v100q-82 20 -115.5 80t-30.5 138h132q-1 -46 20.5 -76t69.5 -30q38 0 61.5 21t23.5 59q0 58 -74 89q-10 4 -53.5 22t-66.5 31q-103 54 -103 181q0 86 58 140.5t154 54.5q110 0 160 -57.5t46 -149.5h-128q0 39 -20.5 63.5
t-57.5 24.5q-35 0 -54.5 -19.5t-19.5 -49.5q0 -52 65 -79q11 -5 46.5 -19t57.5 -24q133 -59 133 -193q0 -98 -63 -156.5t-165 -58.5v-23q78 -22 78 -107q0 -55 -35.5 -85.5t-98.5 -30.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="421" 
d="M162 -259h-20v76h21q44 2 44 40t-44 38h-21v105q-68 15 -98 62t-28 111h128q0 -72 65 -72q27 0 45 12.5t18 35.5t-12.5 35t-40.5 20q-65 18 -81 24q-111 40 -111 158q0 68 51.5 112t131.5 44q86 0 133 -43t48 -126h-123q-2 65 -61 65q-24 0 -38.5 -12.5t-14.5 -32.5
q0 -37 47 -50q11 -3 35 -9t38 -10q67 -20 97.5 -57.5t30.5 -107.5q0 -76 -52.5 -119.5t-131.5 -46.5v-29q78 -22 78 -107q0 -55 -35.5 -85.5t-98.5 -30.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="498" 
d="M317 724h-139l-103 130h124l49 -55l49 55h123zM172 517q0 -52 65 -79q11 -5 46.5 -19t57.5 -24q133 -59 133 -193q0 -98 -63 -156.5t-165 -58.5q-53 0 -93.5 13.5t-65 35t-40 52t-20.5 61t-3 64.5h132q-1 -46 20.5 -76t69.5 -30q38 0 61.5 21t23.5 59q0 58 -74 89
q-10 4 -53.5 22t-66.5 31q-103 54 -103 181q0 86 58 140.5t154 54.5q110 0 160 -57.5t46 -149.5h-128q0 39 -20.5 63.5t-57.5 24.5q-35 0 -54.5 -19.5t-19.5 -49.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="421" 
d="M279 571h-134l-96 142h117l46 -62l46 62h117zM154 393q0 -37 47 -50q11 -3 35 -9t38 -10q67 -20 97.5 -57.5t30.5 -107.5q0 -78 -56 -122t-139 -44q-103 0 -148.5 51t-42.5 129h128q0 -72 65 -72q27 0 45 12.5t18 35.5t-12.5 35t-40.5 20q-65 18 -81 24q-111 40 -111 158
q0 68 51.5 112t131.5 44q86 0 133 -43t48 -126h-123q-2 65 -61 65q-24 0 -38.5 -12.5t-14.5 -32.5z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="424" 
d="M154 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM408 562h-126v-562h-139v562h-126v130h391v-130z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="300" 
d="M109 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM196 619v-84h82v-113h-82v-249q0 -30 11.5 -43.5t39.5 -13.5h31v-114l-9 -2q-9 -2 -26.5 -3.5t-37.5 -1.5q-143 0 -143 140v287h-50v113h50v84h134z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="425" 
d="M281 724h-139l-103 130h124l49 -55l49 55h123zM408 562h-126v-562h-139v562h-126v130h391v-130z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="300" 
d="M244 731h108l-14 -159h-106zM196 619v-84h82v-113h-82v-249q0 -30 11.5 -43.5t39.5 -13.5h31v-114l-9 -2q-9 -2 -26.5 -3.5t-37.5 -1.5q-143 0 -143 140v287h-50v113h50v84h134z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="425" 
d="M408 562h-126v-126h88v-101h-88v-335h-139v335h-88v101h88v126h-126v130h391v-130z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="300" 
d="M196 619v-84h82v-113h-82v-81h82v-99h-82v-69q0 -30 11.5 -43.5t39.5 -13.5h31v-114l-9 -2q-9 -2 -26.5 -3.5t-37.5 -1.5q-143 0 -143 140v107h-47v99h47v81h-50v113h50v84h134z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="636" 
d="M317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5zM376 729q-27 0 -58 17t-44 17q-26 0 -26 -34h-95q-2 58 27 95.5t81 37.5q29 0 59 -17t42 -17q25 0 25 35h95
q2 -61 -26.5 -97.5t-79.5 -36.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="487" 
d="M171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134zM304 572q-27 0 -58 17t-44 17q-26 0 -26 -34h-95q-2 58 27 95.5t81 37.5q29 0 59 -17t42 -17q25 0 25 35h95q2 -61 -26.5 -97.5
t-79.5 -36.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="636" 
d="M317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5zM197 742v98h242v-98h-242z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="487" 
d="M171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134zM122 585v98h242v-98h-242z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="636" 
d="M317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5zM317 723q-73 0 -110.5 40t-37.5 94h102q0 -18 12 -30.5t35 -12.5t35 13t12 30h101q0 -54 -38 -94t-111 -40z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="487" 
d="M171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134zM241 564q-73 0 -109.5 43t-36.5 100h100q0 -22 12 -37t35 -15t35 15t12 37h99q0 -57 -37 -100t-110 -43z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="636" 
d="M317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5zM207 819q0 46 31 76.5t78 30.5q51 0 81.5 -29.5t30.5 -75.5q0 -47 -30.5 -77t-80.5 -30q-48 0 -79 30t-31 75zM275 820
q0 -20 11.5 -32t31.5 -12t31 12t11 33q0 19 -11.5 31t-31.5 12q-19 0 -30.5 -12t-11.5 -32z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="487" 
d="M133 664q0 46 31 76.5t77 30.5q50 0 80.5 -30t30.5 -75q0 -46 -30.5 -76t-79.5 -30q-48 0 -78.5 29.5t-30.5 74.5zM198 666q0 -20 12.5 -33.5t32.5 -13.5q19 0 31.5 13.5t12.5 33.5t-12 32.5t-33 12.5q-19 0 -31.5 -12.5t-12.5 -32.5zM171 535v-336q0 -85 69 -85
q35 0 54.5 23.5t19.5 62.5v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="636" 
d="M218 853h128l-61 -129h-123zM392 853h127l-65 -129h-122zM317 -13q-137 0 -206 75.5t-69 207.5v422h139v-422q0 -71 34.5 -112t103.5 -41q66 0 101 42t35 112v421h139v-421q0 -127 -72.5 -205.5t-204.5 -78.5z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="487" 
d="M171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5v335h134v-535h-133v40q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134zM153 710h128l-65 -138h-122zM327 710h125l-68 -138h-120z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="636" 
d="M421 3q-46 -16 -46 -48q0 -35 46 -37v-93q-84 -5 -128 22.5t-44 71.5q0 41 36 69q-123 9 -183 83.5t-60 198.5v422h139v-422q0 -71 34 -112t103 -41q67 0 102 41.5t35 111.5v422h139v-422q0 -99 -44 -168.5t-129 -98.5z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="487" 
d="M171 535v-336q0 -85 69 -85q35 0 54.5 23.5t19.5 62.5v335h134v-536q-24 -4 -35.5 -16.5t-11.5 -27.5q0 -35 47 -37v-93q-86 -5 -129 22t-43 75q0 47 39 80v38q-36 -46 -112 -46q-85 0 -125.5 51t-40.5 138v352h134z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="989" 
d="M424 854h139l103 -130h-123l-49 54l-50 -54h-123zM635 0l-139 480l-140 -480h-157l-201 692h152l135 -507l140 507h149l140 -507l134 507h143l-196 -692h-160z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="826" 
d="M349 712h134l96 -142h-117l-46 62l-46 -62h-117zM490 535l99 -363l96 363h140l-156 -535h-148l-105 343l-103 -343h-151l-161 535h151l96 -363l99 363h143z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="510" 
d="M184 346l-193 346h155l112 -210l114 210h147l-196 -344v-348h-139v346zM188 854h139l103 -130h-123l-49 54l-50 -54h-123z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="530" 
d="M261 -175h-130l72 196l-204 514h148l125 -332l121 332h138zM203 712h134l96 -142h-117l-46 62l-46 -62h-117z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="510" 
d="M184 346l-193 346h155l112 -210l114 210h147l-196 -344v-348h-139v346zM158 722q-31 0 -52 21t-21 52t21 52t52 21q32 0 54 -21t22 -52t-22 -52t-54 -21zM356 722q-32 0 -53 21t-21 52t21.5 52t52.5 21q32 0 53.5 -21t21.5 -52t-21.5 -52t-53.5 -21z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="582" 
d="M286 854h133l-77 -130h-129zM556 0h-530v94l333 470h-329v128h524v-94l-337 -471h339v-127z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="434" 
d="M223 710h131l-81 -138h-126zM413 438l-233 -327h233v-111h-392v97l227 327h-223v111h388v-97z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="582" 
d="M556 0h-530v94l333 470h-329v128h524v-94l-337 -471h339v-127zM290 730q-31 0 -53.5 22t-22.5 54q0 31 21.5 53t54.5 22q32 0 55 -22t23 -53q0 -32 -22.5 -54t-55.5 -22z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="434" 
d="M413 438l-233 -327h233v-111h-392v97l227 327h-223v111h388v-97zM218 572q-32 0 -54 21.5t-22 54.5q0 32 21.5 53.5t54.5 21.5t55.5 -21.5t22.5 -53.5q0 -33 -22 -54.5t-56 -21.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="582" 
d="M357 724h-139l-103 130h124l49 -55l49 55h123zM556 0h-530v94l333 470h-329v128h524v-94l-337 -471h339v-127z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="434" 
d="M285 571h-134l-96 142h117l46 -62l46 62h117zM413 438l-233 -327h233v-111h-392v97l227 327h-223v111h388v-97z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="350" 
d="M249 526l-16 -92h69v-114h-89l-87 -495h-132l87 495h-48v114h68l17 98q16 88 56 126t120 38q28 0 40 -3v-110h-24q-30 0 -42.5 -14t-18.5 -43z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="648" 
d="M506 0l-69 190h-237l-68 -190h-137l250 692h146l262 -692h-147zM207 819q0 46 31 76.5t78 30.5q51 0 81.5 -29.5t30.5 -75.5q0 -47 -30.5 -77t-80.5 -30q-48 0 -79 30t-31 75zM328 1073h133l-77 -130h-129zM318 516l-75 -208h151zM275 820q0 -20 11.5 -32t31.5 -12t31 12
t11 33q0 19 -11.5 31t-31.5 12q-19 0 -30.5 -12t-11.5 -32z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="469" 
d="M253 922h131l-77 -130h-126zM21 175q0 76 51 126t133 50q61 0 95 -22v27q0 79 -70 79q-34 0 -49.5 -18t-15.5 -44h-125q2 78 50 123t140 45q109 0 155.5 -53.5t46.5 -150.5v-337h-112v34q-38 -41 -113 -41q-81 0 -133.5 50t-52.5 132zM147 176q0 -34 22 -55.5t56 -21.5
q33 0 55 21.5t22 55.5t-22 55t-56 21t-55.5 -21.5t-21.5 -54.5zM132 664q0 46 31 76.5t77 30.5q50 0 80.5 -30t30.5 -75q0 -46 -30.5 -76t-79.5 -30q-48 0 -78.5 29.5t-30.5 74.5zM197 666q0 -20 12.5 -33.5t32.5 -13.5q19 0 31.5 13.5t12.5 33.5t-12 32.5t-33 12.5
q-19 0 -31.5 -12.5t-12.5 -32.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="781" 
d="M746 692v-130h-178v-144h172v-128h-172v-160h180v-130h-319v169h-195l-90 -169h-155l381 692h376zM524 854h133l-77 -130h-129zM296 287h133v249z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="831" 
d="M566 -8q-126 0 -197 79q-49 -80 -152 -80q-87 0 -141.5 49t-54.5 131q-1 79 49.5 128.5t134.5 49.5q61 0 94 -25v32q0 79 -69 79q-33 0 -48.5 -18.5t-15.5 -43.5h-125q2 78 50 123t140 45q108 0 151 -70q60 72 173 72q119 0 189.5 -71.5t71.5 -192.5q0 -21 -2 -55h-381
q2 -48 38.5 -81t94.5 -33q88 0 131 81l103 -58q-27 -60 -86 -100.5t-148 -40.5zM554 431q-51 0 -82 -32t-36 -78h240q-3 49 -36 79.5t-86 30.5zM147 177q0 -34 22 -55.5t56 -21.5q33 0 55 21.5t22 55.5t-22 55t-56 21t-55.5 -21.5t-21.5 -54.5zM404 710h131l-81 -138h-126z
" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="764" 
d="M22 345q0 154 100 256.5t255 102.5q116 0 193 -52l50 57l89 -78l-48 -55q77 -92 77 -229q0 -153 -99.5 -255.5t-258.5 -102.5q-113 0 -190 51l-52 -60l-89 77l50 58q-77 92 -77 230zM165 346q0 -70 29 -123l289 330q-47 25 -104 25q-98 0 -156 -65.5t-58 -166.5zM594 346
q0 65 -29 121l-288 -328q48 -24 102 -24q98 0 156.5 65t58.5 166zM380 854h133l-77 -130h-129z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="597" 
d="M19 267q0 119 79 198t200 79q79 0 143 -37l38 44l74 -62l-37 -43q62 -75 62 -178q0 -119 -78.5 -198t-201.5 -79q-86 0 -143 37l-41 -47l-72 62l39 45q-62 72 -62 179zM448 267q0 45 -18 80l-196 -227q30 -14 65 -14q68 0 108.5 45t40.5 116zM149 267q0 -44 17 -80
l196 228q-30 14 -64 14q-68 0 -108.5 -45.5t-40.5 -116.5zM309 710h131l-81 -138h-126z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="498" 
d="M189 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM172 517q0 -52 65 -79q11 -5 46.5 -19t57.5 -24q133 -59 133 -193q0 -98 -63 -156.5t-165 -58.5q-53 0 -93.5 13.5t-65 35t-40 52t-20.5 61t-3 64.5h132
q-1 -46 20.5 -76t69.5 -30q38 0 61.5 21t23.5 59q0 58 -74 89q-10 4 -53.5 22t-66.5 31q-103 54 -103 181q0 86 58 140.5t154 54.5q110 0 160 -57.5t46 -149.5h-128q0 39 -20.5 63.5t-57.5 24.5q-35 0 -54.5 -19.5t-19.5 -49.5z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="419" 
d="M156 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM154 393q0 -37 47 -50q11 -3 35 -9t38 -10q67 -20 97.5 -57.5t30.5 -107.5q0 -78 -56 -122t-139 -44q-103 0 -148.5 51t-42.5 129h128q0 -72 65 -72
q27 0 45 12.5t18 35.5t-12.5 35t-40.5 20q-65 18 -81 24q-111 40 -111 158q0 68 51.5 112t131.5 44q86 0 133 -43t48 -126h-123q-2 65 -61 65q-24 0 -38.5 -12.5t-14.5 -32.5z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="424" 
d="M154 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM408 562h-126v-562h-139v562h-126v130h391v-130z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="300" 
d="M109 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM196 619v-84h82v-113h-82v-249q0 -30 11.5 -43.5t39.5 -13.5h31v-114l-9 -2q-9 -2 -26.5 -3.5t-37.5 -1.5q-143 0 -143 140v287h-50v113h50v84h134z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="212" 
d="M174 535v-535q0 -176 -169 -176q-25 0 -42 1v114h25q31 1 41 16.5t10 44.5v535h135z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" 
d="M133 712h134l96 -142h-117l-46 62l-46 -62h-117z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" 
d="M267 571h-134l-96 142h117l46 -62l46 62h117z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" 
d="M201 564q-73 0 -109.5 43t-36.5 100h100q0 -22 12 -37t35 -15t35 15t12 37h99q0 -57 -37 -100t-110 -43z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" 
d="M201 572q-32 0 -54 21.5t-22 54.5q0 32 21.5 53.5t54.5 21.5t55.5 -21.5t22.5 -53.5q0 -33 -22 -54.5t-56 -21.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" 
d="M90 664q0 46 31 76.5t77 30.5q50 0 80.5 -30t30.5 -75q0 -46 -30.5 -76t-79.5 -30q-48 0 -78.5 29.5t-30.5 74.5zM155 666q0 -20 12.5 -33.5t32.5 -13.5q19 0 31.5 13.5t12.5 33.5t-12 32.5t-33 12.5q-19 0 -31.5 -12.5t-12.5 -32.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" 
d="M134 0h128q-38 -17 -38 -45q0 -35 46 -37v-93q-85 -5 -128 22t-43 75q0 46 35 78z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" 
d="M258 572q-27 0 -58 17t-44 17q-26 0 -26 -34h-95q-2 58 27 95.5t81 37.5q29 0 59 -17t42 -17q25 0 25 35h95q2 -61 -26.5 -97.5t-79.5 -36.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M83 710h128l-65 -138h-122zM257 710h125l-68 -138h-120z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="989" 
d="M351 854h131l73 -130h-127zM635 0l-139 480l-140 -480h-157l-201 692h152l135 -507l140 507h149l140 -507l134 507h143l-196 -692h-160z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="826" 
d="M490 535l99 -363l96 363h140l-156 -535h-148l-105 343l-103 -343h-151l-161 535h151l96 -363l99 363h143zM272 710h131l76 -138h-126z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="989" 
d="M505 854h133l-77 -130h-129zM635 0l-139 480l-140 -480h-157l-201 692h152l135 -507l140 507h149l140 -507l134 507h143l-196 -692h-160z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="826" 
d="M433 710h131l-81 -138h-126zM490 535l99 -363l96 363h140l-156 -535h-148l-105 343l-103 -343h-151l-161 535h151l96 -363l99 363h143z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="989" 
d="M394 722q-31 0 -52 21t-21 52t21 52t52 21q32 0 54 -21t22 -52t-22 -52t-54 -21zM592 722q-32 0 -53 21t-21 52t21.5 52t52.5 21q32 0 53.5 -21t21.5 -52t-21.5 -52t-53.5 -21zM635 0l-139 480l-140 -480h-157l-201 692h152l135 -507l140 507h149l140 -507l134 507h143
l-196 -692h-160z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="826" 
d="M323 573q-31 0 -52.5 21.5t-21.5 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM506 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5zM490 535l99 -363l96 363h140l-156 -535h-148
l-105 343l-103 -343h-151l-161 535h151l96 -363l99 363h143z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="510" 
d="M184 346l-193 346h155l112 -210l114 210h147l-196 -344v-348h-139v346zM120 854h131l73 -130h-127z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="530" 
d="M261 -175h-130l72 196l-204 514h148l125 -332l121 332h138zM131 710h131l76 -138h-126z" />
    <glyph glyph-name="uni2009" unicode="&#x2009;" horiz-adv-x="15" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="611" 
d="M580 229h-549v110h549v-110z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="951" 
d="M920 229h-889v110h889v-110z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="203" 
d="M180 692l-51 -107q57 -18 57 -76q0 -36 -23 -60t-61 -24q-37 0 -60.5 24t-23.5 60q0 33 21 75l49 108h92z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="203" 
d="M23 433l51 107q-57 18 -57 76q0 36 23 60t61 24q37 0 60 -24t23 -60q0 -31 -20 -76l-50 -107h-91z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="203" 
d="M23 -104l51 107q-57 18 -57 76q0 36 23 60t61 24q37 0 60 -24t23 -60q0 -31 -20 -76l-50 -107h-91z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="399" 
d="M180 692l-51 -107q57 -18 57 -76q0 -36 -23 -60t-61 -24q-37 0 -60.5 24t-23.5 60q0 33 21 75l49 108h92zM376 692l-51 -107q57 -18 57 -76q0 -36 -23 -60t-61 -24q-37 0 -60.5 24t-23.5 60q0 33 21 75l49 108h92z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="399" 
d="M23 433l51 107q-57 18 -57 76q0 36 23 60t61 24q37 0 60 -24t23 -60q0 -31 -20 -76l-50 -107h-91zM219 433l51 107q-57 18 -57 76q0 36 23 60t61 24q37 0 60 -24t23 -60q0 -31 -20 -76l-50 -107h-91z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" 
d="M220 -104l51 107q-57 18 -57 76q0 36 23 60t61 24q37 0 60 -24t23 -60q0 -31 -20 -76l-50 -107h-91zM23 -104l51 107q-57 18 -57 76q0 36 23 60t61 24q37 0 60 -24t23 -60q0 -31 -20 -76l-50 -107h-91z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="391" 
d="M372 428h-109v-428h-136v428h-108v131h108v133h136v-133h109v-131z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="402" 
d="M24 273h109v155h-109v131h109v133h136v-133h109v-131h-109v-155h109v-131h-109v-142h-136v142h-109v131z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="322" 
d="M20 297q0 59 41 100.5t100 41.5t100 -41.5t41 -100.5t-41 -100t-100 -41t-100 41t-41 100z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="649" 
d="M324 -11q-36 0 -60.5 25t-24.5 61q0 37 24 61.5t61 24.5t61 -24.5t24 -61.5q0 -36 -24.5 -61t-60.5 -25zM104 -11q-36 0 -60.5 25t-24.5 61q0 37 24 61.5t61 24.5t61 -24.5t24 -61.5q0 -36 -24.5 -61t-60.5 -25zM545 -11q-36 0 -60.5 25t-24.5 61q0 37 24 61.5t61 24.5
t61 -24.5t24 -61.5q0 -36 -24.5 -61t-60.5 -25z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1174" 
d="M913 164q0 -31 19 -52t50 -21q34 0 52 20.5t18 52.5t-19 52t-51 20q-33 0 -51 -20.5t-18 -51.5zM809 163q0 73 48 122t123 49q79 0 127.5 -48.5t48.5 -122.5q0 -73 -48 -121t-126 -48q-77 0 -125 47.5t-48 121.5zM534 164q0 -31 19 -52t50 -21q33 0 51 20.5t18 52.5
t-19 52t-51 20t-50 -20.5t-18 -51.5zM429 163q0 74 48 122.5t124 48.5q78 0 127 -48.5t49 -122.5q0 -72 -48.5 -120.5t-125.5 -48.5q-78 0 -126 47.5t-48 121.5zM713 692l-512 -692h-116l513 692h115zM122 529q0 -31 19 -51.5t50 -20.5q33 0 51.5 20.5t18.5 51.5
q0 32 -19.5 52.5t-51.5 20.5t-50 -21t-18 -52zM18 529q0 73 48 121.5t123 48.5q79 0 127.5 -48t48.5 -122q0 -73 -48.5 -121.5t-125.5 -48.5q-78 0 -125.5 48t-47.5 122z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="222" 
d="M209 99h-115l-80 169l80 171h115l-82 -171z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="222" 
d="M14 439h114l81 -170l-81 -170h-114l81 170z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="653" 
d="M634 692l-515 -692h-107l514 692h108z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="656" 
d="M19 480h65q37 102 123.5 162.5t206.5 60.5q138 0 227 -78l-82 -101q-60 54 -147 54q-113 0 -163 -76q-2 -3 -13 -22h201v-100h-229q-1 -11 -1 -34q0 -20 1 -29h229v-100h-200q8 -16 18 -29q53 -72 162 -72q79 0 145 56l82 -93q-92 -89 -227 -89q-126 0 -213.5 61.5
t-122.5 165.5h-62v100h44q-1 9 -1 28q0 23 1 35h-44v100z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="626" 
d="M208 692v-77h-51v-205h-85v205h-51v77h187zM525 410v183l-61 -183h-84l-63 182v-182h-79v282h125l61 -182l60 182h121v-282h-80z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="720" 
d="M484 156v64l68 56h-124v82h237v-78l-67 -55q35 -5 60.5 -32t25.5 -75q0 -54 -37.5 -88.5t-105.5 -34.5q-66 0 -100.5 33.5t-35.5 90.5h98q0 -19 11 -30t30 -11q18 0 28.5 11t10.5 28q0 39 -45 39h-54zM625 692l-515 -692h-107l514 692h108zM193 692v-357h-102v269
l-39 -15v81l49 22h92z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="823" 
d="M589 156v63l68 55h-123v84h235v-78l-66 -55q34 -5 59.5 -31.5t25.5 -71.5q0 -56 -38.5 -91t-104.5 -35q-67 0 -100.5 33t-34.5 90h96q0 -19 10.5 -30t29.5 -11q18 0 28.5 11t10.5 28q0 39 -44 39h-52zM303 335h-278v74l127 100q24 18 33.5 31t9.5 30q0 16 -9 26t-26 10
q-37 0 -37 -41h-97q-3 50 30 91.5t104 41.5q66 0 102.5 -32.5t36.5 -89.5q0 -60 -62 -106l-58 -49h124v-86zM731 692l-515 -692h-107l514 692h108z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="712" 
d="M500 259q0 -16 10 -26t25 -10q17 0 26.5 9.5t9.5 26.5q0 16 -10 26.5t-26 10.5t-25.5 -10t-9.5 -27zM404 265q0 44 33.5 74t97.5 30q65 0 99 -29.5t34 -74.5q0 -59 -45 -78q52 -17 52 -81q0 -49 -35.5 -80t-104.5 -32q-69 1 -103.5 31.5t-34.5 78.5q0 61 50 83
q-43 24 -43 78zM496 108q0 -18 11 -29t28 -11q18 0 29.5 11t11.5 29t-11.5 29t-29.5 11q-17 0 -28 -11t-11 -29zM625 692l-515 -692h-107l514 692h108zM193 692v-357h-102v269l-39 -15v81l49 22h92z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="796" 
d="M583 259q0 -16 10 -26t26 -10t26 10t10 26t-10.5 26.5t-25.5 10.5q-16 0 -26 -10t-10 -27zM487 265q0 44 33.5 74t98.5 30t98.5 -29t33.5 -75q0 -59 -44 -78q51 -17 51 -81q0 -49 -35.5 -80t-104.5 -32q-69 1 -103.5 31.5t-34.5 78.5q0 61 51 83q-44 24 -44 78zM579 108
q0 -18 11 -29t29 -11q17 0 28.5 11t11.5 29t-11 29t-29 11t-29 -10.5t-11 -29.5zM110 490v64l67 55h-124v83h237v-78l-66 -55q34 -5 60 -32.5t26 -75.5q0 -54 -37.5 -88t-105.5 -34q-66 0 -101 33.5t-36 90.5h99q0 -20 10.5 -31t29.5 -11q18 0 28.5 11t10.5 29q0 39 -44 39
h-54zM707 692l-515 -692h-107l514 692h108z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="799" 
d="M490 265q0 44 33.5 74t98.5 30t98.5 -29t33.5 -75q0 -59 -44 -78q51 -17 51 -81q0 -49 -35.5 -80t-104.5 -32q-68 1 -102.5 31.5t-34.5 78.5q0 62 50 83q-44 25 -44 78zM710 692l-515 -692h-107l514 692h108zM40 447h98q0 -35 37 -35q38 0 38 39v5q0 39 -39 39
q-21 0 -31 -15h-94v212h245v-83h-147v-49q17 15 57 15q53 0 82 -31t29 -84v-5q0 -56 -38 -91t-102 -35q-74 0 -104.5 34t-30.5 84zM582 108q0 -18 11.5 -29t28.5 -11t28.5 11t11.5 29t-11 29t-29 11t-29 -11t-11 -29zM587 259q0 -16 9.5 -26t25.5 -10t26 10t10 26t-10 26.5
t-26 10.5t-25.5 -10t-9.5 -27z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="727" 
d="M515 259q0 -16 9.5 -26t25.5 -10t26 9.5t10 26.5q0 16 -10 26.5t-26 10.5t-25.5 -10t-9.5 -27zM418 265q0 44 34 74t98 30q65 0 99 -29.5t34 -74.5q0 -59 -45 -78q52 -17 52 -81q0 -48 -36 -79.5t-104 -32.5q-70 1 -104.5 31.5t-34.5 78.5q0 60 51 83q-44 25 -44 78z
M511 108q0 -18 11 -29t28 -11q18 0 29.5 11t11.5 29t-11.5 29t-29.5 11t-28.5 -11t-10.5 -29zM311 619l-134 -284h-114l125 267h-146v90h269v-73zM639 692l-515 -692h-107l514 692h108z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="470" 
d="M27 281v112h415v-112h-415z" />
    <glyph glyph-name="uni24C5" unicode="&#x24c5;" horiz-adv-x="772" 
d="M544 417q0 -58 -35 -92t-93 -34h-45v-134h-97v382h140q63 0 96.5 -33.5t33.5 -88.5zM402 456h-31v-82h31q42 0 42 42q0 40 -42 40zM23 345q0 153 100.5 256.5t258.5 103.5q166 0 267 -101t101 -256q0 -154 -100.5 -257.5t-262.5 -103.5q-164 0 -264 101.5t-100 256.5z
M110 345q0 -122 75 -201t202 -79t201 78.5t74 204.5q0 125 -75 202t-205 77q-122 0 -197 -79t-75 -203z" />
    <glyph glyph-name="f_i" unicode="&#xfb01;" horiz-adv-x="493" 
d="M454 535v-535h-134v535h134zM385 569q-33 0 -56 23t-23 56q0 34 23 57.5t56 23.5q34 0 57.5 -23.5t23.5 -57.5q0 -33 -23.5 -56t-57.5 -23zM196 558v-23h72v-113h-72v-422h-134v422h-50v113h50v24q0 84 39 128.5t131 44.5q23 0 36 -1v-112h-18q-54 0 -54 -61z" />
    <glyph glyph-name="f_l" unicode="&#xfb02;" horiz-adv-x="493" 
d="M454 731v-731h-134v731h134zM196 558v-23h72v-113h-72v-422h-134v422h-50v113h50v24q0 84 39 128.5t131 44.5q23 0 36 -1v-112h-18q-54 0 -54 -61z" />
    <glyph glyph-name="space_uni0326" 
d="M152 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77z" />
    <glyph glyph-name="caron.alt" horiz-adv-x="476" 
d="M257 731h108l-14 -159h-106z" />
    <glyph glyph-name="space_uni0326.alt" 
d="M261 785l-33 -74q54 -14 54 -69q0 -31 -21.5 -53.5t-56.5 -22.5q-34 0 -55 22.5t-21 53.5q0 27 19 67l37 76h77z" />
    <glyph glyph-name="acute.pl" horiz-adv-x="476" 
d="M225 710h118l-45 -138h-116z" />
    <glyph glyph-name="grave.uc" horiz-adv-x="476" 
d="M100 854h131l73 -130h-127z" />
    <glyph glyph-name="dieresis.uc" horiz-adv-x="476" 
d="M135 722q-31 0 -52 21t-21 52t21 52t52 21q32 0 54 -21t22 -52t-22 -52t-54 -21zM333 722q-32 0 -53 21t-21 52t21.5 52t52.5 21q32 0 53.5 -21t21.5 -52t-21.5 -52t-53.5 -21z" />
    <glyph glyph-name="macron.uc" horiz-adv-x="476" 
d="M118 731v99h256v-99h-256z" />
    <glyph glyph-name="acute.uc" horiz-adv-x="476" 
d="M254 854h133l-77 -130h-129z" />
    <glyph glyph-name="circumflex.uc" 
d="M130 854h139l103 -130h-123l-49 54l-50 -54h-123z" />
    <glyph glyph-name="caron.uc" 
d="M269 724h-139l-103 130h124l49 -55l49 55h123z" />
    <glyph glyph-name="breve.uc" 
d="M201 723q-73 0 -110.5 40t-37.5 94h102q0 -18 12 -30.5t35 -12.5t35 13t12 30h101q0 -54 -38 -94t-111 -40z" />
    <glyph glyph-name="dotaccent.uc" 
d="M201 730q-31 0 -53.5 22t-22.5 54q0 31 21.5 53t54.5 22q32 0 55 -22t23 -53q0 -32 -22.5 -54t-55.5 -22z" />
    <glyph glyph-name="ring.uc" horiz-adv-x="473" 
d="M126 819q0 46 31 76.5t78 30.5q51 0 81.5 -29.5t30.5 -75.5q0 -47 -30.5 -77t-80.5 -30q-48 0 -79 30t-31 75zM194 820q0 -20 11.5 -32t31.5 -12t31 12t11 33q0 19 -11.5 31t-31.5 12q-19 0 -30.5 -12t-11.5 -32z" />
    <glyph glyph-name="tilde.uc" horiz-adv-x="476" 
d="M301 728q-28 0 -60.5 17t-44.5 17q-29 0 -29 -35h-95q-3 58 27.5 96t81.5 38q30 0 61 -17t43 -17q27 0 27 35h96q2 -61 -27.5 -97.5t-79.5 -36.5z" />
    <glyph glyph-name="hungarumlaut.uc" 
d="M76 853h128l-61 -129h-123zM250 853h127l-65 -129h-122z" />
    <glyph glyph-name="acute.ucpl" horiz-adv-x="476" 
d="M233 854h121l-46 -130h-119z" />
    <glyph glyph-name="Cacute.pl" horiz-adv-x="627" 
d="M533 175l81 -97q-93 -91 -229 -91q-164 0 -263.5 101t-99.5 257q0 155 100 257.5t260 102.5q139 0 230 -81l-82 -104q-64 56 -147 56q-98 0 -156.5 -65t-58.5 -165q0 -99 60.5 -164t156.5 -65q81 0 148 58zM354 854h121l-46 -130h-119z" />
    <glyph glyph-name="Nacute.pl" horiz-adv-x="667" 
d="M318 854h121l-46 -130h-119zM483 0l-303 456v-456h-135v692h144l297 -464v464h135v-692h-138z" />
    <glyph glyph-name="Oacute.pl" horiz-adv-x="764" 
d="M367 854h121l-46 -130h-119zM22 345q0 154 100.5 257t256.5 103q163 0 263 -101.5t100 -256.5t-100 -257.5t-260 -102.5q-163 0 -261.5 101t-98.5 257zM168 346q0 -100 59 -164.5t155 -64.5q98 0 156 64t58 165q0 100 -59 165t-156 65q-98 0 -155.5 -65t-57.5 -165z" />
    <glyph glyph-name="Sacute.pl" horiz-adv-x="498" 
d="M172 517q0 -52 65 -79q11 -5 46.5 -19t57.5 -24q133 -59 133 -193q0 -98 -63 -156.5t-165 -58.5q-53 0 -93.5 13.5t-65 35t-40 52t-20.5 61t-3 64.5h132q-1 -46 20.5 -76t69.5 -30q38 0 61.5 21t23.5 59q0 58 -74 89q-10 4 -53.5 22t-66.5 31q-103 54 -103 181
q0 86 58 140.5t154 54.5q110 0 160 -57.5t46 -149.5h-128q0 39 -20.5 63.5t-57.5 24.5q-35 0 -54.5 -19.5t-19.5 -49.5zM233 854h121l-46 -130h-119z" />
    <glyph glyph-name="Zacute.pl" horiz-adv-x="582" 
d="M556 0h-530v94l333 470h-329v128h524v-94l-337 -471h339v-127zM269 854h121l-46 -130h-119z" />
    <glyph glyph-name="cacute.pl" horiz-adv-x="487" 
d="M297 112q59 0 112 44l74 -90q-71 -75 -187 -75q-125 0 -201 78t-76 197q0 118 78.5 198t197.5 80q118 0 188 -72l-77 -93q-46 44 -110 44q-65 0 -104 -44t-39 -112q0 -70 42 -112.5t102 -42.5zM265 710h118l-45 -138h-116z" />
    <glyph glyph-name="idotaccent" horiz-adv-x="212" 
d="M173 535v-535h-134v535h134zM106 569q-33 0 -56.5 23t-23.5 56q0 34 23.5 57.5t56.5 23.5q34 0 57 -23.5t23 -57.5q0 -33 -23 -56t-57 -23z" />
    <glyph glyph-name="nacute.pl" horiz-adv-x="490" 
d="M229 710h118l-45 -138h-116zM319 0v337q0 85 -72 85q-35 0 -54.5 -23t-19.5 -62v-337h-134v535h134v-40q37 46 113 46q88 0 127.5 -51.5t39.5 -136.5v-353h-134z" />
    <glyph glyph-name="oacute.pl" horiz-adv-x="597" 
d="M153 267q0 -68 41 -111.5t105 -43.5q65 0 105 43.5t40 111.5q0 67 -41 111.5t-105 44.5q-65 0 -105 -44t-40 -112zM19 266q0 120 79.5 199t199.5 79q125 0 202.5 -78t77.5 -198q0 -119 -78.5 -198t-201.5 -79q-124 0 -201.5 77.5t-77.5 197.5zM285 710h118l-45 -138h-116
z" />
    <glyph glyph-name="sacute.pl" horiz-adv-x="419" 
d="M154 393q0 -37 47 -50q11 -3 35 -9t38 -10q67 -20 97.5 -57.5t30.5 -107.5q0 -78 -56 -122t-139 -44q-103 0 -148.5 51t-42.5 129h128q0 -72 65 -72q27 0 45 12.5t18 35.5t-12.5 35t-40.5 20q-65 18 -81 24q-111 40 -111 158q0 68 51.5 112t131.5 44q86 0 133 -43
t48 -126h-123q-2 65 -61 65q-24 0 -38.5 -12.5t-14.5 -32.5zM197 710h118l-45 -138h-116z" />
    <glyph glyph-name="zacute.pl" horiz-adv-x="434" 
d="M413 438l-233 -327h233v-111h-392v97l227 327h-223v111h388v-97zM199 710h118l-45 -138h-116z" />
    <glyph glyph-name="zero.num" horiz-adv-x="331" 
d="M306 559v-91q0 -64 -37.5 -103t-103.5 -39q-69 0 -104.5 38t-35.5 104v91q0 65 36 103.5t104 38.5q67 0 104 -38.5t37 -103.5zM204 464v98q0 43 -39 43q-38 0 -38 -43v-98q0 -42 38 -42q18 0 28.5 11.5t10.5 30.5z" />
    <glyph glyph-name="one.num" horiz-adv-x="188" 
d="M151 692v-357h-102v270l-39 -15v80l49 22h92z" />
    <glyph glyph-name="two.num" horiz-adv-x="308" 
d="M293 335h-277v74l126 101q43 34 43 63q0 16 -9.5 26.5t-26.5 10.5q-37 0 -37 -41h-96q-3 50 30 91t103 41q65 0 102.5 -33t37.5 -89q0 -34 -15.5 -58t-46.5 -50l-58 -50h124v-86z" />
    <glyph glyph-name="three.num" horiz-adv-x="309" 
d="M91 488v64l67 57h-125v83h237v-78l-66 -57q35 -5 60.5 -32t25.5 -76q0 -55 -37 -89t-105 -34q-67 0 -101.5 33.5t-35.5 90.5h98q0 -42 41 -42q18 0 28.5 11.5t10.5 29.5q0 39 -45 39h-53z" />
    <glyph glyph-name="four.num" horiz-adv-x="333" 
d="M320 403h-38v-71h-99v71h-166v85l165 204h100v-201h38v-88zM183 580l-71 -89h71v89z" />
    <glyph glyph-name="five.num" horiz-adv-x="314" 
d="M22 444h97q0 -36 38 -36q18 0 28 11t10 29v6q0 39 -39 39q-20 0 -32 -15h-93v214h245v-83h-150v-51q17 15 59 15q53 0 82 -31t29 -84v-6q0 -57 -37.5 -91.5t-101.5 -34.5q-74 0 -104.5 34t-30.5 84z" />
    <glyph glyph-name="six.num" horiz-adv-x="317" 
d="M283 657l-61 -64q-22 24 -53 24q-46 0 -46 -48v-11q27 15 58 15q56 0 88.5 -30t32.5 -84v-6q0 -56 -37 -91.5t-100 -35.5q-70 0 -105 38.5t-35 92.5v105q0 59 36.5 99t107.5 40q81 0 114 -44zM123 457v-8q0 -17 10.5 -29t29.5 -12t30 11.5t11 29.5v7q0 18 -11 29.5
t-29 11.5q-19 0 -30 -11.5t-11 -28.5z" />
    <glyph glyph-name="seven.num" horiz-adv-x="269" 
d="M257 619l-99 -284h-110l95 267h-132v90h246v-73z" />
    <glyph glyph-name="eight.num" horiz-adv-x="317" 
d="M26 597q0 44 34 74t98 30q65 0 99 -29.5t34 -74.5q0 -60 -45 -77q51 -20 51 -82q0 -49 -35 -80.5t-104 -31.5t-103.5 31t-34.5 78q0 62 50 85q-44 23 -44 77zM122 591q0 -16 10 -26t27 -10q16 0 26 10t10 26t-10.5 26.5t-25.5 10.5q-17 0 -27 -10t-10 -27zM118 440
q0 -19 11.5 -30t29.5 -11q17 0 28.5 11.5t11.5 29.5t-11 29t-29 11t-29.5 -11t-11.5 -29z" />
    <glyph glyph-name="nine.num" horiz-adv-x="317" 
d="M33 370l62 64q21 -24 53 -24q46 0 46 48v11q-25 -15 -54 -15q-58 0 -91.5 31t-33.5 85v6q0 55 36.5 90t101.5 35q69 0 104 -37.5t35 -93.5v-104q0 -61 -35.5 -100.5t-108.5 -39.5q-76 0 -115 44zM194 570v8q0 17 -10.5 29t-29.5 12t-30 -11.5t-11 -29.5v-7
q0 -18 11 -29.5t29 -11.5q19 0 30 11.5t11 28.5z" />
    <glyph glyph-name="zero.den" horiz-adv-x="331" 
d="M306 224v-91q0 -64 -37.5 -103t-103.5 -39q-69 0 -104.5 38t-35.5 104v91q0 65 36 103.5t104 38.5q67 0 104 -38.5t37 -103.5zM204 129v98q0 43 -39 43q-38 0 -38 -43v-98q0 -42 38 -42q18 0 28.5 11.5t10.5 30.5z" />
    <glyph glyph-name="one.den" horiz-adv-x="188" 
d="M151 357v-357h-102v270l-39 -15v80l49 22h92z" />
    <glyph glyph-name="two.den" horiz-adv-x="308" 
d="M293 0h-277v74l126 100q43 33 43 62q0 16 -9.5 27t-26.5 11q-37 0 -37 -42h-96q-3 50 30 91.5t103 41.5q66 0 103 -33t37 -90q0 -61 -62 -107l-58 -49h124v-86z" />
    <glyph glyph-name="three.den" horiz-adv-x="309" 
d="M91 153v64l67 57h-125v83h237v-78l-66 -57q35 -5 60.5 -32t25.5 -76q0 -54 -37 -88.5t-105 -34.5q-67 0 -101.5 33.5t-35.5 91.5h98q0 -20 11 -31.5t30 -11.5q18 0 28.5 11.5t10.5 29.5q0 39 -45 39h-53z" />
    <glyph glyph-name="four.den" horiz-adv-x="333" 
d="M320 68h-38v-71h-99v71h-166v85l165 204h100v-201h38v-88zM183 245l-71 -89h71v89z" />
    <glyph glyph-name="five.den" horiz-adv-x="314" 
d="M22 109h97q0 -36 38 -36q18 0 28 11t10 29v6q0 39 -39 39q-20 0 -32 -15h-93v214h245v-83h-148v-51q20 15 57 15q53 0 82 -31t29 -84v-6q0 -57 -37.5 -91.5t-101.5 -34.5q-74 0 -104.5 34t-30.5 84z" />
    <glyph glyph-name="six.den" horiz-adv-x="317" 
d="M283 322l-61 -64q-22 24 -53 24q-46 0 -46 -48v-11q27 15 58 15q56 0 88.5 -30t32.5 -84v-6q0 -56 -37 -91.5t-100 -35.5q-70 0 -105 38.5t-35 92.5v105q0 59 36.5 99t107.5 40q81 0 114 -44zM123 122v-8q0 -17 10.5 -29t29.5 -12t30 11.5t11 29.5v7q0 18 -11 29.5
t-29 11.5q-19 0 -30 -11.5t-11 -28.5z" />
    <glyph glyph-name="seven.den" horiz-adv-x="269" 
d="M257 284l-99 -284h-110l95 267h-132v90h246v-73z" />
    <glyph glyph-name="eight.den" horiz-adv-x="317" 
d="M26 262q0 44 34 74t98 30q65 0 99 -29.5t34 -74.5q0 -60 -45 -77q51 -20 51 -82q0 -49 -35 -80.5t-104 -31.5t-103.5 31t-34.5 78q0 62 50 85q-44 23 -44 77zM122 256q0 -16 10 -26t27 -10q16 0 26 10t10 26t-10.5 26.5t-25.5 10.5q-17 0 -27 -10t-10 -27zM118 105
q0 -19 11.5 -30t29.5 -11q17 0 28.5 11t11.5 30q0 18 -11 29t-29 11t-29.5 -11t-11.5 -29z" />
    <glyph glyph-name="nine.den" horiz-adv-x="317" 
d="M33 35l62 64q21 -24 53 -24q46 0 46 48v11q-25 -15 -54 -15q-58 0 -91.5 31t-33.5 85v6q0 55 36.5 90t101.5 35q69 0 104 -37.5t35 -93.5v-104q0 -61 -35.5 -100.5t-108.5 -39.5q-76 0 -115 44zM194 235v8q0 17 -10.5 29t-29.5 12t-30 -11.5t-11 -29.5v-7q0 -18 11 -29.5
t29 -11.5q19 0 30 11.5t11 28.5z" />
    <glyph glyph-name="a.alt" horiz-adv-x="617" 
d="M453 0v53q-19 -24 -60.5 -43t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z" />
    <glyph glyph-name="k.alt" horiz-adv-x="476" 
d="M327 195l148 -195h-156l-145 194v-194h-135v731h135v-217q31 29 97 29q83 0 131.5 -49.5t48.5 -129.5q0 -66 -35 -111t-89 -58zM324 356q0 34 -21.5 56t-55.5 22q-35 0 -56.5 -22t-21.5 -56q0 -33 22.5 -54.5t56.5 -21.5q33 0 54.5 21.5t21.5 54.5z" />
    <glyph glyph-name="u.alt" horiz-adv-x="482" 
d="M311 535h134v-330q0 -97 -53 -154t-151 -57q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="w.alt" horiz-adv-x="756" 
d="M585 535h134v-335q0 -97 -52 -151.5t-151 -54.5q-94 0 -140 53q-47 -53 -137 -53q-202 0 -202 206v335h134v-329q0 -85 70 -85q68 0 70 83v331h134v-331q0 -83 70 -83t70 85v329z" />
    <glyph glyph-name="y.alt" horiz-adv-x="487" 
d="M52 -146l39 107q58 -29 126 -29q50 0 76 21.5t26 54.5v23q-31 -40 -102 -40q-94 0 -137 53t-43 138v353h134v-336q0 -85 70 -85q35 0 54 23.5t19 62.5v335h134v-509q0 -97 -58 -153t-170 -56q-94 0 -168 37z" />
    <glyph glyph-name="aacute.alt" horiz-adv-x="617" 
d="M312 710h131l-81 -138h-126zM453 0v53q-19 -24 -60.5 -43t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5
q-67 0 -107.5 -44.5t-40.5 -112.5z" />
    <glyph glyph-name="abreve.alt" horiz-adv-x="617" 
d="M453 0v53q-19 -24 -60.5 -43t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z
M300 564q-73 0 -109.5 43t-36.5 100h100q0 -22 12 -37t35 -15t35 15t12 37h99q0 -57 -37 -100t-110 -43z" />
    <glyph glyph-name="acircumflex.alt" horiz-adv-x="617" 
d="M235 712h134l96 -142h-117l-46 62l-46 -62h-117zM453 0v53q-19 -24 -60.5 -43t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5
t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z" />
    <glyph glyph-name="adieresis.alt" horiz-adv-x="617" 
d="M208 573q-32 0 -53 21.5t-21 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM391 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5zM453 0v53q-19 -24 -60.5 -43t-99.5 -19
q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z" />
    <glyph glyph-name="agrave.alt" horiz-adv-x="617" 
d="M453 0v53q-19 -24 -60.5 -43t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z
M165 710h131l76 -138h-126z" />
    <glyph glyph-name="amacron.alt" horiz-adv-x="617" 
d="M453 0v53q-19 -24 -60.5 -43t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z
M180 585v98h242v-98h-242z" />
    <glyph glyph-name="aogonek.alt" horiz-adv-x="617" 
d="M406 -79q0 48 47 80v52q-19 -24 -60.5 -43t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-536q-24 -4 -35.5 -16.5t-11.5 -27.5q0 -35 47 -37v-93q-85 -5 -128.5 22.5t-43.5 73.5zM153 268q0 -69 41 -113.5t107 -44.5
q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z" />
    <glyph glyph-name="aring.alt" horiz-adv-x="617" 
d="M190 664q0 46 31 76.5t77 30.5q50 0 80.5 -30t30.5 -75q0 -46 -30.5 -76t-79.5 -30q-48 0 -78.5 29.5t-30.5 74.5zM255 666q0 -20 12.5 -33.5t32.5 -13.5q19 0 31.5 13.5t12.5 33.5t-12 32.5t-33 12.5q-19 0 -31.5 -12.5t-12.5 -32.5zM453 0v53q-19 -24 -60.5 -43
t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z" />
    <glyph glyph-name="aringacute.alt" horiz-adv-x="617" 
d="M191 664q0 46 31 76.5t77 30.5q50 0 80.5 -30t30.5 -75q0 -46 -30.5 -76t-79.5 -30q-48 0 -78.5 29.5t-30.5 74.5zM256 666q0 -20 12.5 -33.5t32.5 -13.5q19 0 31.5 13.5t12.5 33.5t-12 32.5t-33 12.5q-19 0 -31.5 -12.5t-12.5 -32.5zM453 0v53q-19 -24 -60.5 -43
t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z" />
    <glyph glyph-name="atilde.alt" horiz-adv-x="617" 
d="M453 0v53q-19 -24 -60.5 -43t-99.5 -19q-122 0 -198 76.5t-76 200.5q0 120 76 198t198 78q49 0 91.5 -16t66.5 -43v50h127v-535h-125zM153 268q0 -69 41 -113.5t107 -44.5q67 0 107 44.5t40 113.5q0 68 -41.5 112.5t-105.5 44.5q-67 0 -107.5 -44.5t-40.5 -112.5z
M360 572q-27 0 -58 17t-44 17q-26 0 -26 -34h-95q-2 58 27 95.5t81 37.5q29 0 59 -17t42 -17q25 0 25 35h95q2 -61 -26.5 -97.5t-79.5 -36.5z" />
    <glyph glyph-name="kcommaaccent.alt" horiz-adv-x="476" 
d="M192 -250l34 74q-54 14 -54 71q0 31 21 52.5t57 21.5q35 0 55.5 -22.5t20.5 -53.5q0 -27 -19 -67l-38 -76h-77zM327 195l148 -195h-156l-145 194v-194h-135v731h135v-217q31 29 97 29q83 0 131.5 -49.5t48.5 -129.5q0 -66 -35 -111t-89 -58zM324 356q0 34 -21.5 56
t-55.5 22q-35 0 -56.5 -22t-21.5 -56q0 -33 22.5 -54.5t56.5 -21.5q33 0 54.5 21.5t21.5 54.5z" />
    <glyph glyph-name="uacute.alt" horiz-adv-x="482" 
d="M248 710h131l-81 -138h-126zM311 535h134v-330q0 -97 -53 -154t-151 -57q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="ugrave.alt" horiz-adv-x="482" 
d="M108 710h131l76 -138h-126zM311 535h134v-330q0 -97 -53 -154t-151 -57q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="ucircumflex.alt" horiz-adv-x="482" 
d="M173 712h134l96 -142h-117l-46 62l-46 -62h-117zM311 535h134v-330q0 -97 -53 -154t-151 -57q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="udieresis.alt" horiz-adv-x="482" 
d="M149 573q-31 0 -52.5 21.5t-21.5 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM332 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5zM311 535h134v-330q0 -97 -53 -154t-151 -57
q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="ubreve.alt" horiz-adv-x="482" 
d="M240 564q-73 0 -109.5 43t-36.5 100h100q0 -22 12 -37t35 -15t35 15t12 37h99q0 -57 -37 -100t-110 -43zM311 535h134v-330q0 -97 -53 -154t-151 -57q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="uhungarumlaut.alt" horiz-adv-x="482" 
d="M151 710h128l-65 -138h-122zM325 710h125l-68 -138h-120zM311 535h134v-330q0 -97 -53 -154t-151 -57q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="umacron.alt" horiz-adv-x="482" 
d="M119 585v98h242v-98h-242zM311 535h134v-330q0 -97 -53 -154t-151 -57q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="uogonek.alt" horiz-adv-x="482" 
d="M311 535h134v-330q0 -138 -100 -189q-40 -23 -40 -58q0 -38 47 -40v-93q-86 -5 -129 22.5t-43 76.5q0 40 29 72q-172 15 -172 209v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="uring.alt" horiz-adv-x="482" 
d="M131 664q0 46 31 76.5t77 30.5q50 0 80.5 -30t30.5 -75q0 -46 -30.5 -76t-79.5 -30q-48 0 -78.5 29.5t-30.5 74.5zM196 666q0 -20 12.5 -33.5t32.5 -13.5q19 0 31.5 13.5t12.5 33.5t-12 32.5t-33 12.5q-19 0 -31.5 -12.5t-12.5 -32.5zM311 535h134v-330q0 -97 -53 -154
t-151 -57q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="utilde.alt" horiz-adv-x="482" 
d="M298 572q-27 0 -58 17t-44 17q-26 0 -26 -34h-95q-2 58 27 95.5t81 37.5q29 0 59 -17t42 -17q25 0 25 35h95q2 -61 -26.5 -97.5t-79.5 -36.5zM311 535h134v-330q0 -97 -53 -154t-151 -57q-204 0 -204 211v330h134v-329q0 -85 70 -85t70 85v329z" />
    <glyph glyph-name="wacute.alt" horiz-adv-x="756" 
d="M390 710h131l-81 -138h-126zM585 535h134v-335q0 -97 -52 -151.5t-151 -54.5q-94 0 -140 53q-47 -53 -137 -53q-202 0 -202 206v335h134v-329q0 -85 70 -85q68 0 70 83v331h134v-331q0 -83 70 -83t70 85v329z" />
    <glyph glyph-name="wcircumflex.alt" horiz-adv-x="756" 
d="M310 712h134l96 -142h-117l-46 62l-46 -62h-117zM585 535h134v-335q0 -97 -52 -151.5t-151 -54.5q-94 0 -140 53q-47 -53 -137 -53q-202 0 -202 206v335h134v-329q0 -85 70 -85q68 0 70 83v331h134v-331q0 -83 70 -83t70 85v329z" />
    <glyph glyph-name="wdieresis.alt" horiz-adv-x="756" 
d="M284 573q-32 0 -53 21.5t-21 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5zM467 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5zM585 535h134v-335q0 -97 -52 -151.5t-151 -54.5
q-94 0 -140 53q-47 -53 -137 -53q-202 0 -202 206v335h134v-329q0 -85 70 -85q68 0 70 83v331h134v-331q0 -83 70 -83t70 85v329z" />
    <glyph glyph-name="wgrave.alt" horiz-adv-x="756" 
d="M585 535h134v-335q0 -97 -52 -151.5t-151 -54.5q-94 0 -140 53q-47 -53 -137 -53q-202 0 -202 206v335h134v-329q0 -85 70 -85q68 0 70 83v331h134v-331q0 -83 70 -83t70 85v329zM233 710h131l76 -138h-126z" />
    <glyph glyph-name="yacute.alt" horiz-adv-x="487" 
d="M52 -146l39 107q58 -29 126 -29q50 0 76 21.5t26 54.5v23q-31 -40 -102 -40q-94 0 -137 53t-43 138v353h134v-336q0 -85 70 -85q35 0 54 23.5t19 62.5v335h134v-509q0 -97 -58 -153t-170 -56q-94 0 -168 37zM251 710h131l-81 -138h-126z" />
    <glyph glyph-name="ydieresis.alt" horiz-adv-x="487" 
d="M52 -146l39 107q58 -29 126 -29q50 0 76 21.5t26 54.5v23q-31 -40 -102 -40q-94 0 -137 53t-43 138v353h134v-336q0 -85 70 -85q35 0 54 23.5t19 62.5v335h134v-509q0 -97 -58 -153t-170 -56q-94 0 -168 37zM332 573q-31 0 -52 21.5t-21 52.5q0 30 21 51t52 21
q33 0 54.5 -20.5t21.5 -51.5t-22 -52.5t-54 -21.5zM149 573q-31 0 -52.5 21.5t-21.5 52.5q0 30 21.5 51t52.5 21q32 0 53.5 -20.5t21.5 -51.5t-22 -52.5t-53 -21.5z" />
    <glyph glyph-name="ycircumflex.alt" horiz-adv-x="487" 
d="M52 -146l39 107q58 -29 126 -29q50 0 76 21.5t26 54.5v23q-31 -40 -102 -40q-94 0 -137 53t-43 138v353h134v-336q0 -85 70 -85q35 0 54 23.5t19 62.5v335h134v-509q0 -97 -58 -153t-170 -56q-94 0 -168 37zM176 712h134l96 -142h-117l-46 62l-46 -62h-117z" />
    <glyph glyph-name="ygrave.alt" horiz-adv-x="487" 
d="M52 -146l39 107q58 -29 126 -29q50 0 76 21.5t26 54.5v23q-31 -40 -102 -40q-94 0 -137 53t-43 138v353h134v-336q0 -85 70 -85q35 0 54 23.5t19 62.5v335h134v-509q0 -97 -58 -153t-170 -56q-94 0 -168 37zM109 710h131l76 -138h-126z" />
    <glyph glyph-name="f.short" horiz-adv-x="281" 
d="M196 558v-23h72v-113h-72v-422h-134v422h-50v113h50v24q0 84 39 128.5t131 44.5q23 0 36 -1v-112h-18q-54 0 -54 -61z" />
    <glyph glyph-name="f_f.short" horiz-adv-x="533" 
d="M196 558v-23h118v24q0 84 39 128.5t131 44.5q23 0 36 -1v-112h-18q-54 0 -54 -61v-23h72v-113h-72v-422h-134v422h-118v-422h-134v422h-50v113h50v24q0 84 39 128.5t131 44.5q30 0 43 -1v-112h-25q-54 0 -54 -61z" />
    <glyph glyph-name="questiondown.uc" horiz-adv-x="473" 
d="M181 363v144h133v-242h-73q-39 0 -62 -20.5t-23 -59.5q0 -36 22 -58.5t59 -22.5q45 0 63 28t18 64h134q1 -30 -7 -62t-28.5 -67t-67.5 -57.5t-113 -22.5q-101 0 -158 55t-57 141q0 75 45 123.5t115 56.5zM247 703q35 0 60 -24.5t25 -59.5t-25 -60t-60 -25t-59.5 25
t-24.5 60t24.5 59.5t59.5 24.5z" />
    <glyph glyph-name="exclamdown.uc" horiz-adv-x="234" 
d="M185 507v-507h-136v507h136zM116 704q35 0 60 -24.5t25 -59.5q0 -36 -25 -60.5t-60 -24.5t-59.5 24.5t-24.5 60.5q0 34 25 59t59 25z" />
    <glyph glyph-name="hyphen.uc" horiz-adv-x="306" 
d="M31 260v125h244v-125h-244z" />
    <glyph glyph-name="endash.uc" horiz-adv-x="611" 
d="M580 265h-549v111h549v-111z" />
    <glyph glyph-name="emdash.uc" horiz-adv-x="951" 
d="M920 265h-889v111h889v-111z" />
    <glyph glyph-name="guilsinglleft.uc" horiz-adv-x="222" 
d="M209 99h-115l-80 169l80 171h115l-82 -171z" />
    <glyph glyph-name="guilsinglright.uc" horiz-adv-x="222" 
d="M14 439h114l81 -170l-81 -170h-114l81 170z" />
    <glyph glyph-name="guillemotleft.uc" horiz-adv-x="391" 
d="M209 99h-115l-80 169l80 171h115l-82 -171zM377 99h-114l-81 169l81 171h115l-82 -171z" />
    <glyph glyph-name="guillemotright.uc" horiz-adv-x="391" 
d="M14 439h114l81 -170l-81 -170h-114l81 170zM183 439h114l81 -170l-81 -170h-115l82 170z" />
    <glyph glyph-name="parenleft.uc" horiz-adv-x="268" 
d="M40 28v565q0 203 204 203h8v-119h-7q-44 0 -61 -21t-17 -62v-567q0 -42 17 -63t61 -21h7v-118h-8q-204 0 -204 203z" />
    <glyph glyph-name="parenright.uc" horiz-adv-x="268" 
d="M228 593v-565q0 -203 -204 -203h-8v118h7q44 0 61 21t17 63v567q0 41 -17 62t-61 21h-7v119h8q204 0 204 -203z" />
    <glyph glyph-name="bracketleft.uc" horiz-adv-x="268" 
d="M40 -175v971h212v-119h-85v-733h85v-119h-212z" />
    <glyph glyph-name="bracketright.uc" horiz-adv-x="268" 
d="M228 796v-971h-212v119h85v733h-85v119h212z" />
    <glyph glyph-name="braceleft.uc" horiz-adv-x="304" 
d="M203 27q0 -41 17.5 -62t60.5 -21h8v-119h-9q-204 0 -204 203v156q0 36 -11.5 51.5t-47.5 15.5h-1v124h1q36 0 47.5 15.5t11.5 51.5v151q0 203 204 203h9v-119h-8q-43 0 -60.5 -21t-17.5 -62v-177q0 -88 -68 -105q68 -15 68 -105v-180z" />
    <glyph glyph-name="braceright.uc" horiz-adv-x="304" 
d="M101 27v180q0 90 69 105q-69 17 -69 105v177q0 41 -17.5 62t-60.5 21h-7v119h8q204 0 204 -203v-151q0 -36 11.5 -51.5t47.5 -15.5h2v-124h-2q-36 0 -47.5 -15.5t-11.5 -51.5v-156q0 -203 -204 -203h-8v119h7q43 0 60.5 21t17.5 62z" />
    <glyph glyph-name="slash.uc" horiz-adv-x="464" 
d="M466 731l-299 -786h-123l299 786h123z" />
    <glyph glyph-name="backslash.uc" horiz-adv-x="464" 
d="M298 -56l-300 787h124l299 -787h-123z" />
    <glyph glyph-name="bar.uc" horiz-adv-x="242" 
d="M177 731v-786h-113v786h113z" />
    <glyph glyph-name="at.uc" horiz-adv-x="955" 
d="M610 163q-21 -26 -58.5 -43.5t-80.5 -17.5q-98 0 -157.5 59.5t-59.5 151.5q0 90 59 152t149 62q70 0 107 -37v31h117v-256q0 -62 53 -62q37 0 59.5 45t22.5 116q0 144 -89 223.5t-247 79.5t-252.5 -101t-94.5 -250q0 -158 102.5 -253t268.5 -95q142 0 245 87l61 -74
q-49 -50 -130.5 -82t-177.5 -32q-221 0 -352.5 126t-131.5 319q0 200 130 327t335 127q207 0 326 -110.5t119 -284.5q0 -122 -57.5 -195t-149.5 -73q-87 0 -116 60zM378 314q0 -43 25.5 -70.5t67.5 -27.5q43 0 69 27t26 71t-26.5 71.5t-68.5 27.5t-67.5 -27.5t-25.5 -71.5z
" />
    <hkern u1="&#x23;" u2="&#x39;" k="-5" />
    <hkern u1="&#x23;" u2="&#x38;" k="5" />
    <hkern u1="&#x23;" u2="&#x37;" k="-5" />
    <hkern u1="&#x23;" u2="&#x34;" k="25" />
    <hkern u1="&#x23;" u2="&#x33;" k="20" />
    <hkern u1="&#x23;" u2="&#x32;" k="-5" />
    <hkern u1="&#x23;" u2="&#x31;" k="-10" />
    <hkern u1="&#x24;" u2="&#x39;" k="15" />
    <hkern u1="&#x24;" u2="&#x38;" k="-5" />
    <hkern u1="&#x24;" u2="&#x37;" k="10" />
    <hkern u1="&#x24;" u2="&#x35;" k="5" />
    <hkern u1="&#x24;" u2="&#x34;" k="-5" />
    <hkern u1="&#x24;" u2="&#x32;" k="5" />
    <hkern u1="&#x26;" u2="&#x141;" k="10" />
    <hkern u1="&#x26;" u2="&#x127;" k="5" />
    <hkern u1="&#x26;" u2="&#x126;" k="5" />
    <hkern u1="&#x26;" u2="x" k="5" />
    <hkern u1="&#x26;" u2="v" k="55" />
    <hkern u1="&#x26;" u2="V" k="90" />
    <hkern u1="&#x26;" u2="&#x39;" k="25" />
    <hkern u1="&#x26;" u2="&#x38;" k="20" />
    <hkern u1="&#x26;" u2="&#x37;" k="25" />
    <hkern u1="&#x26;" u2="&#x35;" k="30" />
    <hkern u1="&#x26;" u2="&#x34;" k="15" />
    <hkern u1="&#x26;" u2="&#x33;" k="20" />
    <hkern u1="&#x26;" u2="&#x31;" k="30" />
    <hkern u1="&#x2a;" u2="&#x142;" k="20" />
    <hkern u1="&#x2a;" u2="&#x141;" k="15" />
    <hkern u1="&#x2a;" u2="&#x127;" k="-5" />
    <hkern u1="&#x2a;" u2="x" k="5" />
    <hkern u1="&#x2a;" u2="v" k="-10" />
    <hkern u1="&#x2a;" u2="X" k="30" />
    <hkern u1="&#x2a;" u2="V" k="5" />
    <hkern u1="&#x2a;" u2="&#x38;" k="5" />
    <hkern u1="&#x2a;" u2="&#x34;" k="40" />
    <hkern u1="&#x2a;" u2="&#x33;" k="15" />
    <hkern u1="&#x2a;" u2="&#x32;" k="5" />
    <hkern u1="&#x2a;" u2="&#x31;" k="-5" />
    <hkern u1="&#x2c;" u2="&#x135;" k="-5" />
    <hkern u1="&#x2c;" u2="j" k="-15" />
    <hkern u1="&#x31;" u2="&#xee;" k="-10" />
    <hkern u1="&#x32;" g2="at.uc" k="10" />
    <hkern u1="&#x32;" u2="&#x2122;" k="15" />
    <hkern u1="&#x32;" u2="&#x2022;" k="25" />
    <hkern u1="&#x32;" u2="&#xb7;" k="15" />
    <hkern u1="&#x32;" u2="&#xae;" k="-5" />
    <hkern u1="&#x32;" u2="&#xa2;" k="10" />
    <hkern u1="&#x32;" u2="&#x40;" k="10" />
    <hkern u1="&#x32;" u2="&#x3f;" k="5" />
    <hkern u1="&#x32;" u2="&#x3d;" k="5" />
    <hkern u1="&#x32;" u2="&#x3c;" k="10" />
    <hkern u1="&#x32;" u2="&#x34;" k="10" />
    <hkern u1="&#x32;" u2="&#x33;" k="5" />
    <hkern u1="&#x32;" u2="&#x31;" k="-5" />
    <hkern u1="&#x32;" u2="&#x26;" k="5" />
    <hkern u1="&#x32;" u2="&#x23;" k="5" />
    <hkern u1="&#x33;" g2="at.uc" k="-5" />
    <hkern u1="&#x33;" u2="&#x2122;" k="25" />
    <hkern u1="&#x33;" u2="&#xba;" k="25" />
    <hkern u1="&#x33;" u2="&#xb7;" k="10" />
    <hkern u1="&#x33;" u2="&#xb0;" k="20" />
    <hkern u1="&#x33;" u2="&#xae;" k="25" />
    <hkern u1="&#x33;" u2="&#xaa;" k="25" />
    <hkern u1="&#x33;" u2="&#xa2;" k="-5" />
    <hkern u1="&#x33;" u2="_" k="45" />
    <hkern u1="&#x33;" u2="&#x40;" k="-5" />
    <hkern u1="&#x33;" u2="&#x3f;" k="20" />
    <hkern u1="&#x33;" u2="&#x39;" k="15" />
    <hkern u1="&#x33;" u2="&#x38;" k="-5" />
    <hkern u1="&#x33;" u2="&#x37;" k="10" />
    <hkern u1="&#x33;" u2="&#x35;" k="5" />
    <hkern u1="&#x33;" u2="&#x34;" k="-5" />
    <hkern u1="&#x33;" u2="&#x2a;" k="20" />
    <hkern u1="&#x33;" u2="&#x26;" k="-5" />
    <hkern u1="&#x33;" u2="&#x23;" k="-5" />
    <hkern u1="&#x34;" g2="at.uc" k="-5" />
    <hkern u1="&#x34;" u2="&#x2122;" k="40" />
    <hkern u1="&#x34;" u2="&#x20ac;" k="-5" />
    <hkern u1="&#x34;" u2="&#x2022;" k="-5" />
    <hkern u1="&#x34;" u2="&#xba;" k="20" />
    <hkern u1="&#x34;" u2="&#xb7;" k="5" />
    <hkern u1="&#x34;" u2="&#xb0;" k="40" />
    <hkern u1="&#x34;" u2="&#xae;" k="35" />
    <hkern u1="&#x34;" u2="&#xaa;" k="20" />
    <hkern u1="&#x34;" u2="&#xa2;" k="-10" />
    <hkern u1="&#x34;" u2="_" k="40" />
    <hkern u1="&#x34;" u2="&#x40;" k="-5" />
    <hkern u1="&#x34;" u2="&#x3f;" k="35" />
    <hkern u1="&#x34;" u2="&#x3d;" k="-5" />
    <hkern u1="&#x34;" u2="&#x3c;" k="-5" />
    <hkern u1="&#x34;" u2="&#x39;" k="10" />
    <hkern u1="&#x34;" u2="&#x38;" k="-10" />
    <hkern u1="&#x34;" u2="&#x37;" k="10" />
    <hkern u1="&#x34;" u2="&#x34;" k="-10" />
    <hkern u1="&#x34;" u2="&#x32;" k="5" />
    <hkern u1="&#x34;" u2="&#x31;" k="5" />
    <hkern u1="&#x34;" u2="&#x2a;" k="35" />
    <hkern u1="&#x34;" u2="&#x26;" k="-5" />
    <hkern u1="&#x34;" u2="&#x23;" k="-5" />
    <hkern u1="&#x35;" u2="&#x2122;" k="30" />
    <hkern u1="&#x35;" u2="&#x20ac;" k="-5" />
    <hkern u1="&#x35;" u2="&#x2022;" k="-5" />
    <hkern u1="&#x35;" u2="&#xba;" k="25" />
    <hkern u1="&#x35;" u2="&#xb7;" k="5" />
    <hkern u1="&#x35;" u2="&#xb0;" k="35" />
    <hkern u1="&#x35;" u2="&#xae;" k="35" />
    <hkern u1="&#x35;" u2="&#xaa;" k="20" />
    <hkern u1="&#x35;" u2="&#xa2;" k="-5" />
    <hkern u1="&#x35;" u2="_" k="45" />
    <hkern u1="&#x35;" u2="&#x3f;" k="35" />
    <hkern u1="&#x35;" u2="&#x39;" k="15" />
    <hkern u1="&#x35;" u2="&#x38;" k="-5" />
    <hkern u1="&#x35;" u2="&#x37;" k="20" />
    <hkern u1="&#x35;" u2="&#x34;" k="-10" />
    <hkern u1="&#x35;" u2="&#x32;" k="5" />
    <hkern u1="&#x35;" u2="&#x31;" k="5" />
    <hkern u1="&#x35;" u2="&#x2a;" k="30" />
    <hkern u1="&#x35;" u2="&#x26;" k="-5" />
    <hkern u1="&#x35;" u2="&#x23;" k="-5" />
    <hkern u1="&#x36;" u2="&#x2122;" k="45" />
    <hkern u1="&#x36;" u2="&#xba;" k="30" />
    <hkern u1="&#x36;" u2="&#xb7;" k="5" />
    <hkern u1="&#x36;" u2="&#xb0;" k="40" />
    <hkern u1="&#x36;" u2="&#xae;" k="40" />
    <hkern u1="&#x36;" u2="&#xaa;" k="20" />
    <hkern u1="&#x36;" u2="&#xa2;" k="-5" />
    <hkern u1="&#x36;" u2="_" k="40" />
    <hkern u1="&#x36;" u2="&#x3f;" k="45" />
    <hkern u1="&#x36;" u2="&#x39;" k="20" />
    <hkern u1="&#x36;" u2="&#x37;" k="25" />
    <hkern u1="&#x36;" u2="&#x34;" k="-10" />
    <hkern u1="&#x36;" u2="&#x32;" k="10" />
    <hkern u1="&#x36;" u2="&#x2a;" k="40" />
    <hkern u1="&#x37;" g2="at.uc" k="50" />
    <hkern u1="&#x37;" u2="&#x20ac;" k="35" />
    <hkern u1="&#x37;" u2="&#x2022;" k="50" />
    <hkern u1="&#x37;" u2="&#xd7;" k="25" />
    <hkern u1="&#x37;" u2="&#xb7;" k="50" />
    <hkern u1="&#x37;" u2="&#xa7;" k="25" />
    <hkern u1="&#x37;" u2="&#xa2;" k="60" />
    <hkern u1="&#x37;" u2="_" k="50" />
    <hkern u1="&#x37;" u2="&#x40;" k="40" />
    <hkern u1="&#x37;" u2="&#x3f;" k="10" />
    <hkern u1="&#x37;" u2="&#x3e;" k="10" />
    <hkern u1="&#x37;" u2="&#x3d;" k="30" />
    <hkern u1="&#x37;" u2="&#x3c;" k="35" />
    <hkern u1="&#x37;" u2="&#x39;" k="10" />
    <hkern u1="&#x37;" u2="&#x38;" k="15" />
    <hkern u1="&#x37;" u2="&#x35;" k="10" />
    <hkern u1="&#x37;" u2="&#x34;" k="50" />
    <hkern u1="&#x37;" u2="&#x33;" k="10" />
    <hkern u1="&#x37;" u2="&#x32;" k="5" />
    <hkern u1="&#x37;" u2="&#x31;" k="-10" />
    <hkern u1="&#x37;" u2="&#x2a;" k="5" />
    <hkern u1="&#x37;" u2="&#x26;" k="30" />
    <hkern u1="&#x37;" u2="&#x23;" k="55" />
    <hkern u1="&#x38;" g2="at.uc" k="-5" />
    <hkern u1="&#x38;" u2="&#x2122;" k="25" />
    <hkern u1="&#x38;" u2="&#xb7;" k="10" />
    <hkern u1="&#x38;" u2="&#xa2;" k="-5" />
    <hkern u1="&#x38;" u2="_" k="45" />
    <hkern u1="&#x38;" u2="&#x40;" k="-5" />
    <hkern u1="&#x38;" u2="&#x3f;" k="5" />
    <hkern u1="&#x38;" u2="&#x3c;" k="10" />
    <hkern u1="&#x38;" u2="&#x38;" k="-5" />
    <hkern u1="&#x38;" u2="&#x37;" k="5" />
    <hkern u1="&#x38;" u2="&#x34;" k="-15" />
    <hkern u1="&#x38;" u2="&#x2a;" k="5" />
    <hkern u1="&#x38;" u2="&#x26;" k="-5" />
    <hkern u1="&#x3b;" u2="j" k="-10" />
    <hkern u1="&#x3c;" u2="&#x37;" k="5" />
    <hkern u1="&#x3d;" u2="&#x37;" k="30" />
    <hkern u1="&#x3d;" u2="&#x35;" k="5" />
    <hkern u1="&#x3d;" u2="&#x34;" k="-5" />
    <hkern u1="&#x3d;" u2="&#x33;" k="5" />
    <hkern u1="&#x3d;" u2="&#x32;" k="5" />
    <hkern u1="&#x3d;" u2="&#x31;" k="5" />
    <hkern u1="&#x3e;" u2="&#x38;" k="10" />
    <hkern u1="&#x3e;" u2="&#x37;" k="35" />
    <hkern u1="&#x3e;" u2="&#x35;" k="5" />
    <hkern u1="&#x3e;" u2="&#x34;" k="-5" />
    <hkern u1="&#x3e;" u2="&#x33;" k="20" />
    <hkern u1="&#x3e;" u2="&#x32;" k="25" />
    <hkern u1="&#x3e;" u2="&#x31;" k="15" />
    <hkern u1="&#x40;" u2="&#x142;" k="-10" />
    <hkern u1="&#x40;" u2="&#x141;" k="-5" />
    <hkern u1="&#x40;" u2="x" k="30" />
    <hkern u1="&#x40;" u2="v" k="5" />
    <hkern u1="&#x40;" u2="X" k="50" />
    <hkern u1="&#x40;" u2="V" k="50" />
    <hkern u1="&#x40;" u2="&#x37;" k="35" />
    <hkern u1="&#x40;" u2="&#x35;" k="10" />
    <hkern u1="&#x40;" u2="&#x34;" k="-5" />
    <hkern u1="&#x40;" u2="&#x33;" k="20" />
    <hkern u1="&#x40;" u2="&#x32;" k="15" />
    <hkern u1="&#x40;" u2="&#x31;" k="15" />
    <hkern u1="A" u2="&#xf8;" k="15" />
    <hkern u1="A" u2="&#xd8;" k="30" />
    <hkern u1="B" g2="at.uc" k="-5" />
    <hkern u1="B" u2="&#x2122;" k="15" />
    <hkern u1="B" u2="&#x2022;" k="-5" />
    <hkern u1="B" u2="&#x141;" k="-5" />
    <hkern u1="B" u2="&#xb7;" k="10" />
    <hkern u1="B" u2="x" k="15" />
    <hkern u1="B" u2="v" k="5" />
    <hkern u1="B" u2="X" k="25" />
    <hkern u1="B" u2="V" k="25" />
    <hkern u1="B" u2="&#x3f;" k="5" />
    <hkern u1="B" u2="&#x2a;" k="5" />
    <hkern u1="B" u2="&#x26;" k="-5" />
    <hkern u1="C" u2="&#xf8;" k="25" />
    <hkern u1="C" u2="&#xd8;" k="35" />
    <hkern u1="F" g2="at.uc" k="-5" />
    <hkern u1="F" u2="&#x141;" k="5" />
    <hkern u1="F" u2="&#x135;" k="-5" />
    <hkern u1="F" u2="&#x12d;" k="-10" />
    <hkern u1="F" u2="&#x12b;" k="-5" />
    <hkern u1="F" u2="&#x129;" k="-10" />
    <hkern u1="F" u2="&#x126;" k="5" />
    <hkern u1="F" u2="&#xef;" k="-10" />
    <hkern u1="F" u2="&#xed;" k="5" />
    <hkern u1="F" u2="&#xec;" k="5" />
    <hkern u1="F" u2="&#xae;" k="5" />
    <hkern u1="F" u2="x" k="15" />
    <hkern u1="F" u2="v" k="5" />
    <hkern u1="F" u2="_" k="50" />
    <hkern u1="F" u2="X" k="5" />
    <hkern u1="F" u2="V" k="-5" />
    <hkern u1="F" u2="&#x2a;" k="5" />
    <hkern u1="F" u2="&#x26;" k="5" />
    <hkern u1="H" u2="&#xee;" k="-10" />
    <hkern u1="I" u2="&#xee;" k="-10" />
    <hkern u1="K" u2="&#x135;" k="-15" />
    <hkern u1="K" u2="&#x129;" k="-10" />
    <hkern u1="K" u2="&#xf8;" k="30" />
    <hkern u1="K" u2="&#xef;" k="-10" />
    <hkern u1="K" u2="&#xee;" k="-25" />
    <hkern u1="K" u2="&#xed;" k="5" />
    <hkern u1="K" u2="&#xec;" k="-10" />
    <hkern u1="K" u2="&#xd8;" k="45" />
    <hkern u1="M" u2="&#xee;" k="-10" />
    <hkern u1="N" u2="&#xee;" k="-10" />
    <hkern u1="P" u2="&#x2022;" k="20" />
    <hkern u1="P" u2="&#x141;" k="5" />
    <hkern u1="P" u2="&#x12d;" k="-10" />
    <hkern u1="P" u2="&#x12b;" k="-10" />
    <hkern u1="P" u2="&#x127;" k="-5" />
    <hkern u1="P" u2="&#x126;" k="-20" />
    <hkern u1="P" u2="&#xef;" k="-10" />
    <hkern u1="P" u2="&#xee;" k="-10" />
    <hkern u1="P" u2="&#xdf;" k="5" />
    <hkern u1="P" u2="&#xb7;" k="5" />
    <hkern u1="P" u2="&#xae;" k="-15" />
    <hkern u1="P" u2="v" k="-15" />
    <hkern u1="P" u2="_" k="85" />
    <hkern u1="P" u2="X" k="30" />
    <hkern u1="P" u2="V" k="5" />
    <hkern u1="P" u2="&#x40;" k="10" />
    <hkern u1="P" u2="&#x3f;" k="-5" />
    <hkern u1="P" u2="&#x2a;" k="-10" />
    <hkern u1="P" u2="&#x26;" k="20" />
    <hkern u1="Q" g2="at.uc" k="-5" />
    <hkern u1="Q" u2="&#x2122;" k="30" />
    <hkern u1="Q" u2="&#x2022;" k="-10" />
    <hkern u1="Q" u2="&#x142;" k="-5" />
    <hkern u1="Q" u2="&#x141;" k="-5" />
    <hkern u1="Q" u2="&#xb7;" k="-10" />
    <hkern u1="Q" u2="&#xae;" k="10" />
    <hkern u1="Q" u2="x" k="25" />
    <hkern u1="Q" u2="v" k="15" />
    <hkern u1="Q" u2="_" k="40" />
    <hkern u1="Q" u2="X" k="50" />
    <hkern u1="Q" u2="V" k="55" />
    <hkern u1="Q" u2="&#x3f;" k="20" />
    <hkern u1="Q" u2="&#x2e;" k="5" />
    <hkern u1="Q" u2="&#x2c;" k="5" />
    <hkern u1="Q" u2="&#x2a;" k="10" />
    <hkern u1="Q" u2="&#x26;" k="5" />
    <hkern u1="R" u2="&#xf8;" k="20" />
    <hkern u1="R" u2="&#xd8;" k="25" />
    <hkern u1="T" u2="&#x161;" k="45" />
    <hkern u1="T" u2="&#x15d;" k="45" />
    <hkern u1="T" u2="&#x159;" k="-5" />
    <hkern u1="T" u2="&#x135;" k="-15" />
    <hkern u1="T" u2="&#x12b;" k="-15" />
    <hkern u1="T" u2="&#x129;" k="-10" />
    <hkern u1="T" u2="&#x103;" k="50" />
    <hkern u1="T" u2="&#xef;" k="-20" />
    <hkern u1="T" u2="&#xee;" k="-5" />
    <hkern u1="T" u2="&#xed;" k="20" />
    <hkern u1="T" u2="&#xec;" k="-10" />
    <hkern u1="T" u2="&#xe4;" k="35" />
    <hkern u1="T" u2="&#xe3;" k="50" />
    <hkern u1="T" u2="&#xdf;" k="10" />
    <hkern u1="V" g2="at.uc" k="45" />
    <hkern u1="V" g2="ydieresis.alt" k="25" />
    <hkern u1="V" u2="&#x2122;" k="-10" />
    <hkern u1="V" u2="&#x2022;" k="70" />
    <hkern u1="V" u2="&#x17e;" k="20" />
    <hkern u1="V" u2="&#x161;" k="40" />
    <hkern u1="V" u2="&#x15d;" k="40" />
    <hkern u1="V" u2="&#x159;" k="-5" />
    <hkern u1="V" u2="&#x142;" k="10" />
    <hkern u1="V" u2="&#x141;" k="15" />
    <hkern u1="V" u2="&#x135;" k="-15" />
    <hkern u1="V" u2="&#x12b;" k="-5" />
    <hkern u1="V" u2="&#x127;" k="-5" />
    <hkern u1="V" u2="&#x126;" k="-10" />
    <hkern u1="V" u2="&#x103;" k="50" />
    <hkern u1="V" u2="&#xef;" k="-10" />
    <hkern u1="V" u2="&#xee;" k="-10" />
    <hkern u1="V" u2="&#xed;" k="25" />
    <hkern u1="V" u2="&#xec;" k="5" />
    <hkern u1="V" u2="&#xe5;" k="55" />
    <hkern u1="V" u2="&#xe4;" k="40" />
    <hkern u1="V" u2="&#xe3;" k="50" />
    <hkern u1="V" u2="&#xe2;" k="50" />
    <hkern u1="V" u2="&#xdf;" k="20" />
    <hkern u1="V" u2="&#xb7;" k="55" />
    <hkern u1="V" u2="&#xae;" k="-10" />
    <hkern u1="V" u2="x" k="30" />
    <hkern u1="V" u2="v" k="25" />
    <hkern u1="V" u2="_" k="75" />
    <hkern u1="V" u2="X" k="-10" />
    <hkern u1="V" u2="V" k="-20" />
    <hkern u1="V" u2="&#x40;" k="60" />
    <hkern u1="V" u2="&#x3f;" k="10" />
    <hkern u1="V" u2="&#x2a;" k="10" />
    <hkern u1="V" u2="&#x26;" k="45" />
    <hkern u1="W" u2="&#x161;" k="30" />
    <hkern u1="W" u2="&#x15d;" k="30" />
    <hkern u1="W" u2="&#x135;" k="-15" />
    <hkern u1="W" u2="&#x12d;" k="5" />
    <hkern u1="W" u2="&#x12b;" k="-10" />
    <hkern u1="W" u2="&#xef;" k="-15" />
    <hkern u1="W" u2="&#xee;" k="-10" />
    <hkern u1="W" u2="&#xed;" k="15" />
    <hkern u1="W" u2="&#xe4;" k="35" />
    <hkern u1="W" u2="&#xdf;" k="15" />
    <hkern u1="X" g2="at.uc" k="40" />
    <hkern u1="X" u2="&#x2122;" k="5" />
    <hkern u1="X" u2="&#x2022;" k="60" />
    <hkern u1="X" u2="&#x142;" k="20" />
    <hkern u1="X" u2="&#x141;" k="5" />
    <hkern u1="X" u2="&#x135;" k="-5" />
    <hkern u1="X" u2="&#x129;" k="-10" />
    <hkern u1="X" u2="&#xf8;" k="30" />
    <hkern u1="X" u2="&#xef;" k="-10" />
    <hkern u1="X" u2="&#xd8;" k="40" />
    <hkern u1="X" u2="&#xb7;" k="80" />
    <hkern u1="X" u2="&#xae;" k="20" />
    <hkern u1="X" u2="x" k="5" />
    <hkern u1="X" u2="v" k="60" />
    <hkern u1="X" u2="X" k="-20" />
    <hkern u1="X" u2="V" k="-10" />
    <hkern u1="X" u2="&#x40;" k="40" />
    <hkern u1="X" u2="&#x3f;" k="35" />
    <hkern u1="X" u2="&#x2a;" k="30" />
    <hkern u1="X" u2="&#x26;" k="25" />
    <hkern u1="X" u2="&#x21;" k="5" />
    <hkern u1="Y" g2="adieresis.alt" k="80" />
    <hkern u1="Y" u2="&#x161;" k="50" />
    <hkern u1="Y" u2="&#x15d;" k="60" />
    <hkern u1="Y" u2="&#x159;" k="5" />
    <hkern u1="Y" u2="&#x135;" k="-25" />
    <hkern u1="Y" u2="&#x12d;" k="-10" />
    <hkern u1="Y" u2="&#x12b;" k="-5" />
    <hkern u1="Y" u2="&#x129;" k="-15" />
    <hkern u1="Y" u2="&#x10d;" k="80" />
    <hkern u1="Y" u2="&#x103;" k="55" />
    <hkern u1="Y" u2="&#x101;" k="70" />
    <hkern u1="Y" u2="&#xfc;" k="40" />
    <hkern u1="Y" u2="&#xf6;" k="80" />
    <hkern u1="Y" u2="&#xf0;" k="80" />
    <hkern u1="Y" u2="&#xef;" k="-10" />
    <hkern u1="Y" u2="&#xee;" k="-25" />
    <hkern u1="Y" u2="&#xed;" k="35" />
    <hkern u1="Y" u2="&#xec;" k="-10" />
    <hkern u1="Y" u2="&#xe5;" k="75" />
    <hkern u1="Y" u2="&#xe4;" k="45" />
    <hkern u1="Y" u2="&#xe3;" k="60" />
    <hkern u1="Y" u2="&#xe0;" k="60" />
    <hkern u1="Y" u2="&#xdf;" k="20" />
    <hkern u1="Z" u2="&#x135;" k="-5" />
    <hkern u1="Z" u2="&#x12d;" k="-30" />
    <hkern u1="Z" u2="&#x12b;" k="-5" />
    <hkern u1="Z" u2="&#x129;" k="-20" />
    <hkern u1="Z" u2="&#xef;" k="-15" />
    <hkern u1="Z" u2="&#xee;" k="-5" />
    <hkern u1="Z" u2="&#xd8;" k="40" />
    <hkern u1="\" g2="y.alt" k="15" />
    <hkern u1="\" u2="p" k="25" />
    <hkern u1="\" u2="g" k="45" />
    <hkern u1="_" u2="&#x141;" k="5" />
    <hkern u1="_" u2="&#x126;" k="5" />
    <hkern u1="_" u2="v" k="5" />
    <hkern u1="_" u2="V" k="75" />
    <hkern u1="_" u2="&#x39;" k="55" />
    <hkern u1="_" u2="&#x38;" k="45" />
    <hkern u1="_" u2="&#x37;" k="25" />
    <hkern u1="_" u2="&#x35;" k="55" />
    <hkern u1="_" u2="&#x34;" k="40" />
    <hkern u1="_" u2="&#x33;" k="45" />
    <hkern u1="_" u2="&#x31;" k="25" />
    <hkern u1="f" u2="&#x159;" k="-10" />
    <hkern u1="f" u2="&#x135;" k="-10" />
    <hkern u1="f" u2="&#x12d;" k="-5" />
    <hkern u1="f" u2="&#x12b;" k="-15" />
    <hkern u1="f" u2="&#x129;" k="-10" />
    <hkern u1="f" u2="&#xef;" k="-15" />
    <hkern u1="f" u2="&#xee;" k="-10" />
    <hkern u1="f" u2="&#xec;" k="-5" />
    <hkern u1="k" u2="&#xf8;" k="40" />
    <hkern u1="v" g2="at.uc" k="10" />
    <hkern u1="v" u2="&#x2122;" k="10" />
    <hkern u1="v" u2="&#x2022;" k="25" />
    <hkern u1="v" u2="&#x142;" k="10" />
    <hkern u1="v" u2="&#xb7;" k="15" />
    <hkern u1="v" u2="&#xb0;" k="-5" />
    <hkern u1="v" u2="&#xae;" k="-20" />
    <hkern u1="v" u2="v" k="-10" />
    <hkern u1="v" u2="&#x40;" k="10" />
    <hkern u1="v" u2="&#x3f;" k="-10" />
    <hkern u1="v" u2="&#x2a;" k="-5" />
    <hkern u1="v" u2="&#x26;" k="20" />
    <hkern u1="x" g2="at.uc" k="35" />
    <hkern u1="x" u2="&#x2122;" k="30" />
    <hkern u1="x" u2="&#x2022;" k="55" />
    <hkern u1="x" u2="&#x142;" k="25" />
    <hkern u1="x" u2="&#xf8;" k="40" />
    <hkern u1="x" u2="&#xb7;" k="45" />
    <hkern u1="x" u2="&#xb0;" k="5" />
    <hkern u1="x" u2="x" k="-5" />
    <hkern u1="x" u2="&#x40;" k="35" />
    <hkern u1="x" u2="&#x2a;" k="5" />
    <hkern u1="x" u2="&#x26;" k="25" />
    <hkern u1="&#xa1;" u2="&#x126;" k="-5" />
    <hkern u1="&#xa1;" u2="V" k="15" />
    <hkern u1="&#xa1;" u2="&#x37;" k="5" />
    <hkern u1="&#xa3;" u2="&#x37;" k="5" />
    <hkern u1="&#xa3;" u2="&#x35;" k="5" />
    <hkern u1="&#xa3;" u2="&#x34;" k="30" />
    <hkern u1="&#xa3;" u2="&#x31;" k="-5" />
    <hkern u1="&#xa5;" u2="&#x39;" k="20" />
    <hkern u1="&#xa5;" u2="&#x38;" k="20" />
    <hkern u1="&#xa5;" u2="&#x37;" k="-5" />
    <hkern u1="&#xa5;" u2="&#x35;" k="20" />
    <hkern u1="&#xa5;" u2="&#x34;" k="15" />
    <hkern u1="&#xa5;" u2="&#x33;" k="10" />
    <hkern u1="&#xa5;" u2="&#x32;" k="20" />
    <hkern u1="&#xa7;" u2="&#x39;" k="10" />
    <hkern u1="&#xa7;" u2="&#x37;" k="15" />
    <hkern u1="&#xa7;" u2="&#x31;" k="15" />
    <hkern u1="&#xb0;" u2="&#x142;" k="15" />
    <hkern u1="&#xb0;" u2="&#x127;" k="-15" />
    <hkern u1="&#xb0;" u2="x" k="5" />
    <hkern u1="&#xb0;" u2="v" k="-5" />
    <hkern u1="&#xb0;" u2="&#x37;" k="-5" />
    <hkern u1="&#xb0;" u2="&#x35;" k="5" />
    <hkern u1="&#xb0;" u2="&#x34;" k="55" />
    <hkern u1="&#xb0;" u2="&#x33;" k="15" />
    <hkern u1="&#xb0;" u2="&#x31;" k="-10" />
    <hkern u1="&#xb7;" u2="&#x142;" k="-5" />
    <hkern u1="&#xb7;" u2="&#x141;" k="5" />
    <hkern u1="&#xb7;" u2="&#x126;" k="5" />
    <hkern u1="&#xb7;" u2="x" k="45" />
    <hkern u1="&#xb7;" u2="v" k="15" />
    <hkern u1="&#xb7;" u2="X" k="80" />
    <hkern u1="&#xb7;" u2="V" k="55" />
    <hkern u1="&#xb7;" u2="&#x39;" k="5" />
    <hkern u1="&#xb7;" u2="&#x38;" k="10" />
    <hkern u1="&#xb7;" u2="&#x37;" k="45" />
    <hkern u1="&#xb7;" u2="&#x34;" k="-5" />
    <hkern u1="&#xb7;" u2="&#x33;" k="35" />
    <hkern u1="&#xb7;" u2="&#x32;" k="25" />
    <hkern u1="&#xb7;" u2="&#x31;" k="15" />
    <hkern u1="&#xba;" u2="&#x37;" k="-5" />
    <hkern u1="&#xbf;" u2="&#x142;" k="15" />
    <hkern u1="&#xbf;" u2="&#x141;" k="15" />
    <hkern u1="&#xbf;" u2="&#x127;" k="5" />
    <hkern u1="&#xbf;" u2="&#x126;" k="5" />
    <hkern u1="&#xbf;" u2="x" k="5" />
    <hkern u1="&#xbf;" u2="v" k="75" />
    <hkern u1="&#xbf;" u2="V" k="100" />
    <hkern u1="&#xbf;" u2="&#x39;" k="35" />
    <hkern u1="&#xbf;" u2="&#x38;" k="20" />
    <hkern u1="&#xbf;" u2="&#x37;" k="35" />
    <hkern u1="&#xbf;" u2="&#x35;" k="30" />
    <hkern u1="&#xbf;" u2="&#x34;" k="50" />
    <hkern u1="&#xbf;" u2="&#x33;" k="10" />
    <hkern u1="&#xbf;" u2="&#x31;" k="30" />
    <hkern u1="&#xc0;" u2="&#xf8;" k="15" />
    <hkern u1="&#xc1;" u2="&#xf8;" k="15" />
    <hkern u1="&#xc2;" u2="&#xf8;" k="15" />
    <hkern u1="&#xc3;" u2="&#xf8;" k="15" />
    <hkern u1="&#xc4;" u2="&#xf8;" k="15" />
    <hkern u1="&#xc5;" u2="&#xf8;" k="15" />
    <hkern u1="&#xc7;" u2="&#xf8;" k="25" />
    <hkern u1="&#xcc;" u2="&#xee;" k="-10" />
    <hkern u1="&#xcd;" u2="&#xee;" k="-10" />
    <hkern u1="&#xce;" u2="&#xee;" k="-10" />
    <hkern u1="&#xcf;" u2="&#xee;" k="-10" />
    <hkern u1="&#xd1;" u2="&#xee;" k="-10" />
    <hkern u1="&#xd7;" u2="&#x37;" k="25" />
    <hkern u1="&#xd7;" u2="&#x34;" k="-5" />
    <hkern u1="&#xd7;" u2="&#x33;" k="5" />
    <hkern u1="&#xd7;" u2="&#x32;" k="5" />
    <hkern u1="&#xd7;" u2="&#x31;" k="10" />
    <hkern u1="&#xd8;" u2="Z" k="40" />
    <hkern u1="&#xd8;" u2="Y" k="40" />
    <hkern u1="&#xd8;" u2="V" k="35" />
    <hkern u1="&#xd8;" u2="T" k="30" />
    <hkern u1="&#xdd;" g2="adieresis.alt" k="80" />
    <hkern u1="&#xdd;" u2="&#x161;" k="50" />
    <hkern u1="&#xdd;" u2="&#x15d;" k="60" />
    <hkern u1="&#xdd;" u2="&#x159;" k="5" />
    <hkern u1="&#xdd;" u2="&#x135;" k="-25" />
    <hkern u1="&#xdd;" u2="&#x12d;" k="-10" />
    <hkern u1="&#xdd;" u2="&#x12b;" k="-5" />
    <hkern u1="&#xdd;" u2="&#x129;" k="-15" />
    <hkern u1="&#xdd;" u2="&#x10d;" k="80" />
    <hkern u1="&#xdd;" u2="&#x103;" k="55" />
    <hkern u1="&#xdd;" u2="&#x101;" k="70" />
    <hkern u1="&#xdd;" u2="&#xfc;" k="40" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="80" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="80" />
    <hkern u1="&#xdd;" u2="&#xef;" k="-10" />
    <hkern u1="&#xdd;" u2="&#xee;" k="-25" />
    <hkern u1="&#xdd;" u2="&#xed;" k="35" />
    <hkern u1="&#xdd;" u2="&#xec;" k="-10" />
    <hkern u1="&#xdd;" u2="&#xe5;" k="75" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="45" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="60" />
    <hkern u1="&#xdd;" u2="&#xe0;" k="60" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="20" />
    <hkern u1="&#xde;" g2="at.uc" k="-5" />
    <hkern u1="&#xde;" u2="&#x2122;" k="35" />
    <hkern u1="&#xde;" u2="&#x2022;" k="-5" />
    <hkern u1="&#xde;" u2="&#x141;" k="-5" />
    <hkern u1="&#xde;" u2="&#xb7;" k="-10" />
    <hkern u1="&#xde;" u2="&#xae;" k="15" />
    <hkern u1="&#xde;" u2="x" k="40" />
    <hkern u1="&#xde;" u2="v" k="10" />
    <hkern u1="&#xde;" u2="_" k="90" />
    <hkern u1="&#xde;" u2="X" k="65" />
    <hkern u1="&#xde;" u2="V" k="45" />
    <hkern u1="&#xde;" u2="&#x3f;" k="25" />
    <hkern u1="&#xde;" u2="&#x2a;" k="15" />
    <hkern u1="&#xde;" u2="&#x26;" k="5" />
    <hkern u1="&#xdf;" g2="at.uc" k="-5" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="50" />
    <hkern u1="&#xdf;" u2="&#x2022;" k="-5" />
    <hkern u1="&#xdf;" u2="&#xb0;" k="45" />
    <hkern u1="&#xdf;" u2="&#xae;" k="40" />
    <hkern u1="&#xdf;" u2="x" k="35" />
    <hkern u1="&#xdf;" u2="v" k="35" />
    <hkern u1="&#xdf;" u2="&#x40;" k="-5" />
    <hkern u1="&#xdf;" u2="&#x3f;" k="35" />
    <hkern u1="&#xdf;" u2="&#x2a;" k="40" />
    <hkern u1="&#xdf;" u2="&#x26;" k="-5" />
    <hkern u1="&#xed;" u2="&#x17e;" k="-15" />
    <hkern u1="&#xed;" u2="&#x161;" k="-5" />
    <hkern u1="&#xed;" u2="&#x159;" k="-50" />
    <hkern u1="&#xed;" u2="&#xe4;" k="-5" />
    <hkern u1="&#xf8;" u2="y" k="30" />
    <hkern u1="&#x100;" u2="&#xf8;" k="15" />
    <hkern u1="&#x102;" u2="&#xf8;" k="15" />
    <hkern u1="&#x104;" u2="&#xf8;" k="15" />
    <hkern u1="&#x105;" u2="&#x135;" k="-25" />
    <hkern u1="&#x105;" u2="j" k="-25" />
    <hkern u1="&#x106;" u2="&#xf8;" k="25" />
    <hkern u1="&#x108;" u2="&#xf8;" k="25" />
    <hkern u1="&#x10a;" u2="&#xf8;" k="25" />
    <hkern u1="&#x10c;" u2="&#xf8;" k="25" />
    <hkern u1="&#x10f;" g2="f_f.short" k="-75" />
    <hkern u1="&#x10f;" g2="f.short" k="-75" />
    <hkern u1="&#x10f;" g2="kcommaaccent.alt" k="-110" />
    <hkern u1="&#x10f;" g2="k.alt" k="-110" />
    <hkern u1="&#x10f;" g2="idotaccent" k="-115" />
    <hkern u1="&#x10f;" u2="&#xfb02;" k="-75" />
    <hkern u1="&#x10f;" u2="&#xfb01;" k="-75" />
    <hkern u1="&#x10f;" g2="f_f" k="-75" />
    <hkern u1="&#x10f;" u2="&#x21b;" k="-75" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="-90" />
    <hkern u1="&#x10f;" u2="&#x16f;" k="-40" />
    <hkern u1="&#x10f;" u2="&#x167;" k="-75" />
    <hkern u1="&#x10f;" u2="&#x165;" k="-75" />
    <hkern u1="&#x10f;" u2="&#x163;" k="-75" />
    <hkern u1="&#x10f;" u2="&#x161;" k="-90" />
    <hkern u1="&#x10f;" u2="&#x159;" k="-145" />
    <hkern u1="&#x10f;" u2="&#x155;" k="-60" />
    <hkern u1="&#x10f;" u2="&#x148;" k="-70" />
    <hkern u1="&#x10f;" u2="&#x140;" k="-110" />
    <hkern u1="&#x10f;" u2="&#x13e;" k="-110" />
    <hkern u1="&#x10f;" u2="&#x13c;" k="-110" />
    <hkern u1="&#x10f;" u2="&#x13a;" k="-110" />
    <hkern u1="&#x10f;" u2="&#x137;" k="-110" />
    <hkern u1="&#x10f;" u2="&#x135;" k="-115" />
    <hkern u1="&#x10f;" u2="&#x133;" k="-115" />
    <hkern u1="&#x10f;" u2="&#x12f;" k="-115" />
    <hkern u1="&#x10f;" u2="&#x12d;" k="-115" />
    <hkern u1="&#x10f;" u2="&#x12b;" k="-115" />
    <hkern u1="&#x10f;" u2="&#x12a;" k="-110" />
    <hkern u1="&#x10f;" u2="&#x129;" k="-115" />
    <hkern u1="&#x10f;" u2="&#x125;" k="-110" />
    <hkern u1="&#x10f;" u2="&#x115;" k="-30" />
    <hkern u1="&#x10f;" u2="&#x10d;" k="-50" />
    <hkern u1="&#x10f;" u2="&#xfe;" k="-110" />
    <hkern u1="&#x10f;" u2="&#xfa;" k="-15" />
    <hkern u1="&#x10f;" u2="&#xf4;" k="-20" />
    <hkern u1="&#x10f;" u2="&#xef;" k="-115" />
    <hkern u1="&#x10f;" u2="&#xee;" k="-115" />
    <hkern u1="&#x10f;" u2="&#xed;" k="-100" />
    <hkern u1="&#x10f;" u2="&#xec;" k="-115" />
    <hkern u1="&#x10f;" u2="&#xe4;" k="-85" />
    <hkern u1="&#x10f;" u2="&#xdf;" k="-110" />
    <hkern u1="&#x10f;" u2="t" k="-75" />
    <hkern u1="&#x10f;" u2="l" k="-110" />
    <hkern u1="&#x10f;" u2="k" k="-110" />
    <hkern u1="&#x10f;" u2="j" k="-115" />
    <hkern u1="&#x10f;" u2="i" k="-115" />
    <hkern u1="&#x10f;" u2="h" k="-110" />
    <hkern u1="&#x10f;" u2="f" k="-75" />
    <hkern u1="&#x10f;" u2="b" k="-110" />
    <hkern u1="&#x111;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x111;" u2="&#x127;" k="-40" />
    <hkern u1="&#x111;" u2="&#xb0;" k="-15" />
    <hkern u1="&#x111;" u2="&#xae;" k="-15" />
    <hkern u1="&#x111;" u2="&#x3f;" k="-5" />
    <hkern u1="&#x124;" u2="&#xee;" k="-10" />
    <hkern u1="&#x126;" g2="at.uc" k="10" />
    <hkern u1="&#x126;" u2="&#x126;" k="-20" />
    <hkern u1="&#x126;" u2="&#xb7;" k="5" />
    <hkern u1="&#x126;" u2="&#xae;" k="-15" />
    <hkern u1="&#x126;" u2="_" k="5" />
    <hkern u1="&#x126;" u2="V" k="-10" />
    <hkern u1="&#x126;" u2="&#x3f;" k="-5" />
    <hkern u1="&#x128;" u2="&#xee;" k="-10" />
    <hkern u1="&#x12c;" u2="&#xee;" k="-10" />
    <hkern u1="&#x12e;" u2="&#xee;" k="-10" />
    <hkern u1="&#x12f;" u2="&#x135;" k="-25" />
    <hkern u1="&#x12f;" u2="j" k="-25" />
    <hkern u1="&#x130;" u2="&#xee;" k="-10" />
    <hkern u1="&#x136;" u2="&#x135;" k="-15" />
    <hkern u1="&#x136;" u2="&#x129;" k="-10" />
    <hkern u1="&#x136;" u2="&#xf8;" k="30" />
    <hkern u1="&#x136;" u2="&#xef;" k="-10" />
    <hkern u1="&#x136;" u2="&#xee;" k="-25" />
    <hkern u1="&#x136;" u2="&#xed;" k="5" />
    <hkern u1="&#x136;" u2="&#xec;" k="-10" />
    <hkern u1="&#x13d;" g2="braceright.uc" k="-10" />
    <hkern u1="&#x13d;" g2="bracketright.uc" k="-10" />
    <hkern u1="&#x13d;" g2="parenright.uc" k="-10" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="15" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="-5" />
    <hkern u1="&#x13d;" u2="&#x201c;" k="10" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="-5" />
    <hkern u1="&#x13d;" u2="&#x2018;" k="10" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="15" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="10" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="10" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="10" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="15" />
    <hkern u1="&#x13d;" u2="&#x178;" k="15" />
    <hkern u1="&#x13d;" u2="&#x176;" k="15" />
    <hkern u1="&#x13d;" u2="&#x174;" k="10" />
    <hkern u1="&#x13d;" u2="&#x166;" k="15" />
    <hkern u1="&#x13d;" u2="&#x164;" k="15" />
    <hkern u1="&#x13d;" u2="&#x162;" k="15" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="15" />
    <hkern u1="&#x13d;" u2="&#xae;" k="10" />
    <hkern u1="&#x13d;" u2="&#x7d;" k="-10" />
    <hkern u1="&#x13d;" u2="]" k="-10" />
    <hkern u1="&#x13d;" u2="Y" k="15" />
    <hkern u1="&#x13d;" u2="W" k="10" />
    <hkern u1="&#x13d;" u2="V" k="10" />
    <hkern u1="&#x13d;" u2="T" k="15" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="30" />
    <hkern u1="&#x13d;" u2="&#x29;" k="-10" />
    <hkern u1="&#x13e;" g2="f_f.short" k="-75" />
    <hkern u1="&#x13e;" g2="f.short" k="-75" />
    <hkern u1="&#x13e;" g2="kcommaaccent.alt" k="-110" />
    <hkern u1="&#x13e;" g2="k.alt" k="-110" />
    <hkern u1="&#x13e;" g2="idotaccent" k="-115" />
    <hkern u1="&#x13e;" u2="&#xfb02;" k="-75" />
    <hkern u1="&#x13e;" u2="&#xfb01;" k="-75" />
    <hkern u1="&#x13e;" g2="f_f" k="-75" />
    <hkern u1="&#x13e;" u2="&#x21b;" k="-75" />
    <hkern u1="&#x13e;" u2="&#x167;" k="-75" />
    <hkern u1="&#x13e;" u2="&#x165;" k="-75" />
    <hkern u1="&#x13e;" u2="&#x163;" k="-75" />
    <hkern u1="&#x13e;" u2="&#x140;" k="-110" />
    <hkern u1="&#x13e;" u2="&#x13e;" k="-110" />
    <hkern u1="&#x13e;" u2="&#x13c;" k="-110" />
    <hkern u1="&#x13e;" u2="&#x13a;" k="-110" />
    <hkern u1="&#x13e;" u2="&#x137;" k="-110" />
    <hkern u1="&#x13e;" u2="&#x135;" k="-115" />
    <hkern u1="&#x13e;" u2="&#x133;" k="-115" />
    <hkern u1="&#x13e;" u2="&#x12f;" k="-115" />
    <hkern u1="&#x13e;" u2="&#x12d;" k="-115" />
    <hkern u1="&#x13e;" u2="&#x12b;" k="-115" />
    <hkern u1="&#x13e;" u2="&#x12a;" k="-110" />
    <hkern u1="&#x13e;" u2="&#x129;" k="-115" />
    <hkern u1="&#x13e;" u2="&#x125;" k="-110" />
    <hkern u1="&#x13e;" u2="&#xfe;" k="-110" />
    <hkern u1="&#x13e;" u2="&#xef;" k="-115" />
    <hkern u1="&#x13e;" u2="&#xee;" k="-115" />
    <hkern u1="&#x13e;" u2="&#xed;" k="-115" />
    <hkern u1="&#x13e;" u2="&#xec;" k="-115" />
    <hkern u1="&#x13e;" u2="&#xdf;" k="-110" />
    <hkern u1="&#x13e;" u2="t" k="-75" />
    <hkern u1="&#x13e;" u2="l" k="-110" />
    <hkern u1="&#x13e;" u2="k" k="-110" />
    <hkern u1="&#x13e;" u2="j" k="-115" />
    <hkern u1="&#x13e;" u2="i" k="-115" />
    <hkern u1="&#x13e;" u2="h" k="-110" />
    <hkern u1="&#x13e;" u2="f" k="-75" />
    <hkern u1="&#x13e;" u2="b" k="-110" />
    <hkern u1="&#x13f;" g2="at.uc" k="-15" />
    <hkern u1="&#x13f;" u2="V" k="15" />
    <hkern u1="&#x141;" u2="&#x201d;" k="55" />
    <hkern u1="&#x141;" u2="&#x201c;" k="55" />
    <hkern u1="&#x141;" u2="&#x2019;" k="55" />
    <hkern u1="&#x141;" u2="&#x2018;" k="55" />
    <hkern u1="&#x142;" u2="&#x2122;" k="10" />
    <hkern u1="&#x142;" u2="&#x2022;" k="5" />
    <hkern u1="&#x142;" u2="&#xb7;" k="5" />
    <hkern u1="&#x142;" u2="&#xb0;" k="-5" />
    <hkern u1="&#x142;" u2="&#xae;" k="-5" />
    <hkern u1="&#x142;" u2="x" k="5" />
    <hkern u1="&#x142;" u2="v" k="-10" />
    <hkern u1="&#x142;" u2="&#x3f;" k="-5" />
    <hkern u1="&#x142;" u2="&#x26;" k="5" />
    <hkern u1="&#x143;" u2="&#xee;" k="-10" />
    <hkern u1="&#x145;" u2="&#xee;" k="-10" />
    <hkern u1="&#x147;" u2="&#xee;" k="-10" />
    <hkern u1="&#x14a;" u2="&#xee;" k="-10" />
    <hkern u1="&#x154;" u2="&#xf8;" k="20" />
    <hkern u1="&#x156;" u2="&#xf8;" k="20" />
    <hkern u1="&#x158;" u2="&#xf8;" k="20" />
    <hkern u1="&#x159;" u2="&#x159;" k="-10" />
    <hkern u1="&#x159;" u2="&#xe4;" k="15" />
    <hkern u1="&#x162;" u2="&#x161;" k="45" />
    <hkern u1="&#x162;" u2="&#x15d;" k="45" />
    <hkern u1="&#x162;" u2="&#x159;" k="-5" />
    <hkern u1="&#x162;" u2="&#x135;" k="-15" />
    <hkern u1="&#x162;" u2="&#x12b;" k="-15" />
    <hkern u1="&#x162;" u2="&#x129;" k="-10" />
    <hkern u1="&#x162;" u2="&#x103;" k="50" />
    <hkern u1="&#x162;" u2="&#xef;" k="-20" />
    <hkern u1="&#x162;" u2="&#xee;" k="-5" />
    <hkern u1="&#x162;" u2="&#xed;" k="20" />
    <hkern u1="&#x162;" u2="&#xec;" k="-10" />
    <hkern u1="&#x162;" u2="&#xe4;" k="35" />
    <hkern u1="&#x162;" u2="&#xe3;" k="50" />
    <hkern u1="&#x162;" u2="&#xdf;" k="10" />
    <hkern u1="&#x164;" u2="&#x161;" k="45" />
    <hkern u1="&#x164;" u2="&#x15d;" k="45" />
    <hkern u1="&#x164;" u2="&#x159;" k="-5" />
    <hkern u1="&#x164;" u2="&#x135;" k="-15" />
    <hkern u1="&#x164;" u2="&#x12b;" k="-15" />
    <hkern u1="&#x164;" u2="&#x129;" k="-10" />
    <hkern u1="&#x164;" u2="&#x103;" k="50" />
    <hkern u1="&#x164;" u2="&#xef;" k="-20" />
    <hkern u1="&#x164;" u2="&#xee;" k="-5" />
    <hkern u1="&#x164;" u2="&#xed;" k="20" />
    <hkern u1="&#x164;" u2="&#xec;" k="-10" />
    <hkern u1="&#x164;" u2="&#xe4;" k="35" />
    <hkern u1="&#x164;" u2="&#xe3;" k="50" />
    <hkern u1="&#x164;" u2="&#xdf;" k="10" />
    <hkern u1="&#x165;" g2="f_f.short" k="-25" />
    <hkern u1="&#x165;" g2="f.short" k="-25" />
    <hkern u1="&#x165;" g2="kcommaaccent.alt" k="-50" />
    <hkern u1="&#x165;" g2="k.alt" k="-50" />
    <hkern u1="&#x165;" g2="idotaccent" k="-55" />
    <hkern u1="&#x165;" u2="&#xfb02;" k="-25" />
    <hkern u1="&#x165;" u2="&#xfb01;" k="-25" />
    <hkern u1="&#x165;" g2="f_f" k="-25" />
    <hkern u1="&#x165;" u2="&#x21b;" k="-25" />
    <hkern u1="&#x165;" u2="&#x17e;" k="-45" />
    <hkern u1="&#x165;" u2="&#x167;" k="-25" />
    <hkern u1="&#x165;" u2="&#x165;" k="-25" />
    <hkern u1="&#x165;" u2="&#x163;" k="-25" />
    <hkern u1="&#x165;" u2="&#x161;" k="-35" />
    <hkern u1="&#x165;" u2="&#x159;" k="-85" />
    <hkern u1="&#x165;" u2="&#x155;" k="-10" />
    <hkern u1="&#x165;" u2="&#x148;" k="-20" />
    <hkern u1="&#x165;" u2="&#x140;" k="-50" />
    <hkern u1="&#x165;" u2="&#x13e;" k="-50" />
    <hkern u1="&#x165;" u2="&#x13c;" k="-50" />
    <hkern u1="&#x165;" u2="&#x13a;" k="-50" />
    <hkern u1="&#x165;" u2="&#x137;" k="-50" />
    <hkern u1="&#x165;" u2="&#x135;" k="-55" />
    <hkern u1="&#x165;" u2="&#x133;" k="-55" />
    <hkern u1="&#x165;" u2="&#x12f;" k="-55" />
    <hkern u1="&#x165;" u2="&#x12d;" k="-55" />
    <hkern u1="&#x165;" u2="&#x12b;" k="-55" />
    <hkern u1="&#x165;" u2="&#x12a;" k="-50" />
    <hkern u1="&#x165;" u2="&#x129;" k="-55" />
    <hkern u1="&#x165;" u2="&#x125;" k="-50" />
    <hkern u1="&#x165;" u2="&#xfe;" k="-50" />
    <hkern u1="&#x165;" u2="&#xef;" k="-55" />
    <hkern u1="&#x165;" u2="&#xee;" k="-55" />
    <hkern u1="&#x165;" u2="&#xed;" k="-40" />
    <hkern u1="&#x165;" u2="&#xec;" k="-55" />
    <hkern u1="&#x165;" u2="&#xe4;" k="-30" />
    <hkern u1="&#x165;" u2="&#xdf;" k="-50" />
    <hkern u1="&#x165;" u2="t" k="-25" />
    <hkern u1="&#x165;" u2="l" k="-50" />
    <hkern u1="&#x165;" u2="k" k="-50" />
    <hkern u1="&#x165;" u2="j" k="-55" />
    <hkern u1="&#x165;" u2="i" k="-55" />
    <hkern u1="&#x165;" u2="h" k="-50" />
    <hkern u1="&#x165;" u2="f" k="-25" />
    <hkern u1="&#x165;" u2="b" k="-50" />
    <hkern u1="&#x166;" u2="&#x161;" k="45" />
    <hkern u1="&#x166;" u2="&#x15d;" k="45" />
    <hkern u1="&#x166;" u2="&#x159;" k="-5" />
    <hkern u1="&#x166;" u2="&#x135;" k="-15" />
    <hkern u1="&#x166;" u2="&#x12b;" k="-15" />
    <hkern u1="&#x166;" u2="&#x129;" k="-10" />
    <hkern u1="&#x166;" u2="&#x103;" k="50" />
    <hkern u1="&#x166;" u2="&#xef;" k="-20" />
    <hkern u1="&#x166;" u2="&#xee;" k="-5" />
    <hkern u1="&#x166;" u2="&#xed;" k="20" />
    <hkern u1="&#x166;" u2="&#xec;" k="-10" />
    <hkern u1="&#x166;" u2="&#xe4;" k="35" />
    <hkern u1="&#x166;" u2="&#xe3;" k="50" />
    <hkern u1="&#x166;" u2="&#xdf;" k="10" />
    <hkern u1="&#x173;" u2="&#x135;" k="-25" />
    <hkern u1="&#x173;" u2="j" k="-25" />
    <hkern u1="&#x174;" u2="&#x161;" k="30" />
    <hkern u1="&#x174;" u2="&#x15d;" k="30" />
    <hkern u1="&#x174;" u2="&#x135;" k="-15" />
    <hkern u1="&#x174;" u2="&#x12d;" k="5" />
    <hkern u1="&#x174;" u2="&#x12b;" k="-10" />
    <hkern u1="&#x174;" u2="&#xef;" k="-15" />
    <hkern u1="&#x174;" u2="&#xee;" k="-10" />
    <hkern u1="&#x174;" u2="&#xed;" k="15" />
    <hkern u1="&#x174;" u2="&#xe4;" k="35" />
    <hkern u1="&#x174;" u2="&#xdf;" k="15" />
    <hkern u1="&#x176;" g2="adieresis.alt" k="80" />
    <hkern u1="&#x176;" u2="&#x161;" k="50" />
    <hkern u1="&#x176;" u2="&#x15d;" k="60" />
    <hkern u1="&#x176;" u2="&#x159;" k="5" />
    <hkern u1="&#x176;" u2="&#x135;" k="-25" />
    <hkern u1="&#x176;" u2="&#x12d;" k="-10" />
    <hkern u1="&#x176;" u2="&#x12b;" k="-5" />
    <hkern u1="&#x176;" u2="&#x129;" k="-15" />
    <hkern u1="&#x176;" u2="&#x10d;" k="80" />
    <hkern u1="&#x176;" u2="&#x103;" k="55" />
    <hkern u1="&#x176;" u2="&#x101;" k="70" />
    <hkern u1="&#x176;" u2="&#xfc;" k="40" />
    <hkern u1="&#x176;" u2="&#xf6;" k="80" />
    <hkern u1="&#x176;" u2="&#xf0;" k="80" />
    <hkern u1="&#x176;" u2="&#xef;" k="-10" />
    <hkern u1="&#x176;" u2="&#xee;" k="-25" />
    <hkern u1="&#x176;" u2="&#xed;" k="35" />
    <hkern u1="&#x176;" u2="&#xec;" k="-10" />
    <hkern u1="&#x176;" u2="&#xe5;" k="75" />
    <hkern u1="&#x176;" u2="&#xe4;" k="45" />
    <hkern u1="&#x176;" u2="&#xe3;" k="60" />
    <hkern u1="&#x176;" u2="&#xe0;" k="60" />
    <hkern u1="&#x176;" u2="&#xdf;" k="20" />
    <hkern u1="&#x178;" g2="adieresis.alt" k="80" />
    <hkern u1="&#x178;" u2="&#x161;" k="50" />
    <hkern u1="&#x178;" u2="&#x15d;" k="60" />
    <hkern u1="&#x178;" u2="&#x159;" k="5" />
    <hkern u1="&#x178;" u2="&#x135;" k="-25" />
    <hkern u1="&#x178;" u2="&#x12d;" k="-10" />
    <hkern u1="&#x178;" u2="&#x12b;" k="-5" />
    <hkern u1="&#x178;" u2="&#x129;" k="-15" />
    <hkern u1="&#x178;" u2="&#x10d;" k="80" />
    <hkern u1="&#x178;" u2="&#x103;" k="55" />
    <hkern u1="&#x178;" u2="&#x101;" k="70" />
    <hkern u1="&#x178;" u2="&#xfc;" k="40" />
    <hkern u1="&#x178;" u2="&#xf6;" k="80" />
    <hkern u1="&#x178;" u2="&#xf0;" k="80" />
    <hkern u1="&#x178;" u2="&#xef;" k="-10" />
    <hkern u1="&#x178;" u2="&#xee;" k="-25" />
    <hkern u1="&#x178;" u2="&#xed;" k="35" />
    <hkern u1="&#x178;" u2="&#xec;" k="-10" />
    <hkern u1="&#x178;" u2="&#xe5;" k="75" />
    <hkern u1="&#x178;" u2="&#xe4;" k="45" />
    <hkern u1="&#x178;" u2="&#xe3;" k="60" />
    <hkern u1="&#x178;" u2="&#xe0;" k="60" />
    <hkern u1="&#x178;" u2="&#xdf;" k="20" />
    <hkern u1="&#x179;" u2="&#x135;" k="-5" />
    <hkern u1="&#x179;" u2="&#x12d;" k="-30" />
    <hkern u1="&#x179;" u2="&#x12b;" k="-5" />
    <hkern u1="&#x179;" u2="&#x129;" k="-20" />
    <hkern u1="&#x179;" u2="&#xef;" k="-15" />
    <hkern u1="&#x179;" u2="&#xee;" k="-5" />
    <hkern u1="&#x17b;" u2="&#x135;" k="-5" />
    <hkern u1="&#x17b;" u2="&#x12d;" k="-30" />
    <hkern u1="&#x17b;" u2="&#x12b;" k="-5" />
    <hkern u1="&#x17b;" u2="&#x129;" k="-20" />
    <hkern u1="&#x17b;" u2="&#xef;" k="-15" />
    <hkern u1="&#x17b;" u2="&#xee;" k="-5" />
    <hkern u1="&#x17d;" u2="&#x135;" k="-5" />
    <hkern u1="&#x17d;" u2="&#x12d;" k="-30" />
    <hkern u1="&#x17d;" u2="&#x12b;" k="-5" />
    <hkern u1="&#x17d;" u2="&#x129;" k="-20" />
    <hkern u1="&#x17d;" u2="&#xef;" k="-15" />
    <hkern u1="&#x17d;" u2="&#xee;" k="-5" />
    <hkern u1="&#x192;" u2="&#x39;" k="5" />
    <hkern u1="&#x192;" u2="&#x38;" k="10" />
    <hkern u1="&#x192;" u2="&#x37;" k="-15" />
    <hkern u1="&#x192;" u2="&#x34;" k="20" />
    <hkern u1="&#x192;" u2="&#x32;" k="5" />
    <hkern u1="&#x192;" u2="&#x31;" k="-15" />
    <hkern u1="&#x1fa;" u2="&#xf8;" k="15" />
    <hkern u1="&#x21a;" u2="&#x161;" k="45" />
    <hkern u1="&#x21a;" u2="&#x15d;" k="45" />
    <hkern u1="&#x21a;" u2="&#x159;" k="-5" />
    <hkern u1="&#x21a;" u2="&#x135;" k="-15" />
    <hkern u1="&#x21a;" u2="&#x12b;" k="-15" />
    <hkern u1="&#x21a;" u2="&#x129;" k="-10" />
    <hkern u1="&#x21a;" u2="&#x103;" k="50" />
    <hkern u1="&#x21a;" u2="&#xef;" k="-20" />
    <hkern u1="&#x21a;" u2="&#xee;" k="-5" />
    <hkern u1="&#x21a;" u2="&#xed;" k="20" />
    <hkern u1="&#x21a;" u2="&#xec;" k="-10" />
    <hkern u1="&#x21a;" u2="&#xe4;" k="35" />
    <hkern u1="&#x21a;" u2="&#xe3;" k="50" />
    <hkern u1="&#x21a;" u2="&#xdf;" k="10" />
    <hkern u1="&#x1e80;" u2="&#x161;" k="30" />
    <hkern u1="&#x1e80;" u2="&#x15d;" k="30" />
    <hkern u1="&#x1e80;" u2="&#x135;" k="-15" />
    <hkern u1="&#x1e80;" u2="&#x12d;" k="5" />
    <hkern u1="&#x1e80;" u2="&#x12b;" k="-10" />
    <hkern u1="&#x1e80;" u2="&#xef;" k="-15" />
    <hkern u1="&#x1e80;" u2="&#xee;" k="-10" />
    <hkern u1="&#x1e80;" u2="&#xed;" k="15" />
    <hkern u1="&#x1e80;" u2="&#xe4;" k="35" />
    <hkern u1="&#x1e80;" u2="&#xdf;" k="15" />
    <hkern u1="&#x1e82;" u2="&#x161;" k="30" />
    <hkern u1="&#x1e82;" u2="&#x15d;" k="30" />
    <hkern u1="&#x1e82;" u2="&#x135;" k="-15" />
    <hkern u1="&#x1e82;" u2="&#x12d;" k="5" />
    <hkern u1="&#x1e82;" u2="&#x12b;" k="-10" />
    <hkern u1="&#x1e82;" u2="&#xef;" k="-15" />
    <hkern u1="&#x1e82;" u2="&#xee;" k="-10" />
    <hkern u1="&#x1e82;" u2="&#xed;" k="15" />
    <hkern u1="&#x1e82;" u2="&#xe4;" k="35" />
    <hkern u1="&#x1e82;" u2="&#xdf;" k="15" />
    <hkern u1="&#x1e84;" u2="&#x161;" k="30" />
    <hkern u1="&#x1e84;" u2="&#x15d;" k="30" />
    <hkern u1="&#x1e84;" u2="&#x135;" k="-15" />
    <hkern u1="&#x1e84;" u2="&#x12d;" k="5" />
    <hkern u1="&#x1e84;" u2="&#x12b;" k="-10" />
    <hkern u1="&#x1e84;" u2="&#xef;" k="-15" />
    <hkern u1="&#x1e84;" u2="&#xee;" k="-10" />
    <hkern u1="&#x1e84;" u2="&#xed;" k="15" />
    <hkern u1="&#x1e84;" u2="&#xe4;" k="35" />
    <hkern u1="&#x1e84;" u2="&#xdf;" k="15" />
    <hkern u1="&#x1ef2;" g2="adieresis.alt" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x161;" k="50" />
    <hkern u1="&#x1ef2;" u2="&#x15d;" k="60" />
    <hkern u1="&#x1ef2;" u2="&#x159;" k="5" />
    <hkern u1="&#x1ef2;" u2="&#x135;" k="-25" />
    <hkern u1="&#x1ef2;" u2="&#x12d;" k="-10" />
    <hkern u1="&#x1ef2;" u2="&#x12b;" k="-5" />
    <hkern u1="&#x1ef2;" u2="&#x129;" k="-15" />
    <hkern u1="&#x1ef2;" u2="&#x10d;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x103;" k="55" />
    <hkern u1="&#x1ef2;" u2="&#x101;" k="70" />
    <hkern u1="&#x1ef2;" u2="&#xfc;" k="40" />
    <hkern u1="&#x1ef2;" u2="&#xf6;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#xf0;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#xef;" k="-10" />
    <hkern u1="&#x1ef2;" u2="&#xee;" k="-25" />
    <hkern u1="&#x1ef2;" u2="&#xed;" k="35" />
    <hkern u1="&#x1ef2;" u2="&#xec;" k="-10" />
    <hkern u1="&#x1ef2;" u2="&#xe5;" k="75" />
    <hkern u1="&#x1ef2;" u2="&#xe4;" k="45" />
    <hkern u1="&#x1ef2;" u2="&#xe3;" k="60" />
    <hkern u1="&#x1ef2;" u2="&#xe0;" k="60" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="20" />
    <hkern u1="&#x2022;" u2="&#x142;" k="-10" />
    <hkern u1="&#x2022;" u2="&#x141;" k="-5" />
    <hkern u1="&#x2022;" u2="x" k="55" />
    <hkern u1="&#x2022;" u2="v" k="25" />
    <hkern u1="&#x2022;" u2="X" k="60" />
    <hkern u1="&#x2022;" u2="V" k="70" />
    <hkern u1="&#x2022;" u2="&#x39;" k="15" />
    <hkern u1="&#x2022;" u2="&#x37;" k="40" />
    <hkern u1="&#x2022;" u2="&#x35;" k="10" />
    <hkern u1="&#x2022;" u2="&#x34;" k="-15" />
    <hkern u1="&#x2022;" u2="&#x33;" k="15" />
    <hkern u1="&#x2022;" u2="&#x32;" k="25" />
    <hkern u1="&#x2022;" u2="&#x31;" k="20" />
    <hkern u1="&#x2044;" g2="nine.den" k="270" />
    <hkern u1="&#x2044;" g2="eight.den" k="270" />
    <hkern u1="&#x2044;" g2="seven.den" k="230" />
    <hkern u1="&#x2044;" g2="five.den" k="245" />
    <hkern u1="&#x2044;" g2="four.den" k="320" />
    <hkern u1="&#x2044;" g2="three.den" k="255" />
    <hkern u1="&#x2044;" g2="two.den" k="265" />
    <hkern u1="&#x2044;" g2="one.den" k="235" />
    <hkern u1="&#x20ac;" u2="&#x39;" k="25" />
    <hkern u1="&#x20ac;" u2="&#x38;" k="15" />
    <hkern u1="&#x20ac;" u2="&#x37;" k="-10" />
    <hkern u1="&#x20ac;" u2="&#x35;" k="15" />
    <hkern u1="&#x20ac;" u2="&#x34;" k="35" />
    <hkern u1="&#x20ac;" u2="&#x33;" k="5" />
    <hkern u1="&#x20ac;" u2="&#x32;" k="-5" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="-10" />
    <hkern g1="f_f" u2="&#x159;" k="-10" />
    <hkern g1="f_f" u2="&#x135;" k="-10" />
    <hkern g1="f_f" u2="&#x12d;" k="-5" />
    <hkern g1="f_f" u2="&#x12b;" k="-15" />
    <hkern g1="f_f" u2="&#x129;" k="-10" />
    <hkern g1="f_f" u2="&#xef;" k="-15" />
    <hkern g1="f_f" u2="&#xee;" k="-10" />
    <hkern g1="f_f" u2="&#xec;" k="-5" />
    <hkern g1="Cacute.pl" u2="&#xf8;" k="25" />
    <hkern g1="Nacute.pl" u2="&#xee;" k="-10" />
    <hkern g1="Zacute.pl" u2="&#x135;" k="-5" />
    <hkern g1="Zacute.pl" u2="&#x12d;" k="-30" />
    <hkern g1="Zacute.pl" u2="&#x12b;" k="-5" />
    <hkern g1="Zacute.pl" u2="&#x129;" k="-20" />
    <hkern g1="Zacute.pl" u2="&#xef;" k="-15" />
    <hkern g1="Zacute.pl" u2="&#xee;" k="-5" />
    <hkern g1="four.num" g2="nine.num" k="5" />
    <hkern g1="four.num" g2="eight.num" k="-5" />
    <hkern g1="four.num" g2="seven.num" k="5" />
    <hkern g1="four.num" g2="four.num" k="-5" />
    <hkern g1="four.num" u2="&#x2044;" k="240" />
    <hkern g1="five.num" g2="nine.num" k="5" />
    <hkern g1="five.num" u2="&#x2044;" k="255" />
    <hkern g1="six.num" g2="nine.num" k="5" />
    <hkern g1="six.num" g2="seven.num" k="5" />
    <hkern g1="six.num" u2="&#x2044;" k="250" />
    <hkern g1="seven.num" g2="seven.num" k="-5" />
    <hkern g1="seven.num" g2="four.num" k="15" />
    <hkern g1="seven.num" u2="&#x2044;" k="285" />
    <hkern g1="eight.num" u2="&#x2044;" k="250" />
    <hkern g1="two.den" g2="four.den" k="5" />
    <hkern g1="three.den" g2="nine.den" k="5" />
    <hkern g1="three.den" g2="seven.den" k="5" />
    <hkern g1="four.den" g2="seven.den" k="5" />
    <hkern g1="six.den" g2="nine.den" k="5" />
    <hkern g1="six.den" g2="three.den" k="-5" />
    <hkern g1="seven.den" g2="nine.den" k="-5" />
    <hkern g1="seven.den" g2="seven.den" k="-5" />
    <hkern g1="seven.den" g2="four.den" k="20" />
    <hkern g1="seven.den" g2="one.den" k="-10" />
    <hkern g1="f.short" u2="&#x159;" k="-10" />
    <hkern g1="f.short" u2="&#x135;" k="-10" />
    <hkern g1="f.short" u2="&#x12d;" k="-5" />
    <hkern g1="f.short" u2="&#x12b;" k="-15" />
    <hkern g1="f.short" u2="&#x129;" k="-10" />
    <hkern g1="f.short" u2="&#xef;" k="-15" />
    <hkern g1="f.short" u2="&#xee;" k="-10" />
    <hkern g1="f.short" u2="&#xec;" k="-5" />
    <hkern g1="f_f.short" u2="&#x159;" k="-10" />
    <hkern g1="f_f.short" u2="&#x135;" k="-10" />
    <hkern g1="f_f.short" u2="&#x12d;" k="-5" />
    <hkern g1="f_f.short" u2="&#x12b;" k="-15" />
    <hkern g1="f_f.short" u2="&#x129;" k="-10" />
    <hkern g1="f_f.short" u2="&#xef;" k="-15" />
    <hkern g1="f_f.short" u2="&#xee;" k="-10" />
    <hkern g1="f_f.short" u2="&#xec;" k="-5" />
    <hkern g1="questiondown.uc" u2="&#x142;" k="15" />
    <hkern g1="questiondown.uc" u2="&#x141;" k="-5" />
    <hkern g1="questiondown.uc" u2="&#x127;" k="5" />
    <hkern g1="questiondown.uc" u2="&#x126;" k="15" />
    <hkern g1="questiondown.uc" u2="x" k="5" />
    <hkern g1="questiondown.uc" u2="v" k="75" />
    <hkern g1="questiondown.uc" u2="X" k="20" />
    <hkern g1="questiondown.uc" u2="V" k="75" />
    <hkern g1="questiondown.uc" u2="&#x39;" k="40" />
    <hkern g1="questiondown.uc" u2="&#x37;" k="35" />
    <hkern g1="questiondown.uc" u2="&#x35;" k="15" />
    <hkern g1="questiondown.uc" u2="&#x34;" k="10" />
    <hkern g1="questiondown.uc" u2="&#x31;" k="30" />
    <hkern g1="exclamdown.uc" u2="&#x126;" k="-5" />
    <hkern g1="at.uc" u2="&#x142;" k="-10" />
    <hkern g1="at.uc" u2="&#x126;" k="10" />
    <hkern g1="at.uc" u2="x" k="35" />
    <hkern g1="at.uc" u2="v" k="10" />
    <hkern g1="at.uc" u2="X" k="55" />
    <hkern g1="at.uc" u2="V" k="30" />
    <hkern g1="at.uc" u2="&#x37;" k="45" />
    <hkern g1="at.uc" u2="&#x35;" k="10" />
    <hkern g1="at.uc" u2="&#x34;" k="-5" />
    <hkern g1="at.uc" u2="&#x33;" k="20" />
    <hkern g1="at.uc" u2="&#x32;" k="15" />
    <hkern g1="at.uc" u2="&#x31;" k="15" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="B"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="B"
	g2="J,Jcircumflex"
	k="5" />
    <hkern g1="B"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="B"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="5" />
    <hkern g1="B"
	g2="backslash,backslash.uc"
	k="25" />
    <hkern g1="B"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="-5" />
    <hkern g1="B"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="B"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="-5" />
    <hkern g1="B"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="B"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="B"
	g2="plus,divide,minus"
	k="5" />
    <hkern g1="B"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="B"
	g2="quotedbl,quotesingle"
	k="5" />
    <hkern g1="B"
	g2="slash,slash.uc"
	k="50" />
    <hkern g1="B"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="B"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="5" />
    <hkern g1="Euro"
	g2="zero,six"
	k="20" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="F"
	g2="AE,AEacute"
	k="100" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="F"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-5" />
    <hkern g1="F"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="F"
	g2="backslash,backslash.uc"
	k="-5" />
    <hkern g1="F"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="5" />
    <hkern g1="F"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="5" />
    <hkern g1="F"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-5" />
    <hkern g1="F"
	g2="comma,period,ellipsis"
	k="65" />
    <hkern g1="F"
	g2="quoteright,quotedblright"
	k="-5" />
    <hkern g1="F"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="F"
	g2="slash,slash.uc"
	k="85" />
    <hkern g1="F"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="F"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="10" />
    <hkern g1="F"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="F"
	g2="b,h,k,l,germandbls,thorn,hcircumflex,Imacron,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,k.alt,kcommaaccent.alt"
	k="5" />
    <hkern g1="F"
	g2="quoteleft,quotedblleft"
	k="-5" />
    <hkern g1="F"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="F"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="Hbar"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="5" />
    <hkern g1="Hbar"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-10" />
    <hkern g1="Hbar"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-5" />
    <hkern g1="Hbar"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="10" />
    <hkern g1="Hbar"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="Hbar"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="Hbar"
	g2="slash,slash.uc"
	k="40" />
    <hkern g1="Hbar"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-5" />
    <hkern g1="Hbar"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="Hbar"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="10" />
    <hkern g1="Ldot"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-5" />
    <hkern g1="Ldot"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="Ldot"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Ldot"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="95" />
    <hkern g1="Ldot"
	g2="backslash,backslash.uc"
	k="40" />
    <hkern g1="Ldot"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="Ldot"
	g2="slash,slash.uc"
	k="60" />
    <hkern g1="Ldot"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="Ldot"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-10" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="70" />
    <hkern g1="P"
	g2="AE,AEacute"
	k="150" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="40" />
    <hkern g1="P"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="5" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="P"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="10" />
    <hkern g1="P"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="25" />
    <hkern g1="P"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="P"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="30" />
    <hkern g1="P"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="10" />
    <hkern g1="P"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="P"
	g2="plus,divide,minus"
	k="5" />
    <hkern g1="P"
	g2="quoteright,quotedblright"
	k="-15" />
    <hkern g1="P"
	g2="quotedbl,quotesingle"
	k="-15" />
    <hkern g1="P"
	g2="slash,slash.uc"
	k="130" />
    <hkern g1="P"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-15" />
    <hkern g1="P"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="P"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-10" />
    <hkern g1="P"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="P"
	g2="quoteleft,quotedblleft"
	k="-15" />
    <hkern g1="P"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="15" />
    <hkern g1="P"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="P"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="15" />
    <hkern g1="P"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="5" />
    <hkern g1="P"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-5" />
    <hkern g1="P"
	g2="colon,semicolon"
	k="-15" />
    <hkern g1="P"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-10" />
    <hkern g1="P"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,ij,idotaccent"
	k="-5" />
    <hkern g1="P"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="Q"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="Q"
	g2="AE,AEacute"
	k="35" />
    <hkern g1="Q"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="Q"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="45" />
    <hkern g1="Q"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="Q"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="60" />
    <hkern g1="Q"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="25" />
    <hkern g1="Q"
	g2="backslash,backslash.uc"
	k="45" />
    <hkern g1="Q"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="Q"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="Q"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="Q"
	g2="plus,divide,minus"
	k="-5" />
    <hkern g1="Q"
	g2="quoteright,quotedblright"
	k="15" />
    <hkern g1="Q"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="Q"
	g2="slash,slash.uc"
	k="60" />
    <hkern g1="Q"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="Q"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="Q"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="Q"
	g2="quoteleft,quotedblleft"
	k="15" />
    <hkern g1="Q"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-10" />
    <hkern g1="Q"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="Q"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="Thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="Thorn"
	g2="AE,AEacute"
	k="65" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="Thorn"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="Thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="60" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="50" />
    <hkern g1="Thorn"
	g2="backslash,backslash.uc"
	k="20" />
    <hkern g1="Thorn"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="Thorn"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="Thorn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="Thorn"
	g2="plus,divide,minus"
	k="-5" />
    <hkern g1="Thorn"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="Thorn"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="Thorn"
	g2="slash,slash.uc"
	k="85" />
    <hkern g1="Thorn"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="Thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="Thorn"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="10" />
    <hkern g1="Thorn"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="Thorn"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="Thorn"
	g2="quoteleft,quotedblleft"
	k="15" />
    <hkern g1="Thorn"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-10" />
    <hkern g1="Thorn"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="85" />
    <hkern g1="V"
	g2="AE,AEacute"
	k="145" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="V"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-15" />
    <hkern g1="V"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-20" />
    <hkern g1="V"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-25" />
    <hkern g1="V"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="-5" />
    <hkern g1="V"
	g2="backslash,backslash.uc"
	k="-20" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="55" />
    <hkern g1="V"
	g2="hyphen,endash,emdash"
	k="40" />
    <hkern g1="V"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="70" />
    <hkern g1="V"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-15" />
    <hkern g1="V"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="V"
	g2="plus,divide,minus"
	k="45" />
    <hkern g1="V"
	g2="quoteright,quotedblright"
	k="-15" />
    <hkern g1="V"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="V"
	g2="slash,slash.uc"
	k="110" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="30" />
    <hkern g1="V"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="20" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="65" />
    <hkern g1="V"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="65" />
    <hkern g1="V"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="V"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="50" />
    <hkern g1="V"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="45" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="35" />
    <hkern g1="V"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="20" />
    <hkern g1="V"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="55" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="35" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="35" />
    <hkern g1="X"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-25" />
    <hkern g1="X"
	g2="J,Jcircumflex"
	k="-5" />
    <hkern g1="X"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-5" />
    <hkern g1="X"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="X"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-15" />
    <hkern g1="X"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="-5" />
    <hkern g1="X"
	g2="backslash,backslash.uc"
	k="-5" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="40" />
    <hkern g1="X"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="X"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="45" />
    <hkern g1="X"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-5" />
    <hkern g1="X"
	g2="comma,period,ellipsis"
	k="-5" />
    <hkern g1="X"
	g2="plus,divide,minus"
	k="55" />
    <hkern g1="X"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="X"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="X"
	g2="slash,slash.uc"
	k="25" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="50" />
    <hkern g1="X"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-5" />
    <hkern g1="X"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="25" />
    <hkern g1="X"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="X"
	g2="quoteleft,quotedblleft"
	k="30" />
    <hkern g1="X"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="25" />
    <hkern g1="X"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="35" />
    <hkern g1="X"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="55" />
    <hkern g1="X"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="45" />
    <hkern g1="X"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="X"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="X"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="15" />
    <hkern g1="X"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,ij,idotaccent"
	k="-5" />
    <hkern g1="X"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="X"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="30" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="25" />
    <hkern g1="ampersand"
	g2="AE,AEacute"
	k="-5" />
    <hkern g1="ampersand"
	g2="J,Jcircumflex"
	k="-5" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="75" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="70" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="95" />
    <hkern g1="ampersand"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="25" />
    <hkern g1="ampersand"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="55" />
    <hkern g1="ampersand"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="55" />
    <hkern g1="ampersand"
	g2="zero,six"
	k="30" />
    <hkern g1="ampersand"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="20" />
    <hkern g1="ampersand"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="ampersand"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="20" />
    <hkern g1="ampersand"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="ampersand"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="25" />
    <hkern g1="ampersand"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="ampersand"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="15" />
    <hkern g1="ampersand"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="ampersand"
	g2="Eth,Dcroat"
	k="5" />
    <hkern g1="ampersand"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="20" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="105" />
    <hkern g1="asterisk"
	g2="AE,AEacute"
	k="155" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex"
	k="50" />
    <hkern g1="asterisk"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="asterisk"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="10" />
    <hkern g1="asterisk"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="45" />
    <hkern g1="asterisk"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="asterisk"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="asterisk"
	g2="zero,six"
	k="10" />
    <hkern g1="asterisk"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="asterisk"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="asterisk"
	g2="b,h,k,l,germandbls,thorn,hcircumflex,Imacron,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,k.alt,kcommaaccent.alt"
	k="10" />
    <hkern g1="asterisk"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="20" />
    <hkern g1="asterisk"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="asterisk"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="10" />
    <hkern g1="asterisk"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="asterisk"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="asterisk"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="15" />
    <hkern g1="asterisk"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="5" />
    <hkern g1="at"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="at"
	g2="AE,AEacute"
	k="75" />
    <hkern g1="at"
	g2="J,Jcircumflex"
	k="25" />
    <hkern g1="at"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="45" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="at"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="65" />
    <hkern g1="at"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="40" />
    <hkern g1="at"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="at"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="at"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="10" />
    <hkern g1="at"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="at"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="at"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="at"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-5" />
    <hkern g1="at"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="at.uc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="55" />
    <hkern g1="at.uc"
	g2="AE,AEacute"
	k="85" />
    <hkern g1="at.uc"
	g2="J,Jcircumflex"
	k="40" />
    <hkern g1="at.uc"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="at.uc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="at.uc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="at.uc"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="30" />
    <hkern g1="at.uc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="at.uc"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="at.uc"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="10" />
    <hkern g1="at.uc"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="at.uc"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="at.uc"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="at.uc"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="at.uc"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="-5" />
    <hkern g1="at.uc"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="bullet"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="bullet"
	g2="AE,AEacute"
	k="70" />
    <hkern g1="bullet"
	g2="J,Jcircumflex"
	k="40" />
    <hkern g1="bullet"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="65" />
    <hkern g1="bullet"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="bullet"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="80" />
    <hkern g1="bullet"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="40" />
    <hkern g1="bullet"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="-10" />
    <hkern g1="bullet"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="bullet"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="bullet"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="25" />
    <hkern g1="bullet"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="bullet"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="bullet"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="bullet"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="-10" />
    <hkern g1="bullet"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="15" />
    <hkern g1="bullet"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="dcroat"
	g2="backslash,backslash.uc"
	k="-15" />
    <hkern g1="dcroat"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="10" />
    <hkern g1="dcroat"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-10" />
    <hkern g1="dcroat"
	g2="comma,period,ellipsis"
	k="5" />
    <hkern g1="dcroat"
	g2="quoteright,quotedblright"
	k="-25" />
    <hkern g1="dcroat"
	g2="quotedbl,quotesingle"
	k="-15" />
    <hkern g1="dcroat"
	g2="slash,slash.uc"
	k="35" />
    <hkern g1="dcroat"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="dcroat"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="dcroat"
	g2="quoteleft,quotedblleft"
	k="-5" />
    <hkern g1="dcroat"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="dcroat"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="dcroat"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="5" />
    <hkern g1="degree"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="60" />
    <hkern g1="degree"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-5" />
    <hkern g1="degree"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-5" />
    <hkern g1="degree"
	g2="zero,six"
	k="5" />
    <hkern g1="degree"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="35" />
    <hkern g1="degree"
	g2="b,h,k,l,germandbls,thorn,hcircumflex,Imacron,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,k.alt,kcommaaccent.alt"
	k="5" />
    <hkern g1="degree"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="30" />
    <hkern g1="degree"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-10" />
    <hkern g1="degree"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-5" />
    <hkern g1="degree"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="5" />
    <hkern g1="degree"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="5" />
    <hkern g1="eight"
	g2="backslash,backslash.uc"
	k="15" />
    <hkern g1="eight"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="10" />
    <hkern g1="eight"
	g2="comma,period,ellipsis"
	k="5" />
    <hkern g1="eight"
	g2="plus,divide,minus"
	k="10" />
    <hkern g1="eight"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="eight"
	g2="slash,slash.uc"
	k="55" />
    <hkern g1="exclamdown"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="5" />
    <hkern g1="exclamdown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="25" />
    <hkern g1="exclamdown"
	g2="j,jcircumflex"
	k="-30" />
    <hkern g1="exclamdown.uc"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="exclamdown.uc"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="five"
	g2="backslash,backslash.uc"
	k="25" />
    <hkern g1="five"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="-5" />
    <hkern g1="five"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="five"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="five"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="five"
	g2="quotedbl,quotesingle"
	k="15" />
    <hkern g1="five"
	g2="slash,slash.uc"
	k="55" />
    <hkern g1="five"
	g2="quoteleft,quotedblleft"
	k="30" />
    <hkern g1="five"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-5" />
    <hkern g1="five"
	g2="percent,perthousand"
	k="25" />
    <hkern g1="florin"
	g2="zero,six"
	k="10" />
    <hkern g1="four"
	g2="backslash,backslash.uc"
	k="35" />
    <hkern g1="four"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="-5" />
    <hkern g1="four"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="four"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="four"
	g2="comma,period,ellipsis"
	k="20" />
    <hkern g1="four"
	g2="quoteright,quotedblright"
	k="30" />
    <hkern g1="four"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="four"
	g2="slash,slash.uc"
	k="65" />
    <hkern g1="four"
	g2="quoteleft,quotedblleft"
	k="35" />
    <hkern g1="four"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-5" />
    <hkern g1="four"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="5" />
    <hkern g1="four"
	g2="percent,perthousand"
	k="35" />
    <hkern g1="four.num"
	g2="threesuperior,three.num"
	k="-5" />
    <hkern g1="fraction"
	g2="zero.den,six.den"
	k="275" />
    <hkern g1="germandbls"
	g2="backslash,backslash.uc"
	k="30" />
    <hkern g1="germandbls"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="-5" />
    <hkern g1="germandbls"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="germandbls"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="germandbls"
	g2="comma,period,ellipsis"
	k="15" />
    <hkern g1="germandbls"
	g2="quoteright,quotedblright"
	k="30" />
    <hkern g1="germandbls"
	g2="quotedbl,quotesingle"
	k="35" />
    <hkern g1="germandbls"
	g2="slash,slash.uc"
	k="50" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="35" />
    <hkern g1="germandbls"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="20" />
    <hkern g1="germandbls"
	g2="quoteleft,quotedblleft"
	k="35" />
    <hkern g1="germandbls"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="germandbls"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="15" />
    <hkern g1="germandbls"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-10" />
    <hkern g1="germandbls"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="germandbls"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="ldot"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="lslash"
	g2="backslash,backslash.uc"
	k="10" />
    <hkern g1="lslash"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="15" />
    <hkern g1="lslash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="10" />
    <hkern g1="lslash"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="lslash"
	g2="comma,period,ellipsis"
	k="15" />
    <hkern g1="lslash"
	g2="quoteright,quotedblright"
	k="-5" />
    <hkern g1="lslash"
	g2="slash,slash.uc"
	k="40" />
    <hkern g1="lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="lslash"
	g2="quoteleft,quotedblleft"
	k="-5" />
    <hkern g1="lslash"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="lslash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-10" />
    <hkern g1="lslash"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="lslash"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-5" />
    <hkern g1="periodcentered"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="periodcentered"
	g2="AE,AEacute"
	k="100" />
    <hkern g1="periodcentered"
	g2="J,Jcircumflex"
	k="40" />
    <hkern g1="periodcentered"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="65" />
    <hkern g1="periodcentered"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="periodcentered"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="80" />
    <hkern g1="periodcentered"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="55" />
    <hkern g1="periodcentered"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="-10" />
    <hkern g1="periodcentered"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="periodcentered"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="periodcentered"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="15" />
    <hkern g1="periodcentered"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="15" />
    <hkern g1="periodcentered"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="periodcentered"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="periodcentered"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="-10" />
    <hkern g1="periodcentered"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="periodcentered"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="questiondown"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-10" />
    <hkern g1="questiondown"
	g2="AE,AEacute"
	k="-5" />
    <hkern g1="questiondown"
	g2="J,Jcircumflex"
	k="-5" />
    <hkern g1="questiondown"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="75" />
    <hkern g1="questiondown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="90" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="questiondown"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="5" />
    <hkern g1="questiondown"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="35" />
    <hkern g1="questiondown"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="75" />
    <hkern g1="questiondown"
	g2="zero,six"
	k="35" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="30" />
    <hkern g1="questiondown"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="questiondown"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="15" />
    <hkern g1="questiondown"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="60" />
    <hkern g1="questiondown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="40" />
    <hkern g1="questiondown"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="20" />
    <hkern g1="questiondown"
	g2="j,jcircumflex"
	k="-10" />
    <hkern g1="questiondown"
	g2="Eth,Dcroat"
	k="15" />
    <hkern g1="questiondown"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="20" />
    <hkern g1="questiondown.uc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="5" />
    <hkern g1="questiondown.uc"
	g2="AE,AEacute"
	k="15" />
    <hkern g1="questiondown.uc"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="65" />
    <hkern g1="questiondown.uc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="65" />
    <hkern g1="questiondown.uc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="85" />
    <hkern g1="questiondown.uc"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="5" />
    <hkern g1="questiondown.uc"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="35" />
    <hkern g1="questiondown.uc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="questiondown.uc"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="75" />
    <hkern g1="questiondown.uc"
	g2="zero,six"
	k="15" />
    <hkern g1="questiondown.uc"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="questiondown.uc"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="questiondown.uc"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="15" />
    <hkern g1="questiondown.uc"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="questiondown.uc"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="20" />
    <hkern g1="questiondown.uc"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="questiondown.uc"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="20" />
    <hkern g1="questiondown.uc"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="questiondown.uc"
	g2="Eth,Dcroat"
	k="15" />
    <hkern g1="questiondown.uc"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="20" />
    <hkern g1="questiondown.uc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,Dcaron,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Nacute.pl"
	k="5" />
    <hkern g1="seven"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="50" />
    <hkern g1="seven"
	g2="hyphen,endash,emdash"
	k="65" />
    <hkern g1="seven"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-5" />
    <hkern g1="seven"
	g2="comma,period,ellipsis"
	k="110" />
    <hkern g1="seven"
	g2="plus,divide,minus"
	k="40" />
    <hkern g1="seven"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="seven"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="seven"
	g2="slash,slash.uc"
	k="115" />
    <hkern g1="seven"
	g2="zero,six"
	k="20" />
    <hkern g1="seven"
	g2="quoteleft,quotedblleft"
	k="5" />
    <hkern g1="seven"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="65" />
    <hkern g1="seven"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="seven"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="35" />
    <hkern g1="seven.num"
	g2="onesuperior,one.num"
	k="-10" />
    <hkern g1="six"
	g2="backslash,backslash.uc"
	k="25" />
    <hkern g1="six"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="six"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="six"
	g2="quoteright,quotedblright"
	k="30" />
    <hkern g1="six"
	g2="quotedbl,quotesingle"
	k="25" />
    <hkern g1="six"
	g2="slash,slash.uc"
	k="60" />
    <hkern g1="six"
	g2="quoteleft,quotedblleft"
	k="35" />
    <hkern g1="six"
	g2="percent,perthousand"
	k="35" />
    <hkern g1="six.num"
	g2="threesuperior,three.num"
	k="-5" />
    <hkern g1="three"
	g2="backslash,backslash.uc"
	k="10" />
    <hkern g1="three"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="-5" />
    <hkern g1="three"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="three"
	g2="comma,period,ellipsis"
	k="5" />
    <hkern g1="three"
	g2="plus,divide,minus"
	k="5" />
    <hkern g1="three"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="three"
	g2="quotedbl,quotesingle"
	k="5" />
    <hkern g1="three"
	g2="slash,slash.uc"
	k="60" />
    <hkern g1="three"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="three"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-5" />
    <hkern g1="three"
	g2="percent,perthousand"
	k="25" />
    <hkern g1="two"
	g2="backslash,backslash.uc"
	k="10" />
    <hkern g1="two"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="20" />
    <hkern g1="two"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="two"
	g2="comma,period,ellipsis"
	k="-10" />
    <hkern g1="two"
	g2="plus,divide,minus"
	k="15" />
    <hkern g1="two"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="two"
	g2="slash,slash.uc"
	k="20" />
    <hkern g1="two"
	g2="quoteleft,quotedblleft"
	k="-5" />
    <hkern g1="two"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="20" />
    <hkern g1="underscore"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="35" />
    <hkern g1="underscore"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="60" />
    <hkern g1="underscore"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="75" />
    <hkern g1="underscore"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="10" />
    <hkern g1="underscore"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="underscore"
	g2="zero,six"
	k="55" />
    <hkern g1="underscore"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="40" />
    <hkern g1="underscore"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="underscore"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="60" />
    <hkern g1="underscore"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="55" />
    <hkern g1="underscore"
	g2="j,jcircumflex"
	k="-75" />
    <hkern g1="underscore"
	g2="Eth,Dcroat"
	k="15" />
    <hkern g1="v"
	g2="backslash,backslash.uc"
	k="30" />
    <hkern g1="v"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="25" />
    <hkern g1="v"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="v"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="30" />
    <hkern g1="v"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="25" />
    <hkern g1="v"
	g2="comma,period,ellipsis"
	k="85" />
    <hkern g1="v"
	g2="plus,divide,minus"
	k="15" />
    <hkern g1="v"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="v"
	g2="quotedbl,quotesingle"
	k="-15" />
    <hkern g1="v"
	g2="slash,slash.uc"
	k="75" />
    <hkern g1="v"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="v"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="v"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="25" />
    <hkern g1="v"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="v"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="20" />
    <hkern g1="v"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-15" />
    <hkern g1="v"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="25" />
    <hkern g1="v"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="v"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-10" />
    <hkern g1="v"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="15" />
    <hkern g1="x"
	g2="backslash,backslash.uc"
	k="25" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="45" />
    <hkern g1="x"
	g2="hyphen,endash,emdash"
	k="50" />
    <hkern g1="x"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="50" />
    <hkern g1="x"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="x"
	g2="comma,period,ellipsis"
	k="-5" />
    <hkern g1="x"
	g2="plus,divide,minus"
	k="45" />
    <hkern g1="x"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="x"
	g2="slash,slash.uc"
	k="25" />
    <hkern g1="x"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="25" />
    <hkern g1="x"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="25" />
    <hkern g1="x"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="50" />
    <hkern g1="x"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="x"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="15" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="10" />
    <hkern g1="yen"
	g2="zero,six"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="AE,AEacute"
	k="-20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J,Jcircumflex"
	k="-15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="65" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="65" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="backslash,backslash.uc"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="comma,period,ellipsis"
	k="-25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="plus,divide,minus"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quoteright,quotedblright"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="slash,slash.uc"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="55" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="b,h,k,l,germandbls,thorn,hcircumflex,Imacron,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,k.alt,kcommaaccent.alt"
	k="-5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quoteleft,quotedblleft"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="colon,semicolon"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,ij,idotaccent"
	k="-5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="j,jcircumflex"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="-5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Hbar"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Lslash"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="V"
	k="85" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="X"
	k="-25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="ampersand"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="asterisk"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="at"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="at.uc"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="exclam"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="lslash"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="periodcentered"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="question"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="registered"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="trademark"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="v"
	k="70" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="backslash,backslash.uc"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="hyphen,endash,emdash"
	k="45" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="comma,period,ellipsis"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="plus,divide,minus"
	k="45" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="slash,slash.uc"
	k="40" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="35" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="35" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="b,h,k,l,germandbls,thorn,hcircumflex,Imacron,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,k.alt,kcommaaccent.alt"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="quoteleft,quotedblleft"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="50" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="45" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,ij,idotaccent"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="Eth,Dcroat"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,Dcaron,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Nacute.pl"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="Lslash"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="V"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="X"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="ampersand"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="asterisk"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="at"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="at.uc"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="bullet"
	k="40" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="lslash"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="periodcentered"
	k="45" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="question"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="registered"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="v"
	k="35" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="hbar"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Cacute.pl"
	g2="x"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="backslash,backslash.uc"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="hyphen,endash,emdash"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="comma,period,ellipsis"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="quoteright,quotedblright"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="slash,slash.uc"
	k="30" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="quoteleft,quotedblleft"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="V"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="X"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="ampersand"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="asterisk"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="question"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="registered"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="v"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="AE,AEacute"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="45" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="60" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="backslash,backslash.uc"
	k="60" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="quoteright,quotedblright"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="slash,slash.uc"
	k="35" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="quoteleft,quotedblleft"
	k="35" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="V"
	k="40" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="X"
	k="5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="asterisk"
	k="40" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="question"
	k="35" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="registered"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="trademark"
	k="45" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="v"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="x"
	k="-5" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-5" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="slash,slash.uc"
	k="45" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-5" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,Dcaron,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Nacute.pl"
	k="-5" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="question"
	k="5" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="trademark"
	k="15" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="v"
	k="5" />
    <hkern g1="one,H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Nacute.pl"
	g2="degree"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="AE,AEacute"
	k="35" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="comma,period,ellipsis"
	k="15" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="slash,slash.uc"
	k="55" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="X"
	k="10" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="trademark"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="v"
	k="5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="x"
	k="10" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="underscore"
	k="35" />
    <hkern g1="K,Kcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-25" />
    <hkern g1="K,Kcommaaccent"
	g2="AE,AEacute"
	k="5" />
    <hkern g1="K,Kcommaaccent"
	g2="J,Jcircumflex"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="backslash,backslash.uc"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="60" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="40" />
    <hkern g1="K,Kcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="50" />
    <hkern g1="K,Kcommaaccent"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-10" />
    <hkern g1="K,Kcommaaccent"
	g2="comma,period,ellipsis"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="plus,divide,minus"
	k="60" />
    <hkern g1="K,Kcommaaccent"
	g2="slash,slash.uc"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="60" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="70" />
    <hkern g1="K,Kcommaaccent"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="b,h,k,l,germandbls,thorn,hcircumflex,Imacron,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,k.alt,kcommaaccent.alt"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="35" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="65" />
    <hkern g1="K,Kcommaaccent"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="65" />
    <hkern g1="K,Kcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,ij,idotaccent"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="Eth,Dcroat"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="-5" />
    <hkern g1="K,Kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="Hbar"
	k="5" />
    <hkern g1="K,Kcommaaccent"
	g2="Lslash"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="V"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="X"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="ampersand"
	k="35" />
    <hkern g1="K,Kcommaaccent"
	g2="asterisk"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="at"
	k="40" />
    <hkern g1="K,Kcommaaccent"
	g2="at.uc"
	k="50" />
    <hkern g1="K,Kcommaaccent"
	g2="bullet"
	k="70" />
    <hkern g1="K,Kcommaaccent"
	g2="lslash"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="periodcentered"
	k="75" />
    <hkern g1="K,Kcommaaccent"
	g2="question"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="registered"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="v"
	k="55" />
    <hkern g1="K,Kcommaaccent"
	g2="x"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="65" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="65" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="backslash,backslash.uc"
	k="60" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="comma,period,ellipsis"
	k="-15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="plus,divide,minus"
	k="50" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteright,quotedblright"
	k="70" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quotedbl,quotesingle"
	k="50" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="slash,slash.uc"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="50" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteleft,quotedblleft"
	k="70" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="colon,semicolon"
	k="-15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Hbar"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Lslash"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="V"
	k="65" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="X"
	k="-10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="ampersand"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="asterisk"
	k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="at"
	k="15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="at.uc"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="bullet"
	k="40" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="lslash"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="periodcentered"
	k="65" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="question"
	k="65" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="registered"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="trademark"
	k="70" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="v"
	k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="x"
	k="-5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="AE,AEacute"
	k="75" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="60" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="45" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="backslash,backslash.uc"
	k="30" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="-5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="comma,period,ellipsis"
	k="45" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="plus,divide,minus"
	k="-5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="quoteright,quotedblright"
	k="15" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="slash,slash.uc"
	k="80" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="quoteleft,quotedblleft"
	k="15" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-10" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="V"
	k="45" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="X"
	k="60" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="ampersand"
	k="5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="asterisk"
	k="15" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="at.uc"
	k="-5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="bullet"
	k="-10" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="periodcentered"
	k="-10" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="question"
	k="20" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="registered"
	k="5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="trademark"
	k="30" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="v"
	k="10" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="x"
	k="25" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="underscore"
	k="60" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="four"
	k="-5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="one"
	k="5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="seven"
	k="15" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="three"
	k="5" />
    <hkern g1="D,O,copyright,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,uni24C5,Oacute.pl"
	g2="two"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J,Jcircumflex"
	k="-10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="35" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="-5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="backslash,backslash.uc"
	k="40" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="35" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="plus,divide,minus"
	k="35" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quoteright,quotedblright"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="slash,slash.uc"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quoteleft,quotedblleft"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="45" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Eth,Dcroat"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,Dcaron,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Nacute.pl"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Hbar"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Lslash"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="45" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="X"
	k="-25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="asterisk"
	k="30" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="at"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="at.uc"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="bullet"
	k="50" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="exclam"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="lslash"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="periodcentered"
	k="45" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="question"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="registered"
	k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="v"
	k="25" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="AE,AEacute"
	k="35" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="25" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="backslash,backslash.uc"
	k="25" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="-5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="comma,period,ellipsis"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="plus,divide,minus"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="quotedbl,quotesingle"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="slash,slash.uc"
	k="60" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="-5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="quoteleft,quotedblleft"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="Lslash"
	k="-5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="V"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="X"
	k="25" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="ampersand"
	k="-5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="asterisk"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="at.uc"
	k="-5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="periodcentered"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="question"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="trademark"
	k="25" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="v"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="x"
	k="25" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	g2="underscore"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="65" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="AE,AEacute"
	k="95" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="J,Jcircumflex"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-15" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-15" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="backslash,backslash.uc"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="hyphen,endash,emdash"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="comma,period,ellipsis"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="plus,divide,minus"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="slash,slash.uc"
	k="100" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="15" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="50" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="Hbar"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="Lslash"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="V"
	k="-15" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="X"
	k="-5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="ampersand"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="at"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="at.uc"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="bullet"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="lslash"
	k="15" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="periodcentered"
	k="65" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="registered"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="v"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="hbar"
	k="-5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="x"
	k="15" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	g2="underscore"
	k="35" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="30" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="55" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,period,ellipsis"
	k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="quoteright,quotedblright"
	k="-5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="slash,slash.uc"
	k="55" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="quoteleft,quotedblleft"
	k="-5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,Dcaron,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Nacute.pl"
	k="-5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="X"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="question"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="trademark"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="x"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="underscore"
	k="65" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="65" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="110" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="backslash,backslash.uc"
	k="-15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="55" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,ellipsis"
	k="95" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="plus,divide,minus"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quoteright,quotedblright"
	k="-15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash,slash.uc"
	k="85" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quoteleft,quotedblleft"
	k="-5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,ij,idotaccent"
	k="-5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Hbar"
	k="-5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Lslash"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V"
	k="-20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="X"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="asterisk"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at.uc"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="lslash"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="periodcentered"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="question"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="registered"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="trademark"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hbar"
	k="-5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="underscore"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="135" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="-5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="backslash,backslash.uc"
	k="-20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,endash,emdash"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,period,ellipsis"
	k="95" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="plus,divide,minus"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash,slash.uc"
	k="105" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="85" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Eth,Dcroat"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Lslash"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="-25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="X"
	k="-15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ampersand"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at.uc"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="lslash"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="periodcentered"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="question"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="trademark"
	k="-10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="x"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="underscore"
	k="75" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="backslash,backslash.uc"
	k="-5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="plus,divide,minus"
	k="45" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="quoteright,quotedblright"
	k="-5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="slash,slash.uc"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="45" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="45" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="Lslash"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="V"
	k="-5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="X"
	k="-5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="ampersand"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="asterisk"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="at"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="at.uc"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="bullet"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="lslash"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="periodcentered"
	k="70" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="question"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="registered"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="trademark"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	g2="v"
	k="30" />
    <hkern g1="q,y.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	g2="backslash,backslash.uc"
	k="30" />
    <hkern g1="q,y.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	g2="j,jcircumflex"
	k="-30" />
    <hkern g1="q,y.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	g2="asterisk"
	k="5" />
    <hkern g1="q,y.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	g2="trademark"
	k="25" />
    <hkern g1="q,y.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	g2="degree"
	k="5" />
    <hkern g1="backslash,backslash.uc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="backslash,backslash.uc"
	g2="AE,AEacute"
	k="15" />
    <hkern g1="backslash,backslash.uc"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="backslash,backslash.uc"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="105" />
    <hkern g1="backslash,backslash.uc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="80" />
    <hkern g1="backslash,backslash.uc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="120" />
    <hkern g1="backslash,backslash.uc"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="40" />
    <hkern g1="backslash,backslash.uc"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="55" />
    <hkern g1="backslash,backslash.uc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="backslash,backslash.uc"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="75" />
    <hkern g1="backslash,backslash.uc"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="15" />
    <hkern g1="backslash,backslash.uc"
	g2="zero,six"
	k="75" />
    <hkern g1="backslash,backslash.uc"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="50" />
    <hkern g1="backslash,backslash.uc"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="40" />
    <hkern g1="backslash,backslash.uc"
	g2="b,h,k,l,germandbls,thorn,hcircumflex,Imacron,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,k.alt,kcommaaccent.alt"
	k="30" />
    <hkern g1="backslash,backslash.uc"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="25" />
    <hkern g1="backslash,backslash.uc"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="45" />
    <hkern g1="backslash,backslash.uc"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="80" />
    <hkern g1="backslash,backslash.uc"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="55" />
    <hkern g1="backslash,backslash.uc"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="50" />
    <hkern g1="backslash,backslash.uc"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,ij,idotaccent"
	k="30" />
    <hkern g1="backslash,backslash.uc"
	g2="j,jcircumflex"
	k="-70" />
    <hkern g1="backslash,backslash.uc"
	g2="Eth,Dcroat"
	k="30" />
    <hkern g1="backslash,backslash.uc"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="30" />
    <hkern g1="backslash,backslash.uc"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="45" />
    <hkern g1="backslash,backslash.uc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,Dcaron,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Nacute.pl"
	k="45" />
    <hkern g1="backslash,backslash.uc"
	g2="Hbar"
	k="40" />
    <hkern g1="backslash,backslash.uc"
	g2="Lslash"
	k="40" />
    <hkern g1="backslash,backslash.uc"
	g2="V"
	k="110" />
    <hkern g1="backslash,backslash.uc"
	g2="X"
	k="20" />
    <hkern g1="backslash,backslash.uc"
	g2="lslash"
	k="45" />
    <hkern g1="backslash,backslash.uc"
	g2="v"
	k="75" />
    <hkern g1="backslash,backslash.uc"
	g2="hbar"
	k="35" />
    <hkern g1="backslash,backslash.uc"
	g2="x"
	k="25" />
    <hkern g1="backslash,backslash.uc"
	g2="four"
	k="75" />
    <hkern g1="backslash,backslash.uc"
	g2="one"
	k="50" />
    <hkern g1="backslash,backslash.uc"
	g2="seven"
	k="70" />
    <hkern g1="backslash,backslash.uc"
	g2="three"
	k="50" />
    <hkern g1="backslash,backslash.uc"
	g2="two"
	k="30" />
    <hkern g1="backslash,backslash.uc"
	g2="eight"
	k="50" />
    <hkern g1="backslash,backslash.uc"
	g2="five"
	k="65" />
    <hkern g1="backslash,backslash.uc"
	g2="nine"
	k="75" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="backslash,backslash.uc"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="30" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="comma,period,ellipsis"
	k="-15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="plus,divide,minus"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="quotedbl,quotesingle"
	k="-15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="slash,slash.uc"
	k="25" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="quoteleft,quotedblleft"
	k="-15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="30" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="colon,semicolon"
	k="-15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="-5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="ampersand"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="at"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="at.uc"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="bullet"
	k="15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="lslash"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="periodcentered"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="registered"
	k="-5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="trademark"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="v"
	k="-5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="hbar"
	k="15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="x"
	k="5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,cacute.pl"
	g2="degree"
	k="-5" />
    <hkern g1="colon,semicolon"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-10" />
    <hkern g1="colon,semicolon"
	g2="AE,AEacute"
	k="-10" />
    <hkern g1="colon,semicolon"
	g2="J,Jcircumflex"
	k="-5" />
    <hkern g1="colon,semicolon"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="60" />
    <hkern g1="colon,semicolon"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="15" />
    <hkern g1="colon,semicolon"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="colon,semicolon"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="-5" />
    <hkern g1="colon,semicolon"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-5" />
    <hkern g1="colon,semicolon"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="colon,semicolon"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="colon,semicolon"
	g2="Lslash"
	k="5" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="35" />
    <hkern g1="colon,semicolon"
	g2="X"
	k="-5" />
    <hkern g1="colon,semicolon"
	g2="v"
	k="5" />
    <hkern g1="colon,semicolon"
	g2="x"
	k="5" />
    <hkern g1="colon,semicolon"
	g2="one"
	k="5" />
    <hkern g1="colon,semicolon"
	g2="seven"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="backslash,backslash.uc"
	k="60" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="-5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,endash,emdash"
	k="-15" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,period,ellipsis"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="plus,divide,minus"
	k="-10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quoteright,quotedblright"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="slash,slash.uc"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="35" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="15" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quoteleft,quotedblleft"
	k="35" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-15" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="ampersand"
	k="-5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="asterisk"
	k="35" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="at"
	k="-5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="at.uc"
	k="-5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="bullet"
	k="-10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="lslash"
	k="-10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="periodcentered"
	k="-10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="question"
	k="45" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="registered"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="trademark"
	k="50" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="v"
	k="25" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hbar"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="x"
	k="45" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="degree"
	k="45" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="underscore"
	k="10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="backslash,backslash.uc"
	k="-15" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="20" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="25" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="-10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="comma,period,ellipsis"
	k="40" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="plus,divide,minus"
	k="10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="quotedbl,quotesingle"
	k="-10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="slash,slash.uc"
	k="65" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="20" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="5" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="ampersand"
	k="15" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="asterisk"
	k="-5" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="at"
	k="10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="at.uc"
	k="10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="bullet"
	k="25" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="lslash"
	k="5" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="periodcentered"
	k="15" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="question"
	k="-5" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="registered"
	k="-10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="trademark"
	k="-5" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="v"
	k="-10" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="hbar"
	k="-15" />
    <hkern g1="f,f_f,f.short,f_f.short"
	g2="degree"
	k="-10" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="backslash,backslash.uc"
	k="35" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="slash,slash.uc"
	k="20" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="asterisk"
	k="15" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="question"
	k="5" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="trademark"
	k="25" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="hbar"
	k="15" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="x"
	k="5" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="degree"
	k="5" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="75" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="5" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="5" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="V"
	k="45" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="X"
	k="20" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="v"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="x"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="four"
	k="-5" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="one"
	k="5" />
    <hkern g1="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	g2="seven"
	k="15" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="AE,AEacute"
	k="45" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="55" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="80" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="25" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="20" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="-5" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="Lslash"
	k="-10" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="V"
	k="65" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="X"
	k="55" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="lslash"
	k="-5" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="v"
	k="25" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="hbar"
	k="5" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="x"
	k="50" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="four"
	k="-5" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="one"
	k="15" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="seven"
	k="25" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="three"
	k="5" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="two"
	k="5" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="five"
	k="5" />
    <hkern g1="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	g2="nine"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="25" />
    <hkern g1="hyphen,endash,emdash"
	g2="AE,AEacute"
	k="50" />
    <hkern g1="hyphen,endash,emdash"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="45" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="65" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="25" />
    <hkern g1="hyphen,endash,emdash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="hyphen,endash,emdash"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="hyphen,endash,emdash"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="hyphen,endash,emdash"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="Lslash"
	k="-5" />
    <hkern g1="hyphen,endash,emdash"
	g2="V"
	k="40" />
    <hkern g1="hyphen,endash,emdash"
	g2="X"
	k="35" />
    <hkern g1="hyphen,endash,emdash"
	g2="lslash"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="v"
	k="25" />
    <hkern g1="hyphen,endash,emdash"
	g2="x"
	k="50" />
    <hkern g1="hyphen,endash,emdash"
	g2="four"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="one"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="seven"
	k="45" />
    <hkern g1="hyphen,endash,emdash"
	g2="three"
	k="5" />
    <hkern g1="hyphen,endash,emdash"
	g2="two"
	k="25" />
    <hkern g1="hyphen,endash,emdash"
	g2="five"
	k="5" />
    <hkern g1="hyphen,endash,emdash"
	g2="nine"
	k="15" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="AE,AEacute"
	k="75" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="45" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="65" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="40" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="-10" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="10" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="-10" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="Hbar"
	k="10" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="Lslash"
	k="-5" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="V"
	k="50" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="X"
	k="55" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="lslash"
	k="-5" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="v"
	k="25" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="x"
	k="50" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="four"
	k="-10" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="one"
	k="20" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="seven"
	k="45" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="three"
	k="5" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="two"
	k="25" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="five"
	k="5" />
    <hkern g1="hyphen.uc,endash.uc,emdash.uc"
	g2="nine"
	k="15" />
    <hkern g1="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,f_i,idotaccent"
	g2="slash,slash.uc"
	k="35" />
    <hkern g1="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,f_i,idotaccent"
	g2="trademark"
	k="5" />
    <hkern g1="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,f_i,idotaccent"
	g2="hbar"
	k="-5" />
    <hkern g1="j,ij,jcircumflex"
	g2="slash,slash.uc"
	k="10" />
    <hkern g1="j,ij,jcircumflex"
	g2="j,jcircumflex"
	k="-20" />
    <hkern g1="j,ij,jcircumflex"
	g2="trademark"
	k="5" />
    <hkern g1="j,ij,jcircumflex"
	g2="hbar"
	k="-5" />
    <hkern g1="k,kcommaaccent"
	g2="backslash,backslash.uc"
	k="20" />
    <hkern g1="k,kcommaaccent"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="60" />
    <hkern g1="k,kcommaaccent"
	g2="hyphen,endash,emdash"
	k="60" />
    <hkern g1="k,kcommaaccent"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="50" />
    <hkern g1="k,kcommaaccent"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="k,kcommaaccent"
	g2="plus,divide,minus"
	k="50" />
    <hkern g1="k,kcommaaccent"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="slash,slash.uc"
	k="20" />
    <hkern g1="k,kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-5" />
    <hkern g1="k,kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-5" />
    <hkern g1="k,kcommaaccent"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-5" />
    <hkern g1="k,kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="35" />
    <hkern g1="k,kcommaaccent"
	g2="b,h,k,l,germandbls,thorn,hcircumflex,Imacron,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,k.alt,kcommaaccent.alt"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="quoteleft,quotedblleft"
	k="-5" />
    <hkern g1="k,kcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="30" />
    <hkern g1="k,kcommaaccent"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="55" />
    <hkern g1="k,kcommaaccent"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,ij,idotaccent"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="15" />
    <hkern g1="k,kcommaaccent"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="15" />
    <hkern g1="k,kcommaaccent"
	g2="ampersand"
	k="30" />
    <hkern g1="k,kcommaaccent"
	g2="asterisk"
	k="10" />
    <hkern g1="k,kcommaaccent"
	g2="at"
	k="35" />
    <hkern g1="k,kcommaaccent"
	g2="at.uc"
	k="35" />
    <hkern g1="k,kcommaaccent"
	g2="bullet"
	k="60" />
    <hkern g1="k,kcommaaccent"
	g2="exclam"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="lslash"
	k="25" />
    <hkern g1="k,kcommaaccent"
	g2="periodcentered"
	k="60" />
    <hkern g1="k,kcommaaccent"
	g2="question"
	k="-5" />
    <hkern g1="k,kcommaaccent"
	g2="registered"
	k="-5" />
    <hkern g1="k,kcommaaccent"
	g2="trademark"
	k="25" />
    <hkern g1="k,kcommaaccent"
	g2="v"
	k="-5" />
    <hkern g1="k,kcommaaccent"
	g2="hbar"
	k="15" />
    <hkern g1="k,kcommaaccent"
	g2="x"
	k="-10" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="backslash,backslash.uc"
	k="45" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="5" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="5" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="plus,divide,minus"
	k="-5" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="quoteright,quotedblright"
	k="30" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="slash,slash.uc"
	k="30" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="quoteleft,quotedblleft"
	k="25" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="10" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="5" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="10" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="ampersand"
	k="15" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="asterisk"
	k="25" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="bullet"
	k="5" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="lslash"
	k="-5" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="question"
	k="35" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="registered"
	k="25" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="trademark"
	k="40" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="v"
	k="25" />
    <hkern g1="k.alt,kcommaaccent.alt"
	g2="degree"
	k="30" />
    <hkern g1="d,l,dcaron,Imacron,lacute,lcommaaccent,lcaron,f_l"
	g2="backslash,backslash.uc"
	k="-5" />
    <hkern g1="d,l,dcaron,Imacron,lacute,lcommaaccent,lcaron,f_l"
	g2="slash,slash.uc"
	k="30" />
    <hkern g1="d,l,dcaron,Imacron,lacute,lcommaaccent,lcaron,f_l"
	g2="asterisk"
	k="10" />
    <hkern g1="d,l,dcaron,Imacron,lacute,lcommaaccent,lcaron,f_l"
	g2="trademark"
	k="5" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="backslash,backslash.uc"
	k="55" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="quoteright,quotedblright"
	k="25" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="slash,slash.uc"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="quoteleft,quotedblleft"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="asterisk"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="question"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="registered"
	k="25" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="trademark"
	k="50" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="v"
	k="25" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,aringacute,nacute.pl"
	g2="degree"
	k="35" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="backslash,backslash.uc"
	k="60" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="comma,period,ellipsis"
	k="25" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="plus,divide,minus"
	k="-10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="quoteright,quotedblright"
	k="40" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="slash,slash.uc"
	k="60" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="35" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="25" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="quoteleft,quotedblleft"
	k="50" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="15" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="-10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="colon,semicolon"
	k="15" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="asterisk"
	k="50" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="at"
	k="-5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="at.uc"
	k="-5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="bullet"
	k="-10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="lslash"
	k="-5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="periodcentered"
	k="-10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="question"
	k="45" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="registered"
	k="40" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="trademark"
	k="65" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="v"
	k="30" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="x"
	k="50" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="degree"
	k="60" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,oacute.pl"
	g2="underscore"
	k="10" />
    <hkern g1="onesuperior,one.num"
	g2="fraction"
	k="230" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-15" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-15" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="15" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="zero,six"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="15" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="15" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="j,jcircumflex"
	k="-65" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,Dcaron,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Itilde,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Nacute.pl"
	k="-5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="Hbar"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="Lslash"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="V"
	k="-15" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="X"
	k="-5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="lslash"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="v"
	k="25" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="hbar"
	k="-10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="x"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="four"
	k="15" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="one"
	k="-10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="seven"
	k="-10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="two"
	k="-5" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="eight"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft,parenleft.uc,bracketleft.uc,braceleft.uc"
	g2="nine"
	k="5" />
    <hkern g1="percent,perthousand"
	g2="zero,six"
	k="5" />
    <hkern g1="percent,perthousand"
	g2="four"
	k="-5" />
    <hkern g1="percent,perthousand"
	g2="one"
	k="5" />
    <hkern g1="percent,perthousand"
	g2="seven"
	k="20" />
    <hkern g1="percent,perthousand"
	g2="three"
	k="-5" />
    <hkern g1="percent,perthousand"
	g2="eight"
	k="-5" />
    <hkern g1="percent,perthousand"
	g2="nine"
	k="35" />
    <hkern g1="comma,period,ellipsis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-25" />
    <hkern g1="comma,period,ellipsis"
	g2="AE,AEacute"
	k="-10" />
    <hkern g1="comma,period,ellipsis"
	g2="J,Jcircumflex"
	k="-5" />
    <hkern g1="comma,period,ellipsis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="75" />
    <hkern g1="comma,period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="95" />
    <hkern g1="comma,period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="95" />
    <hkern g1="comma,period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="25" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteright,quotedblright"
	k="100" />
    <hkern g1="comma,period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="comma,period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="95" />
    <hkern g1="comma,period,ellipsis"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-5" />
    <hkern g1="comma,period,ellipsis"
	g2="zero,six"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="comma,period,ellipsis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteleft,quotedblleft"
	k="100" />
    <hkern g1="comma,period,ellipsis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="35" />
    <hkern g1="comma,period,ellipsis"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="45" />
    <hkern g1="comma,period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="25" />
    <hkern g1="comma,period,ellipsis"
	g2="Eth,Dcroat"
	k="5" />
    <hkern g1="comma,period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="15" />
    <hkern g1="comma,period,ellipsis"
	g2="Lslash"
	k="5" />
    <hkern g1="comma,period,ellipsis"
	g2="V"
	k="100" />
    <hkern g1="comma,period,ellipsis"
	g2="X"
	k="-5" />
    <hkern g1="comma,period,ellipsis"
	g2="lslash"
	k="15" />
    <hkern g1="comma,period,ellipsis"
	g2="v"
	k="85" />
    <hkern g1="comma,period,ellipsis"
	g2="x"
	k="-5" />
    <hkern g1="comma,period,ellipsis"
	g2="four"
	k="25" />
    <hkern g1="comma,period,ellipsis"
	g2="one"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="seven"
	k="40" />
    <hkern g1="comma,period,ellipsis"
	g2="two"
	k="-10" />
    <hkern g1="comma,period,ellipsis"
	g2="eight"
	k="5" />
    <hkern g1="comma,period,ellipsis"
	g2="five"
	k="15" />
    <hkern g1="comma,period,ellipsis"
	g2="nine"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="35" />
    <hkern g1="plus,divide,minus"
	g2="AE,AEacute"
	k="75" />
    <hkern g1="plus,divide,minus"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="plus,divide,minus"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="45" />
    <hkern g1="plus,divide,minus"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="plus,divide,minus"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="55" />
    <hkern g1="plus,divide,minus"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="40" />
    <hkern g1="plus,divide,minus"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="-10" />
    <hkern g1="plus,divide,minus"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="plus,divide,minus"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="plus,divide,minus"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="5" />
    <hkern g1="plus,divide,minus"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="15" />
    <hkern g1="plus,divide,minus"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="plus,divide,minus"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="5" />
    <hkern g1="plus,divide,minus"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="-5" />
    <hkern g1="plus,divide,minus"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="plus,divide,minus"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="plus,divide,minus"
	g2="V"
	k="40" />
    <hkern g1="plus,divide,minus"
	g2="X"
	k="55" />
    <hkern g1="plus,divide,minus"
	g2="lslash"
	k="-5" />
    <hkern g1="plus,divide,minus"
	g2="v"
	k="15" />
    <hkern g1="plus,divide,minus"
	g2="x"
	k="45" />
    <hkern g1="plus,divide,minus"
	g2="four"
	k="-5" />
    <hkern g1="plus,divide,minus"
	g2="one"
	k="15" />
    <hkern g1="plus,divide,minus"
	g2="seven"
	k="35" />
    <hkern g1="plus,divide,minus"
	g2="three"
	k="25" />
    <hkern g1="plus,divide,minus"
	g2="two"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="eight"
	k="10" />
    <hkern g1="plus,divide,minus"
	g2="nine"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="AE,AEacute"
	k="-5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="95" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="15" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="75" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="80" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="zero,six"
	k="15" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="50" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="25" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="25" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="j,jcircumflex"
	k="-20" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="Eth,Dcroat"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="Hbar"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="Lslash"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="V"
	k="115" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="lslash"
	k="20" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="v"
	k="80" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="four"
	k="25" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="one"
	k="25" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="seven"
	k="30" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="three"
	k="-5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="two"
	k="-5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="eight"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="five"
	k="10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="nine"
	k="30" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="85" />
    <hkern g1="quoteleft,quotedblleft"
	g2="AE,AEacute"
	k="120" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J,Jcircumflex"
	k="35" />
    <hkern g1="quoteleft,quotedblleft"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zacute.pl"
	k="5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="45" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="quoteleft,quotedblleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-15" />
    <hkern g1="quoteleft,quotedblleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="25" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="-5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Hbar"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Lslash"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="V"
	k="-15" />
    <hkern g1="quoteleft,quotedblleft"
	g2="lslash"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="v"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="hbar"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="x"
	k="-5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="four"
	k="55" />
    <hkern g1="quoteleft,quotedblleft"
	g2="one"
	k="-25" />
    <hkern g1="quoteleft,quotedblleft"
	g2="seven"
	k="-15" />
    <hkern g1="quoteleft,quotedblleft"
	g2="three"
	k="5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="two"
	k="-5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="nine"
	k="-5" />
    <hkern g1="quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="quoteright,quotedblright"
	g2="AE,AEacute"
	k="170" />
    <hkern g1="quoteright,quotedblright"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="quoteright,quotedblright"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-10" />
    <hkern g1="quoteright,quotedblright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="quoteright,quotedblright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-5" />
    <hkern g1="quoteright,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="75" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="quoteright,quotedblright"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="15" />
    <hkern g1="quoteright,quotedblright"
	g2="zero,six"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="55" />
    <hkern g1="quoteright,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="45" />
    <hkern g1="quoteright,quotedblright"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="35" />
    <hkern g1="quoteright,quotedblright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-5" />
    <hkern g1="quoteright,quotedblright"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="quoteright,quotedblright"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="15" />
    <hkern g1="quoteright,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="15" />
    <hkern g1="quoteright,quotedblright"
	g2="Hbar"
	k="-5" />
    <hkern g1="quoteright,quotedblright"
	g2="Lslash"
	k="15" />
    <hkern g1="quoteright,quotedblright"
	g2="V"
	k="-10" />
    <hkern g1="quoteright,quotedblright"
	g2="X"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="lslash"
	k="15" />
    <hkern g1="quoteright,quotedblright"
	g2="hbar"
	k="-20" />
    <hkern g1="quoteright,quotedblright"
	g2="x"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="four"
	k="85" />
    <hkern g1="quoteright,quotedblright"
	g2="one"
	k="-15" />
    <hkern g1="quoteright,quotedblright"
	g2="seven"
	k="-15" />
    <hkern g1="quoteright,quotedblright"
	g2="three"
	k="5" />
    <hkern g1="quoteright,quotedblright"
	g2="two"
	k="5" />
    <hkern g1="quoteright,quotedblright"
	g2="eight"
	k="5" />
    <hkern g1="quoteright,quotedblright"
	g2="nine"
	k="5" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="AE,AEacute"
	k="120" />
    <hkern g1="quotedbl,quotesingle"
	g2="J,Jcircumflex"
	k="50" />
    <hkern g1="quotedbl,quotesingle"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-5" />
    <hkern g1="quotedbl,quotesingle"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-5" />
    <hkern g1="quotedbl,quotesingle"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-5" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-15" />
    <hkern g1="quotedbl,quotesingle"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-15" />
    <hkern g1="quotedbl,quotesingle"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="-5" />
    <hkern g1="quotedbl,quotesingle"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="5" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="quotedbl,quotesingle"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="Hbar"
	k="-5" />
    <hkern g1="quotedbl,quotesingle"
	g2="Lslash"
	k="15" />
    <hkern g1="quotedbl,quotesingle"
	g2="V"
	k="-5" />
    <hkern g1="quotedbl,quotesingle"
	g2="X"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="lslash"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="v"
	k="-15" />
    <hkern g1="quotedbl,quotesingle"
	g2="hbar"
	k="-15" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="35" />
    <hkern g1="quotedbl,quotesingle"
	g2="one"
	k="-15" />
    <hkern g1="quotedbl,quotesingle"
	g2="seven"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="two"
	k="-5" />
    <hkern g1="quotedbl,quotesingle"
	g2="five"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="backslash,backslash.uc"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,ellipsis"
	k="55" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="plus,divide,minus"
	k="5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteright,quotedblright"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="slash,slash.uc"
	k="75" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteleft,quotedblleft"
	k="-15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="ampersand"
	k="15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="asterisk"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="at"
	k="5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="at.uc"
	k="5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="bullet"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="lslash"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="periodcentered"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="question"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="registered"
	k="-15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="trademark"
	k="15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="v"
	k="-10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="degree"
	k="-5" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="backslash,backslash.uc"
	k="45" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="plus,divide,minus"
	k="5" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="slash,slash.uc"
	k="30" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="5" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="ampersand"
	k="-5" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="asterisk"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="lslash"
	k="5" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="periodcentered"
	k="5" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="question"
	k="25" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="registered"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="trademark"
	k="40" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="v"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="x"
	k="30" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	g2="degree"
	k="30" />
    <hkern g1="slash,slash.uc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="65" />
    <hkern g1="slash,slash.uc"
	g2="AE,AEacute"
	k="110" />
    <hkern g1="slash,slash.uc"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="slash,slash.uc"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A"
	k="-10" />
    <hkern g1="slash,slash.uc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-15" />
    <hkern g1="slash,slash.uc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-20" />
    <hkern g1="slash,slash.uc"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="65" />
    <hkern g1="slash,slash.uc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="slash,slash.uc"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="slash,slash.uc"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="30" />
    <hkern g1="slash,slash.uc"
	g2="zero,six"
	k="30" />
    <hkern g1="slash,slash.uc"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Sacute.pl"
	k="20" />
    <hkern g1="slash,slash.uc"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="70" />
    <hkern g1="slash,slash.uc"
	g2="b,h,k,l,germandbls,thorn,hcircumflex,Imacron,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,k.alt,kcommaaccent.alt"
	k="-5" />
    <hkern g1="slash,slash.uc"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="50" />
    <hkern g1="slash,slash.uc"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="slash,slash.uc"
	g2="C,G,O,Q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,uni24C5,Cacute.pl,Oacute.pl"
	k="30" />
    <hkern g1="slash,slash.uc"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="10" />
    <hkern g1="slash,slash.uc"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="slash,slash.uc"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,nacute.pl"
	k="35" />
    <hkern g1="slash,slash.uc"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="30" />
    <hkern g1="slash,slash.uc"
	g2="Lslash"
	k="5" />
    <hkern g1="slash,slash.uc"
	g2="V"
	k="-10" />
    <hkern g1="slash,slash.uc"
	g2="X"
	k="-10" />
    <hkern g1="slash,slash.uc"
	g2="lslash"
	k="10" />
    <hkern g1="slash,slash.uc"
	g2="v"
	k="30" />
    <hkern g1="slash,slash.uc"
	g2="hbar"
	k="-15" />
    <hkern g1="slash,slash.uc"
	g2="x"
	k="35" />
    <hkern g1="slash,slash.uc"
	g2="four"
	k="70" />
    <hkern g1="slash,slash.uc"
	g2="one"
	k="-15" />
    <hkern g1="slash,slash.uc"
	g2="seven"
	k="-25" />
    <hkern g1="slash,slash.uc"
	g2="three"
	k="5" />
    <hkern g1="slash,slash.uc"
	g2="two"
	k="15" />
    <hkern g1="slash,slash.uc"
	g2="eight"
	k="20" />
    <hkern g1="slash,slash.uc"
	g2="five"
	k="5" />
    <hkern g1="slash,slash.uc"
	g2="nine"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="backslash,backslash.uc"
	k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="comma,period,ellipsis"
	k="-10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="plus,divide,minus"
	k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="slash,slash.uc"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="quoteleft,quotedblleft"
	k="-5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,u.alt,w.alt,y.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt,yacute.alt,ydieresis.alt,ycircumflex.alt,ygrave.alt"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="ampersand"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="asterisk"
	k="-5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="at"
	k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="at.uc"
	k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="bullet"
	k="20" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="lslash"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="periodcentered"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="registered"
	k="-5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="trademark"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="v"
	k="-5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="degree"
	k="-5" />
    <hkern g1="threesuperior,three.num"
	g2="fraction"
	k="260" />
    <hkern g1="threesuperior,three.num"
	g2="nine.num"
	k="5" />
    <hkern g1="threesuperior,three.num"
	g2="seven.num"
	k="5" />
    <hkern g1="twosuperior,two.num"
	g2="fraction"
	k="215" />
    <hkern g1="twosuperior,two.num"
	g2="four.num"
	k="5" />
    <hkern g1="twosuperior,two.num"
	g2="twosuperior,two.num"
	k="-5" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	g2="backslash,backslash.uc"
	k="30" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	g2="slash,slash.uc"
	k="30" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	g2="asterisk"
	k="5" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	g2="trademark"
	k="25" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	g2="degree"
	k="5" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="backslash,backslash.uc"
	k="30" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="comma,period,ellipsis"
	k="15" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="slash,slash.uc"
	k="45" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-5" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="asterisk"
	k="5" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="trademark"
	k="25" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="x"
	k="15" />
    <hkern g1="u.alt,w.alt,uacute.alt,ugrave.alt,ucircumflex.alt,udieresis.alt,ubreve.alt,uhungarumlaut.alt,umacron.alt,uogonek.alt,uring.alt,utilde.alt,wacute.alt,wcircumflex.alt,wdieresis.alt,wgrave.alt"
	g2="degree"
	k="5" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="backslash,backslash.uc"
	k="25" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="25" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,ellipsis"
	k="70" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="plus,divide,minus"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="quotedbl,quotesingle"
	k="-15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="slash,slash.uc"
	k="70" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-5" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-5" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="25" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="quoteleft,quotedblleft"
	k="-15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ampersand"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="asterisk"
	k="-5" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="at"
	k="5" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="at.uc"
	k="5" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="bullet"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="lslash"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="periodcentered"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="question"
	k="-5" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="registered"
	k="-20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="trademark"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="v"
	k="-10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="degree"
	k="-5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="backslash,backslash.uc"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="35" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,period,ellipsis"
	k="95" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="plus,divide,minus"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="slash,slash.uc"
	k="75" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="f,f_f,f_i,f_l,f.short,f_f.short"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotright,guilsinglright,guilsinglright.uc,guillemotright.uc"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ampersand"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="asterisk"
	k="-5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="at"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="at.uc"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="bullet"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="lslash"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="periodcentered"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="question"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="registered"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="trademark"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="v"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hbar"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="degree"
	k="-5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="underscore"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="backslash,backslash.uc"
	k="25" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="guillemotleft,guilsinglleft,guilsinglleft.uc,guillemotleft.uc"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,oslashacute,cacute.pl,oacute.pl,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,aringacute.alt,atilde.alt"
	k="25" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="comma,period,ellipsis"
	k="-5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="plus,divide,minus"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="slash,slash.uc"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="z,zacute,zdotaccent,zcaron,zacute.pl"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,sacute.pl"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="hyphen.uc,endash.uc,emdash.uc"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="ampersand"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="at"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="at.uc"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="bullet"
	k="25" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="lslash"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="periodcentered"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="question"
	k="-5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="registered"
	k="-5" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="trademark"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron,zacute.pl"
	g2="v"
	k="-5" />
    <hkern g1="zero,nine"
	g2="backslash,backslash.uc"
	k="25" />
    <hkern g1="zero,nine"
	g2="parenright,bracketright,braceright,parenright.uc,bracketright.uc,braceright.uc"
	k="5" />
    <hkern g1="zero,nine"
	g2="comma,period,ellipsis"
	k="20" />
    <hkern g1="zero,nine"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="zero,nine"
	g2="slash,slash.uc"
	k="75" />
    <hkern g1="zero,nine"
	g2="percent,perthousand"
	k="5" />
    <hkern g1="zero,nine"
	g2="asterisk"
	k="10" />
    <hkern g1="zero,nine"
	g2="question"
	k="10" />
    <hkern g1="zero,nine"
	g2="registered"
	k="5" />
    <hkern g1="zero,nine"
	g2="trademark"
	k="25" />
    <hkern g1="zero,nine"
	g2="degree"
	k="5" />
    <hkern g1="zero,nine"
	g2="underscore"
	k="50" />
    <hkern g1="zero,nine"
	g2="four"
	k="-5" />
    <hkern g1="zero,nine"
	g2="seven"
	k="15" />
    <hkern g1="zero,nine"
	g2="two"
	k="5" />
    <hkern g1="zero.num,nine.num"
	g2="fraction"
	k="265" />
  </font>
</defs></svg>
