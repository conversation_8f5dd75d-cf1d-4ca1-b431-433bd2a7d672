import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@11.16.0?target=deno';
import { corsHeaders } from '../_shared/cors.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.0.0?target=deno';

// Initialize Stripe client - ensure STRIPE_SECRET_KEY is set in Supabase secrets
const stripe = Stripe(Deno.env.get('STRIPE_SECRET_KEY')!, {
  httpClient: Stripe.createFetchHttpClient(),
  apiVersion: '2023-10-16',
});

// Adjust based on the actual fields you need in the frontend
interface StripeInvoiceWithCustomer {
  id: string;
  stripe_invoice_id: string;
  stripe_customer_id: string | null;
  customer_email: string | null;
  customer_name: string | null;
  status: string | null;
  amount_due: number | null;
  amount_paid: number | null;
  amount_remaining: number | null;
  currency: string | null;
  due_date: string | null;
  invoice_pdf: string | null;
  hosted_invoice_url: string | null;
  created_at: string;
}


serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // --- Get Pagination Params from Request Body ---
    const {
      limit = 10,
      starting_after = undefined,
      count_only = false,
      include_manual = true,
      include_stripe = true
    } = await req.json() || {};
    const fetchLimit = Math.max(1, Math.min(100, parseInt(limit, 10))); // Ensure limit is reasonable (1-100)
    // --- End Pagination Params ---

    if (count_only) {
      try {
        // Request just enough invoices to determine if we're over 99
        const invoices = await stripe.invoices.list({
          limit: 100, // Just enough to check if we have 99+ invoices
        });

        // Get the count from the data array
        const count = invoices.data.length;

        // Check if we have more than 99 or if there are more pages
        const displayCount = (count > 99 || invoices.has_more) ? "99+" : count.toString();

        return new Response(
          JSON.stringify({
            count: count,
            displayCount: displayCount
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 200,
          }
        );
      } catch (error) {
        console.error('Error counting invoices:', error);
        return new Response(
          JSON.stringify({
            error: 'Failed to count invoices',
            count: 0,
            displayCount: "0"
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        );
      }
    }

    // --- Fetch Invoices Directly from Stripe if requested ---
    let stripeInvoicesResponse = { data: [], has_more: false };

    if (include_stripe) {
      console.log(`Fetching invoices from Stripe with limit: ${fetchLimit}, starting_after: ${starting_after}`);
      stripeInvoicesResponse = await stripe.invoices.list({
        limit: fetchLimit,
        starting_after: starting_after,
        // You can add other filters here, e.g., status: 'open'
        expand: ['data.customer'], // Expand customer data directly
      });
      console.log(`Fetched ${stripeInvoicesResponse.data.length} invoices from Stripe. Has more: ${stripeInvoicesResponse.has_more}`);
    } else {
      console.log('Skipping Stripe invoices as requested');
    }
    // --- End Fetch Invoices ---


    // --- Map Stripe Data ---
    const processedInvoices: StripeInvoiceWithCustomer[] = [];

    // Only process Stripe invoices if they were requested and returned
    if (include_stripe && stripeInvoicesResponse.data.length > 0) {
      // Map Stripe invoices to our format
      stripeInvoicesResponse.data.forEach(invoice => {
        // Type guard to check if customer is expanded and not deleted
        const customer = invoice.customer && typeof invoice.customer === 'object' && 'id' in invoice.customer
          ? invoice.customer
          : null;

        processedInvoices.push({
          id: invoice.id, // Use Stripe ID
          stripe_invoice_id: invoice.id,
          stripe_customer_id: typeof invoice.customer === 'string' ? invoice.customer : customer?.id ?? null,
          customer_email: customer?.email ?? (typeof invoice.customer_email === 'string' ? invoice.customer_email : null),
          customer_name: customer?.name ?? (typeof invoice.customer_name === 'string' ? invoice.customer_name : null),
          status: invoice.status,
          amount_due: invoice.amount_due,
          amount_paid: invoice.amount_paid,
          amount_remaining: invoice.amount_remaining,
          currency: invoice.currency,
          due_date: invoice.due_date ? new Date(invoice.due_date * 1000).toISOString() : null,
          invoice_pdf: invoice.invoice_pdf,
          hosted_invoice_url: invoice.hosted_invoice_url,
          created_at: new Date(invoice.created * 1000).toISOString(),
        });
      });
    }
    // --- End Map Stripe Data ---

    // --- Fetch Manual Invoices if requested ---
    if (include_manual) {
      // Initialize Supabase client
      const supabaseAdmin = createClient(
        Deno.env.get('SUPABASE_URL') || '',
        Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '',
        { auth: { persistSession: false } }
      );

      // Fetch manual invoices
      const { data: manualInvoices, error: manualError } = await supabaseAdmin
        .from('invoices')
        .select('*')
        .eq('is_manual', true)
        .order('created_at', { ascending: false })
        .limit(fetchLimit);

      if (!manualError && manualInvoices && manualInvoices.length > 0) {
        const mappedManualInvoices = manualInvoices.map(invoice => ({
          id: invoice.id,
          stripe_invoice_id: invoice.stripe_invoice_id,
          stripe_customer_id: invoice.stripe_customer_id,
          customer_email: invoice.customer_email,
          customer_name: invoice.customer_name,
          status: invoice.status,
          amount_due: invoice.amount_due,
          amount_paid: invoice.amount_paid,
          amount_remaining: invoice.amount_remaining,
          currency: invoice.currency,
          due_date: invoice.due_date,
          invoice_pdf: null,
          hosted_invoice_url: null,
          created_at: invoice.created_at,
          is_manual: true,
          line_items: invoice.line_items,
          payment_method: invoice.payment_method,
          notes: invoice.notes
        }));

        // Combine Stripe and manual invoices
        processedInvoices.push(...mappedManualInvoices);

        // Sort by created_at date (newest first)
        processedInvoices.sort((a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        // Limit to the requested number
        if (processedInvoices.length > fetchLimit) {
          processedInvoices.length = fetchLimit;
        }
      }
    }

    // --- Determine Last Invoice ID for Next Page ---
    const lastInvoiceId = processedInvoices.length > 0 ? processedInvoices[processedInvoices.length - 1].id : null;
    // --- End Determine Last Invoice ID ---
    return new Response(
      JSON.stringify({
        invoices: processedInvoices,
        has_more: stripeInvoicesResponse.has_more,
        last_invoice_id: lastInvoiceId,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    console.error('Function Error:', error);
    // Check if the error is a Stripe error for more specific messages
    let errorMessage = error.message;
    if (error.type) { // Basic check for Stripe error object structure
      errorMessage = `Stripe Error (${error.type}): ${error.message}`;
    }
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: error.statusCode || 500, // Use Stripe's status code if available
    });
  }
});