import { FileText, DollarSign, CheckCircle, CreditCard, BarChart2 } from 'lucide-react';
import BgImgWorks2 from '../../assets/home/<USER>';
import Arrow from '../../assets/home/<USER>';
import Grantready1 from "../../assets/home/<USER>";
import Arrow2 from "./Arrrow.svg";

function Works2() {
    const steps = [
        {
            icon: <FileText className="w-4 h-4 md:w-6 md:h-6 lg:w-8 lg:h-8 xl:w-10 xl:h-10" />,
            title: "PLANNING PHASE",
            description: "Set up capability planning guides and create detailed work plans to align with grant objectives",
        },
        {
            icon: <DollarSign className="w-4 h-4 md:w-6 md:h-6 lg:w-8 lg:h-8 xl:w-10 xl:h-10" />,
            title: "BUDGET ALLOCATION",
            description: "Develop detailed spend plans and budgets that reflect program priorities and compliance requirements",
        },
        {
            icon: <CheckCircle className="w-4 h-4 md:w-6 md:h-6 lg:w-8 lg:h-8 xl:w-10 xl:h-10 " />,
            title: "IMPLEMENTATION",
            description: "Execute work plans and track progress with easy-to-use dashboards and monitoring tools",
        },
        {
            icon: <CreditCard className="w-4 h-4 md:w-6 md:h-6 lg:w-8 lg:h-8 xl:w-10 xl:h-10" />,
            title: "FINANCIAL TRACKING",
            description: "Manage invoices, track expenditures, and ensure proper allocation of funds across all programs",
        },
        {
            icon: <BarChart2 className="w-4 h-4 md:w-6 md:h-6 lg:w-8 lg:h-8 xl:w-10 xl:h-10" />,
            title: "REPORTING & ANALYSIS",
            description: "Set up capability planning guides and create detailed work plans to align with grant objectives",
        },
    ];

    return (
        <div className="w-full py-4 sm:py-6 md:py-8 lg:py-12 overflow-x-hidden">
            <div
                className="max-w-full text-center mb-6 sm:mb-8 md:mb-12 h-48 xs:h-56 sm:h-64 md:h-80 lg:h-96 flex flex-col justify-center items-center bg-contain bg-center bg-no-repeat relative px-2 sm:px-4"
                style={{ backgroundImage: `url(${BgImgWorks2})` }}
            >
                {/* Optional overlay for better text readability */}
                {/* <div className="absolute inset-0 bg-white/70 backdrop-blur-sm"></div> */}

                {/* Content */}
                <div className="relative z-10 w-full max-w-4xl mx-auto px-2 sm:px-4">
                    <h2 className="text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-800 mb-2 sm:mb-3 md:mb-4 leading-tight">
                        HOW <span className="bg-gradient-to-r from-[#46A899] to-[#797EEC] bg-clip-text text-transparent">GrantReady™</span> WORKS
                    </h2>
                    <p className="text-xs xs:text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto px-2">
                        Our platform supports the entire grant lifecycle from initial planning to final reporting ensuring compliance and efficiency
                    </p>
                </div>
            </div>

            {/* Desktop Layout */}
            <div className="hidden lg:flex justify-center items-center space-x-4 lg:space-x-6 xl:space-x-8 2xl:space-x-12 mx-2 lg:mx-4 xl:mx-8 2xl:mx-12">
                {steps.map((step, index) => (
                    <div key={index} className="flex flex-col items-center relative flex-1 max-w-xs">
                        {/* Icon and Circle with Shadow Effect */}
                        <div className='flex flex-col items-center relative p-2 border border-indigo-50 rounded-full'>
                            {/* Shadow Layer */}
                            <div className="absolute inset-0 w-24 h-24 lg:w-28 lg:h-28 xl:w-32 xl:h-32 bg-indigo-400/40 rounded-full blur-md transform translate-x-2 translate-y-2"></div>

                            {/* Main Circle */}
                            <div className="relative w-24 h-24 lg:w-28 lg:h-28 xl:w-32 xl:h-32 border-2 rounded-full flex items-center justify-center">
                                {step.icon}
                            </div>
                            {/* Enhanced Arrow */}
                        {index < steps.length - 1 && (
                            <div className='absolute w-32 lg:w-40 xl:w-48 2xl:w-56 left-10 lg:left-30 xl:left-32 2xl:left-40 top-8 lg:top-10 xl:top-12 2xl:top-5 transform -translate-y-2 z-10'>
                                <img
                                    className='w-24 h-24 lg:w-20 lg:h-20 xl:w-24 xl:h-24 2xl:w-32 2xl:h-32 object-contain filter drop-shadow-lg brightness-110 contrast-125 transition-all duration-300 hover:scale-110'
                                    src={Arrow2}
                                    alt="Arrow pointing to next step"
                                />
                            </div>
                        )}
                        </div>

                        

                        {/* Title and Description */}
                        <h3 className="mt-3 lg:mt-4 text-sm lg:text-base xl:text-lg 2xl:text-xl font-semibold text-gray-800 text-center leading-tight">{step.title}</h3>
                        <p className="mt-2 text-xs lg:text-sm xl:text-base text-gray-600 text-center leading-relaxed px-1">{step.description}</p>
                    </div>
                ))}
            </div>

            {/* Mobile/Tablet Layout */}
            <div className="lg:hidden px-4 sm:px-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8">
                    {steps.map((step, index) => (
                        <div key={index} className="flex flex-col items-center text-center">
                            {/* Mobile Circle */}
                            <div className='flex flex-col items-center relative p-2 border border-indigo-300 rounded-full mb-4'>
                                {/* Shadow Layer */}
                                <div className="absolute inset-0 w-20 h-20 sm:w-24 sm:h-24 bg-indigo-400/40 rounded-full blur-md transform translate-x-1 translate-y-1"></div>

                                {/* Main Circle */}
                                <div className="relative w-20 h-20 sm:w-24 sm:h-24 bg-white border-2 border-indigo-300 rounded-full flex items-center justify-center shadow-lg">
                                    {step.icon}
                                </div>
                            </div>

                            {/* Title and Description */}
                            <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-2">{step.title}</h3>
                            <p className="text-sm sm:text-base text-gray-600 leading-relaxed">{step.description}</p>
                        </div>
                    ))}
                </div>
            </div>
            {/* Bottom Image Section */}
            <div className='mx-auto w-full max-w-7xl mt-8 sm:mt-12 md:mt-16 px-4 sm:px-6'>
                <div className='shadow-2xl rounded-lg overflow-hidden'>
                    <img className='w-full h-auto object-cover' src={Grantready1} alt='GrantReady platform overview' />
                </div>
            </div>
        </div>
    );
}

export default Works2;