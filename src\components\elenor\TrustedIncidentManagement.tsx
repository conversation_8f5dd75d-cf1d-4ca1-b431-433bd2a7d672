import React from 'react';

interface StatCard {
  value: string;
  label: string;
}

const TrustedIncidentManagement: React.FC = () => {
  const stats: StatCard[] = [
    {
      value: '500+',
      label: 'Organizations Served'
    },
    {
      value: '24/7',
      label: 'Real-Time Monitoring'
    },
    {
      value: '99.9%+',
      label: 'System Uptime'
    },
    {
      value: '10K+',
      label: 'Incidents Managed'
    }
  ];

  return (
    <section
      className="relative w-full py-20"
      style={{
        backgroundColor: 'white',
        // padding: '0 4px 40px 4px'
      }}
    >
      <div
        className="flex flex-col items-center justify-center gap-8 sm:gap-12 md:gap-16 lg:gap-[57px] rounded-[14px] w-full px-4 sm:px-6 md:px-8 lg:px-12"
      >
        {/* Header Section */}
        <div className="flex flex-col items-center justify-center gap-2 sm:gap-3 md:gap-4 lg:gap-[10px]">
          <h2
            className="text-center text-black px-2 sm:px-4"
            style={{
              fontFamily: 'Platform, system-ui, sans-serif',
              fontWeight: 500,
              fontSize: 'clamp(24px, 5vw, 54px)',
              lineHeight: '1.236em'
            }}
          >
            Trusted Incident Management for Every Situation
          </h2>
          <p
            className="text-center text-black px-2 sm:px-4 md:px-6"
            style={{
              fontFamily: 'Inter, system-ui, sans-serif',
              fontWeight: 500,
              fontSize: 'clamp(14px, 3vw, 28px)',
              lineHeight: '1.371em',
              maxWidth: '1324px'
            }}
          >
            Elenor Solution brings together emergency responders, agencies, and organizations on a single
            platform. From crisis to daily operations, our tools streamline communication, resource allocation,
            and incident tracking. so you can focus on what matters most: saving lives and maintaining safety.
          </p>
        </div>

        {/* Statistics Container */}
        <div
          className="w-full max-w-7xl rounded-8 flex flex-col items-center justify-center gap-2 sm:gap-3 md:gap-[10px]"
        >
          {/* Statistics Grid */}
          <div className="w-full flex flex-col gap-1 sm:gap-2 md:gap-[6px]">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-[6px] w-full">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="flex flex-col w-full border border-gray-200 shadow-sm"
                  style={{
                    background: 'white',
                    minHeight: '100px',
                    borderRadius: '10px',
                    gap: '8px',
                    padding: '20px 16px'
                  }}
                >
                  {/* Value */}
                  <div
                    className="text-black w-full mb-3 text-xl md:text-[44px]"
                    style={{
                      fontFamily: 'Zenith Trial',
                      fontWeight: 400,
                      fontStyle: 'italic',
                      letterSpacing: '0%',
                      verticalAlign: 'middle'
                    }}
                  >
                    {stat.value}
                  </div>

                  {/* Label */}
                  <div
                    className="text-black text-base sm:text-lg md:text-xl"
                    style={{
                      fontFamily: 'Inter, system-ui, sans-serif',
                      fontWeight: 700,
                      lineHeight: '1.21em',
                      letterSpacing: '-1%'
                    }}
                  >
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustedIncidentManagement;
