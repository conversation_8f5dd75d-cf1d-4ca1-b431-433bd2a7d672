-- Create case_studies table
CREATE TABLE IF NOT EXISTS case_studies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  organization TEXT NOT NULL,
  impact TEXT NOT NULL,
  category TEXT NOT NULL,
  image_url TEXT NOT NULL,
  content TEXT NOT NULL,
  is_public BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE case_studies ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Enable read access for all users" ON case_studies
  FOR SELECT USING (is_public = TRUE);

-- More reliable admin policy
CREATE POLICY "Enable all access for admin" ON case_studies
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE id = auth.uid() AND (raw_user_meta_data->>'role')::text = 'admin'
    )
  );

-- Add insert and update permissions for authenticated users
CREATE POLICY "Enable insert for authenticated users" ON case_studies
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users" ON case_studies
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Add delete permission for authenticated users
CREATE POLICY "Enable delete for authenticated users" ON case_studies
  FOR DELETE USING (auth.role() = 'authenticated');

-- Update grant permissions to include DELETE
GRANT ALL PRIVILEGES ON TABLE case_studies TO postgres;
GRANT ALL PRIVILEGES ON TABLE case_studies TO supabase_admin;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE case_studies TO authenticated;
GRANT SELECT ON TABLE case_studies TO anon;