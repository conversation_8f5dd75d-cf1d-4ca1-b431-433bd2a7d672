# Supabase Edge Functions Gateway

This project provides a Deno-based gateway for routing HTTP requests to Supabase Edge Functions in a self-hosted Supabase environment. The gateway supports JWT authentication, dynamic routing to individual functions, and the ability to disable JWT verification for specific functions (e.g., public endpoints or webhooks). This README explains the gateway's functionality, how to configure it, and best practices for creating performant and secure edge functions.

## Table of Contents

- Overview
- Gateway Code
- Key Features
- Setup Instructions
- Disabling JWT for Specific Functions
- Best Practices
- Example: Individual Edge Function
- Testing and Debugging
- Troubleshooting

## Overview

The gateway function (`main`) is a central entry point that:

- Authenticates requests using JWT (optional, based on environment variables).
- Routes requests to individual edge functions based on the URL pathname (e.g., `/public_function` routes to `/home/<USER>/functions/public_function`).
- Allows disabling JWT verification for specific functions (e.g., `stripe-webhook`) while keeping others protected.
- Creates isolated workers for each function with configured resource limits.

This setup is ideal for self-hosted Supabase environments where you need centralized authentication and routing for multiple edge functions.

## Gateway Code

The following is the gateway function (`/functions/main/index.ts`):

```typescript
import { serve } from 'https://deno.land/std@0.131.0/http/server.ts'
import * as jose from 'https://deno.land/x/jose@v4.14.4/index.ts'

console.log('main function started')

const JWT_SECRET = Deno.env.get('JWT_SECRET')
const VERIFY_JWT = Deno.env.get('VERIFY_JWT') === 'true'

function getAuthToken(req: Request) {
  const authHeader = req.headers.get('authorization')
  if (!authHeader) {
    throw new Error('Missing authorization header')
  }
  const [bearer, token] = authHeader.split(' ')
  if (bearer !== 'Bearer') {
    throw new Error(`Auth header is not 'Bearer {token}'`)
  }
  return token
}

async function verifyJWT(jwt: string): Promise<boolean> {
  const encoder = new TextEncoder()
  const secretKey = encoder.encode(JWT_SECRET)
  try {
    await jose.jwtVerify(jwt, secretKey)
  } catch (err) {
    console.error(err)
    return false
  }
  return true
}

serve(async (req: Request) => {
  const url = new URL(req.url)
  const { pathname } = url
  const path_parts = pathname.split('/')
  const service_name = path_parts[1]

  // Skip JWT verification for specific functions
  const publicFunctions = ['public_function', 'stripe-webhook'] // Add function names here
  const skipJwtVerification = publicFunctions.includes(service_name)

  if (req.method !== 'OPTIONS' && VERIFY_JWT && !skipJwtVerification) {
    try {
      const token = getAuthToken(req)
      const isValidJWT = await verifyJWT(token)

      if (!isValidJWT) {
        return new Response(JSON.stringify({ msg: 'Invalid JWT' }), {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        })
      }
    } catch (e) {
      console.error(e)
      return new Response(JSON.stringify({ msg: e.toString() }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      })
    }
  }

  if (!service_name || service_name === '') {
    const error = { msg: 'missing function name in request' }
    return new Response(JSON.stringify(error), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    })
  }

  const servicePath = `/home/<USER>/functions/${service_name}`
  console.error(`serving the request with ${servicePath}`)

  const memoryLimitMb = 150
  const workerTimeoutMs = 1 * 60 * 1000
  const noModuleCache = false
  const importMapPath = null
  const envVarsObj = Deno.env.toObject()
  const envVars = Object.keys(envVarsObj).map((k) => [k, envVarsObj[k]])

  try {
    const worker = await EdgeRuntime.userWorkers.create({
      servicePath,
      memoryLimitMb,
      workerTimeoutMs,
      noModuleCache,
      importMapPath,
      envVars,
    })
    return await worker.fetch(req)
  } catch (e) {
    const error = { msg: e.toString() }
    return new Response(JSON.stringify(error), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
})
```

### Explanation

- **Imports**: Uses `deno.land/std/http/server` for HTTP handling and `jose` for JWT verification.
- **Environment Variables**:
  - `JWT_SECRET`: Secret key for verifying JWTs (must match Supabase Auth or your token issuer).
  - `VERIFY_JWT`: Boolean (`true`/`false`) to enable/disable JWT verification globally.
  - `PUBLIC_FUNCTIONS` (optional): Comma-separated list of function names to skip JWT verification.
- **JWT Verification**:
  - Extracts Bearer token from `Authorization` header.
  - Verifies token using `jose.jwtVerify` and `JWT_SECRET`.
  - Skips verification for functions listed in `publicFunctions` (e.g., `public_function`, `stripe-webhook`).
- **Routing**:
  - Parses URL pathname to get `service_name` (e.g., `/public_function` → `public_function`).
  - Routes to `/home/<USER>/functions/<service_name>` if valid.
- **Worker Creation**:
  - Creates an isolated worker with 150MB memory, 60s timeout, and environment variables.
  - Forwards the request to the worker and returns its response.
- **Error Handling**:
  - Returns 401 for invalid/missing JWTs, 400 for missing function names, 500 for server errors.
  - Logs errors for debugging without exposing sensitive details.

## Key Features

- **Centralized Authentication**: Enforces JWT verification for protected functions.
- **Selective JWT Bypass**: Disables JWT checks for specific functions (e.g., webhooks).
- **Dynamic Routing**: Routes requests to individual functions based on URL.
- **Resource Limits**: Configures memory and timeout for each worker.
- **Secure Error Handling**: Returns generic error messages to clients, logs details internally.

## Setup Instructions

1. **Clone the Repository**:

   ```bash
   git clone <your-repo>
   cd <your-repo>
   ```

2. **Set Up Environment Variables**:

   - Create a `.env` file in the project root:

     ```env
     JWT_SECRET=your-jwt-secret
     VERIFY_JWT=true
     PUBLIC_FUNCTIONS=public_function,stripe-webhook
     ```
   - Add to `docker-compose.yml` under the `functions` service:

     ```yaml
     services:
       functions:
         image: supabase/edge-runtime:latest
         environment:
           - JWT_SECRET=${JWT_SECRET}
           - VERIFY_JWT=${VERIFY_JWT}
           - PUBLIC_FUNCTIONS=${PUBLIC_FUNCTIONS}
         env_file:
           - .env
     ```

3. **Directory Structure**:

   - Place the gateway function in `/functions/main/index.ts`.
   - Place individual functions in `/home/<USER>/functions/<function_name>/index.ts` (e.g., `/home/<USER>/functions/public_function/index.ts`).

4. **Deploy the Gateway**:

   ```bash
   supabase functions deploy main --project-ref your-project-id
   ```

5. **Deploy Individual Functions**:

   ```bash
   supabase functions deploy public_function --project-ref your-project-id
   ```

6. **Start the Supabase Stack**:

   ```bash
   docker-compose up -d
   ```

7. **Verify Routing**:

   - Ensure Nginx/Kong routes `/api/*` to the functions service (port 54321 by default).

## Disabling JWT for Specific Functions

To disable JWT verification for a specific function (e.g., `public_function` or `stripe-webhook`):

1. **Update** `publicFunctions`:

   - In `/functions/main/index.ts`, add the function name to the `publicFunctions` array:

     ```typescript
     const publicFunctions = ['public_function', 'stripe-webhook']
     ```
   - Alternatively, use the `PUBLIC_FUNCTIONS` environment variable:

     ```typescript
     const publicFunctions = Deno.env.get('PUBLIC_FUNCTIONS')?.split(',') || []
     ```

2. **Redeploy the Gateway**:

   ```bash
   supabase functions deploy main --project-ref your-project-id
   ```

4. **Secure Public Functions**:

   - Implement alternative authentication (e.g., API keys, webhook signatures):

     ```typescript
     // In /home/<USER>/functions/stripe-webhook/index.ts
     const STRIPE_WEBHOOK_SECRET = Deno.env.get('STRIPE_WEBHOOK_SECRET')
     const signature = req.headers.get('stripe-signature')
     // Verify signature using Stripe's library
     ```

### Why Disable JWT?

- **Webhooks**: Services like Stripe use signatures (e.g., `stripe-signature`) instead of JWTs.
- **Public APIs**: Endpoints like status checks need to be accessible without authentication.
- **Testing**: Temporarily disable JWT for debugging or public demos.

## Best Practices

To ensure your Supabase Edge Functions are performant, secure, and maintainable:

### Performance

- **Minimize Dependencies**: Import only necessary modules (e.g., specific `jose` functions) to reduce bundle size.
- **Set Resource Limits**:
  - Use `memoryLimitMb: 150` and `workerTimeoutMs: 60000`.
  - Adjust based on function needs to balance cost and performance.
- **Enable Caching**: Set `noModuleCache: false` to cache modules, reducing cold starts.
- **Early Exit for Public Functions**: Check `skipJwtVerification` before JWT processing to save resources.
- **Handle CORS Efficiently**: Return minimal responses for `OPTIONS` requests.

### Security

- **Enforce Authentication**:
  - Use Supabase Auth tokens for protected functions.
  - For public functions, implement alternative auth (e.g., API keys, signatures).
- **Validate Inputs**:
  - Check URL paths, headers, and payloads (e.g., `service_name`, `Authorization`).
  - Sanitize inputs to prevent injection attacks.
- **Secure Secrets**:
  - Store `JWT_SECRET`, `STRIPE_WEBHOOK_SECRET`, etc., in `.env` or Supabase secrets.
  - Never hardcode secrets.
- **Use HTTPS**: Ensure all requests are served over HTTPS.
- **Limit Public Functions**: Only disable JWT for necessary functions.
- **Error Handling**:
  - Return generic error messages (e.g., `{ msg: "Invalid JWT" }`).
  - Log detailed errors internally.

### Maintainability

- **Modularize Code**:
  - Use functions like `getAuthToken` and `verifyJWT` for reusability.
  - Keep individual functions focused on single responsibilities.
- **Centralize Public Function List**:
  - Use `publicFunctions` or `PUBLIC_FUNCTIONS` env variable.
- **Consistent Error Responses**:
  - Use `{ msg: "error message" }` format with 400, 401, or 500 status codes.
- **Logging**:
  - Log errors and key events (e.g., `console.error`).
  - Avoid excessive logging.
- **Version Dependencies**: Pin versions (e.g., `jose@v4.14.4`).
- **Document Exemptions**: Comment why functions skip JWT.

### Supabase-Specific

- **Leverage Supabase Auth**: Use Supabase’s JWTs for protected functions.
- **Test Locally**: Run `supabase functions serve` to test locally.
- **Monitor Usage**: Use Supabase dashboard or `docker logs supabase-edge-functions`.
- **Deploy Efficiently**: Use `supabase functions deploy`.

## Example: Individual Edge Function

Example of a public function (`/home/<USER>/functions/public_function/index.ts`):

```typescript
import { serve } from 'https://deno.land/std@0.131.0/http/server.ts'

serve(async (req: Request) => {
  try {
    // Optional: Validate alternative auth (e.g., API key)
    const apiKey = req.headers.get('x-api-key')
    if (apiKey && apiKey !== Deno.env.get('API_KEY')) {
      return new Response(JSON.stringify({ msg: 'Invalid API key' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Business logic
    const result = { message: 'This is a public function' }
    return new Response(JSON.stringify(result), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (e) {
    console.error(e)
    return new Response(JSON.stringify({ msg: 'Server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
})
```

- **No JWT Check**: Relies on the gateway to skip JWT verification.
- **Optional Security**: Uses an API key for authentication (adjust as needed).
- **Deploy**: Run `supabase functions deploy public_function`.

  Expected: 200 OK with valid JWT, 401 with invalid/missing JWT.
- **Local Testing**:

  ```bash
  supabase functions serve --project-ref your-project-id
  ```
- **Check Logs**:

  ```bash
  docker logs supabase-edge-functions
  ```

## Troubleshooting

- **JWT Errors (e.g.,** `JWSInvalidSignature`**)**:
  - Ensure `JWT_SECRET` matches Supabase Auth or your token issuer.
  - Verify token format and signing algorithm (e.g., HS256).
- **Environment Variables Not Loading**:
  - Check `.env` and `docker-compose.yml` for correct variable definitions.
  - Restart containers: `docker-compose restart`.
- **CORS Issues**:
  - Update Nginx `cors.conf` to allow `Authorization`, `Content-Type`, etc.
  - Example:

    ```nginx
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type';
    ```
- **Webhook Failures**:
  - For `stripe-webhook`, verify `STRIPE_WEBHOOK_SECRET` and test with Stripe CLI:

    ```bash
    stripe listen --forward-to https://your-domain.com/api/stripe-webhook
    ```
- **Function Not Found**:
  - Ensure function exists in `/home/<USER>/functions/<function_name>` and is deployed.
  - Check `service_name` in URL (e.g., `/api/public_function`).