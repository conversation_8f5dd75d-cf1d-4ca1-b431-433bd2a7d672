-- Drop existing admin-related objects
DROP TABLE IF EXISTS admin_users CASCADE;
DROP FUNCTION IF EXISTS verify_admin CASCADE;
DROP FUNCTION IF EXISTS check_admin_credentials CASCADE;

-- Create standalone admin table
CREATE TABLE standalone_admins (
  id SERIAL PRIMARY KEY,
  username text UNIQUE NOT NULL,
  password_hash text NOT NULL,
  role text NOT NULL CHECK (role IN ('admin', 'editor')),
  created_at timestamptz DEFAULT now(),
  last_login timestamptz
);

-- Enable RLS
ALTER TABLE standalone_admins ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Ad<PERSON> can view own data"
  ON standalone_admins
  FOR SELECT
  USING (true);

-- Function to verify admin credentials
CREATE OR REPLACE FUNCTION verify_admin_login(admin_username text, admin_password text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM standalone_admins
    WHERE username = admin_username
    AND password_hash = crypt(admin_password, password_hash)
  );
END;
$$;

-- Function to update last login
CREATE OR REPLACE FUNCTION update_admin_last_login(admin_username text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE standalone_admins
  SET last_login = now()
  WHERE username = admin_username;
END;
$$;

-- Insert default admin
INSERT INTO standalone_admins (username, password_hash, role)
VALUES (
  '<EMAIL>',
  crypt('11111111', gen_salt('bf')),
  'admin'
);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, anon, authenticated, service_role;