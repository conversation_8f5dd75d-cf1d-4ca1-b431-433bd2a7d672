import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, LucideIcon } from 'lucide-react';

export interface SolutionsDropdownCard {
    name: string;
    path?: string;
    href?: string;
    description: string;
    icon: LucideIcon | React.ComponentType<any>;
    bgColor?: string;
    textColor?: string;
}

interface SolutionsDropdownProps {
    cards: SolutionsDropdownCard[];
    isActive: boolean;
    onClose: () => void;
    gridCols?: string;
}

const CARD_MIN_HEIGHT = 'h-[120px] ';

export default function SolutionsDropdown({
    cards,
    isActive,
    onClose,
}: SolutionsDropdownProps) {
    const renderCard = (card: SolutionsDropdownCard, idx: number, keyPrefix = '') => {
        const IconComponent = card.icon;
        const cardStyle = { background: card.bgColor || 'white', color: card.textColor && !card.textColor.startsWith('text-') ? card.textColor : undefined };
        const textClass = `text-lg font-bold${card.textColor && card.textColor.startsWith('text-') ? ' ' + card.textColor : ''}`;
        const descClass = `text-sm${card.textColor && card.textColor.startsWith('text-') ? ' ' + card.textColor : ''} opacity-90 leading-relaxed`;
        const iconClass = `w-6 h-6${card.textColor}`;
        const arrowClass = `h-4 w-4${card.textColor}`;
        if (card.href) {
            return (
                <a
                    key={keyPrefix + (card.href || idx)}
                    href={card.href}
                    className={`rounded-lg p-4 hover:shadow-lg transition-all duration-200 group/card w-full flex flex-col justify-between relative overflow-hidden ${CARD_MIN_HEIGHT}`}
                    style={cardStyle}
                    onClick={onClose}
                >
                    <div className="absolute top-0 left-0 w-full h-full pointer-events-none"></div>
                    <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-4">
                            <div className="w-6 h-6">
                                {typeof IconComponent === 'function' && IconComponent.name === 'CustomIcon' ? (
                                    <IconComponent />
                                ) : (
                                    <IconComponent className={iconClass} />
                                )}
                            </div>
                            <h3 className={textClass}>{card.name}</h3>
                        </div>
                        <div className="bg-white rounded-md p-2 group-hover/card:translate-x-1 transition-transform duration-200 flex-shrink-0">
                            <ArrowRight className={arrowClass} />
                        </div>
                    </div>
                    <p className={descClass}>{card.description}</p>
                </a>
            );
        }
        return (
            <Link
                key={keyPrefix + (card.path || idx)}
                to={card.path || '/'}
                className={`rounded-lg p-4 hover:shadow-lg transition-all duration-200 group/card w-full flex flex-col justify-between relative overflow-hidden ${CARD_MIN_HEIGHT}`}
                style={cardStyle}
                onClick={onClose}
            >
                <div className="absolute top-0 left-0 w-full h-full pointer-events-none"></div>
                <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-4">
                        <div className="w-6 h-6">
                            {typeof IconComponent === 'function' && IconComponent.name === 'CustomIcon' ? (
                                <IconComponent />
                            ) : (
                                <IconComponent className={iconClass} />
                            )}
                        </div>
                        <h3 className={textClass}>{card.name}</h3>
                    </div>
                    <div className="bg-white rounded-md p-2 group-hover/card:translate-x-1 transition-transform duration-200 flex-shrink-0">
                        <ArrowRight className={arrowClass} />
                    </div>
                </div>
                <p className={descClass}>{card.description}</p>
            </Link>
        );
    };
    return (
        <div
            className={`fixed left-0 px-0 w-fit mx-auto rounded-lg right-0 shadow-xl transition-all duration-200 ${isActive ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible transform -translate-y-2'}`}
            style={{ top: '7.2rem', zIndex: 50 }}
        >
            <div className="max-w-[1900px] bg-white rounded-lg w-full mx-auto px-2 py-2">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                    {cards.map((card, idx) => renderCard(card, idx, 'row1-'))}
                </div>
            </div>
        </div>
    );
}
