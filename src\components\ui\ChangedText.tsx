import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface ChangedTextProps {
  name: string;
}

const ChangedText: React.FC<ChangedTextProps> = ({ name }) => {
  const [text, setText] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const { data, error } = await supabase
          .from('site_settings')
          .select('settings')
          .order('created_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        
        if (data && data.length > 0) {
          const settings = data[0].settings;
          
          if (name.toLowerCase() === 'soar') {
            setText(settings.Soar_modifiedText || '');
          } else if (name.toLowerCase() === 'grant') {
            setText(settings.Grant_modifiedText || '');
          }
        }
      } catch (error) {
        console.error('Error fetching settings:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [name]);

  if (loading) {
    return <p className="text-gray-500">Loading...</p>;
  }

  if (!text) {
    return null;
  }

  return (
    <p id={name.toLowerCase()} className="text-black text-center py-4 leading-relaxed max-w-4xl mx-auto">
      {text}
    </p>
  );
};

export default ChangedText;