import React, { useState } from "react";
import { X, Download, ExternalLink, Mail, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { InvoicePDFDownload } from "../../../components/InvoicePDF";
import { generateAndUploadInvoicePDF } from "../../../components/InvoicePDFUploader";
import { supabase } from "../../../lib/supabase";

// Use the same Invoice interface as in InvoicesTable
interface InvoiceItem {
  product_name: string;
  price: number;
  quantity: number;
  billing_cycle: string;
  amount: number;
}

interface Invoice {
  id: string;
  stripe_invoice_id: string;
  stripe_customer_id: string | null;
  customer_email: string | null;
  customer_name: string | null;
  status: string | null;
  amount_due: number | null;
  amount_paid: number | null;
  amount_remaining: number | null;
  currency: string | null;
  due_date: string | null;
  invoice_pdf: string | null;
  hosted_invoice_url: string | null;
  created_at: string;
  is_manual?: boolean;
  line_items?: Array<InvoiceItem>;
  payment_method?: {
    type: string;
    bank_name?: string;
    account_number?: string;
    routing_number?: string;
  };
  notes?: string;
  ref?: string; // 8-character reference code
}

interface InvoiceDetailsModalProps {
  invoice: Invoice;
  onClose: () => void;
}

const InvoiceDetailsModal: React.FC<InvoiceDetailsModalProps> = ({
  invoice,
  onClose,
}) => {
  // State for email modal
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [emailSubject, setEmailSubject] = useState("");
  const [emailBody, setEmailBody] = useState("");
  const [emailTo, setEmailTo] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [emailSuccess, setEmailSuccess] = useState(false);

  // Helper function to format currency
  const formatCurrency = (amount: number | null, currency: string | null) => {
    if (amount === null || currency === null) return "N/A";
    // Stripe amounts are usually in cents
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase() || "USD",
    }).format(amount / 100);
  };

  // Helper function to format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "PPp"); // e.g., Sep 21, 2023, 10:30:00 AM
    } catch {
      return "Invalid Date";
    }
  };

  // Format payment method display
  const formatPaymentMethod = (paymentMethod: any) => {
    if (!paymentMethod) return "Not specified";

    const type = paymentMethod.type
      .replace("_", " ")
      .replace(/\b\w/g, (l: string) => l.toUpperCase());

    if (paymentMethod.type === "bank_transfer" && paymentMethod.bank_name) {
      return (
        <>
          {type}
          <div className="text-sm text-gray-500">
            Bank: {paymentMethod.bank_name}
            {paymentMethod.account_number && (
              <div>Account: {paymentMethod.account_number}</div>
            )}
            {paymentMethod.routing_number && (
              <div>Routing: {paymentMethod.routing_number}</div>
            )}
          </div>
        </>
      );
    }

    return type;
  };

  // Function to open email modal with default values
  const handleOpenEmailModal = () => {
    // Set default email values
    const invoiceRef = invoice.ref || invoice.id.substring(0, 8);
    const defaultSubject = `Invoice #${invoiceRef} from International Responder Systems`;

    // Create default email body
    const defaultBody = `
      <p>Dear ${invoice.customer_name || "Valued Customer"},</p>

      <p>Please find attached your invoice #${invoiceRef} for the amount of ${formatCurrency(
      invoice.amount_due,
      invoice.currency || "usd"
    )}.</p>

      <p>Invoice details:</p>
      <ul>
        <li>Invoice #: ${invoiceRef}</li>
        <li>Date: ${formatDate(invoice.created_at)}</li>
        <li>Due Date: ${formatDate(invoice.due_date)}</li>
        <li>Amount Due: ${formatCurrency(
          invoice.amount_due,
          invoice.currency || "usd"
        )}</li>
        <li>Status: ${invoice.status?.toUpperCase() || "N/A"}</li>
      </ul>

      <p>If you have any questions regarding this invoice, please don't hesitate to contact us.</p>

      <p>Thank you for your business!</p>

      <p>Best regards,<br>
      International Responder Systems<br>
      <EMAIL></p>
    `;

    setEmailSubject(defaultSubject);
    setEmailBody(defaultBody);
    setEmailTo(invoice.customer_email || "");
    setEmailError(null);
    setEmailSuccess(false);
    setShowEmailModal(true);
  };

  // Function to send email
  const handleSendEmail = async () => {
    // Validate inputs
    if (!emailTo) {
      setEmailError("Recipient email is required");
      return;
    }

    if (!emailSubject) {
      setEmailError("Email subject is required");
      return;
    }

    if (!emailBody) {
      setEmailError("Email body is required");
      return;
    }

    setIsSending(true);
    setEmailError(null);

    try {
      // Prepare the invoice data for PDF generation
      const invoiceData = {
        ...invoice,
        status: invoice.status || "draft",
        amount_due: invoice.amount_due || 0,
        amount_paid: invoice.amount_paid || 0,
        amount_remaining: invoice.amount_remaining || 0,
        currency: invoice.currency || "usd",
        due_date: invoice.due_date || "",
        created_at: invoice.created_at,
        line_items: invoice.line_items || [],
        is_manual: invoice.is_manual,
        payment_method: invoice.payment_method,
        notes: invoice.notes,
      };

      // Generate and upload the PDF
      console.log("Generating and uploading PDF...");
      const pdfUrl = await generateAndUploadInvoicePDF(invoiceData);
      console.log("PDF uploaded successfully:", pdfUrl);

      // Call the Edge Function to send the email with the PDF URL
      const { error } = await supabase.functions.invoke("send-invoice-email", {
        body: {
          to: emailTo,
          subject: emailSubject,
          html: emailBody,
          pdfUrl: pdfUrl,
          invoiceRef: invoice.ref || invoice.id.substring(0, 8),
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      // Show success message
      setEmailSuccess(true);

      // Close modal after 2 seconds
      setTimeout(() => {
        setShowEmailModal(false);
        setEmailSuccess(false);
      }, 2000);
    } catch (err: any) {
      console.error("Error sending email:", err);
      setEmailError(err.message || "Failed to send email");
    } finally {
      setIsSending(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">
            Invoice Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Invoice Header */}
          <div className="flex flex-col md:flex-row justify-between mb-6">
            <div className="mb-4 md:mb-0">
              <h3 className="text-lg font-medium text-gray-900 mb-1">
                Invoice Information
              </h3>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Reference:</span>{" "}
                {invoice.ref || invoice.id.substring(0, 8)}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Status:</span>{" "}
                <span
                  className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    invoice.status === "paid"
                      ? "bg-green-100 text-green-800"
                      : invoice.status === "open"
                      ? "bg-blue-100 text-blue-800"
                      : invoice.status === "draft"
                      ? "bg-gray-100 text-gray-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {invoice.status}
                </span>
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Type:</span>{" "}
                {invoice.is_manual ? "Manual" : "Stripe"}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Created:</span>{" "}
                {formatDate(invoice.created_at)}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Due Date:</span>{" "}
                {formatDate(invoice.due_date)}
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-1">
                Customer Information
              </h3>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Name:</span>{" "}
                {invoice.customer_name || "N/A"}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Email:</span>{" "}
                {invoice.customer_email || "N/A"}
              </p>
              {invoice.stripe_customer_id && (
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Stripe ID:</span>{" "}
                  {invoice.stripe_customer_id}
                </p>
              )}
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-1">
                Payment Information
              </h3>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Amount Due:</span>{" "}
                {formatCurrency(invoice.amount_due, invoice.currency)}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Amount Paid:</span>{" "}
                {formatCurrency(invoice.amount_paid, invoice.currency)}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Remaining:</span>{" "}
                {formatCurrency(invoice.amount_remaining, invoice.currency)}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Payment Method:</span>{" "}
                {formatPaymentMethod(invoice.payment_method)}
              </p>
            </div>
          </div>

          {/* Notes Section */}
          {invoice.notes && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <h3 className="text-md font-medium text-gray-900 mb-2">Notes</h3>
              <p className="text-sm text-gray-600 whitespace-pre-wrap">
                {invoice.notes}
              </p>
            </div>
          )}

          {/* Line Items */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">
              Line Items
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Product
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Billing Cycle
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Price
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Quantity
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Amount
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {invoice.line_items && invoice.line_items.length > 0 ? (
                    invoice.line_items.map((item, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.product_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.billing_cycle}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                          {formatCurrency(item.price, invoice.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                          {item.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                          {formatCurrency(item.amount, invoice.currency)}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={5}
                        className="px-6 py-4 text-center text-sm text-gray-500"
                      >
                        No line items available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end items-center p-6 border-t border-gray-200 gap-4">
          {/* Email Invoice Button - Show for all invoices */}
          {invoice.customer_email && (
            <button
              onClick={handleOpenEmailModal}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <Mail className="h-4 w-4 mr-2" />
              Email Invoice
            </button>
          )}

          {/* PDF Download Button */}
          {invoice.is_manual && invoice.line_items ? (
            <InvoicePDFDownload
              invoice={{
                ...invoice,
                status: invoice.status || "draft",
                amount_due: invoice.amount_due || 0,
                amount_paid: invoice.amount_paid || 0,
                amount_remaining: invoice.amount_remaining || 0,
                currency: invoice.currency || "usd",
                due_date: invoice.due_date || "",
                created_at: invoice.created_at,
                line_items: invoice.line_items || [],
                is_manual: invoice.is_manual,
                payment_method: invoice.payment_method,
                notes: invoice.notes,
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </InvoicePDFDownload>
          ) : invoice.hosted_invoice_url ? (
            <a
              href={invoice.hosted_invoice_url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View on Stripe
            </a>
          ) : null}

          <button
            onClick={onClose}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Close
          </button>
        </div>
      </div>

      {/* Email Modal */}
      {showEmailModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Email Invoice
              </h3>
              <button
                onClick={() => setShowEmailModal(false)}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {emailError && (
              <div className="mb-4 p-3 bg-red-50 text-red-700 text-sm rounded-md">
                {emailError}
              </div>
            )}

            {emailSuccess && (
              <div className="mb-4 p-3 bg-green-50 text-green-700 text-sm rounded-md">
                Email sent successfully!
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label
                  htmlFor="email-to"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  To
                </label>
                <input
                  type="email"
                  id="email-to"
                  value={emailTo}
                  onChange={(e) => setEmailTo(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  disabled={isSending}
                />
              </div>

              <div>
                <label
                  htmlFor="email-subject"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Subject
                </label>
                <input
                  type="text"
                  id="email-subject"
                  value={emailSubject}
                  onChange={(e) => setEmailSubject(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  disabled={isSending}
                />
              </div>

              <div>
                <label
                  htmlFor="email-body"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Message
                </label>
                <textarea
                  id="email-body"
                  value={emailBody}
                  onChange={(e) => setEmailBody(e.target.value)}
                  rows={10}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  disabled={isSending}
                />
                <p className="mt-1 text-xs text-gray-500">
                  HTML formatting is supported.
                </p>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowEmailModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  disabled={isSending}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSendEmail}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                  disabled={isSending}
                >
                  {isSending ? (
                    <>
                      <Loader2 className="animate-spin h-4 w-4 mr-2" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="h-4 w-4 mr-2" />
                      Send Email
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoiceDetailsModal;
