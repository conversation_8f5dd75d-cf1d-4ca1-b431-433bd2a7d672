-- Drop all existing policies from blog_posts
DROP POLICY IF EXISTS "blog_posts_read_all" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_all" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_insert" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_update" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_delete" ON blog_posts;

-- Disable RLS on blog_posts table
ALTER TABLE blog_posts DISABLE ROW LEVEL SECURITY;

-- Grant full access to all roles
GRANT ALL ON blog_posts TO authenticated;
GRANT ALL ON blog_posts TO anon;
GRANT ALL ON blog_posts TO service_role;

-- Keep the validation trigger for data integrity
CREATE OR REPLACE FUNCTION validate_blog_post()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Validate title
  IF length(trim(NEW.title)) = 0 THEN
    RAISE EXCEPTION 'Title cannot be empty';
  END IF;

  -- Validate content
  IF length(trim(NEW.content)) = 0 THEN
    RAISE EXCEPTION 'Content cannot be empty';
  END IF;

  -- Validate status
  IF NEW.status NOT IN ('draft', 'published', 'archived') THEN
    RAISE EXCEPTION 'Invalid status value';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Ensure the validation trigger exists
DROP TRIGGER IF EXISTS validate_blog_post_trigger ON blog_posts;
CREATE TRIGGER validate_blog_post_trigger
  BEFORE INSERT OR UPDATE ON blog_posts
  FOR EACH ROW
  EXECUTE FUNCTION validate_blog_post();