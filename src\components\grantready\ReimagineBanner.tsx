import React from 'react';
import { Link } from 'react-router-dom';

// Import assets
import backgroundImage from '../../assets/images/grantready/reimagine-background-withoutbg.png';
import circleAnimationVideo from '../../assets/images/grantready/reimagine-grantwork-smaller.mp4';
import dashboardScreenshot from '../../assets/images/grantready/dashboard-screenshot.png';

const ReimagineBanner: React.FC = () => {
    return (
        <section className="bg-white relative w-full overflow-hidden min-h-screen flex flex-col" style={{ backgroundColor: '#141215' }}>
            {/* Full Background Video - Golden Circle Animation */}
            <div className="absolute inset-0 w-full h-full overflow-hidden flex justify-center items-center">
                <video
                    autoPlay
                    muted
                    loop
                    playsInline
                    className="object-cover"
                    style={{
                        opacity: 0.8,
                        transform: 'translateY(-10%)',
                        width: '100%',
                        height: '90%',
                    }}
                >
                    <source src={circleAnimationVideo} type="video/mp4" />
                </video>
            </div>

            {/* Centered Background Image on top of video */}
            <div className="hidden md:flex absolute max-w-7xl mx-auto inset-0 w-full h-full justify-center items-center" style={{ zIndex: 1, transform: 'translateY(-11%)' }}>
                <img
                    src={backgroundImage}
                    alt="Reimagine Background"
                    className="w-full h-auto object-contain"
                    style={{ opacity: 0.8, filter: 'grayscale(100%)' }}
                />
            </div>



            {/* Content Container - using flexbox for better zoom compatibility */}
            <div className="relative z-20 flex-1 flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 py-12">
                {/* Title Section */}
                <div className="flex-1 flex items-end justify-center pb-4 mt-40">
                    <div className="text-center animate-fadeInUp" style={{ animationDelay: '0.2s', transform: 'translateY(100%)' }}>
                        <h1
                            className="text-white uppercase"
                            style={{
                                fontFamily: 'Platform',
                                fontWeight: 500,
                                fontSize: '60px',
                                lineHeight: '70px',
                                letterSpacing: '0%',
                                textAlign: 'center',
                                textTransform: 'uppercase',
                                textShadow: '0 0 20px rgba(0, 0, 0, 0.8), 0 0 40px rgba(0, 0, 0, 0.6)',
                            }}
                        >
                            Re-imagine<br />
                            Grant Work
                        </h1>
                    </div>
                </div>

                {/* Dashboard Screenshot with Description and Button */}
                <div className="flex-1 flex flex-col justify-end items-center w-full pb-8">
                    <div className="max-w-4xl w-full flex flex-col items-center space-y-6">
                        {/* Dashboard Screenshot */}
                        <div className="animate-fadeInUp w-full" style={{ animationDelay: '0.4s' }}>
                            <div
                                className="relative rounded-2xl overflow-hidden"
                                style={{
                                    border: '9px solid rgba(79, 99, 217, 0.37)',
                                    boxShadow: '0 0 60px rgba(79, 99, 217, 0.5), 0 0 120px rgba(79, 99, 217, 0.3)',
                                }}
                            >
                                <img
                                    src={dashboardScreenshot}
                                    alt="GrantReady Dashboard"
                                    className="w-full h-auto object-cover"
                                />
                            </div>
                        </div>

                        {/* Description */}
                        <div className="text-center animate-fadeInUp" style={{ animationDelay: '0.6s' }}>
                            <p className="text-lg sm:text-xl md:text-2xl text-white font-normal leading-relaxed mb-6" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
                                An easy, automated, and intuitive solution ensuring state, local and rural jurisdictions are maximizing grant funds they have been awarded. Reduce months of work to a few weeks
                            </p>
                        </div>

                        {/* CTA Button */}
                        <div className="flex justify-center animate-fadeInUp" style={{ animationDelay: '0.8s' }}>
                            <Link
                                to="/book-a-demo"
                                className="group relative text-white border border-white px-4 py-2 rounded-md font-medium text-sm flex items-center gap-3 hover:bg-white hover:text-black transition-all duration-300"
                                style={{
                                    fontFamily: 'Inter, system-ui, sans-serif',
                                    textTransform: 'capitalize'
                                }}
                            >
                                <span className="relative z-10 px-3">Book A Demo</span>
                                <div className="bg-white rounded-sm p-2.5 group-hover:bg-black transition-colors duration-300">
                                    <svg
                                        width="16"
                                        height="16"
                                        viewBox="0 0 16 16"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="text-black group-hover:text-white transition-colors duration-300"
                                    >
                                        <path
                                            d="M3.33333 8H12.6667M8 3.33333L12.6667 8L8 12.6667"
                                            stroke="currentColor"
                                            strokeWidth="1.33"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                        />
                                    </svg>
                                </div>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            {/* Custom CSS for animations */}
            <style dangerouslySetInnerHTML={{
                __html: `
                    @keyframes fadeInUp {
                        0% {
                            opacity: 0;
                            transform: translateY(30px);
                        }
                        100% {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    .animate-fadeInUp {
                        animation: fadeInUp 0.8s ease-out forwards;
                        opacity: 0;
                    }

                    .background-image {
                        display: none;
                    }

                    @media (min-width: 768px) {
                        .background-image {
                            display: block;
                            width: auto;
                            height: auto;
                            object-fit: contain;
                        }
                    }
                `
            }} />
        </section>
    );
};

export default ReimagineBanner;
