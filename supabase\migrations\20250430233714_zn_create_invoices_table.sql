-- Invoices Table
CREATE TABLE invoices (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE SET NULL, -- Link to the user/customer
    stripe_invoice_id TEXT UNIQUE NOT NULL, -- Stripe's Invoice ID
    stripe_customer_id TEXT, -- Store Stripe Customer ID for easier lookup
    status TEXT NOT NULL, -- e.g., 'draft', 'open', 'paid', 'uncollectible', 'void'
    amount_due BIGINT, -- Amount in cents
    amount_paid BIGINT, -- Amount in cents
    amount_remaining BIGINT, -- Amount in cents
    currency TEXT DEFAULT 'usd',
    due_date TIMESTAMPTZ,
    invoice_pdf TEXT, -- URL to the PDF hosted by Stripe
    hosted_invoice_url TEXT, -- URL to the Stripe hosted invoice page
    created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Optional: Indexes for faster querying
CREATE INDEX idx_invoices_user_id ON invoices(user_id);
CREATE INDEX idx_invoices_stripe_invoice_id ON invoices(stripe_invoice_id);
CREATE INDEX idx_invoices_status ON invoices(status);

-- RLS Policies (Example - Adapt to your security needs)
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow admin read access" ON invoices
    FOR SELECT USING (is_admin(auth.uid())); -- Assuming you have an is_admin function

CREATE POLICY "Allow admin insert access" ON invoices
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Allow admin update access" ON invoices
    FOR UPDATE USING (is_admin(auth.uid()));

-- Add other policies as needed, e.g., allowing service roles for webhooks

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = timezone('utc'::text, now()); 
   RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_invoices_updated_at
BEFORE UPDATE ON invoices
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();