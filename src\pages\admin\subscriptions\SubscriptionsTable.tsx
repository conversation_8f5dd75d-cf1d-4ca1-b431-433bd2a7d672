import React, { useState } from 'react';
import {
    Check,
    X,
    Edit,
    Trash2,
    FileText,
    ChevronLeft,
    ChevronRight,
    Search
} from 'lucide-react';

// Ensure user_full_name and period are present
interface Subscription {
    id: string;
    user_id: string;
    status: string;
    // --- Ensure period type matches DB ---
    period: 'month' | 'year'; // Correct type
    current_period_start: string;
    current_period_end: string;
    cancel_at_period_end: boolean;
    created_at: string;
    quantity: number;
    deleted: boolean;
    deleted_at?: string | null;
    ref?: string | null;
    stripe_subscription_id?: string | null;
    user_email?: string;
    user_full_name?: string | null; // Add this for Full Name column
}

interface SubscriptionsTableProps {
    subscriptions: Subscription[];
    isEditing: boolean;
    currentSubscription: Subscription | null;
    onEdit: (subscription: Subscription) => void;
    onDelete: (id: string) => void;
    onSaveEdit: () => void;
    onCancelEdit: () => void;
    setCurrentSubscription: React.Dispatch<React.SetStateAction<Subscription | null>>;
    onViewDetails: (subscription: Subscription) => void;
    // Pagination props
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    onPageChange: (page: number) => void;
    onItemsPerPageChange: (itemsPerPage: number) => void;
    // --- Add filter props ---
    statusFilter: string;
    onStatusFilterChange: (status: string) => void;
    // --- Update billing cycle filter prop types ---
    billingCycleFilter: 'all' | 'month' | 'year'; // Use 'month'/'year'
    onBillingCycleFilterChange: (cycle: 'all' | 'month' | 'year') => void; // Use 'month'/'year'
}

const SubscriptionsTable: React.FC<SubscriptionsTableProps> = ({
    subscriptions,
    isEditing,
    currentSubscription,
    onSaveEdit,
    onCancelEdit,
    setCurrentSubscription,
    onViewDetails,
    // Pagination props
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    onPageChange,
    onItemsPerPageChange,
    // --- Destructure filter props ---
    statusFilter,
    onStatusFilterChange,
    // --- Destructure updated billing cycle filter props ---
    billingCycleFilter,
    onBillingCycleFilterChange
}) => {
    const [searchQuery, setSearchQuery] = useState('');

    // Items per page options
    const itemsPerPageOptions = [10, 25, 50, 100];
    // Status options
    const statusOptions = ['all', 'active', 'trialing', 'past_due', 'canceled', 'pending cancellation']; // Add more as needed
    // --- Billing cycle options ---
    const billingCycleOptions: Array<'all' | 'month' | 'year'> = ['all', 'month', 'year']; // Use 'month'/'year'

    // Helper function to format date as MM-DD-YYYY
    const formatDateMMDDYYYY = (dateString: string | null | undefined): string => {
        if (!dateString) return 'N/A';
        try {
            const date = new Date(dateString);
            // Ensure date is valid
            if (isNaN(date.getTime())) {
                return 'Invalid Date';
            }
            const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
            const day = date.getDate().toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${month}-${day}-${year}`;
        } catch (error) {
            console.error("Error formatting date:", dateString, error);
            return 'Invalid Date';
        }
    };


    const filteredSubscriptions = subscriptions.filter(subs => {
        // Format dates first for consistent searching
        const formattedPeriodEnd = formatDateMMDDYYYY(subs.current_period_end);
        const formattedCreated = formatDateMMDDYYYY(subs.created_at);
        const lowerCaseQuery = searchQuery.toLowerCase(); // Convert search query once

        // Check if search query matches any relevant field, including formatted dates
        const matchesSearch = (subs.ref?.toLowerCase() || '').includes(lowerCaseQuery) ||
            (subs.user_email?.toLowerCase() || '').includes(lowerCaseQuery) ||
            (subs.user_full_name?.toLowerCase() || '').includes(lowerCaseQuery) ||
            formattedPeriodEnd.toLowerCase().includes(lowerCaseQuery) || // Search the formatted period end date
            formattedCreated.toLowerCase().includes(lowerCaseQuery);    // Search the formatted created date

        return matchesSearch;
    });

    return (
        <div className="overflow-x-auto">
            {/* --- Filter Section --- */}
            <div className="mb-4 flex flex-wrap items-center justify-end gap-8">
                {/* Status Filter */}
                <div className="flex items-center space-x-2">
                    <label htmlFor="statusFilter" className="text-sm font-medium text-gray-700 whitespace-nowrap">
                        Status:
                    </label>
                    <select
                        id="statusFilter"
                        name="statusFilter"
                        value={statusFilter}
                        onChange={(e) => onStatusFilterChange(e.target.value)}
                        // --- Enhanced Styling ---
                        className="block w-40 pl-3 pr-10 py-2 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent sm:text-sm shadow-sm appearance-none" // Added border, rounded-lg, focus styles, appearance-none
                    >
                        {statusOptions.map(status => (
                            <option key={status} value={status} className="capitalize">
                                {status.replace(/_/g, ' ')}
                            </option>
                        ))}
                    </select>
                </div>

                {/* --- Billing Cycle Filter --- */}
                <div className="flex items-center space-x-2">
                    <label htmlFor="billingCycleFilter" className="text-sm font-medium text-gray-700 whitespace-nowrap">
                        Billing Cycle:
                    </label>
                    <select
                        id="billingCycleFilter"
                        name="billingCycleFilter"
                        value={billingCycleFilter}
                        onChange={(e) => onBillingCycleFilterChange(e.target.value as 'all' | 'month' | 'year')}
                        // --- Enhanced Styling ---
                        className="block w-32 pl-3 pr-10 py-2 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent sm:text-sm shadow-sm appearance-none" // Added border, rounded-lg, focus styles, appearance-none
                    >
                        {billingCycleOptions.map(cycle => (
                            <option key={cycle} value={cycle} className="capitalize">
                                {cycle}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Search Input - Adjusted width and placeholder */}
                <div className="relative flex-grow sm:flex-grow-0 w-full sm:w-64"> {/* Control width */}
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                        type="text"
                        // --- Updated Placeholder ---
                        placeholder="Search by Ref, User, Name, Dates..." // Updated placeholder
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        // Consistent styling with selects
                        className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent sm:text-sm"
                    />
                </div>
            </div>
            {/* --- End Filter Section --- */}

            <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                    <tr>
                        {/* Keep User (Email) */}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ref</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        {/* Add Full Name */}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Full Name</th>
                        {/* Remove Product column */}
                        {/* <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th> */}
                        {/* Keep Status */}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        {/* Add Billing Cycle */}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Billing Cycle</th>
                        {/* Keep Period End */}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period End At</th>
                        {/* Keep Created */}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                        {/* Keep Actions */}
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                    {filteredSubscriptions.length > 0 ? (
                        filteredSubscriptions.map((subscription) => (
                            <tr key={subscription.id}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 uppercase font-bold">
                                    #{subscription.ref || "N/A"}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {subscription.user_email || subscription.user_id}
                                </td>
                                {/* Full Name */}
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {subscription.user_full_name || 'N/A'}
                                </td>

                                <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full capitalize ${subscription.status === 'active' ? 'bg-green-100 text-green-800' :
                                        subscription.status === 'trialing' ? 'bg-blue-100 text-blue-800' :
                                            subscription.status === 'canceled' ? 'bg-red-100 text-red-800' :
                                                'bg-yellow-100 text-yellow-800' // Consider adding 'pending cancellation' style
                                        }`}>
                                        {subscription.status}
                                    </span>
                                </td>
                                {/* Billing Cycle Cell */}
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize">
                                    {/* Display the correct 'month' or 'year' value */}
                                    {subscription.period}
                                </td>
                                {/* Period End Cell */}
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {isEditing && currentSubscription?.id === subscription.id ? (
                                        <input
                                            type="date"
                                            className="w-full p-1 border rounded"
                                            value={currentSubscription.current_period_end ? new Date(currentSubscription.current_period_end).toISOString().split('T')[0] : ''}
                                            onChange={(e) => setCurrentSubscription(prev => prev ? {
                                                ...prev,
                                                current_period_end: new Date(e.target.value).toISOString()
                                            } : null)}
                                        />
                                    ) : (
                                        formatDateMMDDYYYY(subscription.current_period_end) // Use the helper function
                                    )}
                                </td>
                                {/* Created Cell */}
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {formatDateMMDDYYYY(subscription.created_at)} {/* Use the helper function */}
                                </td>
                                {/* Actions Cell */}
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    {isEditing && currentSubscription?.id === subscription.id ? (
                                        <div className="flex justify-end space-x-2">
                                            <button
                                                onClick={onSaveEdit}
                                                className="text-green-600 hover:text-green-900"
                                            >
                                                <Check className="h-5 w-5" />
                                            </button>
                                            <button
                                                onClick={onCancelEdit}
                                                className="text-red-600 hover:text-red-900"
                                            >
                                                <X className="h-5 w-5" />
                                            </button>
                                        </div>
                                    ) : (
                                        <div className="flex justify-end space-x-2">
                                            <button
                                                onClick={() => onViewDetails(subscription)}
                                                className="text-indigo-600 hover:text-indigo-900"
                                                title="View Details"
                                            >
                                                <FileText className="h-5 w-5" />
                                            </button>
                                        </div>
                                    )}
                                </td>
                            </tr>
                        ))
                    ) : (
                        <tr>
                            {/* Adjust colSpan to match the number of columns (currently 8) */}
                            <td colSpan={8} className="px-6 py-8 text-center">
                                <div className="flex flex-col items-center justify-center space-y-2">
                                    <FileText className="h-12 w-12 text-gray-400" />
                                    <h3 className="text-lg font-medium text-gray-900">
                                        No Subscriptions Found
                                    </h3>
                                    <p className="text-sm text-gray-500">No subscriptions match your current filters.</p>
                                </div>
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>

            {/* Pagination Controls */}
            <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4">
                <div className="flex flex-1 justify-between sm:hidden">
                    <button
                        onClick={() => onPageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'}`}
                    >
                        Previous
                    </button>
                    <button
                        onClick={() => onPageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'}`}
                    >
                        Next
                    </button>
                </div>
                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p className="text-sm text-gray-700">
                            Showing <span className="font-medium">{totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1}</span> to{' '}
                            <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalItems)}</span> of{' '}
                            <span className="font-medium">{totalItems}</span> results
                        </p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div>
                            <label htmlFor="itemsPerPage" className="mr-2 text-sm text-gray-700">Items per page:</label>
                            <select
                                id="itemsPerPage"
                                value={itemsPerPage}
                                onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
                                className="rounded border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                {itemsPerPageOptions.map(option => (
                                    <option key={option} value={option}>{option}</option>
                                ))}
                            </select>
                        </div>
                        <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <button
                                onClick={() => onPageChange(currentPage - 1)}
                                disabled={currentPage === 1}
                                className={`relative inline-flex items-center rounded-l-md px-2 py-2 ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-400 hover:bg-gray-50'}`}
                            >
                                <span className="sr-only">Previous</span>
                                <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                            </button>

                            {/* Page numbers */}
                            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                // Logic to show pages around current page
                                let pageNum;
                                if (totalPages <= 5) {
                                    pageNum = i + 1;
                                } else if (currentPage <= 3) {
                                    pageNum = i + 1;
                                } else if (currentPage >= totalPages - 2) {
                                    pageNum = totalPages - 4 + i;
                                } else {
                                    pageNum = currentPage - 2 + i;
                                }

                                return (
                                    <button
                                        key={pageNum}
                                        onClick={() => onPageChange(pageNum)}
                                        className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${currentPage === pageNum ? 'bg-blue-600 text-white focus:z-20' : 'text-gray-900 hover:bg-gray-50'}`}
                                    >
                                        {pageNum}
                                    </button>
                                );
                            })}

                            <button
                                onClick={() => onPageChange(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className={`relative inline-flex items-center rounded-r-md px-2 py-2 ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-400 hover:bg-gray-50'}`}
                            >
                                <span className="sr-only">Next</span>
                                <ChevronRight className="h-5 w-5" aria-hidden="true" />
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default SubscriptionsTable;
