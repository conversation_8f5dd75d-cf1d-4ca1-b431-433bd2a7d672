import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

export default function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    // First immediate scroll
    window.scrollTo(0, 0);

    // Then use multiple timeouts to ensure it works even if other code tries to scroll
    const timeoutId1 = setTimeout(() => {
      window.scrollTo(0, 0);
    }, 0);

    const timeoutId2 = setTimeout(() => {
      window.scrollTo(0, 0);
    }, 50);

    const timeoutId3 = setTimeout(() => {
      window.scrollTo(0, 0);
    }, 100);

    return () => {
      clearTimeout(timeoutId1);
      clearTimeout(timeoutId2);
      clearTimeout(timeoutId3);
    };
  }, [pathname]);

  return null;
}
