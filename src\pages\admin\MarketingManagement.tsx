import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { Mail, Send, Users, ChevronRight, Pie<PERSON>hart, BarChart, Calendar, Edit, Trash2, Plus, AlertCircle, CheckCircle, X } from 'lucide-react';

interface EmailCampaign {
  id: string;
  name: string;
  subject: string;
  content: string;
  status: 'draft' | 'scheduled' | 'sent';
  scheduled_for?: string;
  sent_at?: string;
  opens: number;
  clicks: number;
  recipients: number;
}

interface Subscriber {
  id: string;
  email: string;
  full_name: string;
  subscribed_at: string;
  status: 'active' | 'unsubscribed';
  last_email_sent?: string;
}

export default function MarketingManagement() {
  const [campaigns, setCampaigns] = useState<EmailCampaign[]>([]);
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    content: '',
    scheduled_for: '',
    status: 'draft' as 'draft' | 'scheduled', // Add status to form data state
  });

  useEffect(() => {
    fetchCampaigns();
    fetchSubscribers();

    // Auto-dismiss messages after 3 seconds
    const timer = setTimeout(() => {
      if (error || success) {
        setError(null);
        setSuccess(null);
      }
    }, 3000);

    return () => clearTimeout(timer);
  }, [error, success]);

  const fetchCampaigns = async () => {
    try {
      const { data, error } = await supabase
        .from('email_campaigns')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setCampaigns(data || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const fetchSubscribers = async () => {
    try {
      const { data, error } = await supabase
        .from('subscribers')
        .select('*')
        .order('subscribed_at', { ascending: false });

      if (error) throw error;
      setSubscribers(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Add these functions inside the MarketingManagement component, after fetchSubscribers

  const handleEditCampaign = (campaign: EmailCampaign) => {
    setFormData({
      name: campaign.name,
      subject: campaign.subject,
      content: campaign.content,
      scheduled_for: campaign.scheduled_for ? new Date(campaign.scheduled_for).toISOString().substring(0, 16) : '', // Format for datetime-local
      // Set initial status based on campaign, default to draft if not scheduled
      status: campaign.status === 'scheduled' ? 'scheduled' : 'draft',
    });
    setShowForm(true);
    setEditingId(campaign.id);
  };

  const handleUpdateCampaign = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      // Validate: If status is 'scheduled', scheduled_for must exist
      if (formData.status === 'scheduled' && !formData.scheduled_for) {
        throw new Error('Please provide a schedule date/time for scheduled campaigns.');
      }
      // Convert scheduled_for to UTC only if status is 'scheduled'
      const scheduledForUTC = formData.status === 'scheduled' && formData.scheduled_for
        ? new Date(formData.scheduled_for).toISOString()
        : null;

      const { error } = await supabase
        .from('email_campaigns')
        .update({
          name: formData.name,
          subject: formData.subject,
          content: formData.content,
          scheduled_for: scheduledForUTC, // Use potentially null UTC value
          status: formData.status, // Use status from form
        })
        .eq('id', editingId);

      if (error) throw error;

      setSuccess('Campaign updated successfully!');
      setShowForm(false);
      setEditingId(null);
      // Reset form data including status
      setFormData({
        name: '',
        subject: '',
        content: '',
        scheduled_for: '',
        status: 'draft',
      });
      fetchCampaigns();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCampaign = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this campaign?')) {
      try {
        const { error } = await supabase
          .from('email_campaigns')
          .delete()
          .eq('id', id);

        if (error) throw error;

        setSuccess('Campaign deleted successfully!');
        fetchCampaigns();
      } catch (err: any) {
        setError(err.message);
      }
    }
  };

  const handleUnsubscribe = async (id: string) => {
    if (window.confirm('Are you sure you want to unsubscribe this user?')) {
      try {
        const { error } = await supabase
          .from('subscribers')
          .update({ status: 'unsubscribed' })
          .eq('id', id);

        if (error) throw error;

        setSuccess('User unsubscribed successfully!');
        fetchSubscribers();
      } catch (err: any) {
        setError(err.message);
      }
    }
  };

  const handleReactivate = async (id: string) => {
    if (window.confirm('Are you sure you want to reactivate this subscriber?')) {
      try {
        const { error } = await supabase
          .from('subscribers')
          .update({ status: 'active' })
          .eq('id', id);

        if (error) throw error;

        setSuccess('Subscriber reactivated successfully!');
        fetchSubscribers();
      } catch (err: any) {
        setError(err.message);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      // Validate: If status is 'scheduled', scheduled_for must exist
      if (formData.status === 'scheduled' && !formData.scheduled_for) {
        throw new Error('Please provide a schedule date/time for scheduled campaigns.');
      }
      // Convert scheduled_for to UTC only if status is 'scheduled'
      const scheduledForUTC = formData.status === 'scheduled' && formData.scheduled_for
        ? new Date(formData.scheduled_for).toISOString()
        : null;

      const { error } = await supabase
        .from('email_campaigns')
        .insert([{
          name: formData.name,
          subject: formData.subject,
          content: formData.content,
          scheduled_for: scheduledForUTC, // Use potentially null UTC value
          status: formData.status, // Use status from form
          opens: 0,
          clicks: 0,
          recipients: subscribers.filter(s => s.status === 'active').length
        }]);

      if (error) throw error;

      setSuccess('Campaign created successfully!');
      setShowForm(false);
      // Reset form data including status
      setFormData({
        name: '',
        subject: '',
        content: '',
        scheduled_for: '',
        status: 'draft',
      });
      fetchCampaigns();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (id: string, newStatus: 'draft' | 'scheduled' | 'sent') => {
    try {
      const { error } = await supabase
        .from('email_campaigns')
        .update({ status: newStatus })
        .eq('id', id);

      if (error) throw error;

      setSuccess('Campaign status updated successfully!');
      fetchCampaigns();
    } catch (err: any) {
      setError(err.message);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-blue-50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Subscribers</p>
              <h3 className="text-2xl font-bold">{subscribers.length}</h3>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
          <div className="mt-4">
            <div className="flex items-center text-sm text-green-600">
              <ChevronRight className="h-4 w-4" />
              <span>{subscribers.filter(s => s.status === 'active').length} active</span>
            </div>
          </div>
        </div>

        <div className="bg-red-50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Unsubscribed</p>
              <h3 className="text-2xl font-bold">
                {subscribers.filter(s => s.status === 'unsubscribed').length}
              </h3>
            </div>
            <X className="h-8 w-8 text-red-600" />
          </div>
          <div className="mt-4">
            <div className="flex items-center text-sm text-red-600">
              <ChevronRight className="h-4 w-4" />
              <span>
                {Math.round(
                  (subscribers.filter(s => s.status === 'unsubscribed').length / 
                   subscribers.length) * 100 || 0
                )}% of total
              </span>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Scheduled Campaigns</p>
              <h3 className="text-2xl font-bold">
                {campaigns.filter(c => c.status === 'scheduled').length}
              </h3>
            </div>
            <Calendar className="h-8 w-8 text-purple-600" />
          </div>
          <div className="mt-4">
            <div className="flex items-center text-sm text-purple-600">
              <ChevronRight className="h-4 w-4" />
              <span>
                {campaigns.length > 0 
                  ? Math.round(
                      (campaigns.filter(c => c.status === 'scheduled').length / 
                       campaigns.length) * 100
                    )
                  : 0}% of total
              </span>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Campaigns</p>
              <h3 className="text-2xl font-bold">{campaigns.length}</h3>
            </div>
            <Mail className="h-8 w-8 text-orange-600" />
          </div>
          <div className="mt-4">
            <div className="flex items-center text-sm text-orange-600">
              <ChevronRight className="h-4 w-4" />
              <span>{campaigns.filter(c => c.status === 'sent').length} sent</span>
            </div>
          </div>
        </div>
      </div>

      {/* Email Campaigns */}
      <div className="bg-white rounded-lg shadow-lg">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Email Campaigns</h2>
            <button
              onClick={() => setShowForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
            >
              <Plus className="h-5 w-5 mr-2" />
              New Campaign
            </button>
          </div>
        </div>

        {error && (
          <div className="m-6 bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </div>
        )}

        {success && (
          <div className="m-6 bg-green-50 text-green-600 p-4 rounded-lg flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            {success}
          </div>
        )}

        {showForm && (
          <div className="p-6 border-b">
            <form onSubmit={editingId ? handleUpdateCampaign : handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Campaign Name
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Subject
                </label>
                <input
                  type="text"
                  required
                  value={formData.subject}
                  onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Content
                </label>
                <textarea
                  required
                  rows={6}
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as 'draft' | 'scheduled' })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="draft">Draft</option>
                  <option value="scheduled">Scheduled</option>
                </select>
                {formData.status === 'scheduled' && !formData.scheduled_for && (
                  <p className="text-xs text-red-600 mt-1">Schedule date/time is required for scheduled status.</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Schedule Send (Required if Status is Scheduled)
                </label>
                <input
                  type="datetime-local"
                  value={formData.scheduled_for}
                  onChange={(e) => setFormData({ ...formData, scheduled_for: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  // Optionally make it required visually if status is scheduled
                  // required={formData.status === 'scheduled'}
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="text-gray-600 hover:text-gray-900"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? (editingId ? 'Updating...' : 'Creating...') : (editingId ? 'Update Campaign' : 'Create Campaign')}
                </button>
              </div>
            </form>
          </div>
        )}

        <div className="divide-y">
          {campaigns.map((campaign) => (
            <div key={campaign.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-semibold text-lg mb-1">{campaign.name}</h3>
                  <p className="text-gray-600 mb-2">{campaign.subject}</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <select
                      value={campaign.status}
                      onChange={(e) => handleStatusChange(campaign.id, e.target.value as 'draft' | 'scheduled' | 'sent')}
                      className={`w-24 text-center font-semibold px-2 py-1 rounded-full ${
                        campaign.status === 'sent'
                          ? 'bg-green-100 text-green-800'
                          : campaign.status === 'scheduled'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      <option value="draft">Draft</option>
                      <option value="scheduled">Scheduled</option>
                      <option value="sent">Sent</option>
                    </select>
                    {/* Modify the display logic for scheduled date */}
                    {campaign.status === 'scheduled' && (
                      <span className="text-gray-500">
                        {campaign.scheduled_for 
                          ? `Scheduled for: ${new Date(campaign.scheduled_for).toLocaleString()}` 
                          : 'Not scheduled yet'}
                      </span>
                    )}
                    {/* Add display for sent date if applicable */}
                    {campaign.status === 'sent' && campaign.sent_at && (
                       <span className="text-gray-500">
                         Sent on: {new Date(campaign.sent_at).toLocaleString()}
                       </span>
                    )}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEditCampaign(campaign)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                  >
                      <Edit className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => handleDeleteCampaign(campaign.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                  >
                      <Trash2 className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Subscribers List */}
      <div className="bg-white rounded-lg shadow-lg">
        <div className="p-6 border-b">
          <h2 className="text-2xl font-bold">Subscribers</h2>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subscriber
                </th>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subscribed Date
                </th>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Email
                </th>
                <th className="px-6 py-3 bg-gray-50"></th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {subscribers.map((subscriber) => (
                <tr key={subscriber.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {subscriber.full_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {subscriber.email}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${subscriber.status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                      }`}>
                      {subscriber.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(subscriber.subscribed_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {subscriber.last_email_sent
                      ? new Date(subscriber.last_email_sent).toLocaleDateString()
                      : 'Never'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {subscriber.status === 'active' ? (
                      <button
                        onClick={() => handleUnsubscribe(subscriber.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    ) : (
                      <button
                        onClick={() => handleReactivate(subscriber.id)}
                        className="text-green-600 hover:text-green-900"
                      >
                        <CheckCircle className="h-5 w-5" />
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}