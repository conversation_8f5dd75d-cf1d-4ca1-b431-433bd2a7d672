-- -- Create products table
-- CREATE TABLE products (
--   id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
--   name text NOT NULL,
--   description text NOT NULL,
--   price numeric NOT NULL CHECK (price >= 0),
--   image_url text,
--   category text NOT NULL,
--   created_at timestamptz DEFAULT now(),
--   updated_at timestamptz DEFAULT now()
-- );

-- -- Create media_items table
-- CREATE TABLE media_items (
--   id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
--   title text NOT NULL,
--   description text NOT NULL,
--   type text NOT NULL CHECK (type IN ('guide', 'whitepaper', 'tutorial', 'template')),
--   url text NOT NULL,
--   thumbnail_url text,
--   created_at timestamptz DEFAULT now(),
--   updated_at timestamptz DEFAULT now()
-- );

-- -- Enable RLS
-- ALTER TABLE products ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE media_items ENABLE ROW LEVEL SECURITY;

-- -- Create policies for products
-- CREATE POLICY "Anyone can view products"
--   ON products
--   FOR SELECT
--   USING (true);

-- CREATE POLICY "Admins can manage products"
--   ON products
--   FOR ALL
--   TO authenticated
--   USING (EXISTS (
--     SELECT 1 FROM profiles
--     WHERE id = auth.uid()
--     AND role = 'admin'
--   ));

-- -- Create policies for media_items
-- CREATE POLICY "Anyone can view media items"
--   ON media_items
--   FOR SELECT
--   USING (true);

-- CREATE POLICY "Admins can manage media items"
--   ON media_items
--   FOR ALL
--   TO authenticated
--   USING (EXISTS (
--     SELECT 1 FROM profiles
--     WHERE id = auth.uid()
--     AND role = 'admin'
--   ));

-- -- Create function to update timestamps
-- CREATE OR REPLACE FUNCTION update_updated_at_column()
-- RETURNS TRIGGER AS $$
-- BEGIN
--   NEW.updated_at = CURRENT_TIMESTAMP;
--   RETURN NEW;
-- END;
-- $$ language 'plpgsql';

-- -- Create triggers for updated_at
-- CREATE TRIGGER update_products_updated_at
--   BEFORE UPDATE ON products
--   FOR EACH ROW
--   EXECUTE FUNCTION update_updated_at_column();

-- CREATE TRIGGER update_media_items_updated_at
--   BEFORE UPDATE ON media_items
--   FOR EACH ROW
--   EXECUTE FUNCTION update_updated_at_column();

-- -- Insert sample products
-- INSERT INTO products (name, description, price, category, image_url) VALUES
--   (
--     'GrantReady™ Basic',
--     'Perfect for small organizations. Includes basic grant management features and email support.',
--     99.99,
--     'Software',
--     'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80'
--   ),
--   (
--     'GrantReady™ Pro',
--     'Ideal for growing organizations. Includes advanced features, priority support, and custom workflows.',
--     199.99,
--     'Software',
--     'https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80'
--   ),
--   (
--     'GrantReady™ Enterprise',
--     'For large organizations. Includes unlimited users, 24/7 support, and custom integrations.',
--     499.99,
--     'Software',
--     'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80'
--   );

-- -- Insert sample media items
-- INSERT INTO media_items (title, description, type, url, thumbnail_url) VALUES
--   (
--     'Grant Management Guide 2024',
--     'Comprehensive guide to managing grants effectively in 2024.',
--     'guide',
--     'https://example.com/guides/grant-management-2024',
--     'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80'
--   ),
--   (
--     'Emergency Response Best Practices',
--     'Learn the latest best practices in emergency response management.',
--     'whitepaper',
--     'https://example.com/whitepapers/emergency-response',
--     'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80'
--   );

-- -- Grant necessary permissions
-- GRANT ALL ON products TO authenticated;
-- GRANT ALL ON media_items TO authenticated;