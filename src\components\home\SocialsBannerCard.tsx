import React from 'react';
import { Link } from 'react-router-dom';

// Assuming these are the correct paths to your image assets
import PhoneMockupLeft from '../../assets/images/Frame-1316.png'; // Assumed to be angled to the right
import PhoneMockupRight from '../../assets/images/Frame-3.png'; // Assumed to be angled to the left

const SocialsBannerCard: React.FC = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center font-inter overflow-hidden">
      {/* Background with gradient */}
      <div className="absolute inset-0" style={{ background: 'linear-gradient(0deg, #170031 0.04%, #470097 100.04%)' }}></div>

      {/* Phone on the LEFT - angled outward to the left */}
      <div className="hidden xl:block absolute left-[5%] 2xl:left-[15%] bottom-0 w-full max-w-[480px] h-[90%] z-0">
        <div className="relative w-full h-full transform -rotate-0 origin-bottom">
          <img
            src={PhoneMockupLeft}
            alt="Phone mockup on the left, angled outwards"
            className="w-full h-full object-contain object-bottom drop-shadow-2xl"
          />
        </div>
      </div>

      {/* Phone on the RIGHT - angled outward to the right */}
      <div className="hidden xl:block absolute right-[5%] 2xl:right-[15%] bottom-0 w-full max-w-[480px] h-[90%] z-0">
        <div className="relative w-full h-full transform rotate-0 origin-bottom">
          <img
            src={PhoneMockupRight}
            alt="Phone mockup on the right, angled outwards"
            className="w-full h-full object-contain object-bottom drop-shadow-2xl"
          />
        </div>
      </div>

      {/* Content - z-index is 10, ensuring it's above the phones (z-0) */}
      <div className="relative z-10 flex flex-col items-center gap-3 sm:gap-4 md:gap-5 lg:gap-6 px-4 py-6 sm:py-8 md:py-10 lg:py-12">
        {/* Header */}
        <div className="flex flex-col items-center gap-4 lg:gap-6">
          <div className="bg-white text-[#7C3AED] text-[10px] sm:text-[12.44px] font-normal tracking-[4.83%] px-[6px] sm:px-[7.78px] py-[3px] sm:py-[4.67px] rounded-[3px] animate-fadeInDown">
            News
          </div>
          <h2 className="text-2xl sm:text-3xl lg:text-[48px] font-semibold text-white leading-[1.1] text-center animate-fadeInDown animation-delay-200">
            Engage with Us
          </h2>
        </div>

        {/* Description */}
        <p className="text-base sm:text-lg lg:text-[20px] font-medium text-white text-center max-w-[544px] leading-[1.37] animate-fadeInDown animation-delay-400">
          See the biggest topics and changes to the public health landscape
        </p>

        {/* Button */}
        <Link
          to="/news"
          className="bg-white text-black rounded-lg flex items-center gap-2 micro-button animate-fadeInDown animation-delay-600"
          style={{
            padding: '13.81px 21.71px',
            fontFamily: 'Inter, sans-serif',
            fontWeight: 700,
            fontSize: '15.66px',
            lineHeight: '23.68px',
            letterSpacing: '0%',
            verticalAlign: 'middle',
          }}
        >
          All News
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="icon">
            <path d="M4.16667 10H15.8333M10 4.16667L15.8333 10L10 15.8333" stroke="currentColor" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </Link>

        {/* Social Media Section */}
        <div className="flex flex-col items-center gap-2 animate-fadeInDown animation-delay-800">
          <p className="text-[12.44px] font-normal text-white tracking-[4.83%]">Follow us</p>
          <div className="flex gap-3">
            <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" fill="#7C3AED" />
              </svg>
            </div>
            <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" fill="#7C3AED" />
              </svg>
            </div>
            <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" fill="#7C3AED" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SocialsBannerCard;