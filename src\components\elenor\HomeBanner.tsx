import { Link } from "react-router-dom";
import elenorBgVideo from "../../assets/video/elenor-banner-earth-smaller.webm";
import { useEffect, useRef } from "react";

interface ButtonProps {
  to: string;
  variant: "primary" | "secondary";
  label: string;
  icon?: React.ReactNode;
}

function HomeBanner() {
  const videoRef = useRef<HTMLVideoElement | null>(null);

  // Optimize video playback for performance
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          video.play().catch(() => {}); // Handle autoplay errors gracefully
        } else {
          video.pause();
        }
      },
      { threshold: 0.2 }
    );

    observer.observe(video);
    return () => observer.disconnect();
  }, []);

  return (
    <div className="relative w-full overflow-hidden rounded-lg">
      {" "}
      {/* Full-width background container for 2xl+ screens */}
      <div className="relative w-full h-[500px] sm:h-[380px] md:h-[460px] lg:h-[520px] xl:h-[580px] 2xl:h-[620px] rounded-lg md:rounded-[20px] 2xl:rounded-none bg-[#051329]">
        {/* Content container with max-width constraint */}
        <div className="relative max-w-[1900px] mx-auto h-full overflow-hidden">
          <div className="absolute inset-0 w-full h-full flex">
            <div className="w-full h-full overflow-hidden relative">
              <video
                ref={videoRef}
                className="absolute right-0 top-1/2 -translate-y-1/2 h-full 2xl:h-[105%] object-contain mix-blend-normal "
                autoPlay
                muted
                loop
                playsInline
                preload="metadata"
                aria-hidden="true"
              >
                <source src={elenorBgVideo} type="video/webm" />
                {/* Fallback message */}
                Your browser does not support the video tag.
              </video>

              {/* Animated Background Particles */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-pulse opacity-60"></div>
                <div className="absolute top-3/4 left-1/3 w-1 h-1 bg-white rounded-full animate-ping opacity-40"></div>
                <div
                  className="absolute top-1/2 left-1/5 w-1.5 h-1.5 bg-blue-300 rounded-full animate-pulse opacity-50"
                  style={{ animationDelay: "1s" }}
                ></div>
                <div
                  className="absolute top-1/3 right-1/4 w-1 h-1 bg-blue-500 rounded-full animate-ping opacity-30"
                  style={{ animationDelay: "2s" }}
                ></div>
                <div
                  className="absolute bottom-1/4 right-1/3 w-2 h-2 bg-blue-200 rounded-full animate-pulse opacity-40"
                  style={{ animationDelay: "1.5s" }}
                ></div>
              </div>

              {/* Content Container */}
              <div className="max-w-[1900px] mx-auto px-6 sm:px-8 lg:px-16 h-full flex items-center relative z-10">
                <div className="relative flex flex-col lg:flex-row items-start justify-between w-full gap-8 lg:gap-0">
                  <div className="lg:w-1/2 text-white space-y-6 lg:space-y-8 relative z-20">
                    <h1
                      className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-[60px] font-extrabold leading-tight tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-white via-blue-100 to-blue-400 animate-fadeIn drop-shadow-2xl relative"
                      style={{ animationDelay: "0.2s" }}
                    >
                      Situational Awareness
                      <br />
                      at your{" "}
                      <span className="text-blue-400 relative">
                        Fingertips
                        <div className="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-transparent rounded-full"></div>
                      </span>
                    </h1>
                    <p
                      className="text-base sm:text-lg md:text-xl text-gray-200 leading-relaxed max-w-lg animate-fadeIn backdrop-blur-sm relative"
                      style={{ animationDelay: "0.4s" }}
                    >
                      Elenor Solution empowers your team with instant
                      information sharing, intuitive tools, and seamless
                      coordination.{" "}
                      <span className="font-semibold text-blue-300">
                        all in one platform
                      </span>
                      .
                    </p>
                    <div
                      className="flex flex-col sm:flex-row gap-4 pt-4 animate-fadeIn relative"
                      style={{ animationDelay: "0.6s" }}
                    >
                      <ButtonLink
                        to="/solutions/elenor"
                        variant="primary"
                        label="Learn More"
                        icon={
                          <svg
                            className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                            />
                          </svg>
                        }
                      />
                      <ButtonLink
                        to="/book-a-demo"
                        variant="secondary"
                        label="Request a Demo"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

const ButtonLink: React.FC<ButtonProps> = ({ to, variant, label, icon }) => {
  const baseStyles =
    "group inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-offset-2";
  const variantStyles = {
    primary: "bg-white text-gray-900 hover:bg-gray-100 focus:ring-blue-400",
    secondary:
      "bg-transparent text-white border-2 border-white hover:bg-white/10 focus:ring-white",
  };

  return (
    <Link to={to} className={`${baseStyles} ${variantStyles[variant]}`}>
      {label}
      {icon}
    </Link>
  );
};

export default HomeBanner;

// Add custom CSS animations
const styles = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(59, 130, 246, 0.4); }
  }

  @keyframes twinkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
  }

  @keyframes sparkle {
    0%, 100% { opacity: 0.2; transform: scale(0.8) rotate(0deg); }
    25% { opacity: 0.8; transform: scale(1.1) rotate(90deg); }
    50% { opacity: 1; transform: scale(1.3) rotate(180deg); }
    75% { opacity: 0.6; transform: scale(1) rotate(270deg); }
  }

  .animate-fadeIn { animation: fadeIn 1s ease-out forwards; opacity: 0; }
  .animate-spin-slow { animation: spin-slow 20s linear infinite; }
  .animate-float { animation: float 3s ease-in-out infinite; }
  .animate-glow { animation: glow 2s ease-in-out infinite; }
  .animate-twinkle { animation: twinkle 2s ease-in-out infinite; }
  .animate-sparkle { animation: sparkle 3s ease-in-out infinite; }
`;

// Inject styles
if (typeof document !== "undefined") {
  const styleSheet = document.createElement("style");
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
