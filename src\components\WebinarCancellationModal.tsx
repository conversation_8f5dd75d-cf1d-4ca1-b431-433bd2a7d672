import { Calendar, Clock, User, AlertCircle, X } from 'lucide-react';

interface WebinarCancellationModalProps {
  webinarTitle: string;
  webinarDate: string;
  webinarTime: string;
  webinarSpeaker: string;
  onConfirm: () => void;
  onCancel: () => void;
}

// Helper function to convert 24-hour time to 12-hour time with AM/PM
const formatTimeWithAMPM = (time: string): string => {
  if (!time) return '';

  // Parse the time string (expected format: HH:MM)
  const [hours, minutes] = time.split(':').map(Number);

  if (isNaN(hours) || isNaN(minutes)) return time;

  // Convert to 12-hour format
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
};

export default function WebinarCancellationModal({
  webinarTitle,
  webinarDate,
  webinarTime,
  webinarSpeaker,
  onConfirm,
  onCancel
}: WebinarCancellationModalProps) {
  return (
    <div
      className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fadeIn"
      onClick={(e) => {
        // Close modal when clicking outside
        if (e.target === e.currentTarget) {
          onCancel();
        }
      }}
    >
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md overflow-hidden transform transition-all duration-300 ease-in-out animate-scaleIn">
        <div className="relative">
          {/* Header with gradient background */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle className="h-6 w-6 mr-2" />
                <h3 className="text-lg font-semibold">Cancel Registration</h3>
              </div>
              <button
                onClick={onCancel}
                className="text-white/80 hover:text-white transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="mb-6">
              <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{webinarTitle}</h4>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                You are already registered for this webinar. Would you like to cancel your registration?
              </p>

              {/* Webinar details */}
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 space-y-2">
                <div className="flex items-center text-gray-700 dark:text-gray-300">
                  <Calendar className="h-5 w-5 text-blue-500 mr-2" />
                  <span>{webinarDate}</span>
                </div>
                <div className="flex items-center text-gray-700 dark:text-gray-300">
                  <Clock className="h-5 w-5 text-blue-500 mr-2" />
                  <span>{formatTimeWithAMPM(webinarTime)}</span>
                </div>
                <div className="flex items-center text-gray-700 dark:text-gray-300">
                  <User className="h-5 w-5 text-blue-500 mr-2" />
                  <span>{webinarSpeaker}</span>
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex justify-end space-x-3">
              <button
                onClick={onCancel}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Keep Registration
              </button>
              <button
                onClick={onConfirm}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
              >
                Cancel Registration
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
