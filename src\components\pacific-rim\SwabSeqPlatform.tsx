import { Check, ExternalLink } from 'lucide-react';

interface SwabSeqFeature {
  title: string;
  items: string[];
}

interface SwabSeqPlatformProps {
  features: SwabSeqFeature[];
}

export const SwabSeqPlatform = ({ features }: SwabSeqPlatformProps) => {
  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-orange-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">SwabSeq Metagenomic Platform</h2>
          <p className="text-gray-600 max-w-3xl mx-auto">
            A state-of-the-art meta-genomics based untargeted next generation sequencing diagnostic platform for comprehensive pathogen detection and variant identification.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-xl shadow-lg">
              <h3 className="text-xl font-semibold mb-4">{feature.title}</h3>
              <ul className="space-y-2">
                {feature.items.map((item, i) => (
                  <li key={i} className="flex items-start gap-2">
                    <Check className="h-5 w-5 text-orange-600 flex-shrink-0 mt-1" />
                    <span className="text-gray-600">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <a
            href="https://swabseq.compmed.ucla.edu/"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-orange-600 hover:text-orange-700"
          >
            Learn more about SwabSeq
            <ExternalLink className="ml-2 h-5 w-5" />
          </a>
        </div>
      </div>
    </section>
  );
};
