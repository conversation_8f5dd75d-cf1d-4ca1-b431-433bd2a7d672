-- First ensure all foreign keys have ON DELETE CASCADE
ALTER TABLE IF EXISTS subscriptions
  DROP CONSTRAINT IF EXISTS subscriptions_user_id_fkey,
  ADD CONSTRAINT subscriptions_user_id_fkey
    FOREIGN KEY (user_id)
    REFERENCES auth.users(id)
    ON DELETE CASCADE;

ALTER TABLE IF EXISTS subscription_items
  DROP CONSTRAINT IF EXISTS subscription_items_subscription_id_fkey,
  ADD CONSTRAINT subscription_items_subscription_id_fkey
    FOREIGN KEY (subscription_id)
    REFERENCES subscriptions(id)
    ON DELETE CASCADE;

ALTER TABLE IF EXISTS blog_posts
  DROP CONSTRAINT IF EXISTS blog_posts_author_id_fkey,
  ADD CONSTRAINT blog_posts_author_id_fkey
    FOREIGN KEY (author_id)
    REFERENCES auth.users(id)
    ON DELETE CASCADE;

ALTER TABLE IF EXISTS event_registrations
  DROP CONSTRAINT IF EXISTS event_registrations_user_id_fkey,
  ADD CONSTRAINT event_registrations_user_id_fkey
    FOREI<PERSON><PERSON> KEY (user_id)
    REFERENCES auth.users(id)
    ON DELETE CASCADE;

-- Grant necessary permissions to service_role
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Create a security definer function to handle user deletion
CREATE OR REPLACE FUNCTION delete_user_with_related_data(user_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Cancel active subscriptions
  UPDATE subscriptions
  SET status = 'canceled',
      deleted = true,
      deleted_at = NOW()
  WHERE user_id = $1
  AND status IN ('active', 'trialing', 'past_due');

  -- Mark subscription items as canceled
  UPDATE subscription_items si
  SET status = 'canceled'
  FROM subscriptions s
  WHERE s.id = si.subscription_id
  AND s.user_id = $1;

  -- Delete the user (this will cascade to all related data)
  DELETE FROM auth.users WHERE id = $1;
END;
$$;