-- Add type column to blog_posts table
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS type TEXT DEFAULT 'blog';

-- Update existing posts to have 'blog' type
UPDATE blog_posts SET type = 'blog' WHERE type IS NULL;

-- Create index for faster filtering
CREATE INDEX IF NOT EXISTS blog_posts_type_idx ON blog_posts(type);

-- Grant permissions
GRANT SELECT ON blog_posts TO anon, authenticated;

-- First, drop the existing policy
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all blog posts" ON blog_posts;

-- Then create the new policy with proper permissions
CREATE POLICY "<PERSON><PERSON> can manage all blog posts" 
ON blog_posts 
FOR ALL 
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
  )
);



-- Added by eziane
/*
  # Create subscriptions and related tables
 
  1. New Tables
    - `subscriptions`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `plan_id` (text)
      - `status` (text)
      - `current_period_end` (timestamptz)
      - `created_at` (timestamptz)
    - `plans`
      - `id` (text, primary key)
      - `name` (text)
      - `description` (text)
      - `price` (numeric)
      - `features` (text[])
  2. Security
    - Enable RLS on both tables
    - Add policies for authenticated users to read their own subscriptions
    - Add policies for all users to read plans
*/
 
-- Drop existing policies if they exist
-- DROP POLICY IF EXISTS "Anyone can read plans" ON plans;
-- DROP POLICY IF EXISTS "Users can read own subscriptions" ON subscriptions;
-- DROP POLICY IF EXISTS "Admins can manage subscriptions" ON subscriptions;
-- DROP POLICY IF EXISTS "Admins can manage plans" ON plans;
 
-- Drop existing tables if they exist (cascade to remove dependencies)
-- DROP TABLE IF EXISTS subscriptions CASCADE;
-- DROP TABLE IF EXISTS plans CASCADE;
 
-- Create plans table
-- CREATE TABLE IF NOT EXISTS plans (
--   id text PRIMARY KEY,
--   name text NOT NULL,
--   description text NOT NULL,
--   price numeric NOT NULL,
--   features text[] NOT NULL,
--   created_at timestamptz DEFAULT now()
-- );
 
-- Create subscriptions table
-- CREATE TABLE IF NOT EXISTS subscriptions (
--   id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
--   user_id uuid REFERENCES profiles(id) NOT NULL,
--   product_id uuid REFERENCES products(id) NOT NULL,
--   status text NOT NULL,
--   current_period_end timestamptz NOT NULL,
--   created_at timestamptz DEFAULT now()
-- );
 
-- Enable RLS
-- ALTER TABLE plans ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
 
-- Create policies
-- CREATE POLICY "Anyone can read plans"
--   ON plans
--   FOR SELECT
--   TO public
--   USING (true);
 
-- Update policies to reference products instead of products
-- CREATE POLICY "Users can read own subscriptions"
--   ON subscriptions
--   FOR SELECT
--   TO authenticated
--   USING (auth.uid() = user_id);
 
-- CREATE POLICY "Admins can manage subscriptions"
--   ON subscriptions
--   FOR ALL
--   TO authenticated
--   USING (
--     EXISTS (
--       SELECT 1 FROM profiles
--       WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
--     )
--   );
 
-- Add policy for admins to manage plans
-- CREATE POLICY "Admins can manage plans"
--   ON plans
--   FOR ALL
--   TO authenticated
--   USING (
--     EXISTS (
--       SELECT 1 FROM profiles
--       WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
--     )
--   );
 
-- Grant permissions to service_role (for server-side operations)
-- GRANT ALL ON plans TO service_role;
-- GRANT ALL ON subscriptions TO service_role;
 
-- Grant permissions to authenticated users (with RLS applied)
-- GRANT S/ELECT ON plans TO authenticated;
-- GRANT SELECT ON subscriptions TO authenticated;
-- GRANT INSERT, UPDATE, DELETE ON subscriptions TO authenticated;
 
-- Grant permissions to anon users (with RLS applied)
-- GRANT SELECT ON plans TO anon;
 
-- Insert default plans
-- INSERT INTO plans (id, name, description, price, features) VALUES
--   ('basic', 'Basic', 'Perfect for small organizations', 99.99, ARRAY[
--     'Up to 5 users',
--     'Basic grant management',
--     'Email support'
--   ]),
--   ('pro', 'Professional', 'Ideal for growing organizations', 199.99, ARRAY[
--     'Up to 20 users',
--     'Advanced grant management',
--     'Priority support',
--     'Custom workflows'
--   ]),
--   ('enterprise', 'Enterprise', 'For large organizations', 499.99, ARRAY[
--     'Unlimited users',
--     'Enterprise features',
--     '24/7 support',
--     'Custom integration',
--     'Dedicated account manager'
--   ]);
 
-- -- Insert test subscriptions
-- INSERT INTO subscriptions (user_id, product_id, status, current_period_end) VALUES
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'active', NOW() + INTERVAL '30 days'),
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'active', NOW() + INTERVAL '30 days'),
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'active', NOW() + INTERVAL '30 days'),
 
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'trialing', NOW() + INTERVAL '14 days'),
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'trialing', NOW() + INTERVAL '14 days'),
 
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'past_due', NOW() - INTERVAL '5 days'),
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'past_due', NOW() - INTERVAL '3 days'),
 
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'canceled', NOW() + INTERVAL '5 days'),
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'canceled', NOW() - INTERVAL '15 days'),
 
--   ('37f5168a-14ec-425e-9993-977f5c08983d', '550e8400-e29b-41d4-a716-************', 'canceled', NOW() - INTERVAL '60 days');