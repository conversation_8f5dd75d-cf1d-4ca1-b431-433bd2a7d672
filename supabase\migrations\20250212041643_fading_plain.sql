-- Drop existing policies
DROP POLICY IF EXISTS "blog_posts_read_all" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_insert" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_update" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_delete" ON blog_posts;

-- Create new policies for blog_posts
CREATE POLICY "blog_posts_read_all"
  ON blog_posts
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "blog_posts_admin_insert"
  ON blog_posts
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "blog_posts_admin_update"
  ON blog_posts
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "blog_posts_admin_delete"
  ON blog_posts
  FOR DELETE
  TO authenticated
  USING (true);

-- Grant necessary permissions
GRANT ALL ON blog_posts TO authenticated;
GRANT ALL ON blog_posts TO anon;
GRANT ALL ON blog_posts TO service_role;