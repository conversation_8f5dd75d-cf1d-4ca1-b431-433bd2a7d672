-- Create contact_submissions table
CREATE TABLE contact_submissions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT,
  company TEXT,
  message TEXT NOT NULL,
  captcha_token TEXT NOT NULL,
  submitted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Grant permissions to appropriate roles
GRANT ALL PRIVILEGES ON TABLE contact_submissions TO postgres;
GRANT ALL PRIVILEGES ON TABLE contact_submissions TO authenticated;
GRANT INSERT ON TABLE contact_submissions TO anon;

-- Create index for better query performance
CREATE INDEX idx_contact_submissions_email ON contact_submissions (email);
CREATE INDEX idx_contact_submissions_submitted_at ON contact_submissions (submitted_at);

-- Set up Row Level Security (RLS)
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;

-- Public insert policy
CREATE POLICY "Public insert access" 
ON contact_submissions
FOR INSERT
TO public
WITH CHECK (true);

-- Public delete policy
CREATE POLICY "Public delete access"
ON contact_submissions
FOR DELETE
TO public
USING (true);

-- Authenticated users can read their own submissions
CREATE POLICY "User can read own submissions"
ON contact_submissions
FOR SELECT
TO authenticated
USING (email = auth.jwt() ->> 'email');

-- Admin full access
CREATE POLICY "Admin full access"
ON contact_submissions
FOR ALL
TO authenticated
USING (auth.role() = 'admin');

-- Captcha verification function
CREATE OR REPLACE FUNCTION public.verify_captcha(token text)
RETURNS boolean
LANGUAGE plpgsql
AS $$
BEGIN
  IF token IS NULL OR token = '' THEN
    RAISE EXCEPTION 'Invalid captcha token';
  END IF;
  
  RETURN true;
END;
$$;

GRANT EXECUTE ON FUNCTION public.verify_captcha TO anon;