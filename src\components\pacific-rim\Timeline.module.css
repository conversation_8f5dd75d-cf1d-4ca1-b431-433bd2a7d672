@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  70% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes borderPulse {
  0% {
    border-color: rgba(249, 115, 22, 0.4);
    box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.4);
  }
  50% {
    border-color: rgba(249, 115, 22, 0.8);
    box-shadow: 0 0 0 4px rgba(249, 115, 22, 0.1);
  }
  100% {
    border-color: rgba(249, 115, 22, 0.4);
    box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.4);
  }
}

@keyframes nodeGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(249, 115, 22, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(249, 115, 22, 0);
  }
}

.timeline-node {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.timeline-node.active {
  transform: scale(1.2);
  animation: nodeGlow 2s ease-in-out infinite;
}

.timeline-card {
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform;
  opacity: 0.6;
}

.timeline-card:hover {
  transform: scale(1.02) translateY(-8px);
}

.timeline-card.active {
  transform: translateY(-8px);
  opacity: 1;
}

.gradient-text {
  background: linear-gradient(135deg, #f97316, #9333ea, #f97316);
  background-size: 200% 200%;
  animation: gradientFlow 6s ease infinite;
  -webkit-background-clip: text;
  color: transparent;
  background-clip: text;
  will-change: background-position;
}

.progress-line {
  transition: height 1.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: height;
  backdrop-filter: blur(8px);
}

.timeline-connector {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity, width;
}

.timeline-item {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform, opacity;
}

.timeline-item.visible {
  opacity: 1;
  transform: translateY(0);
}

.timeline-content {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity;
}

.timeline-content:hover {
  opacity: 0.9;
  cursor: pointer;
}

.parallax-element {
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}
