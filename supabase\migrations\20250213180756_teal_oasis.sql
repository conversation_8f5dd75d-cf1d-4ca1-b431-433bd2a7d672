-- Add HTML and CSS columns to pages table
ALTER TABLE pages 
ADD COLUMN html_content text,
ADD COLUMN custom_css text,
ADD COLUMN custom_js text;

-- Create function to sync frontend content to pages
CREATE OR REPLACE FUNCTION sync_frontend_content()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Home page
  INSERT INTO pages (slug, title, content, html_content, meta_description, is_published)
  VALUES (
    '',
    'Home',
    '# Welcome to International Responder Systems',
    '<div class="min-h-screen">
      <section class="relative bg-gradient-to-r from-blue-600 to-blue-800 text-white py-32">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 class="text-5xl font-bold mb-6">Revolutionizing Healthcare Response Systems</h1>
              <p class="text-xl text-blue-100 mb-8">Empowering healthcare organizations with intelligent solutions for better patient outcomes</p>
              <div class="space-x-4">
                <a href="/solutions" class="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300">Explore Solutions</a>
                <a href="/contact" class="inline-flex items-center border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white/10 transition duration-300">Contact Us</a>
              </div>
            </div>
            <div class="relative">
              <div class="bg-white/10 backdrop-blur-sm p-6 rounded-xl">
                <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" alt="Healthcare innovation" class="rounded-lg shadow-xl">
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>',
    'Welcome to International Responder Systems - Leading provider of healthcare emergency response solutions',
    true
  )
  ON CONFLICT (slug) DO UPDATE
  SET 
    title = EXCLUDED.title,
    content = EXCLUDED.content,
    html_content = EXCLUDED.html_content,
    meta_description = EXCLUDED.meta_description;

  -- Solutions page
  INSERT INTO pages (slug, title, content, html_content, meta_description, is_published)
  VALUES (
    'solutions',
    'Solutions',
    '# World-class Solutions',
    '<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 class="text-4xl font-bold mb-6">World-class Solutions</h1>
        <p class="text-xl text-blue-100 max-w-3xl">Our team has domain knowledge in government, healthcare, disaster and emergency response, and engineering.</p>
      </div>
    </section>',
    'Discover our comprehensive suite of healthcare emergency response solutions',
    true
  )
  ON CONFLICT (slug) DO UPDATE
  SET 
    title = EXCLUDED.title,
    content = EXCLUDED.content,
    html_content = EXCLUDED.html_content,
    meta_description = EXCLUDED.meta_description;

  -- Add more pages here...
END;
$$;

-- Run the sync function
SELECT sync_frontend_content();

-- Drop the sync function as it's no longer needed
DROP FUNCTION sync_frontend_content();

-- Create view for page management
CREATE OR REPLACE VIEW page_management AS
SELECT 
  id,
  slug,
  title,
  content,
  html_content,
  custom_css,
  custom_js,
  meta_description,
  is_published,
  created_at,
  updated_at
FROM pages;

-- Grant access to the view
GRANT SELECT ON page_management TO authenticated;