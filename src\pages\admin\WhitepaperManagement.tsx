import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { FileText, Trash2, Edit, Plus, Image, AlertCircle, X, Upload, XCircle, Link, File, ExternalLink, Download } from 'lucide-react';
import { downloadFile } from '../../utils/fileDownloader';

interface MediaItem {
  id: string;
  title: string;
  description: string;
  type: 'guide' | 'whitepaper' | 'tutorial' | 'template';
  url: string;
  file_url?: string | null;
  thumbnail_url: string | null;
  created_at: string;
}

export default function WhitepaperManagement() {
  const [whitepapers, setWhitepapers] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [imageInputType, setImageInputType] = useState<'upload' | 'url'>('upload');
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(null);
  const [externalImageUrl, setExternalImageUrl] = useState<string | null>(null);
  const [imageUploading, setImageUploading] = useState(false);
  // We don't need fileInputType state since we're always showing both URL and upload
  const [fileUploading, setFileUploading] = useState(false);
  const [downloadingFileId, setDownloadingFileId] = useState<string | null>(null);
  // We don't need filePreview state since we're using formData.file_url directly
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [whitepaperToDelete, setWhitepaperToDelete] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: '',
    url: '',
    file_url: '',
    thumbnail_url: ''
  });

  useEffect(() => {
    fetchWhitepapers();
  }, []);

  const fetchWhitepapers = async () => {
    try {
      const { data, error } = await supabase
        .from('media_items')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setWhitepapers(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to extract file path from URL
  const getFilePathFromUrl = (url: string, bucketName: string): string | null => {
    if (!url || !url.includes(bucketName)) return null;
    try {
      const urlObj = new URL(url);
      const pathSegments = urlObj.pathname.split('/');
      return pathSegments.slice(pathSegments.indexOf(bucketName) + 1).join('/');
    } catch (e) {
      console.error('Error parsing URL:', e);
      return null;
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setImageUploading(true);
      setError(null);

      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Please upload a valid image file (JPEG, PNG, GIF, or WEBP)');
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('File size should be less than 5MB');
      }

      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const fileName = `${Math.random().toString(36).substring(2, 9)}-${Date.now()}.${fileExt}`;
      const filePath = `whitepapers/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('whitepaper-images')
        .upload(filePath, file, { upsert: true });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('whitepaper-images')
        .getPublicUrl(filePath);

      // Set the image URL in the form data and track it separately
      setFormData(prev => ({ ...prev, thumbnail_url: publicUrl }));
      setUploadedImageUrl(publicUrl);
      setImagePreview(publicUrl);
      setSuccess('Image uploaded successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message);
      setTimeout(() => setError(null), 5000);
    } finally {
      setImageUploading(false);
    }
  };

  // Handle image removal from storage
  const handleRemoveImage = async (imageUrl: string) => {
    try {
      // Only attempt to remove from storage if it's an uploaded image
      if (imageUrl && imageUrl.includes('whitepaper-images')) {
        const filePath = getFilePathFromUrl(imageUrl, 'whitepaper-images');

        if (filePath) {
          setImageUploading(true);
          const { error } = await supabase.storage
            .from('whitepaper-images')
            .remove([filePath]);

          if (error) {
            console.error('Error removing image from storage:', error);
            setError('Failed to remove image from storage');
            setTimeout(() => setError(null), 5000);
            return;
          }

          setSuccess('Image removed successfully');
          setTimeout(() => setSuccess(null), 3000);
        }
      }

      // Clear the image URL from the form data
      setFormData(prev => ({ ...prev, thumbnail_url: '' }));
      setUploadedImageUrl(null);
      setImagePreview(null);
    } catch (err: any) {
      console.error('Error in handleRemoveImage:', err);
      setError(err.message || 'Failed to remove image');
      setTimeout(() => setError(null), 5000);
    } finally {
      setImageUploading(false);
    }
  };

  // Handle PDF file upload
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setFileUploading(true);
      setError(null);

      // Validate file type
      if (file.type !== 'application/pdf') {
        throw new Error('Please upload a valid PDF file');
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size should be less than 10MB');
      }

      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const fileName = `${Math.random().toString(36).substring(2, 9)}-${Date.now()}.${fileExt}`;
      const filePath = `whitepapers/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('whitepaper-files')
        .upload(filePath, file, { upsert: true });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('whitepaper-files')
        .getPublicUrl(filePath);

      // Set the file URL in the form data
      setFormData(prev => ({ ...prev, file_url: publicUrl }));
      // No need to set filePreview
      setSuccess('PDF uploaded successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message);
      setTimeout(() => setError(null), 5000);
    } finally {
      setFileUploading(false);
    }
  };

  // Handle file removal from storage
  const handleRemoveFile = async (fileUrl: string) => {
    try {
      // Only attempt to remove from storage if it's an uploaded file
      if (fileUrl && fileUrl.includes('whitepaper-files')) {
        const filePath = getFilePathFromUrl(fileUrl, 'whitepaper-files');

        if (filePath) {
          setFileUploading(true);
          const { error } = await supabase.storage
            .from('whitepaper-files')
            .remove([filePath]);

          if (error) {
            console.error('Error removing file from storage:', error);
            setError('Failed to remove file from storage');
            setTimeout(() => setError(null), 5000);
            return;
          }

          setSuccess('File removed successfully');
          setTimeout(() => setSuccess(null), 3000);
        }
      }

      // Clear the file URL from the form data
      setFormData(prev => ({ ...prev, file_url: '' }));
      // No need to set filePreview
    } catch (err: any) {
      console.error('Error in handleRemoveFile:', err);
      setError(err.message || 'Failed to remove file');
      setTimeout(() => setError(null), 5000);
    } finally {
      setFileUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      const { title, description, url, file_url, thumbnail_url } = formData;

      if (!title.trim() || !description.trim()) {
        throw new Error('Title and description are required');
      }

      // At least one of URL or file_url is recommended but not required
      if (!url.trim() && !file_url) {
        console.warn('Neither URL nor PDF file provided');
      }

      const whitepaperData = {
        title: title.trim(),
        description: description.trim(),
        type: 'whitepaper' as const,
        url: url.trim(),
        file_url: file_url || null,
        thumbnail_url: thumbnail_url || null
      };

      if (editingId) {
        const { error } = await supabase
          .from('media_items')
          .update(whitepaperData)
          .eq('id', editingId);

        if (error) throw error;
        setSuccess('Whitepaper updated successfully!');
      } else {
        const { error } = await supabase
          .from('media_items')
          .insert([whitepaperData]);

        if (error) throw error;
        setSuccess('Whitepaper created successfully!');
      }

      setFormData({
        title: '',
        description: '',
        type: 'whitepaper',
        url: '',
        file_url: '',
        thumbnail_url: ''
      });
      setShowForm(false);
      setEditingId(null);
      setImagePreview(null);
      setUploadedImageUrl(null);
      setExternalImageUrl(null);
      // No need to set filePreview
      fetchWhitepapers();
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message);
      setTimeout(() => setError(null), 5000);
    }
  };

  const handleEdit = (item: MediaItem) => {
    setFormData({
      title: item.title,
      description: item.description,
      type: 'whitepaper',
      url: item.url || '',
      file_url: item.file_url || '',
      thumbnail_url: item.thumbnail_url || ''
    });
    // Set the appropriate image URL based on the source
    if (item.thumbnail_url && item.thumbnail_url.includes('whitepaper-images')) {
      setUploadedImageUrl(item.thumbnail_url);
      setExternalImageUrl(null);
    } else if (item.thumbnail_url) {
      setExternalImageUrl(item.thumbnail_url);
      setUploadedImageUrl(null);
    } else {
      setUploadedImageUrl(null);
      setExternalImageUrl(null);
    }
    setImagePreview(item.thumbnail_url);
    // No need to set filePreview as we're using formData.file_url directly

    // Set the correct image input type based on the image URL
    if (item.thumbnail_url && item.thumbnail_url.includes('whitepaper-images')) {
      setImageInputType('upload');
    } else if (item.thumbnail_url) {
      setImageInputType('url');
    } else {
      setImageInputType('upload'); // Default to upload if no image
    }

    // We don't need to set file input type anymore since we always show both options

    setEditingId(item.id);
    setShowForm(true);
  };

  const handleDelete = (id: string) => {
    setWhitepaperToDelete(id);
    setShowDeleteConfirmation(true);
  };

  const confirmDelete = async () => {
    if (!whitepaperToDelete) return;

    try {
      const { error } = await supabase
        .from('media_items')
        .delete()
        .eq('id', whitepaperToDelete);

      if (error) throw error;

      fetchWhitepapers();
      setShowDeleteConfirmation(false);
      setWhitepaperToDelete(null);
      setSuccess('Whitepaper deleted successfully!');
      setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
    } catch (err: any) {
      setError(err.message);
      setTimeout(() => setError(null), 5000); // Auto-dismiss after 5 seconds
    }
  };

  const cancelDelete = () => {
    setShowDeleteConfirmation(false);
    setWhitepaperToDelete(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Whitepapers</h2>
        <button
          onClick={() => {
            setShowForm(true);
            setEditingId(null);
            setFormData({
              file_url: '',
              title: '',
              description: '',
              type: 'whitepaper',
              url: '',
              thumbnail_url: ''
            });
            setUploadedImageUrl(null);
            setExternalImageUrl(null);
            setImagePreview(null);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Whitepaper
        </button>
      </div>

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 text-green-600 p-4 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {success}
        </div>
      )}

      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 overflow-y-auto">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto relative">
            {/* Header with sticky position */}
            <div className="sticky top-0 bg-white px-6 py-4 border-b flex justify-between items-center z-10">
              <h3 className="text-xl font-bold text-gray-800">
                {editingId ? 'Edit Whitepaper' : 'Add New Whitepaper'}
              </h3>
              <button
                onClick={() => {
                  setShowForm(false);
                  setEditingId(null);
                }}
                className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Close"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-6">
              <form id="whitepaper-form" onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information Section */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h4 className="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                    <FileText className="h-4 w-4 mr-2" /> Basic Information
                  </h4>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Title
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.title}
                        onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter whitepaper title"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        required
                        rows={3}
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter a brief description of this whitepaper"
                      />
                    </div>
                  </div>
                </div>

                {/* Document Section */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h4 className="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                    <File className="h-4 w-4 mr-2" /> Document
                  </h4>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Document URL
                      </label>
                      <div className="flex items-center">
                        <div className="relative flex-1">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Link className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            type="url"
                            value={formData.url || ''}
                            onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="https://example.com/documents/whitepaper.pdf"
                          />
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Optional: External URL where the whitepaper can be accessed</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        PDF Upload <span className="text-gray-500 text-xs font-normal">(Optional)</span>
                      </label>
                      <div className="w-full">
                        <label className="flex-1">
                          <div className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 cursor-pointer transition-colors">
                            <File className="h-5 w-5 mr-2 text-gray-500" />
                            {fileUploading ? 'Uploading...' : 'Choose PDF File'}
                            <input
                              type="file"
                              accept="application/pdf"
                              onChange={handleFileUpload}
                              className="hidden"
                              disabled={fileUploading}
                            />
                          </div>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* File preview */}
                  {formData.file_url && (
                    <div className="mt-4 flex justify-center bg-blue-50 p-4 rounded-lg">
                      <div className="relative max-w-xs">
                        <div className="relative flex items-center justify-center p-4 bg-white rounded border border-gray-200 shadow-sm">
                          <FileText className="h-10 w-10 text-blue-500" />
                          <div className="ml-3">
                            <p className="text-sm font-medium">PDF Uploaded</p>
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                const filename = formData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.pdf';
                                setDownloadingFileId('preview');
                                downloadFile(formData.file_url, filename, {
                                  onComplete: () => setDownloadingFileId(null),
                                  onError: () => setDownloadingFileId(null)
                                });
                              }}
                              disabled={downloadingFileId === 'preview'}
                              className={`text-xs hover:underline flex items-center mt-1 ${downloadingFileId === 'preview' ? 'text-gray-400' : 'text-blue-500'}`}
                            >
                              {downloadingFileId === 'preview' ? (
                                <>
                                  Downloading... <div className="h-3 w-3 ml-1 border-2 border-gray-300 border-t-gray-500 rounded-full animate-spin"></div>
                                </>
                              ) : (
                                <>
                                  Download PDF <Download className="h-3 w-3 ml-1" />
                                </>
                              )}
                            </button>
                          </div>
                          {/* Remove file button */}
                          <button
                            type="button"
                            onClick={() => handleRemoveFile(formData.file_url || '')}
                            className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100 transition-colors"
                            title="Remove file"
                            disabled={fileUploading}
                          >
                            <XCircle className="h-5 w-5 text-red-500" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                  <p className="text-xs text-gray-500 mt-2">Upload a PDF file for the whitepaper (optional)</p>
                </div>

                {/* Image Section */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                    <Image className="h-4 w-4 mr-2" /> Thumbnail Image
                  </h4>

                  <div className="space-y-4">
                    <div className="flex flex-col md:flex-row md:items-center gap-4">
                      <div className="flex-1">
                        <div className="mb-2">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center">
                              <input
                                type="radio"
                                id="image_upload_option"
                                name="image_source"
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                checked={imageInputType === 'upload'}
                                onChange={() => {
                                  setImageInputType('upload');
                                  // Use the uploaded image URL if available
                                  if (uploadedImageUrl) {
                                    setFormData({ ...formData, thumbnail_url: uploadedImageUrl });
                                  } else if (formData.thumbnail_url && !formData.thumbnail_url.includes('whitepaper-images')) {
                                    // Clear external URL when switching to upload
                                    setFormData({ ...formData, thumbnail_url: '' });
                                  }
                                }}
                              />
                              <label
                                htmlFor="image_upload_option"
                                className="ml-2 text-sm text-gray-700 cursor-pointer"
                              >
                                Upload Image
                              </label>
                            </div>
                            <div className="flex items-center">
                              <input
                                type="radio"
                                id="image_url_option"
                                name="image_source"
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                checked={imageInputType === 'url'}
                                onChange={() => {
                                  setImageInputType('url');
                                  // Use the external image URL if available
                                  if (externalImageUrl) {
                                    setFormData({ ...formData, thumbnail_url: externalImageUrl });
                                  } else if (formData.thumbnail_url && formData.thumbnail_url.includes('whitepaper-images')) {
                                    // Clear uploaded URL when switching to external
                                    setFormData({ ...formData, thumbnail_url: '' });
                                  }
                                }}
                              />
                              <label
                                htmlFor="image_url_option"
                                className="ml-2 text-sm text-gray-700 cursor-pointer"
                              >
                                Image URL
                              </label>
                            </div>
                          </div>
                        </div>

                        {imageInputType === 'upload' ? (
                          <div className="w-full">
                            <label className="flex-1">
                              <div className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 cursor-pointer transition-colors">
                                <Upload className="h-5 w-5 mr-2 text-gray-500" />
                                {imageUploading ? 'Uploading...' : 'Choose Image'}
                                <input
                                  type="file"
                                  accept="image/*"
                                  onChange={handleImageUpload}
                                  className="hidden"
                                  disabled={imageUploading}
                                />
                              </div>
                            </label>
                          </div>
                        ) : (
                          <div className="w-full">
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Image className="h-5 w-5 text-gray-400" />
                              </div>
                              <input
                                id="thumbnail_url"
                                type="text"
                                placeholder="https://example.com/image.jpg"
                                value={formData.thumbnail_url || ''}
                                onChange={(e) => {
                                  const newUrl = e.target.value;
                                  setFormData({ ...formData, thumbnail_url: newUrl });
                                  setExternalImageUrl(newUrl);
                                }}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                            </div>
                          </div>
                        )}
                      </div>


                      {/* Image preview */}
                      {(formData.thumbnail_url || imagePreview) && (
                        <div className="md:w-1/3 mt-4 md:mt-0">
                          <div className="bg-white p-2 rounded-lg border border-gray-200 shadow-sm">
                            <div className="relative">
                              <img
                                src={formData.thumbnail_url || imagePreview || ''}
                                alt="Whitepaper image preview"
                                className="w-full h-auto object-contain rounded"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = 'https://via.placeholder.com/300x200?text=Preview';
                                }}
                              />
                              {/* Remove image button */}
                              <button
                                type="button"
                                onClick={() => handleRemoveImage(formData.thumbnail_url || imagePreview || '')}
                                className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100 transition-colors"
                                title="Remove image"
                                disabled={imageUploading}
                              >
                                <XCircle className="h-5 w-5 text-red-500" />
                              </button>
                            </div>
                            <div className="text-center text-xs text-gray-500 mt-1">
                              Preview
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                    <p className="text-xs text-gray-500 mt-2">Upload an image or provide a URL for the whitepaper thumbnail</p>
                  </div>
                </div>

                {/* Buttons moved to the footer */}
              </form>
            </div>

            {/* Footer with sticky position */}
            <div className="sticky bottom-0 bg-white px-6 py-4 border-t flex justify-end space-x-3 z-10">
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setEditingId(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                form="whitepaper-form"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center"
              >
                {editingId ? (
                  <>
                    <Edit className="h-4 w-4 mr-2" />
                    Update Whitepaper
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Whitepaper
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="grid md:grid-cols-3 gap-6">
        {whitepapers.length === 0 ? (
          <div className="col-span-3 bg-gray-50 p-8 rounded-lg text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">No whitepapers found</h3>
            <p className="text-gray-500">Click the "Add Whitepaper" button to create your first whitepaper.</p>
          </div>
        ) : (
          whitepapers.map((item) => (
            <div key={item.id} className="bg-white rounded-lg shadow overflow-hidden hover:shadow-md transition-shadow border border-gray-100">
              <div className="relative">
                {item.thumbnail_url ? (
                  <img
                    src={item.thumbnail_url}
                    alt={item.title}
                    className="w-full h-40 object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.src = 'https://via.placeholder.com/400x200?text=No+Image';
                    }}
                  />
                ) : (
                  <div className="w-full h-40 bg-gray-100 flex items-center justify-center">
                    <FileText className="h-10 w-10 text-gray-300" />
                  </div>
                )}
                <div className="absolute top-2 right-2 flex space-x-1">
                  <button
                    onClick={() => handleEdit(item)}
                    className="p-1.5 bg-white rounded-full shadow hover:bg-blue-50 transition-colors"
                    title="Edit Whitepaper"
                  >
                    <Edit className="h-4 w-4 text-blue-600" />
                  </button>
                  <button
                    onClick={() => handleDelete(item.id)}
                    className="p-1.5 bg-white rounded-full shadow hover:bg-red-50 transition-colors"
                    title="Delete Whitepaper"
                  >
                    <Trash2 className="h-4 w-4 text-red-600" />
                  </button>
                </div>
              </div>

              <div className="p-4">
                <h3 className="text-lg font-bold line-clamp-1 mb-1">{item.title}</h3>
                <p className="text-gray-600 text-sm line-clamp-2 mb-2 min-h-[40px]">{item.description}</p>

                <div className="flex flex-wrap gap-2 mt-4 pt-3 border-t border-gray-100">
                  {item.url && (
                    <a
                      href={item.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm px-3 py-1.5 bg-blue-50 text-blue-600 rounded flex items-center hover:bg-blue-100 transition-colors font-medium"
                    >
                      <ExternalLink className="h-4 w-4 mr-1.5" /> View Link
                    </a>
                  )}
                  {item.file_url && (
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        const filename = item.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.pdf';
                        setDownloadingFileId(item.id);
                        downloadFile(item.file_url!, filename, {
                          onComplete: () => setDownloadingFileId(null),
                          onError: () => setDownloadingFileId(null)
                        });
                      }}
                      disabled={downloadingFileId === item.id}
                      className={`text-sm px-3 py-1.5 rounded flex items-center transition-colors font-medium ${downloadingFileId === item.id ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-green-50 text-green-600 hover:bg-green-100'}`}
                    >
                      {downloadingFileId === item.id ? (
                        <>
                          <div className="h-4 w-4 mr-1.5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                          Downloading...
                        </>
                      ) : (
                        <>
                          <Download className="h-4 w-4 mr-1.5" /> Download PDF
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn"
          onClick={(e) => {
            // Close modal when clicking outside
            if (e.target === e.currentTarget) {
              cancelDelete();
            }
          }}
          onKeyDown={(e) => {
            // Close modal when pressing Escape key
            if (e.key === 'Escape') {
              cancelDelete();
            }
          }}
          tabIndex={0}
        >
          <div className="bg-white rounded-lg shadow-xl overflow-hidden max-w-md w-full mx-4 transform transition-all duration-300 ease-in-out animate-scaleIn">
            <div className="bg-gradient-to-r from-red-600 to-red-700 px-6 py-4 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <AlertCircle className="h-6 w-6 mr-2" />
                  <h3 className="text-lg font-semibold">Delete Whitepaper</h3>
                </div>
                <button
                  onClick={cancelDelete}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete this whitepaper? This action cannot be undone.
              </p>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelDelete}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
