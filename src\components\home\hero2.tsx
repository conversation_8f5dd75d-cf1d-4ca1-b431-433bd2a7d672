import React from 'react';
import { Link } from 'react-router-dom';
import SphereWaveAnimation from '../animations/SphereWaveAnimation';

const Hero: React.FC = () => {
    return (
        <section className="relative w-full h-[500px] sm:h-[600px] md:h-[650px] lg:h-[600px] xl:h-[620px] 2xl:h-[720px] overflow-hidden font-inter mb-2 rounded-lg">
            {/* Sphere Wave Animation Background */}
            <div className="absolute inset-0 w-full h-full bg-gradient-to-b from-gray-900 via-blue-900 to-black">
                <SphereWaveAnimation className="w-full h-full" />
                {/* Subtle overlay for better text readability */}
                <div className="absolute inset-0 bg-black/20"></div>
            </div>

            {/* Content Container */}
            <div className="relative h-full flex flex-col justify-center items-center px-6 sm:px-8 md:px-10 lg:px-12 xl:px-16 2xl:px-20 text-center z-10">
                <div className="max-w-[914px] mx-auto flex flex-col items-center gap-6 sm:gap-8 lg:gap-[63px]">
                    {/* Text Content */}
                    <div className="flex flex-col items-center gap-3 sm:gap-4">
                        <h1
                            className="text-[30px] md:text-[40px] lg:text-[52px] 2xl:text-[72px] font-semibold leading-[1.3] tracking-wide text-center animate-fadeInDown"
                            style={{ fontFamily: 'Platform, sans-serif' }}
                        >
                            <span className="text-white">Revolutionizing</span><br />
                            <span
                                className="bg-clip-text text-transparent"
                                style={{
                                    background: 'linear-gradient(90deg, #79D68F -58.06%, #7A8EDB 99.86%)',
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                }}
                            >
                                Public Health Response Systems
                            </span>
                        </h1>
                        <p className="text-base sm:text-lg md:text-xl lg:text-[22px] text-white/90 max-w-[510px] leading-[1.4] font-normal text-center px-4 sm:px-0 animate-fadeInDown animation-delay-400">
                            Empowering public health organizations with intelligent solutions for better patient outcomes
                        </p>
                    </div>

                    {/* Buttons */}
                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 lg:gap-[15px] w-full sm:w-auto animate-fadeInDown animation-delay-800">
                        <Link
                            to="/solutions"
                            className="px-4 sm:px-6 lg:px-[22px] py-3 sm:py-[14px] bg-white rounded-lg text-black font-semibold flex items-center justify-center gap-2 transition-all duration-200 hover:shadow-lg hover:scale-105 text-sm sm:text-base whitespace-nowrap"
                        >
                            Explore Solutions
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="flex-shrink-0">
                                <path d="M4.16667 10H15.8333M10 4.16667L15.8333 10L10 15.8333" stroke="currentColor" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                        </Link>
                        <Link
                            to="/book-a-demo"
                            className="px-4 sm:px-6 lg:px-[22px] py-3 sm:py-[14px] bg-transparent border border-white rounded-lg text-white font-semibold flex items-center justify-center gap-2 transition-all duration-200 hover:bg-white/10 text-sm sm:text-base whitespace-nowrap micro-button"
                        >
                            Book a Demo
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="flex-shrink-0 icon">
                                <path d="M4.16667 10H15.8333M10 4.16667L15.8333 10L10 15.8333" stroke="currentColor" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                        </Link>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Hero;