-- Drop existing view if it exists
DROP VIEW IF EXISTS support_tickets_with_users;

-- Create view for support tickets with user info
CREATE OR REPLACE VIEW support_tickets_with_users AS
SELECT 
  t.*,
  u.email as user_email,
  u.full_name as user_full_name,
  a.email as assignee_email,
  a.full_name as assignee_full_name,
  r.email as resolver_email,
  r.full_name as resolver_full_name
FROM support_tickets t
LEFT JOIN profiles u ON t.user_id = u.id
LEFT JOIN profiles a ON t.assigned_to = a.id
LEFT JOIN profiles r ON t.resolved_by = r.id;

-- Grant access to the view
GRANT SELECT ON support_tickets_with_users TO authenticated;

-- Create function to get recent tickets
CREATE OR REPLACE FUNCTION get_recent_tickets(limit_count integer DEFAULT 5)
RETURNS TABLE (
  id uuid,
  title text,
  description text,
  status text,
  priority text,
  category text,
  contact_name text,
  contact_phone text,
  created_at timestamptz,
  user_id uuid,
  user_email text,
  user_full_name text
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.title,
    t.description,
    t.status,
    t.priority,
    t.category,
    t.contact_name,
    t.contact_phone,
    t.created_at,
    t.user_id,
    u.email,
    u.full_name
  FROM support_tickets t
  LEFT JOIN profiles u ON t.user_id = u.id
  ORDER BY t.created_at DESC
  LIMIT limit_count;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_recent_tickets TO authenticated;