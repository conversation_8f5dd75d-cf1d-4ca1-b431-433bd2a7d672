-- Drop existing policies
DROP POLICY IF EXISTS "blog_posts_read_all" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_insert" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_update" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_delete" ON blog_posts;

-- Create new policies for blog_posts with proper admin access
CREATE POLICY "blog_posts_read_all"
  ON blog_posts
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "blog_posts_admin_all"
  ON blog_posts
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM standalone_admins 
      WHERE username = current_user
    )
  );

-- Grant necessary permissions
GRANT ALL ON blog_posts TO authenticated;
GRANT ALL ON blog_posts TO anon;
GRANT ALL ON blog_posts TO service_role;