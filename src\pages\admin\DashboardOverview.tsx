import { useState, useEffect } from 'react';
import {
  Users2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DollarSign,
  Layers,
  Refresh<PERSON><PERSON>,
  AlertCircle
} from 'lucide-react';
import { Bar, Line } from 'react-chartjs-2';
import { Chart as ChartJS, registerables } from 'chart.js';
import { supabase } from '../../lib/supabase';
import { Doughnut } from 'react-chartjs-2';

ChartJS.register(...registerables);

interface DashboardOverviewProps {
  stats: {
    admins: number;
    users: number;
    products: number;
    posts: number;
    media: number;
    support: number;
    jobs: number;
    revenue: number;
    contactSubmissions: number;
    subscriptions: number;
    invoices: number;
    webinars: number;
    events: number;
    guides: number;
    caseStudies: number;
    teams: number;
    demoRequests: number;
    consultingServices: number;
  };
}

export default function DashboardOverview({ stats }: DashboardOverviewProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [profileStats, setProfileStats] = useState({
    total: 0,
    withAvatar: 0,
    withFullName: 0,
    recentlyActive: 0,
    inactive: 0,
  });

  // Add state for admin last login
  const [adminLastLogin, setAdminLastLogin] = useState<string | null>(null);

  // Chart data states
  const [userGrowthData, setUserGrowthData] = useState({
    labels: [] as string[],
    datasets: [{
      label: 'New Users',
      data: [] as number[],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.5)',
    }]
  });

  const [revenueData, setRevenueData] = useState({
    labels: [] as string[],
    datasets: [{
      label: 'Revenue ($)',
      data: [] as number[],
      backgroundColor: 'rgba(124, 58, 237, 0.5)',
    }]
  });

  const [userStatusData, setUserStatusData] = useState({
    labels: ['Active', 'Inactive'],
    datasets: [{
      data: [0, 0],
      backgroundColor: [
        'rgba(75, 192, 192, 0.5)',
        'rgba(255, 99, 132, 0.5)'
      ],
      borderColor: [
        'rgba(75, 192, 192, 1)',
        'rgba(255, 99, 132, 1)'
      ],
      borderWidth: 1,
    }]
  });

  // Enhanced chart options with better tooltips and formatting
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const label = context.dataset.label || '';
            const value = context.raw;

            if (context.chart.config.type === 'doughnut') {
              const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
              const percentage = Math.round((value / total) * 100);
              return `${context.label}: ${value} (${percentage}%)`;
            }

            return `${label}: ${value}`;
          }
        }
      }
    },
  };

  const lineChartOptions = {
    ...chartOptions,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
      x: {
        grid: {
          display: false
        }
      }
    }
  };

  const [recentSubscriptions, setRecentSubscriptions] = useState<Array<{
    userName: string;
    productName: string;
    status: string;
  }>>([]);

  const fetchDashboardData = async () => {
    setLoading(true);
    setError(null);
    try {
      // Fetch profiles data
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, email, full_name, avatar_url, created_at, updated_at')
        .order('created_at', { ascending: true });

      if (profilesError) throw new Error(profilesError.message);

      // Fetch subscriptions with product and user data
      const { data: subscriptions, error: subscriptionsError } = await supabase
        .from('subscriptions')
        .select(`
          id,
          status,
          created_at,
          current_period_end,
          user_id,
          subscription_items(
            id,
            quantity,
            amount,
            product:product_id(id, name, price)
          )
        `)
        .order('created_at', { ascending: false })

      if (subscriptionsError) throw new Error(subscriptionsError.message);

      const subscriptionsData = subscriptions || [];
      setRecentSubscriptions(subscriptionsData.map(sub => {
        // Handle multiple subscription items
        let productName = "No products";
        if (sub.subscription_items && sub.subscription_items.length > 0) {
          if (sub.subscription_items.length === 1) {
            productName = sub.subscription_items[0]?.product?.name || 'Unknown';
          } else {
            // For multiple items, show the first one plus count of additional items
            productName = `${sub.subscription_items[0]?.product?.name || 'Unknown'} +${sub.subscription_items.length - 1} more`;
          }
        }

        return {
          userName: profiles.find(p => p.id === sub.user_id)?.email || 'Unknown',
          productName: productName,
          productPrice: sub.subscription_items?.[0]?.amount || 0, // Use amount instead of product.price
          productQuantity: sub.subscription_items?.[0]?.quantity || 0,
          status: sub.status
        };
      }));

      // Calculate monthly revenue for chart
      const monthlyRevenue = subscriptionsData.reduce((acc, sub) => {
        if (sub.status === 'active' || sub.status === 'trialing') {
          const date = new Date(sub.created_at);
          const monthYear = date.toLocaleString('default', { month: 'short', year: 'numeric' });
          // Calculate total amount from all subscription items
          const totalAmount = sub.subscription_items?.reduce((itemSum, item) =>
            itemSum + (item.amount || 0), 0) || 0;
          acc[monthYear] = (acc[monthYear] || 0) + totalAmount;
        }
        return acc;
      }, {} as Record<string, number>);

      // Sort chronologically
      const sortedMonths = Object.keys(monthlyRevenue).sort((a, b) => {
        const dateA = new Date(a);
        const dateB = new Date(b);
        return dateA.getTime() - dateB.getTime();
      });

      setRevenueData({
        labels: sortedMonths,
        datasets: [{
          label: 'Revenue ($)',
          data: sortedMonths.map(month => monthlyRevenue[month]),
          backgroundColor: 'rgba(124, 58, 237, 0.7)',
        }]
      });

      // Calculate total revenue from active subscriptions
      const totalRevenue = subscriptionsData
        .filter(sub => sub.status === 'active' || sub.status === 'trialing')
        .reduce((sum, sub) => {
          // Sum up amounts from all subscription items
          const subTotal = sub.subscription_items?.reduce((itemSum, item) => {
            // Ensure amount is treated as a number
            return itemSum + (Number(item.amount) || 0);
          }, 0) || 0;
          return sum + subTotal;
        }, 0);

      // Update stats with calculated revenue
      stats.revenue = totalRevenue;

      const profilesData = profiles || [];

      // Calculate profile statistics
      if (profilesData.length > 0) {
        const withAvatar = profilesData.filter(p => p.avatar_url).length;
        const withFullName = profilesData.filter(p => p.full_name).length;

        // Consider profiles updated in the last 30 days as recently active
        const now = new Date();
        const thirtyDaysAgo = new Date(now.setDate(now.getDate() - 30));
        const recentlyActive = profilesData.filter(p =>
          new Date(p.updated_at) > thirtyDaysAgo
        ).length;
        const inactive = profilesData.length - recentlyActive;


        setProfileStats({
          total: profilesData.length,
          withAvatar,
          withFullName,
          recentlyActive,
          inactive,
        });

        // Process user growth data - group by month
        const monthlyUserCounts = profilesData.reduce((acc, profile) => {
          const date = new Date(profile.created_at);
          const monthYear = date.toLocaleString('default', { month: 'short', year: 'numeric' });
          acc[monthYear] = (acc[monthYear] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        // Sort chronologically
        const sortedMonths = Object.keys(monthlyUserCounts).sort((a, b) => {
          const dateA = new Date(a);
          const dateB = new Date(b);
          return dateA.getTime() - dateB.getTime();
        });

        // Get the actual counts per month (not cumulative)
        const monthlyCounts = sortedMonths.map(month => monthlyUserCounts[month]);

        setUserGrowthData({
          labels: sortedMonths,
          datasets: [{
            label: 'New Users',
            data: monthlyCounts,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.5)',
            tension: 0.3,
          }]
        });

        // Set user status data based on profile activity
        setUserStatusData({
          labels: ['Active Users', 'Inactive Users'],
          datasets: [{
            data: [recentlyActive, profilesData.length - recentlyActive],
            backgroundColor: [
              '#22c55e',
              '#9333ea'
            ],
            borderColor: [
              'rgba(22, 163, 74, 1)',
              'rgba(147, 51, 234, 1)'
            ],
            borderWidth: 0.5,
          }]
        });
      }
    } catch (err: any) {
      console.error('Error fetching dashboard data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch admin last login
  const fetchAdminLastLogin = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/get-users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          page: 1,
          pageSize: 10,
          roleFilter: 'admin',
        })
      });
      const result = await response.json();
      const adminUsers = result.data || [];
      let lastLogin = null;
      if (adminUsers.length > 0) {
        const sorted = [...adminUsers].sort((a, b) => new Date(b.last_sign_in_at || 0).getTime() - new Date(a.last_sign_in_at || 0).getTime());
        lastLogin = sorted[0]?.last_sign_in_at || null;
      }
      let formattedLastLogin = null;
      if (lastLogin) {
        const lastLoginDate = new Date(lastLogin);
        const now = new Date();
        const timeDiff = now.getTime() - lastLoginDate.getTime();
        if (timeDiff < 60000) {
          formattedLastLogin = 'Just now';
        } else if (timeDiff < 3600000) {
          const minutes = Math.floor(timeDiff / 60000);
          formattedLastLogin = `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
        } else if (timeDiff < 86400000) {
          const hours = Math.floor(timeDiff / 3600000);
          formattedLastLogin = `${hours} hour${hours !== 1 ? 's' : ''} ago`;
        } else if (timeDiff < 604800000) {
          const days = Math.floor(timeDiff / 86400000);
          formattedLastLogin = `${days} day${days !== 1 ? 's' : ''} ago`;
        } else {
          formattedLastLogin = lastLoginDate.toLocaleDateString();
        }
      } else if (lastLogin === null) {
        formattedLastLogin = null;
      }
      setAdminLastLogin(formattedLastLogin !== null ? formattedLastLogin : (lastLogin ? new Date(lastLogin).toLocaleDateString() : null));
    } catch (err) {
      setAdminLastLogin(null);
    }
  };

  // Refresh data function
  const handleRefresh = () => {
    setRefreshing(true);
    fetchDashboardData();
  };

  useEffect(() => {
    fetchDashboardData();
    fetchAdminLastLogin();
  }, []);

  return (
    <div className="space-y-6">
      {/* Dashboard Header with Refresh Button */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">Dashboard Overview</h2>
        <button
          onClick={handleRefresh}
          disabled={loading || refreshing}
          className="flex items-center gap-2 px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>{refreshing ? 'Refreshing...' : 'Refresh Data'}</span>
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start gap-2">
          <AlertCircle className="h-5 w-5 mt-0.5" />
          <div>
            <p className="font-semibold">Error loading dashboard data</p>
            <p className="text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Two Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Content Stats */}
        <div className="lg:col-span-2 space-y-6">
          {/* User Stats */}
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold mb-6">User Statistics</h3>
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <Users2 className="h-8 w-8 text-blue-600" />
                  <span className="text-sm text-blue-600 flex items-center">
                    {profileStats.total > 0 ? '▲' : '▼'}
                    {profileStats.total > 0 ? Math.round((profileStats.recentlyActive / profileStats.total) * 100) : 0}%
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-2">Total Users</p>
                <p className="text-2xl font-bold">{profileStats.total || stats.users}</p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <UserCheck className="h-8 w-8 text-[#22c55e]" />
                  <span className="text-sm text-[#22c55e] flex items-center">
                    {profileStats.withFullName > 0 ? '▲' : '▼'}
                    {profileStats.total > 0 ? Math.round((profileStats.inactive / profileStats.total) * 100) : 0}%
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-2">Inactive Users</p>
                <p className="text-2xl font-bold">{profileStats.inactive}</p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="h-64 relative">
                {loading && <div className="absolute inset-0 bg-white/70 flex items-center justify-center">Loading...</div>}
                <Line data={userGrowthData} options={lineChartOptions} />
              </div>
              <div className="h-64 relative">
                {loading && <div className="absolute inset-0 bg-white/70 flex items-center justify-center">Loading...</div>}
                <Doughnut data={userStatusData} options={chartOptions} />
              </div>
            </div>
          </div>

          {/* Content Stats */}
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold mb-6">Content Overview</h3>
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="bg-purple-50 p-4 rounded-lg">
                <Layers className="h-8 w-8 text-purple-600 mb-2" />
                <p className="text-sm text-gray-600">Blog Posts</p>
                <p className="text-2xl font-bold">{stats.posts}</p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <Layers className="h-8 w-8 text-blue-600 mb-2" />
                <p className="text-sm text-gray-600">Webinars</p>
                <p className="text-2xl font-bold">{stats.webinars}</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <Layers className="h-8 w-8 text-[#22c55e] mb-2" />
                <p className="text-sm text-gray-600">WhitePapers & Guides</p>
                <p className="text-2xl font-bold">{stats.media + stats.guides}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Admin & System Stats */}
        <div className="space-y-6">
          {/* Admin Stats */}
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold mb-6">Admin Statistics</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Admins</span>
                <span className="font-semibold">{stats.admins}</span>
              </div>
              {/* <div className="flex justify-between items-center">
                <span className="text-gray-600">Active Sessions</span>
                <span className="font-semibold">{Math.floor(stats.admins * 0.6)}</span>
              </div> */}
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Last Login</span>
                <span className="font-semibold">
                  {adminLastLogin || 'No recent logins'}
                  {loading && <span className="ml-2 text-xs text-gray-400">(loading...)</span>}
                </span>
              </div>
            </div>
          </div>

          {/* Revenue Stats */}
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold mb-6">Revenue</h3>
            <div className="flex items-center justify-between mb-4">
              <DollarSign className="h-8 w-8 text-[#22c55e]" />
              <span className="text-2xl font-bold">
                ${(Number(stats.revenue) || 0).toFixed(2)}
              </span>
            </div>
            <div className="h-48 relative">
              {loading && <div className="absolute inset-0 bg-white/70 flex items-center justify-center">Loading...</div>}
              <Bar data={revenueData} options={chartOptions} />
            </div>
          </div>

          {/* System Status */}
          <div className="bg-white p-6 rounded-xl shadow-md">
            <div className="space-y-4">

              {/* Recent Subscriptions Section */}
              <div className="mt-4">
                <h4 className="text-md font-medium mb-2">Recent Subscriptions</h4>
                {loading ? (
                  <div className="text-gray-500">Loading...</div>
                ) : recentSubscriptions.length > 0 ? (
                  <div className="space-y-2">
                    {recentSubscriptions.slice(0, 3).map((sub, index) => (
                      <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <div>
                          <p className="font-medium">{sub.userName}</p>
                          <p className="text-sm text-gray-600">{sub.productName}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${sub.status === 'active' ? 'bg-green-100 text-green-800' :
                          sub.status === 'trialing' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                          {sub.status}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500">No recent subscriptions</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}