/*
  # Clear Users and Admin Users

  1. Changes
    - Remove all existing users from auth.users
    - Remove all existing records from admin_users
    - Remove all existing records from profiles
    
  2. Security
    - Maintains table structures and policies
    - Only removes data
*/

-- First remove data from dependent tables
DELETE FROM profiles;
DELETE FROM admin_users;

-- Then remove from auth.users
DELETE FROM auth.users;