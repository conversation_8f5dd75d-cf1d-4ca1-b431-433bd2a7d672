/*
  # Fix admin user setup

  1. Changes
    - Properly handle existing admin user cleanup
    - Create admin user with proper password hashing
    - Set up RLS policies
    - Add verification functions

  2. Security
    - Enable RLS
    - Add secure policies
    - Proper password hashing
*/

-- First clean up any existing admin data
DO $$ 
BEGIN
  -- Delete from profiles first due to foreign key constraint
  DELETE FROM profiles WHERE email = '<EMAIL>';
  -- Then delete from admin_users
  DELETE FROM admin_users WHERE email = '<EMAIL>';
  -- Finally delete from auth.users
  DELETE FROM auth.users WHERE email = '<EMAIL>';
END $$;

-- Create the admin user in auth.users
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role,
  aud,
  confirmation_token
)
VALUES (
  '00000000-0000-0000-0000-000000000000'::uuid,
  '00000000-0000-0000-0000-000000000000'::uuid,
  '<EMAIL>',
  crypt('11111111', gen_salt('bf')),
  now(),
  now(),
  now(),
  '{"provider":"email","providers":["email"]}'::jsonb,
  '{"name":"Admin User"}'::jsonb,
  false,
  'authenticated',
  'authenticated',
  'confirmed'
);

-- Add admin user to admin_users table
INSERT INTO admin_users (
  id,
  email,
  role,
  created_at
)
VALUES (
  '00000000-0000-0000-0000-000000000000'::uuid,
  '<EMAIL>',
  'admin',
  now()
);

-- Add admin user to profiles table if it doesn't exist
INSERT INTO profiles (
  id,
  email,
  full_name,
  created_at,
  updated_at
)
SELECT
  '00000000-0000-0000-0000-000000000000'::uuid,
  '<EMAIL>',
  'Admin User',
  now(),
  now()
WHERE NOT EXISTS (
  SELECT 1 FROM profiles WHERE email = '<EMAIL>'
);

-- Ensure proper RLS policies
DROP POLICY IF EXISTS "Admin users can view own data" ON admin_users;
DROP POLICY IF EXISTS "Admin users can update own data" ON admin_users;

CREATE POLICY "Admin users can view own data"
  ON admin_users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Admin users can update own data"
  ON admin_users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Function to verify admin status
CREATE OR REPLACE FUNCTION verify_admin(user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM admin_users
    WHERE id = user_id
    AND role = 'admin'
  );
END;
$$;

-- Function to check admin credentials
CREATE OR REPLACE FUNCTION check_admin_credentials(email text, password text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM auth.users u
    JOIN admin_users a ON u.id = a.id
    WHERE u.email = check_admin_credentials.email
    AND u.encrypted_password = crypt(check_admin_credentials.password, u.encrypted_password)
    AND a.role = 'admin'
  );
END;
$$;