-- Create users table
CREATE TABLE users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  password_hash text NOT NULL,
  full_name text,
  created_at timestamptz DEFAULT now(),
  last_login timestamptz,
  CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can read own data"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own data"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid()::text = id::text);

-- Function to create new user
CREATE OR REPLACE FUNCTION create_user(
  user_email text,
  user_password text,
  user_full_name text
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
BEGIN
  -- Validate email format
  IF NOT (user_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$') THEN
    RAISE EXCEPTION 'Invalid email format';
  END IF;

  -- Check if email already exists
  IF EXISTS (SELECT 1 FROM users WHERE email = user_email) THEN
    RAISE EXCEPTION 'Email already registered';
  END IF;

  -- Create new user
  INSERT INTO users (email, password_hash, full_name)
  VALUES (
    user_email,
    crypt(user_password, gen_salt('bf')),
    user_full_name
  )
  RETURNING id INTO new_user_id;

  RETURN new_user_id;
END;
$$;

-- Function to verify user credentials
CREATE OR REPLACE FUNCTION verify_user(
  user_email text,
  user_password text
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM users
    WHERE email = user_email
    AND password_hash = crypt(user_password, password_hash)
  );
END;
$$;

-- Function to update last login
CREATE OR REPLACE FUNCTION update_user_last_login(user_email text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE users
  SET last_login = now()
  WHERE email = user_email;
END;
$$;

-- Grant necessary permissions
GRANT ALL ON users TO authenticated;
GRANT EXECUTE ON FUNCTION create_user TO anon;
GRANT EXECUTE ON FUNCTION verify_user TO anon;
GRANT EXECUTE ON FUNCTION update_user_last_login TO authenticated;