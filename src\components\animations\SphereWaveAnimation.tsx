import React, { useEffect, useRef } from 'react';

interface SphereWaveAnimationProps {
  className?: string;
}

const SphereWaveAnimation: React.FC<SphereWaveAnimationProps> = ({ className = '' }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const spheresRef = useRef<any[]>([]);
  const droppingSphereRef = useRef<any>(null);
  const waveRef = useRef<any[]>([]);
  const waveCompletionRef = useRef<any>(null); // Track wave completion

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth * window.devicePixelRatio;
      canvas.height = canvas.offsetHeight * window.devicePixelRatio;
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize floor spheres with realistic 3D positioning
    const initFloorSpheres = () => {
      spheresRef.current = [];
      const baseSphereSize = 75; // Same size as falling sphere
      const rows = 20; // Many more rows for extensive depth
      const vanishingPointY = canvas.offsetHeight * 0.35; // More natural vanishing point
      const floorStartY = canvas.offsetHeight - 60; // Floor position

      // Realistic 3D spacing - fewer balls per line but same line length
      const frontSpacing = baseSphereSize * 6.0; // Much more spacing between balls for fewer per line
      const rowSpacing = baseSphereSize * 3.0; // More spacing between rows for 3D depth
      const cols = Math.floor(canvas.offsetWidth / frontSpacing) + 6; // Extend lines to maintain length
      const centerX = canvas.offsetWidth / 2;

      // 3D depth parameters
      const maxDepth = 800; // Virtual 3D depth
      const cameraDistance = 600; // Camera distance for perspective

      // Calculate center column and target row for the falling sphere
      const centerCol = Math.floor(cols / 2);
      const targetRow = 2; // 3rd row (0-indexed) where falling sphere will land

      // Create perfectly aligned columns that run from front to back
      for (let col = 0; col < cols; col++) {
        // Calculate the X position for this column (fixed for all rows)
        const columnX = centerX + ((col - cols / 2) * frontSpacing);

        // Create spheres in this column from front to back
        for (let row = 0; row < rows; row++) {
          // Handle the center position in the 3rd row - make it invisible for the falling sphere
          const isTargetPosition = (col === centerCol && row === targetRow);

          // Don't skip - create the sphere but make it invisible if it's the target position
          // This maintains the grid structure while keeping the landing spot visually empty
          // Calculate real 3D position
          const z3D = (row / (rows - 1)) * maxDepth; // 0 to maxDepth
          const depthRatio = row / (rows - 1); // 0 (front) to 1 (back)

          // Proper perspective projection
          const perspectiveFactor = cameraDistance / (cameraDistance + z3D);
          const sphereRadius = baseSphereSize * perspectiveFactor;

          // Skip spheres that are too small to be visible
          if (sphereRadius < 15) continue; // Increased minimum size for larger 75px spheres

          // X position - perfectly straight lines in 3D space
          const x = centerX + ((columnX - centerX) * perspectiveFactor);

          // Y position with proper perspective
          const baseY = floorStartY - sphereRadius; // Sit on floor
          const y = vanishingPointY + ((baseY - vanishingPointY) * perspectiveFactor);

          // Calculate 3D world position for physics
          const worldX = columnX - centerX; // World X relative to center
          const worldY = 0; // On the floor
          const worldZ = z3D;

          // Realistic lighting based on distance and 3D position
          const distanceFade = perspectiveFactor; // Use perspective factor for distance
          const baseBrightness = Math.max(0.01, 0.04 * distanceFade); // Darker base

          // Ambient lighting from environment
          const ambientLight = 0.08 * distanceFade;

          // Distance-based atmospheric scattering
          const atmosphericScattering = Math.max(0, 0.02 * (1 - distanceFade));

          // Make target position invisible until falling ball lands
          // Make all floor balls very dim initially for dark floor effect
          const floorDarkness = 0.15; // Very dark floor effect

          // Add aggressive depth-based darkness to completely hide line endings
          const depthDarkness = Math.pow(1 - depthRatio, 0.2); // Much stronger darkness at the back
          const endLineDarkness = depthRatio > 0.5 ? Math.pow(1 - ((depthRatio - 0.5) / 0.5), 3) : 1; // Fade out last 50% of depth aggressively
          const finalFade = depthRatio > 0.8 ? Math.pow(1 - ((depthRatio - 0.8) / 0.2), 5) : 1; // Complete fade in last 20%

          const brightness = isTargetPosition ? 0 : ((baseBrightness + ambientLight + atmosphericScattering) * floorDarkness * depthDarkness * endLineDarkness * finalFade);

          // Atmospheric depth for fog/haze effect
          const atmosphericDepth = perspectiveFactor;

          spheresRef.current.push({
            x,
            y,
            z: row, // Depth for 3D calculations
            z3D, // Real 3D depth
            radius: sphereRadius,
            originalRadius: sphereRadius,
            brightness,
            originalBrightness: brightness,
            waveIntensity: 0,
            row,
            col,
            perspectiveScale: perspectiveFactor, // Use perspective factor
            depthRatio,
            atmosphericDepth,
            worldX,
            worldY,
            worldZ,
            // Physics properties
            mass: sphereRadius * 0.1, // Mass based on size
            velocity: { x: 0, y: 0, z: 0 },
            acceleration: { x: 0, y: 0, z: 0 },
            material: 'asteroid', // Dark rocky asteroid material
            // Asteroid properties for realistic rocky appearance
            asteroidRotation: Math.random() * Math.PI * 2, // Random starting rotation
            rockPattern: Math.random(), // Random rock formation pattern
            surfaceRoughness: 0.9 + Math.random() * 0.1, // Very rough rocky surface
            rockFormation: Math.random(), // Different rock formations (0-1)
            mineralDeposits: Math.random(), // Mineral vein patterns (0-1)
            weathering: 0.3 + Math.random() * 0.4, // Surface weathering and erosion
            rockType: Math.floor(Math.random() * 3), // 0=basalt, 1=granite, 2=iron-rich
            isTargetPosition: isTargetPosition, // Mark if this is the invisible target position
            isVisible: !isTargetPosition // Initially invisible if it's the target position
          });
        }
      }
    };

    // Initialize dropping sphere with realistic physics
    const initDroppingSphere = () => {
      const centerX = canvas.offsetWidth / 2;
      const startHeight = -120; // Start higher for more dramatic effect

      // Calculate 3rd row target position - must match the reserved empty spot
      const targetRow = 2; // 3rd row (0-indexed)
      const maxDepth = 800;
      const targetZ3D = (targetRow / 19) * maxDepth; // 19 is rows-1 (20 rows total)
      const targetPerspectiveFactor = 600 / (600 + targetZ3D); // 600 is cameraDistance

      // Calculate exact target position in the reserved empty spot
      const baseSphereSize = 75; // Match the larger sphere size
      const frontSpacing = baseSphereSize * 6.0; // Match the new larger spacing
      const cols = Math.floor(canvas.offsetWidth / frontSpacing) + 6; // Match the new column count
      const centerCol = Math.floor(cols / 2);
      const vanishingPointY = canvas.offsetHeight * 0.35;

      // Calculate exact screen position for the center column in 3rd row
      const columnX = centerX + ((centerCol - cols / 2) * frontSpacing);
      const targetScreenX = centerX + ((columnX - centerX) * targetPerspectiveFactor);
      const baseY = canvas.offsetHeight - 60 - (75 * targetPerspectiveFactor); // Sit on floor with same size
      const targetScreenY = vanishingPointY + ((baseY - vanishingPointY) * targetPerspectiveFactor);

      droppingSphereRef.current = {
        // Screen position - start above the target 3rd row position
        x: targetScreenX, // Start directly above the target X position
        y: startHeight,
        radius: 75, // Same size as floor spheres

        // 3D world position - targeting 3rd row
        worldX: (centerCol - cols / 2) * frontSpacing, // Exact world X for center column
        worldY: 600, // High above the floor
        worldZ: targetZ3D, // Target 3rd row depth

        // Target position for smooth trajectory
        targetScreenX: targetScreenX,
        targetScreenY: targetScreenY,
        targetZ3D: targetZ3D,
        targetPerspectiveFactor: targetPerspectiveFactor,
        targetWorldX: (centerCol - cols / 2) * frontSpacing, // Target world X position

        // Physics properties
        velocity: { x: 0, y: 0, z: 0 },
        acceleration: { x: 0, y: 9.81 * 2.8, z: 0 }, // Much faster gravity for dramatic effect
        mass: 7.8, // Heavy metallic sphere
        drag: 0.008, // Very low air resistance for very fast fall

        // Material properties - Neptune-like ice giant
        material: 'neptune',
        reflectivity: 0.6,
        roughness: 0.3,

        // Neptune-like properties
        neptuneRotation: 0,
        iceIntensity: 1.0,
        atmosphericWinds: 0,
        stormActivity: 0,

        // Animation state
        brightness: 2.0, // Bright but cold luminescence
        isDropping: true,
        hasTriggeredWave: false,
        waveCount: 0,
        targetColumn: 'center',

        // Rotation for realism
        rotation: { x: 0, y: 0, z: 0 },
        angularVelocity: { x: 0.02, y: 0.01, z: 0 }
      };
    };

    // Create Z-axis wave effect with proper completion tracking
    const createWave = (impactX: number, impactY: number) => {
      // Increment wave count
      if (droppingSphereRef.current) {
        droppingSphereRef.current.waveCount++;
      }

      waveRef.current = [];
      const currentTime = Date.now();
      let maxWaveEndTime = currentTime;

      // Group spheres by row (Z-depth) and create wave that travels from front to back
      const rowGroups: { [key: number]: any[] } = {};
      spheresRef.current.forEach((sphere, index) => {
        if (!rowGroups[sphere.row]) {
          rowGroups[sphere.row] = [];
        }
        rowGroups[sphere.row].push({ sphere, index });
      });

      // Create wave that propagates along Z-axis (row by row from front to back)
      Object.keys(rowGroups).forEach(rowKey => {
        const row = parseInt(rowKey);
        const spheresInRow = rowGroups[row];

        // Wave travels from front (row 0) to back (higher rows)
        const zDelay = row * 150; // 150ms delay between each row
        const waveDuration = 800;
        const waveEndTime = currentTime + zDelay + waveDuration;

        // Track the latest end time
        if (waveEndTime > maxWaveEndTime) {
          maxWaveEndTime = waveEndTime;
        }

        spheresInRow.forEach(({ sphere, index }) => {
          // All spheres in the same row light up simultaneously
          const intensity = 1.0; // Full intensity for all spheres in the wave

          waveRef.current.push({
            sphereIndex: index,
            delay: zDelay,
            intensity,
            startTime: currentTime + zDelay,
            duration: waveDuration,
            row: sphere.row
          });
        });
      });

      // Set wave completion time
      waveCompletionRef.current = {
        endTime: maxWaveEndTime,
        waveNumber: droppingSphereRef.current?.waveCount || 0
      };
    };

    // Conservative occlusion - only hide spheres that are COMPLETELY blocked
    const isOccluded = (sphere: any, allSpheres: any[]) => {
      // Check all spheres in front of this one
      for (let i = 0; i < allSpheres.length; i++) {
        const frontSphere = allSpheres[i];

        // Only check spheres that are directly in front (one row ahead)
        if (frontSphere.row !== sphere.row - 1) continue;

        // Calculate 2D distance between sphere centers
        const dx = sphere.x - frontSphere.x;
        const dy = sphere.y - frontSphere.y;
        const distance2D = Math.sqrt(dx * dx + dy * dy);

        // Very strict occlusion - only hide if almost perfectly aligned
        const occlusionThreshold = Math.min(frontSphere.radius * 0.6, sphere.radius * 0.4);

        // Only hide if front sphere is much larger and perfectly aligned
        if (distance2D < occlusionThreshold && frontSphere.radius > sphere.radius * 1.3) {
          return true; // This sphere is completely occluded
        }
      }
      return false; // Keep most spheres visible
    };

    // Very gentle partial occlusion - mostly for subtle depth effect
    const calculateVisibleArea = (sphere: any, allSpheres: any[]) => {
      let visibilityFactor = 1.0; // Start with full visibility

      // Only apply very subtle effects for immediate neighbors
      for (let i = 0; i < allSpheres.length; i++) {
        const frontSphere = allSpheres[i];

        // Only check spheres directly in front (one row ahead)
        if (frontSphere.row !== sphere.row - 1) continue;

        const dx = sphere.x - frontSphere.x;
        const dy = sphere.y - frontSphere.y;
        const distance2D = Math.sqrt(dx * dx + dy * dy);

        // Very gentle partial occlusion
        const overlapDistance = (frontSphere.radius + sphere.radius) - distance2D;
        if (overlapDistance > 0) {
          const overlapRatio = overlapDistance / (sphere.radius * 2);
          visibilityFactor *= (1 - overlapRatio * 0.15); // Maximum 15% reduction
        }
      }

      return Math.max(0.8, visibilityFactor); // Minimum 80% visibility
    };

    // Draw realistic 3D sphere with advanced lighting and materials
    const drawSphere = (x: number, y: number, radius: number, brightness: number, perspectiveScale: number = 1, isOnFloor: boolean = true, atmosphericDepth: number = 1, visibilityFactor: number = 1, material: string = 'metal', sphereData: any = null) => {
      // Apply visibility and atmospheric effects
      const adjustedBrightness = brightness * visibilityFactor * atmosphericDepth;
      const adjustedRadius = radius * Math.sqrt(visibilityFactor);

      // Multiple light sources for realistic lighting
      const lights = [
        { x: canvas.offsetWidth * 0.85, y: canvas.offsetHeight * 0.15, intensity: 1.0, color: [255, 255, 240] }, // Main light (warm white)
        { x: canvas.offsetWidth * 0.15, y: canvas.offsetHeight * 0.25, intensity: 0.4, color: [180, 200, 255] }, // Fill light (cool blue)
        { x: canvas.offsetWidth * 0.5, y: canvas.offsetHeight * 0.05, intensity: 0.6, color: [255, 255, 255] }  // Top light (pure white)
      ];

      // Calculate lighting from all sources
      let totalLighting = { r: 0, g: 0, b: 0, intensity: 0 };
      let lightDirections = [];

      lights.forEach(light => {
        const lightDirX = light.x - x;
        const lightDirY = light.y - y;
        const lightDistance = Math.sqrt(lightDirX * lightDirX + lightDirY * lightDirY);
        const lightIntensity = Math.max(0.05, light.intensity * (1 - (lightDistance / (canvas.offsetWidth * 1.2))));

        // Accumulate lighting
        totalLighting.r += light.color[0] * lightIntensity;
        totalLighting.g += light.color[1] * lightIntensity;
        totalLighting.b += light.color[2] * lightIntensity;
        totalLighting.intensity += lightIntensity;

        lightDirections.push({
          x: lightDirX / lightDistance,
          y: lightDirY / lightDistance,
          intensity: lightIntensity
        });
      });

      // Normalize lighting
      if (totalLighting.intensity > 0) {
        totalLighting.r /= totalLighting.intensity;
        totalLighting.g /= totalLighting.intensity;
        totalLighting.b /= totalLighting.intensity;
      }

      // Draw realistic contact shadow
      if (isOnFloor && adjustedRadius > 3) {
        const shadowOffset = adjustedRadius * 0.8; // Shadow directly below sphere
        const shadowRadius = adjustedRadius * 1.2; // Tighter, more realistic shadow
        const shadowOpacity = Math.max(0.2, 0.8 * perspectiveScale * atmosphericDepth * visibilityFactor * totalLighting.intensity);

        const shadowGradient = ctx.createRadialGradient(
          x, y + shadowOffset, 0,
          x, y + shadowOffset, shadowRadius
        );

        // More realistic shadow with soft edges
        shadowGradient.addColorStop(0, `rgba(0, 0, 0, ${shadowOpacity * 0.9})`);
        shadowGradient.addColorStop(0.3, `rgba(0, 0, 0, ${shadowOpacity * 0.6})`);
        shadowGradient.addColorStop(0.7, `rgba(0, 0, 0, ${shadowOpacity * 0.2})`);
        shadowGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

        ctx.fillStyle = shadowGradient;
        ctx.beginPath();
        ctx.ellipse(x, y + shadowOffset, shadowRadius, shadowRadius * 0.3, 0, 0, Math.PI * 2);
        ctx.fill();
      }

      // Realistic material-based rendering
      const primaryLight = lightDirections[0] || { x: 0.6, y: -0.8, intensity: 0.8 };

      // Calculate highlight position based on primary light
      const lightOffsetX = primaryLight.x * adjustedRadius * 0.3;
      const lightOffsetY = primaryLight.y * adjustedRadius * 0.3;

      // Create realistic sphere gradient based on material
      const sphereGradient = ctx.createRadialGradient(
        x + lightOffsetX, y + lightOffsetY, 0, // Highlight position
        x, y, adjustedRadius * 1.1 // Full sphere coverage
      );

      // Material-specific rendering
      if (material === 'neptune') {
        // Neptune-like ice giant with deep blue atmosphere and dynamic storms
        const time = Date.now() * 0.004;
        const windActivity = Math.sin(time * 1.5) * 0.3 + 0.7;
        const iceIntensity = adjustedBrightness * 1.2;

        // Neptune's characteristic deep blue colors with ice crystal effects
        const neptuneDeepBlue = [30, 80, 180];
        const neptuneBlue = [60, 120, 220];
        const neptuneLightBlue = [100, 160, 255];
        const neptuneIceBlue = [150, 200, 255];
        const neptuneWhite = [200, 230, 255];

        // Create dynamic atmospheric appearance with storm bands
        sphereGradient.addColorStop(0, `rgba(${neptuneWhite[0]}, ${neptuneWhite[1]}, ${neptuneWhite[2]}, ${iceIntensity})`);
        sphereGradient.addColorStop(0.15, `rgba(${Math.floor(neptuneIceBlue[0] * windActivity)}, ${Math.floor(neptuneIceBlue[1] * windActivity)}, ${Math.floor(neptuneIceBlue[2] * windActivity)}, 1.0)`);
        sphereGradient.addColorStop(0.35, `rgba(${Math.floor(neptuneLightBlue[0] * windActivity)}, ${Math.floor(neptuneLightBlue[1] * windActivity)}, ${Math.floor(neptuneLightBlue[2] * windActivity)}, 1.0)`);
        sphereGradient.addColorStop(0.65, `rgba(${Math.floor(neptuneBlue[0] * windActivity)}, ${Math.floor(neptuneBlue[1] * windActivity)}, ${Math.floor(neptuneBlue[2] * windActivity)}, 1.0)`);
        sphereGradient.addColorStop(0.85, `rgba(${Math.floor(neptuneDeepBlue[0] * windActivity)}, ${Math.floor(neptuneDeepBlue[1] * windActivity)}, ${Math.floor(neptuneDeepBlue[2] * windActivity)}, 1.0)`);
        sphereGradient.addColorStop(1, `rgba(${Math.floor(neptuneDeepBlue[0] * 0.6)}, ${Math.floor(neptuneDeepBlue[1] * 0.6)}, ${Math.floor(neptuneDeepBlue[2] * 0.6)}, 0.9)`);
      } else if (material === 'asteroid') {
        // Rocky asteroid material with realistic geological features
        const time = Date.now() * 0.001;
        const rockPattern = sphereData?.rockPattern || Math.random();
        const mineralDeposits = sphereData?.mineralDeposits || Math.random();
        const rockType = sphereData?.rockType || 0;

        const rockVariation = Math.sin(time * 0.3 + rockPattern * 10) * 0.15 + 0.85;
        const mineralGlow = Math.sin(time * 0.8 + mineralDeposits * 15) * 0.1 + 0.9;
        const asteroidIntensity = Math.max(0.08, adjustedBrightness * 0.25); // Very dark rocky base

        // Different rock types with unique color palettes
        let rockColors;
        if (rockType === 0) {
          // Basalt - dark volcanic rock
          rockColors = {
            dark: [25, 25, 30],
            medium: [45, 45, 50],
            light: [65, 65, 70],
            highlight: [85, 85, 90]
          };
        } else if (rockType === 1) {
          // Granite - speckled gray rock
          rockColors = {
            dark: [35, 35, 40],
            medium: [55, 55, 60],
            light: [75, 75, 80],
            highlight: [95, 95, 100]
          };
        } else {
          // Iron-rich - reddish-brown rock
          rockColors = {
            dark: [30, 25, 20],
            medium: [50, 40, 30],
            light: [70, 55, 40],
            highlight: [90, 70, 50]
          };
        }

        // Add mineral deposits that glow slightly
        const mineralIntensity = mineralDeposits > 0.7 ? mineralGlow * 1.2 : 1.0;

        sphereGradient.addColorStop(0, `rgba(${Math.floor(rockColors.highlight[0] * rockVariation * mineralIntensity)}, ${Math.floor(rockColors.highlight[1] * rockVariation * mineralIntensity)}, ${Math.floor(rockColors.highlight[2] * rockVariation * mineralIntensity)}, ${asteroidIntensity})`);
        sphereGradient.addColorStop(0.2, `rgba(${Math.floor(rockColors.light[0] * rockVariation)}, ${Math.floor(rockColors.light[1] * rockVariation)}, ${Math.floor(rockColors.light[2] * rockVariation)}, 1.0)`);
        sphereGradient.addColorStop(0.5, `rgba(${Math.floor(rockColors.medium[0] * rockVariation)}, ${Math.floor(rockColors.medium[1] * rockVariation)}, ${Math.floor(rockColors.medium[2] * rockVariation)}, 1.0)`);
        sphereGradient.addColorStop(0.8, `rgba(${Math.floor(rockColors.dark[0] * rockVariation)}, ${Math.floor(rockColors.dark[1] * rockVariation)}, ${Math.floor(rockColors.dark[2] * rockVariation)}, 1.0)`);
        sphereGradient.addColorStop(1, `rgba(${Math.floor(rockColors.dark[0] * 0.4)}, ${Math.floor(rockColors.dark[1] * 0.4)}, ${Math.floor(rockColors.dark[2] * 0.4)}, 0.9)`);
      } else if (material === 'illuminated') {
        // Illuminated asteroid material - realistic blue energy lighting when wave hits
        const energyIntensity = adjustedBrightness * 1.8; // Moderate brightness increase

        // Original blue energy colors for realistic lighting
        const energyWhite = [255, 255, 255];
        const energyLightBlue = [180, 220, 255];
        const energyBlue = [120, 180, 255];
        const energyDeepBlue = [80, 140, 220];

        // Create steady illuminated appearance without flashing
        sphereGradient.addColorStop(0, `rgba(${energyWhite[0]}, ${energyWhite[1]}, ${energyWhite[2]}, ${energyIntensity})`);
        sphereGradient.addColorStop(0.2, `rgba(${energyLightBlue[0]}, ${energyLightBlue[1]}, ${energyLightBlue[2]}, 1.0)`);
        sphereGradient.addColorStop(0.5, `rgba(${energyBlue[0]}, ${energyBlue[1]}, ${energyBlue[2]}, 1.0)`);
        sphereGradient.addColorStop(0.8, `rgba(${energyDeepBlue[0]}, ${energyDeepBlue[1]}, ${energyDeepBlue[2]}, 1.0)`);
        sphereGradient.addColorStop(1, `rgba(${Math.floor(energyDeepBlue[0] * 0.7)}, ${Math.floor(energyDeepBlue[1] * 0.7)}, ${Math.floor(energyDeepBlue[2] * 0.7)}, 0.9)`);
      } else if (material === 'energy') {
        // Energy material - bright blue energy when wave hits moon spheres
        const time = Date.now() * 0.008;
        const energyPulse = Math.sin(time * 3) * 0.3 + 0.7;
        const energyIntensity = adjustedBrightness * energyPulse;

        // Bright blue energy colors
        const energyWhite = [255, 255, 255];
        const energyLightBlue = [180, 220, 255];
        const energyBlue = [120, 180, 255];
        const energyDeepBlue = [80, 140, 220];

        // Create pulsating energy appearance
        sphereGradient.addColorStop(0, `rgba(${energyWhite[0]}, ${energyWhite[1]}, ${energyWhite[2]}, ${energyIntensity})`);
        sphereGradient.addColorStop(0.2, `rgba(${Math.floor(energyLightBlue[0] * energyPulse)}, ${Math.floor(energyLightBlue[1] * energyPulse)}, ${Math.floor(energyLightBlue[2] * energyPulse)}, 1.0)`);
        sphereGradient.addColorStop(0.5, `rgba(${Math.floor(energyBlue[0] * energyPulse)}, ${Math.floor(energyBlue[1] * energyPulse)}, ${Math.floor(energyBlue[2] * energyPulse)}, 1.0)`);
        sphereGradient.addColorStop(0.8, `rgba(${Math.floor(energyDeepBlue[0] * energyPulse)}, ${Math.floor(energyDeepBlue[1] * energyPulse)}, ${Math.floor(energyDeepBlue[2] * energyPulse)}, 1.0)`);
        sphereGradient.addColorStop(1, `rgba(${Math.floor(energyDeepBlue[0] * 0.7)}, ${Math.floor(energyDeepBlue[1] * 0.7)}, ${Math.floor(energyDeepBlue[2] * 0.7)}, 0.9)`);
      } else if (material === 'chrome' || !isOnFloor) {
        // Chrome/metallic material - highly reflective
        const reflectivity = 0.9;
        const baseColor = [200, 200, 210];
        const highlightColor = [255, 255, 255];

        sphereGradient.addColorStop(0, `rgba(${highlightColor[0]}, ${highlightColor[1]}, ${highlightColor[2]}, ${reflectivity})`);
        sphereGradient.addColorStop(0.2, `rgba(${Math.floor(baseColor[0] + totalLighting.r * 0.3)}, ${Math.floor(baseColor[1] + totalLighting.g * 0.3)}, ${Math.floor(baseColor[2] + totalLighting.b * 0.3)}, 0.95)`);
        sphereGradient.addColorStop(0.6, `rgba(${Math.floor(baseColor[0] * 0.7)}, ${Math.floor(baseColor[1] * 0.7)}, ${Math.floor(baseColor[2] * 0.7)}, 0.9)`);
        sphereGradient.addColorStop(1, `rgba(${Math.floor(baseColor[0] * 0.3)}, ${Math.floor(baseColor[1] * 0.3)}, ${Math.floor(baseColor[2] * 0.3)}, 0.8)`);
      } else {
        // Dark metallic material for floor spheres
        const baseIntensity = Math.max(0.1, adjustedBrightness);
        const lightBoost = totalLighting.intensity * 0.4;

        if (adjustedBrightness < 0.4) {
          // Dark state
          sphereGradient.addColorStop(0, `rgba(${Math.floor(120 + lightBoost * 80)}, ${Math.floor(120 + lightBoost * 80)}, ${Math.floor(130 + lightBoost * 70)}, 1.0)`);
          sphereGradient.addColorStop(0.3, `rgba(80, 80, 90, 1.0)`);
          sphereGradient.addColorStop(0.7, `rgba(40, 40, 50, 1.0)`);
          sphereGradient.addColorStop(1, `rgba(20, 20, 30, 1.0)`);
        } else {
          // Energized state
          sphereGradient.addColorStop(0, `rgba(255, 255, 255, 1.0)`);
          sphereGradient.addColorStop(0.2, `rgba(${Math.floor(180 + totalLighting.r * 0.2)}, ${Math.floor(200 + totalLighting.g * 0.2)}, 255, 1.0)`);
          sphereGradient.addColorStop(0.5, `rgba(120, 142, 219, 1.0)`);
          sphereGradient.addColorStop(1, `rgba(60, 80, 140, 1.0)`);
        }
      }

      // Add subtle atmospheric effects for Neptune (no overwhelming halo)
      if (material === 'neptune') {
        const time = Date.now() * 0.006;
        const windIntensity = (Math.sin(time * 0.8) * 0.4 + 0.6) * adjustedBrightness;

        // Add Great Dark Spot effect only
        const spotTime = time * 0.3;
        const spotX = x + Math.cos(spotTime) * adjustedRadius * 0.3;
        const spotY = y + Math.sin(spotTime) * adjustedRadius * 0.2;
        const spotGradient = ctx.createRadialGradient(spotX, spotY, 0, spotX, spotY, adjustedRadius * 0.4);

        spotGradient.addColorStop(0, `rgba(20, 40, 120, ${windIntensity * 0.6})`);
        spotGradient.addColorStop(0.6, `rgba(30, 60, 150, ${windIntensity * 0.3})`);
        spotGradient.addColorStop(1, 'rgba(40, 80, 180, 0)');

        ctx.fillStyle = spotGradient;
        ctx.beginPath();
        ctx.arc(spotX, spotY, adjustedRadius * 0.4, 0, Math.PI * 2);
        ctx.fill();
      }

      // Add surface effects for different materials
      if (material === 'asteroid' && adjustedRadius > 12) {
        const time = Date.now() * 0.002;
        const rockPattern = sphereData?.rockPattern || Math.random();
        const mineralDeposits = sphereData?.mineralDeposits || Math.random();
        const weathering = sphereData?.weathering || 0.5;

        // Add 3-5 rock formations and cracks for realism
        const numFeatures = 3 + Math.floor(rockPattern * 3);
        for (let i = 0; i < numFeatures; i++) {
          const featureAngle = (time * 0.05 + i * Math.PI * 0.6 + rockPattern * 10) % (Math.PI * 2);
          const featureDistance = adjustedRadius * (0.1 + i * 0.12);
          const featureX = x + Math.cos(featureAngle) * featureDistance;
          const featureY = y + Math.sin(featureAngle) * featureDistance;
          const featureSize = adjustedRadius * (0.06 + i * 0.03);

          // Create rock formation shadow/highlight
          const rockGradient = ctx.createRadialGradient(
            featureX, featureY, 0,
            featureX, featureY, featureSize
          );

          if (i % 2 === 0) {
            // Dark rock cracks/shadows
            rockGradient.addColorStop(0, `rgba(0, 0, 0, ${0.7 * weathering})`);
            rockGradient.addColorStop(0.6, `rgba(0, 0, 0, ${0.4 * weathering})`);
            rockGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
          } else {
            // Mineral highlights
            const mineralIntensity = mineralDeposits * 0.3;
            rockGradient.addColorStop(0, `rgba(120, 100, 80, ${mineralIntensity})`);
            rockGradient.addColorStop(0.5, `rgba(80, 70, 60, ${mineralIntensity * 0.6})`);
            rockGradient.addColorStop(1, 'rgba(40, 35, 30, 0)');
          }

          ctx.fillStyle = rockGradient;
          ctx.beginPath();
          ctx.arc(featureX, featureY, featureSize, 0, Math.PI * 2);
          ctx.fill();
        }

        // Add mineral veins for some asteroids
        if (mineralDeposits > 0.6) {
          const veinAngle = rockPattern * Math.PI * 2;
          ctx.strokeStyle = `rgba(150, 120, 90, ${mineralDeposits * 0.4})`;
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.moveTo(
            x + Math.cos(veinAngle) * adjustedRadius * 0.3,
            y + Math.sin(veinAngle) * adjustedRadius * 0.3
          );
          ctx.lineTo(
            x + Math.cos(veinAngle + Math.PI) * adjustedRadius * 0.3,
            y + Math.sin(veinAngle + Math.PI) * adjustedRadius * 0.3
          );
          ctx.stroke();
        }
      } else if (material === 'moon' && adjustedRadius > 12) {
        const time = Date.now() * 0.002;

        // Add 2-3 crater shadows for realism
        for (let i = 0; i < 3; i++) {
          const craterAngle = (time * 0.1 + i * Math.PI * 0.7) % (Math.PI * 2);
          const craterDistance = adjustedRadius * (0.2 + i * 0.15);
          const craterX = x + Math.cos(craterAngle) * craterDistance;
          const craterY = y + Math.sin(craterAngle) * craterDistance;
          const craterSize = adjustedRadius * (0.1 + i * 0.05);

          const craterGradient = ctx.createRadialGradient(craterX, craterY, 0, craterX, craterY, craterSize);
          craterGradient.addColorStop(0, `rgba(20, 20, 25, ${adjustedBrightness * 0.8})`);
          craterGradient.addColorStop(0.7, `rgba(30, 30, 35, ${adjustedBrightness * 0.4})`);
          craterGradient.addColorStop(1, 'rgba(40, 40, 45, 0)');

          ctx.fillStyle = craterGradient;
          ctx.beginPath();
          ctx.arc(craterX, craterY, craterSize, 0, Math.PI * 2);
          ctx.fill();
        }
      }

      // Draw the main sphere with material gradient
      ctx.fillStyle = sphereGradient;
      ctx.beginPath();
      ctx.arc(x, y, adjustedRadius, 0, Math.PI * 2);
      ctx.fill();

      // Add realistic specular highlights
      if (adjustedRadius > 8 && totalLighting.intensity > 0.3) {
        const specularSize = adjustedRadius * 0.25;
        const specularIntensity = totalLighting.intensity * 0.7;

        const specularGradient = ctx.createRadialGradient(
          x + lightOffsetX, y + lightOffsetY, 0,
          x + lightOffsetX, y + lightOffsetY, specularSize
        );

        if (material === 'neptune') {
          // Icy crystal reflections for Neptune
          const time = Date.now() * 0.006;
          const iceSpotIntensity = (Math.sin(time * 1.2) * 0.3 + 0.7) * specularIntensity;
          specularGradient.addColorStop(0, `rgba(255, 255, 255, ${iceSpotIntensity})`);
          specularGradient.addColorStop(0.3, `rgba(200, 230, 255, ${iceSpotIntensity * 0.8})`);
          specularGradient.addColorStop(0.7, `rgba(150, 200, 255, ${iceSpotIntensity * 0.5})`);
          specularGradient.addColorStop(1, 'rgba(100, 160, 255, 0)');
        } else if (material === 'asteroid') {
          // Rocky asteroid reflections with mineral glints
          const mineralDeposits = sphereData?.mineralDeposits || Math.random();
          const rockSpotIntensity = specularIntensity * (0.3 + mineralDeposits * 0.2); // Varies with mineral content
          const time = Date.now() * 0.003;
          const mineralGlint = Math.sin(time * 2 + mineralDeposits * 10) * 0.2 + 0.8;

          specularGradient.addColorStop(0, `rgba(${Math.floor(140 * mineralGlint)}, ${Math.floor(120 * mineralGlint)}, ${Math.floor(100 * mineralGlint)}, ${rockSpotIntensity})`);
          specularGradient.addColorStop(0.4, `rgba(${Math.floor(100 * mineralGlint)}, ${Math.floor(90 * mineralGlint)}, ${Math.floor(80 * mineralGlint)}, ${rockSpotIntensity * 0.7})`);
          specularGradient.addColorStop(1, 'rgba(60, 55, 50, 0)');
        } else if (material === 'moon') {
          // Subtle rocky reflections for moon
          const moonSpotIntensity = specularIntensity * 0.4; // Much dimmer than other materials
          specularGradient.addColorStop(0, `rgba(120, 120, 130, ${moonSpotIntensity})`);
          specularGradient.addColorStop(0.5, `rgba(90, 90, 100, ${moonSpotIntensity * 0.6})`);
          specularGradient.addColorStop(1, 'rgba(60, 60, 70, 0)');
        } else if (material === 'illuminated') {
          // Steady blue energy reflections without flashing
          const illuminatedSpotIntensity = specularIntensity * 1.2; // Moderate increase

          specularGradient.addColorStop(0, `rgba(255, 255, 255, ${illuminatedSpotIntensity})`);
          specularGradient.addColorStop(0.3, `rgba(200, 230, 255, ${illuminatedSpotIntensity * 0.8})`);
          specularGradient.addColorStop(0.7, `rgba(150, 200, 255, ${illuminatedSpotIntensity * 0.5})`);
          specularGradient.addColorStop(1, 'rgba(100, 160, 255, 0)');
        } else if (material === 'energy') {
          // Bright energy reflections
          const time = Date.now() * 0.01;
          const energySpotIntensity = (Math.sin(time * 2) * 0.4 + 0.6) * specularIntensity;
          specularGradient.addColorStop(0, `rgba(255, 255, 255, ${energySpotIntensity})`);
          specularGradient.addColorStop(0.3, `rgba(200, 230, 255, ${energySpotIntensity * 0.8})`);
          specularGradient.addColorStop(0.7, `rgba(150, 200, 255, ${energySpotIntensity * 0.5})`);
          specularGradient.addColorStop(1, 'rgba(100, 160, 255, 0)');
        } else if (material === 'chrome' || !isOnFloor) {
          // Sharp, bright specular for chrome
          specularGradient.addColorStop(0, `rgba(255, 255, 255, ${specularIntensity * 0.9})`);
          specularGradient.addColorStop(0.4, `rgba(255, 255, 255, ${specularIntensity * 0.5})`);
          specularGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        } else {
          // Softer specular for metal
          specularGradient.addColorStop(0, `rgba(${Math.floor(totalLighting.r)}, ${Math.floor(totalLighting.g)}, ${Math.floor(totalLighting.b)}, ${specularIntensity * 0.6})`);
          specularGradient.addColorStop(0.6, `rgba(200, 200, 220, ${specularIntensity * 0.3})`);
          specularGradient.addColorStop(1, 'rgba(150, 150, 170, 0)');
        }

        ctx.fillStyle = specularGradient;
        ctx.beginPath();
        ctx.arc(x + lightOffsetX, y + lightOffsetY, specularSize, 0, Math.PI * 2);
        ctx.fill();
      }

      // Add subtle rim lighting for depth
      if (totalLighting.intensity > 0.2 && adjustedRadius > 6) {
        const rimIntensity = totalLighting.intensity * 0.3;
        const rimGradient = ctx.createRadialGradient(x, y, adjustedRadius * 0.8, x, y, adjustedRadius * 1.05);

        rimGradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
        rimGradient.addColorStop(0.85, 'rgba(255, 255, 255, 0)');
        rimGradient.addColorStop(0.95, `rgba(${Math.floor(totalLighting.r)}, ${Math.floor(totalLighting.g)}, ${Math.floor(totalLighting.b)}, ${rimIntensity * 0.4})`);
        rimGradient.addColorStop(1, `rgba(255, 255, 255, ${rimIntensity * 0.6})`);

        ctx.fillStyle = rimGradient;
        ctx.beginPath();
        ctx.arc(x, y, adjustedRadius * 1.05, 0, Math.PI * 2);
        ctx.fill();
      }

      // Subtle border that blends with gray ball background
      if (adjustedBrightness < 0.4) {
        // Soft gray border that blends with gray ball
        ctx.strokeStyle = `rgba(60, 60, 70, ${Math.min(0.3, adjustedBrightness * 2 * atmosphericDepth + 0.1)})`;
        ctx.lineWidth = adjustedRadius > 30 ? 0.8 : 0.6; // Much thinner border
      } else {
        // Soft blue border when lit
        ctx.strokeStyle = `rgba(80, 100, 160, ${Math.min(0.4, adjustedBrightness * 0.3 * atmosphericDepth)})`;
        ctx.lineWidth = 0.7; // Thin border
      }
      ctx.beginPath();
      ctx.arc(x, y, adjustedRadius - 0.5, 0, Math.PI * 2); // Slightly inset
      ctx.stroke();
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.offsetWidth, canvas.offsetHeight);

      // Define perspective constants for this frame
      const cameraDistance = 600;
      const centerX = canvas.offsetWidth / 2;
      const vanishingPointY = canvas.offsetHeight * 0.35;

      // Add subtle atmospheric gradient for depth
      const atmosphereGradient = ctx.createLinearGradient(0, 0, 0, canvas.offsetHeight);
      atmosphereGradient.addColorStop(0, 'rgba(20, 30, 60, 0.1)');
      atmosphereGradient.addColorStop(0.7, 'rgba(10, 15, 30, 0.05)');
      atmosphereGradient.addColorStop(1, 'rgba(5, 10, 20, 0.02)');

      ctx.fillStyle = atmosphereGradient;
      ctx.fillRect(0, 0, canvas.offsetWidth, canvas.offsetHeight);

      // Add dark floor effect to obscure floor balls until Neptune falls
      const floorDarkness = ctx.createLinearGradient(0, canvas.offsetHeight * 0.6, 0, canvas.offsetHeight);
      floorDarkness.addColorStop(0, 'rgba(0, 0, 0, 0)');
      floorDarkness.addColorStop(0.3, 'rgba(0, 0, 0, 0.3)');
      floorDarkness.addColorStop(0.7, 'rgba(0, 0, 0, 0.6)');
      floorDarkness.addColorStop(1, 'rgba(0, 0, 0, 0.8)');

      ctx.fillStyle = floorDarkness;
      ctx.fillRect(0, 0, canvas.offsetWidth, canvas.offsetHeight);

      // Add aggressive depth-based darkness to completely hide line endings
      const depthDarkness = ctx.createRadialGradient(
        canvas.offsetWidth * 0.5, canvas.offsetHeight * 0.65, // Center point slightly higher
        canvas.offsetWidth * 0.2, // Smaller inner radius for more darkness
        canvas.offsetWidth * 0.5, canvas.offsetHeight * 0.65, // Same center
        canvas.offsetWidth * 0.7 // Smaller outer radius for stronger effect
      );
      depthDarkness.addColorStop(0, 'rgba(0, 0, 0, 0)');
      depthDarkness.addColorStop(0.4, 'rgba(0, 0, 0, 0.3)');
      depthDarkness.addColorStop(0.6, 'rgba(0, 0, 0, 0.6)');
      depthDarkness.addColorStop(0.8, 'rgba(0, 0, 0, 0.85)');
      depthDarkness.addColorStop(1, 'rgba(0, 0, 0, 0.95)');

      ctx.fillStyle = depthDarkness;
      ctx.fillRect(0, 0, canvas.offsetWidth, canvas.offsetHeight);

      // Add additional linear gradient to hide the back lines completely
      const backLineFade = ctx.createLinearGradient(0, canvas.offsetHeight * 0.4, 0, canvas.offsetHeight);
      backLineFade.addColorStop(0, 'rgba(0, 0, 0, 0)');
      backLineFade.addColorStop(0.5, 'rgba(0, 0, 0, 0.1)');
      backLineFade.addColorStop(0.7, 'rgba(0, 0, 0, 0.4)');
      backLineFade.addColorStop(0.85, 'rgba(0, 0, 0, 0.7)');
      backLineFade.addColorStop(1, 'rgba(0, 0, 0, 0.9)');

      ctx.fillStyle = backLineFade;
      ctx.fillRect(0, 0, canvas.offsetWidth, canvas.offsetHeight);

      // Update dropping sphere with realistic physics
      if (droppingSphereRef.current.isDropping) {
        const sphere = droppingSphereRef.current;
        const deltaTime = 1/60; // Assume 60fps for physics calculations

        // Apply gravity
        sphere.velocity.y += sphere.acceleration.y * deltaTime;

        // Apply air resistance
        sphere.velocity.y *= (1 - sphere.drag);

        // Update 3D world position
        sphere.worldY -= sphere.velocity.y * deltaTime * 10; // Scale for visual effect

        // Update rotation for realism
        sphere.rotation.x += sphere.angularVelocity.x;
        sphere.rotation.y += sphere.angularVelocity.y;
        sphere.rotation.z += sphere.angularVelocity.z;

        // Update Neptune-like effects
        sphere.neptuneRotation += 0.03; // Moderate rotation like Neptune
        sphere.atmosphericWinds = Math.sin(Date.now() * 0.008) * 0.4 + 0.6; // Supersonic winds
        sphere.stormActivity = Math.sin(Date.now() * 0.005 + sphere.neptuneRotation) * 0.3 + 0.7; // Great Dark Spot activity

        // Ice crystallization effect as it falls through atmosphere
        const fallSpeed = Math.abs(sphere.velocity.y);
        sphere.iceIntensity = 1.0 + (fallSpeed * 0.05); // Subtle ice crystal formation

        // Calculate fall progress (0 = start, 1 = landed)
        const startWorldY = 600;
        const endWorldY = 0;
        const fallProgress = Math.max(0, Math.min(1, (startWorldY - sphere.worldY) / (startWorldY - endWorldY)));

        // Project current 3D position to screen coordinates
        const currentPerspectiveFactor = cameraDistance / (cameraDistance + sphere.worldZ);

        // Calculate screen position based on current 3D world position
        sphere.x = centerX + (sphere.worldX * currentPerspectiveFactor);

        // Interpolate Y position from start to target
        const startY = -120;
        sphere.y = startY + (sphere.targetScreenY - startY) * fallProgress;

        // Update radius based on current perspective
        sphere.radius = 75 * currentPerspectiveFactor;

        // Check collision with floor - use world Y position for more accurate detection
        if (sphere.worldY <= 0 && !sphere.hasTriggeredWave) {
          // Snap to final target position in the reserved spot
          sphere.x = sphere.targetScreenX;
          sphere.y = sphere.targetScreenY;
          sphere.worldX = sphere.targetWorldX; // Exact world X for center column
          sphere.worldY = 0; // On the floor
          sphere.worldZ = sphere.targetZ3D; // 3rd row depth
          sphere.radius = 75 * sphere.targetPerspectiveFactor; // Final target size - same as floor spheres
          sphere.velocity = { x: 0, y: 0, z: 0 }; // Stop movement

          // Calculate center column
          const baseSphereSize = 75; // Match the larger sphere size
          const frontSpacing = baseSphereSize * 6.0; // Match the new larger spacing
          const cols = Math.floor(canvas.offsetWidth / frontSpacing) + 6; // Match the new column count
          const centerCol = Math.floor(cols / 2);

          // Add this sphere to the floor spheres array as the trigger sphere in 3rd row
          const targetRow = 2; // 3rd row (0-indexed)
          const depthRatio = targetRow / 19; // 19 is rows-1 (20 rows total)
          const perspectiveScale = 1 - (depthRatio * 0.5);
          const atmosphericDepth = perspectiveScale;

          const triggerSphere = {
            x: sphere.x,
            y: sphere.y,
            z: targetRow, // 3rd row
            z3D: sphere.worldZ,
            radius: sphere.radius,
            originalRadius: sphere.radius,
            brightness: 2.0, // Keep it bright as the trigger
            originalBrightness: 2.0,
            waveIntensity: 0,
            row: targetRow, // 3rd row
            col: centerCol, // Center column
            perspectiveScale: perspectiveScale,
            depthRatio: depthRatio,
            atmosphericDepth: atmosphericDepth,
            worldX: sphere.targetWorldX, // Use exact target world X
            worldY: sphere.worldY,
            worldZ: sphere.worldZ,
            mass: sphere.mass,
            velocity: { x: 0, y: 0, z: 0 },
            acceleration: { x: 0, y: 0, z: 0 },
            material: 'neptune', // Same Neptune material as falling ball
            // Neptune-like properties to match falling ball
            neptuneRotation: sphere.neptuneRotation || 0,
            iceIntensity: sphere.iceIntensity || 1.0,
            atmosphericWinds: sphere.atmosphericWinds || 0.6,
            stormActivity: sphere.stormActivity || 0.7,
            isTriggerSphere: true // Mark as the trigger sphere
          };

          // Find and replace the invisible target position sphere with the trigger sphere
          const targetIndex = spheresRef.current.findIndex(s => s.row === targetRow && s.col === centerCol && s.isTargetPosition);
          if (targetIndex !== -1) {
            spheresRef.current[targetIndex] = triggerSphere;
          } else {
            // Fallback: add the trigger sphere if target not found
            spheresRef.current.push(triggerSphere);
          }

          // Trigger first wave
          const floorY = canvas.offsetHeight - 120;
          createWave(sphere.x, floorY);
          sphere.hasTriggeredWave = true;
          sphere.isDropping = false;

          // Schedule wave completion checking
          const checkWaveCompletion = () => {
            const currentTime = Date.now();

            if (waveCompletionRef.current && currentTime >= waveCompletionRef.current.endTime) {
              const waveNumber = waveCompletionRef.current.waveNumber;

              if (waveNumber < 3) {
                // Start next wave after current one completes
                setTimeout(() => {
                  if (droppingSphereRef.current && droppingSphereRef.current.waveCount < 3) {
                    const floorY = canvas.offsetHeight - 120;
                    createWave(droppingSphereRef.current.x, floorY);
                    checkWaveCompletion(); // Check for next completion
                  }
                }, 100); // Small delay to ensure wave is fully complete
              } else {
                // All 3 waves complete, start new sphere cycle
                setTimeout(() => {
                  initDroppingSphere();
                }, 1000); // 1 second pause before new sphere
              }
            } else {
              // Keep checking
              setTimeout(checkWaveCompletion, 100);
            }
          };

          // Start checking for wave completion
          checkWaveCompletion();
        }
      }

      // Update wave effects with proper cleanup
      const currentTime = Date.now();
      waveRef.current = waveRef.current.filter(wave => {
        if (currentTime >= wave.startTime) {
          const elapsed = currentTime - wave.startTime;
          const progress = Math.min(elapsed / wave.duration, 1);
          const sphere = spheresRef.current[wave.sphereIndex];

          if (sphere && progress < 1) {
            const waveValue = Math.sin(progress * Math.PI) * wave.intensity;
            // Dramatic brightness increase for clear wave visibility
            sphere.brightness = sphere.originalBrightness + waveValue * 2.2;
            sphere.radius = sphere.originalRadius; // Keep original size
            sphere.waveIntensity = waveValue;

            // Transform asteroid material to illuminated when wave hits
            if (waveValue > 0.3 && sphere.material === 'asteroid') {
              sphere.material = 'illuminated'; // Transform to illuminated material with blue energy
            }

            return true; // Keep this wave
          } else if (sphere && progress >= 1) {
            // Reset sphere to original state when wave completes
            if (sphere.isTriggerSphere) {
              // Keep trigger sphere bright
              sphere.brightness = Math.max(sphere.originalBrightness, 1.5);
            } else {
              sphere.brightness = sphere.originalBrightness;
              // Reset asteroid spheres back to asteroid material after wave
              if (sphere.material === 'illuminated') {
                sphere.material = 'asteroid';
              }
            }
            sphere.waveIntensity = 0;
            return false; // Remove completed wave
          }
        }
        return true; // Keep pending waves
      });

      // Clean up completed waves
      waveRef.current = waveRef.current.filter(wave => {
        const elapsed = currentTime - wave.startTime;
        return elapsed < wave.duration;
      });

      // Draw floor spheres with 3D occlusion physics (back to front for proper layering)
      const sortedSpheres = [...spheresRef.current].sort((a, b) => b.row - a.row);

      sortedSpheres.forEach(sphere => {
        // Skip invisible spheres (target position before falling ball lands)
        if (sphere.isTargetPosition && !sphere.isVisible) {
          return; // Don't draw invisible target position sphere
        }

        // Special handling for trigger sphere - always visible and prominent
        if (sphere.isTriggerSphere) {
          drawSphere(
            sphere.x,
            sphere.y,
            sphere.radius,
            Math.max(sphere.brightness, 1.5), // Ensure it stays bright
            sphere.perspectiveScale,
            true,
            sphere.atmosphericDepth,
            1.0, // Always fully visible
            'neptune' // Same Neptune material as falling ball
          );
          return;
        }

        // Skip completely occluded spheres for performance
        if (isOccluded(sphere, spheresRef.current)) {
          return; // Don't draw completely hidden spheres
        }

        // Calculate visibility factor for partial occlusion
        const visibilityFactor = calculateVisibleArea(sphere, spheresRef.current);

        drawSphere(
          sphere.x,
          sphere.y,
          sphere.radius,
          sphere.brightness,
          sphere.perspectiveScale,
          true,
          sphere.atmosphericDepth,
          visibilityFactor,
          sphere.material || 'metal',
          sphere // Pass the entire sphere object for asteroid properties
        );
      });

      // Draw dropping sphere with enhanced lighting
      if (droppingSphereRef.current.isDropping) {
        // Calculate enhanced brightness for falling sphere based on distance from light
        const fallingSphereX = droppingSphereRef.current.x;
        const fallingSphereY = droppingSphereRef.current.y;
        const lightSourceX = canvas.offsetWidth * 0.85;
        const lightSourceY = canvas.offsetHeight * 0.15;

        const lightDirX = lightSourceX - fallingSphereX;
        const lightDirY = lightSourceY - fallingSphereY;
        const lightDistance = Math.sqrt(lightDirX * lightDirX + lightDirY * lightDirY);
        const fallingSphereLight = Math.max(0.3, 1 - (lightDistance / (canvas.offsetWidth * 0.6)));

        // Enhanced brightness for falling sphere
        const enhancedBrightness = droppingSphereRef.current.brightness + fallingSphereLight * 0.8;

        drawSphere(
          fallingSphereX,
          fallingSphereY,
          droppingSphereRef.current.radius,
          enhancedBrightness * droppingSphereRef.current.iceIntensity, // Enhanced with ice crystal intensity
          1,
          false,
          1,
          1,
          droppingSphereRef.current.material || 'neptune'
        );
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    // Initialize and start animation
    initFloorSpheres();
    initDroppingSphere();
    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <div className={`relative w-full h-full overflow-hidden ${className}`}>
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ background: 'transparent' }}
      />
    </div>
  );
};

export default SphereWaveAnimation;
