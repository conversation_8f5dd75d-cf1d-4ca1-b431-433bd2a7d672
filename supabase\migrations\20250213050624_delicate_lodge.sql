-- Create storage bucket for blog images if it doesn't exist
DO $$
BEGIN
  INSERT INTO storage.buckets (id, name, public)
  VALUES ('blog-images', 'blog-images', true)
  ON CONFLICT (id) DO NOTHING;
END $$;

-- Create storage policy to allow authenticated users to upload images
CREATE POLICY "Authenticated users can upload blog images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'blog-images' AND
  CASE 
    WHEN POSITION('/' in name) > 0 THEN
      split_part(name, '/', 1) = 'posts'
    ELSE
      false
  END
);

-- Create storage policy to allow public to view blog images
CREATE POLICY "Public can view blog images"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'blog-images');

-- Add image format validation function
CREATE OR REPLACE FUNCTION is_valid_image_format(filename text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN filename ~* '.*\.(jpg|jpeg|png|gif|webp)$';
END;
$$;

-- Create storage policy with format validation
CREATE POLICY "Validate blog image uploads"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'blog-images' AND
  CASE 
    WHEN POSITION('/' in name) > 0 THEN
      split_part(name, '/', 1) = 'posts'
    ELSE
      false
  END AND
  is_valid_image_format(name)
);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA storage TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA storage TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA storage TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA storage TO postgres, anon, authenticated, service_role;