import 'jsr:@supabase/functions-js/edge-runtime.d.ts';
import Stripe from 'npm:stripe@17.7.0';
import { createClient } from 'npm:@supabase/supabase-js@2.49.1';
import nodemailer from 'npm:nodemailer';
// Stripe and Supabase Setup
const stripeSecret = Deno.env.get('STRIPE_SECRET_KEY');
const stripeWebhookSecret = Deno.env.get('STRIPE_WEBHOOK_SCHEDULED_SECRET');
if (!stripeWebhookSecret) {
  throw new Error('STRIPE_WEBHOOK_SECRET environment variable not set');
}
const stripe = new Stripe(stripeSecret, {
  appInfo: {
    name: 'IRS Integration',
    version: '1.0.0'
  }
});
const supabase = createClient(Deno.env.get('SUPABASE_URL'), Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'), {
  global: {
    headers: {
      Authorization: `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
    }
  }
});
// Main Handler
Deno.serve(async (req)=>{
  try {
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Stripe-Signature'
        }
      });
    }
    if (req.method !== 'POST') {
      return new Response('Method not allowed', {
        status: 405
      });
    }
    const signature = req.headers.get('stripe-signature');
    if (!signature) {
      return new Response('No signature found', {
        status: 400
      });
    }
    const body = await req.text();
    let event;
    try {
      event = await stripe.webhooks.constructEventAsync(body, signature, stripeWebhookSecret);
    } catch (error) {
      console.error(`Webhook signature verification failed: ${error.message}`);
      return new Response(`Webhook error: ${error.message}`, {
        status: 403,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    EdgeRuntime.waitUntil(handleEvent(event));
    return Response.json({
      received: true
    });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return Response.json({
      error: error.message
    }, {
      status: 500
    });
  }
});

// Handle Stripe Event
async function handleEvent(event: Stripe.Event) {
  console.log('Received Stripe event type:', event.type);

  if (
    event.type === 'subscription_schedule.created' ||
    event.type === 'subscription_schedule.updated'
  ) {
    const schedule = event.data.object as Stripe.SubscriptionSchedule;
    const subscriptionId = typeof schedule.subscription === 'string'
      ? schedule.subscription
      : schedule.subscription?.id;
    if (!subscriptionId) return;

    // Get DB subscription ID
    const { data: dbSubscription } = await supabase
      .from('subscriptions')
      .select('id')
      .eq('stripe_subscription_id', subscriptionId)
      .single();
    if (!dbSubscription) return;
    const dbSubscriptionId = dbSubscription.id;

    // Remove all existing scheduled items for this subscription
    await supabase
      .from('subscription_items')
      .delete()
      .eq('subscription_id', dbSubscriptionId)
      .eq('status', 'scheduled');

    // Add new scheduled items based on the *last* future phase
    console.log('📛📛📛 schedule.phases: ', schedule.phases);

    // Filter phases that start in the future and have items
    const futurePhases = schedule.phases?.filter(
      phase => phase.start_date > Math.floor(Date.now() / 1000) && phase.items && phase.items.length > 0
    ) ?? []; // Ensure we have an array even if filter returns undefined/null

    // Sort future phases by start_date ascending
    futurePhases.sort((a, b) => a.start_date - b.start_date);

    // Select the *last* future phase (representing the final scheduled state)
    const targetPhase = futurePhases.length > 0 ? futurePhases[futurePhases.length - 1] : null;

    if (targetPhase) {
      console.log('🎯🎯 Target Future Phase (Last): ', targetPhase); // Log the selected phase
      for (const item of targetPhase.items) {
        const productId = await getProductIdByPriceId(item.price);
        if (productId === 'unknown') continue;
        console.log('INSIDE SCHEDULE (using last future phase) : ', item);
        
        // Get price information to calculate amount
        let unitAmount = 0;
        try {
          const price = await stripe.prices.retrieve(item.price);
          unitAmount = price.unit_amount || 0;
        } catch (error) {
          console.error(`Failed to retrieve price information for ${item.price}:`, error);
        }
        
        const itemData = {
          subscription_id: dbSubscriptionId,
          product_id: productId,
          stripe_price_id: item.price,
          quantity: item.quantity ?? 1,
          status: 'scheduled',
          amount: (unitAmount / 100) * (item.quantity ?? 1), // Add amount field with conversion from cents to dollars
          ...(item.id && { stripe_subscription_item_id: item.id })
        };

        await supabase
          .from('subscription_items')
          .insert(itemData);
      }
    } else {
       console.log('No relevant future phase found to schedule.');
    }
    return;
  }
  else if (event.type === 'subscription_schedule.released') {
    const schedule = event.data.object as Stripe.SubscriptionSchedule;
    const subscriptionId = typeof schedule.subscription === 'string'
      ? schedule.subscription
      : schedule.subscription?.id;
    if (!subscriptionId) return;

    // Get DB subscription ID
    const { data: dbSubscription } = await supabase
      .from('subscriptions')
      .select('id')
      .eq('stripe_subscription_id', subscriptionId)
      .single();
    if (!dbSubscription) return;
    const dbSubscriptionId = dbSubscription.id;

    // Remove all scheduled items for this subscription
    await supabase
      .from('subscription_items')
      .delete()
      .eq('subscription_id', dbSubscriptionId)
      .eq('status', 'scheduled');
    return;
  }
  // Add new handler for when schedule completes
  else if (event.type === 'subscription_schedule.completed') {
    const schedule = event.data.object as Stripe.SubscriptionSchedule;
    const subscriptionId = typeof schedule.subscription === 'string' 
      ? schedule.subscription 
      : schedule.subscription?.id;
    
    if (subscriptionId) {
      // Clean up any remaining scheduled items
      const { data: dbSubscription } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('stripe_subscription_id', subscriptionId)
        .single();

      if (dbSubscription) {
        await supabase
          .from('subscription_items')
          .delete()
          .eq('subscription_id', dbSubscription.id)
          .eq('status', 'scheduled');
      }
    }
  }
  else {
    console.log(`Unhandled event type: ${event.type}`);
  }
}

// Map Stripe price_id to local product_id
async function getProductIdByPriceId(priceId: string): Promise<string> {
  // --- Updated Query ---
  const { data: product, error } = await supabase
    .from('products')
    .select('id')
    .or(`stripe_monthly_price_id.eq.${priceId},stripe_yearly_price_id.eq.${priceId}`) 
    .limit(1) 
    .single(); 
  // --- End Updated Query ---

  if (error || !product) {
    console.error(`Product lookup failed for Stripe Price ID: ${priceId}. Error: ${error?.message || 'Product not found'}`);
    return 'unknown'; 
  }

  return product.id; 
}

// Send confirmation email - Modified to accept and use derived dates if originals are missing
async function sendEmailToClient(
    email: string,
    subscription: Stripe.Subscription,
    productIds: string[],
    derivedDates?: { start: Date; end: Date } // Add optional derived dates
) {
  const productNames = await Promise.all(productIds.map(async (id)=>{
    const { data } = await supabase.from('products').select('name').eq('id', id).single();
    return data?.name || 'Unknown Product';
  }));

  // Use derived dates if provided and original dates are invalid, otherwise use originals
  const startDate = (typeof subscription.current_period_start === 'number')
    ? new Date(subscription.current_period_start * 1000)
    : derivedDates?.start;
  const endDate = (typeof subscription.current_period_end === 'number')
    ? new Date(subscription.current_period_end * 1000)
    : derivedDates?.end;

  const transporter = nodemailer.createTransport({
    host: 'smtp.office365.com',
    port: 587,
    secure: false,
    auth: {
      user: Deno.env.get('SMTP_USERNAME'),
      pass: Deno.env.get('SMTP_PASSWORD')
    }
  });

  const message = `
Hi there,

Thanks for subscribing! Here are the details of your subscription:

🛍️ Plan:
${productNames.map((name, index)=>`  ${index + 1}. ${name}`).join('\n')}
🔁 Status: ${subscription.status}
🔁 Period: ${subscription.items.data[0]?.price.recurring?.interval ?? 'N/A'}
📅 Starts on: ${startDate ? startDate.toLocaleDateString() : 'N/A'}
📅 Ends on: ${endDate ? endDate.toLocaleDateString() : 'N/A'}

If you have any questions, feel free to reach out.

Best regards,
Your Team
`;
  await transporter.sendMail({
    from: Deno.env.get('SMTP_USERNAME'),
    to: email,
    subject: 'Your Subscription Confirmation',
    text: message
  });
  console.log(`Confirmation email sent to ${email}`);
}
