
import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { Save, Globe, Share2, Mail, Phone, MapPin, AlertCircle, CheckCircle, Search, Link as LinkIcon, Settings as SettingsIcon, FileText } from 'lucide-react';
import { useAuthStore } from '../../store/authStore';
import { useAuth } from '../../lib/AuthContext';
import {Link} from "react-router-dom";

interface SiteSettings {
  general: {
    siteName: string;
    siteDescription: string;
    contactEmail: string;
    phone: string;
    address: string;
    socialLinks: {
      linkedin: string;
      twitter: string;
      facebook: string;
    };
  };
  seo: {
    defaultTitle: string;
    defaultDescription: string;
    defaultKeywords: string;
    googleAnalyticsId: string;
    googleTagManagerId: string;
    googleSiteVerification: string;
    bingVerification: string;
    robotsTxt: string;
    sitemapEnabled: boolean;
  };
  appearance: {
    logo: string;
    favicon: string;
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
  };
  features: {
    blogEnabled: boolean;
    commentsEnabled: boolean;
    userRegistrationEnabled: boolean;
    maintenanceMode: boolean;
    // Resources features
    resourcesLibraryEnabled: boolean;
    webinarsEnabled: boolean;
    whitepapersEnabled: boolean;
    guidesEnabled: boolean;
    caseStudiesEnabled: boolean;
    eventsEnabled: boolean;
  };
  // New content fields
  Soar_modifiedText: string;
  Grant_modifiedText: string;
}

export default function SettingsManagement() {
  const [settings, setSettings] = useState<SiteSettings>({
    general: {
      siteName: '',
      siteDescription: '',
      contactEmail: '',
      phone: '',
      address: '',
      socialLinks: {
        linkedin: '',
        twitter: '',
        facebook: ''
      }
    },
    seo: {
      defaultTitle: '',
      defaultDescription: '',
      defaultKeywords: '',
      googleAnalyticsId: '',
      googleTagManagerId: '',
      googleSiteVerification: '',
      bingVerification: '',
      robotsTxt: '',
      sitemapEnabled: false
    },
    appearance: {
      logo: '',
      favicon: '',
      primaryColor: '#2563eb',
      secondaryColor: '#1e40af',
      fontFamily: 'Inter'
    },
    features: {
      blogEnabled: false,
      commentsEnabled: false,
      userRegistrationEnabled: false,
      maintenanceMode: false,
      // Resources features
      resourcesLibraryEnabled: true,
      webinarsEnabled: true,
      whitepapersEnabled: true,
      guidesEnabled: true,
      caseStudiesEnabled: true,
      eventsEnabled: true
    },
    // New content fields
    Soar_modifiedText: '',
    Grant_modifiedText: ''
  });
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('site_settings')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;
      if (data && data.length > 0) {
        setSettings({
          ...settings,
          ...data[0].settings
        });
      }
    } catch (err: any) {
      console.error('Error fetching settings:', err);
      setError(err.message);
    }
  };

  const handleMaintenanceModeChange = (checked: boolean) => {
    setSettings({
      ...settings,
      features: {
        ...settings.features,
        maintenanceMode: checked
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Show confirmation dialog
    const confirmed = window.confirm('Are you sure you want to save these settings?');
    if (!confirmed) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // First try to get the latest settings record
      const { data: existing } = await supabase
        .from('site_settings')
        .select('id')
        .order('created_at', { ascending: false })
        .limit(1);

      let { error } = await supabase
        .from('site_settings')
        .upsert({
          id: existing?.[0]?.id, // Use existing ID if found
          settings,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;
      setSuccess('Settings saved successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'general', label: 'General', icon: SettingsIcon },
    { id: 'seo', label: 'SEO', icon: Search },
    { id: 'features', label: 'Features', icon: Share2 },
    { id: 'content', label: 'Content', icon: FileText }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Site Name</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.general.siteName}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, siteName: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <SettingsIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Site Description</label>
              <div className="mt-1">
                <textarea
                  value={settings.general.siteDescription}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, siteDescription: e.target.value }
                  })}
                  rows={3}
                  className="block w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Contact Email</label>
              <div className="mt-1 relative">
                <input
                  type="email"
                  value={settings.general.contactEmail}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, contactEmail: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Phone</label>
              <div className="mt-1 relative">
                <input
                  type="tel"
                  value={settings.general.phone}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, phone: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Address</label>
              <div className="mt-1 relative">
                <textarea
                  value={settings.general.address}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, address: e.target.value }
                  })}
                  rows={2}
                  className="block pl-8 w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <MapPin className="absolute left-3 top-4 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Social Links</h3>
              <div>
                <label className="block text-sm font-medium text-gray-700">LinkedIn</label>
                <div className="mt-1 relative">
                  <input
                    type="url"
                    value={settings.general.socialLinks.linkedin}
                    onChange={(e) => setSettings({
                      ...settings,
                      general: {
                        ...settings.general,
                        socialLinks: { ...settings.general.socialLinks, linkedin: e.target.value }
                      }
                    })}
                    className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                  />
                  <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </div>
              <div>
                <label className="flex items-baseline justify-start text-xs italic font-medium text-gray-700 gap-x-2 w-fit">
                  <span className='w-4 h-4'>
                    <svg viewBox="0 0 22 22" aria-hidden="true" className="r-4qtqp9 r-yyyyoo r-dnmrzs r-bnwqim r-lrvibr r-m6rgpd r-k200y r-18jsvk2 r-5sfk15 r-kzbkwu">
                      <g>
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" stroke="#FFFFFF"></path>
                      </g>
                    </svg>
                  </span>
                  (Twitter)
                </label>
                <div className="mt-1 relative">
                  <input
                    type="url"
                    value={settings.general.socialLinks.twitter}
                    onChange={(e) => setSettings({
                      ...settings,
                      general: {
                        ...settings.general,
                        socialLinks: { ...settings.general.socialLinks, twitter: e.target.value }
                      }
                    })}
                    className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                  />
                  <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Facebook</label>
                <div className="mt-1 relative">
                  <input
                    type="url"
                    value={settings.general.socialLinks.facebook}
                    onChange={(e) => setSettings({
                      ...settings,
                      general: {
                        ...settings.general,
                        socialLinks: { ...settings.general.socialLinks, facebook: e.target.value }
                      }
                    })}
                    className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                  />
                  <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </div>
            </div>
          </div>
        );

      case 'seo':
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Default Meta Title</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.defaultTitle}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, defaultTitle: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-1 text-sm text-gray-500">Recommended length: 50-60 characters</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Default Meta Description</label>
              <div className="mt-1">
                <textarea
                  value={settings.seo.defaultDescription}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, defaultDescription: e.target.value }
                  })}
                  rows={3}
                  className="block w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
              </div>
              <p className="mt-1 text-sm text-gray-500">Recommended length: 150-160 characters</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Default Keywords</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.defaultKeywords}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, defaultKeywords: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-1 text-sm text-gray-500">Comma-separated keywords</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Google Tag Manager ID</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.googleTagManagerId}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, googleTagManagerId: e.target.value }
                  })}
                  placeholder="GTM-XXXXXXXXXX"
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Google Site Verification</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.googleSiteVerification}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, googleSiteVerification: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Bing Verification</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.bingVerification}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, bingVerification: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Favicon URL</label>
              <div className="mt-1 relative">
                <input
                  type="url"
                  value={settings.appearance.favicon}
                  onChange={(e) => setSettings({
                    ...settings,
                    appearance: { ...settings.appearance, favicon: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>
          </div>
        );

      case 'features':
        return (
          <div className="space-y-6">

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Comments</h3>
                <p className="text-sm text-gray-500">Allow users to comment on blog posts</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.commentsEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    features: { ...settings.features, commentsEnabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">User Registration</h3>
                <p className="text-sm text-gray-500">Allow new users to register</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.userRegistrationEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    features: { ...settings.features, userRegistrationEnabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Maintenance Mode</h3>
                <p className="text-sm text-gray-500">Enable to put the site under maintenance</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.maintenanceMode}
                  onChange={(e) => handleMaintenanceModeChange(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* Resources Section */}
            <div className="border-t border-gray-200 pt-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Resources Menu Items</h2>
              <p className="text-sm text-gray-600 mb-6">Control which items appear in the Resources dropdown menu</p>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Resources Library</h3>
                    <p className="text-sm text-gray-500">Show resources library main page in resources menu</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.features.resourcesLibraryEnabled}
                      onChange={(e) => setSettings({
                        ...settings,
                        features: { ...settings.features, resourcesLibraryEnabled: e.target.checked }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Blog</h3>
                    <p className="text-sm text-gray-500">Show blog section in resources menu</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.features.blogEnabled}
                      onChange={(e) => setSettings({
                        ...settings,
                        features: { ...settings.features, blogEnabled: e.target.checked }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Webinars</h3>
                    <p className="text-sm text-gray-500">Show webinars section in resources menu</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.features.webinarsEnabled}
                      onChange={(e) => setSettings({
                        ...settings,
                        features: { ...settings.features, webinarsEnabled: e.target.checked }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Whitepapers</h3>
                    <p className="text-sm text-gray-500">Show whitepapers section in resources menu</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.features.whitepapersEnabled}
                      onChange={(e) => setSettings({
                        ...settings,
                        features: { ...settings.features, whitepapersEnabled: e.target.checked }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Guides</h3>
                    <p className="text-sm text-gray-500">Show guides section in resources menu</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.features.guidesEnabled}
                      onChange={(e) => setSettings({
                        ...settings,
                        features: { ...settings.features, guidesEnabled: e.target.checked }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Case Studies</h3>
                    <p className="text-sm text-gray-500">Show case studies section in resources menu</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.features.caseStudiesEnabled}
                      onChange={(e) => setSettings({
                        ...settings,
                        features: { ...settings.features, caseStudiesEnabled: e.target.checked }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Events</h3>
                    <p className="text-sm text-gray-500">Show events section in resources menu</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.features.eventsEnabled}
                      onChange={(e) => setSettings({
                        ...settings,
                        features: { ...settings.features, eventsEnabled: e.target.checked }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        );

      case 'content':
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Content Management</h2>
              <p className="text-sm text-gray-600 mb-6">Manage custom content for your services</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SOAR Modified Text
              </label>
              <div className="mt-1 relative">
                <textarea
                  value={settings.Soar_modifiedText}
                  onChange={(e) => setSettings({
                    ...settings,
                    Soar_modifiedText: e.target.value
                  })}
                  rows={6}
                  className="block w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400 resize-vertical"
                  placeholder="Enter the modified text for SOAR service description..."
                />
                <FileText className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-sm text-gray-500">
                This text will be used to describe the SOAR service.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Grant Modified Text
              </label>
              <div className="mt-1 relative">
                <textarea
                  value={settings.Grant_modifiedText}
                  onChange={(e) => setSettings({
                    ...settings,
                    Grant_modifiedText: e.target.value
                  })}
                  rows={6}
                  className="block w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400 resize-vertical"
                  placeholder="Enter the modified text for Grant service description..."
                />
                <FileText className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-2 text-sm text-gray-500">
                This text will be used to describe the GrantReady service.
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-blue-800">Content Usage</h3>
                  <p className="text-sm text-blue-700 mt-1">
                    These modified texts will replace the default service descriptions for both
                    <Link to={'/solutions/soar/#soar'} className="underline"> SOAR </Link> and <Link to={'/solutions/grantready/#grant'} className="underline" > GrantReady </Link> services.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Site Settings</h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage your site configuration and content
          </p>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm text-green-700">{success}</p>
              </div>
            </div>
          </div>
        )}

        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          <form onSubmit={handleSubmit} className="p-6">
            {renderTabContent()}

            <div className="mt-6 flex justify-end">
              <button
                type="submit"
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Save className="h-4 w-4" />
                )}
                <span>{loading ? 'Saving...' : 'Save Settings'}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );


}