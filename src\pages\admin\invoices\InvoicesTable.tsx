import React, { useState } from "react";
import { format } from "date-fns";
import {
  ChevronLeft,
  ChevronRight,
  Download,
  Edit,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreditCard,
  FileText,
  Loader2,
  Eye,
} from "lucide-react";
import { InvoicePDFDownload } from "../../../components/InvoicePDF";
import InvoiceDetailsModal from "./InvoiceDetailsModal";

// Assuming the Invoice interface is defined here or imported
interface Invoice {
  id: string;
  stripe_invoice_id: string;
  stripe_customer_id: string | null;
  customer_email: string | null;
  customer_name: string | null;
  status: string | null;
  amount_due: number | null;
  amount_paid: number | null;
  amount_remaining: number | null;
  currency: string | null;
  due_date: string | null;
  invoice_pdf: string | null;
  hosted_invoice_url: string | null;
  created_at: string;
  is_manual?: boolean;
  line_items?: Array<{
    product_name: string;
    price: number;
    quantity: number;
    billing_cycle: string;
    amount: number;
  }>;
  payment_method?: {
    type: string;
    bank_name?: string;
    account_number?: string;
    routing_number?: string;
  };
  notes?: string;
  ref?: string; // 8-character reference code
}

type TabType = "stripe" | "manual";

interface InvoicesTableProps {
  invoices: Invoice[];
  itemsPerPage: number;
  onItemsPerPageChange: (size: number) => void;
  currentPageIndex: number;
  hasNextPage: boolean;
  onNextPage: () => void;
  onPreviousPage: () => void;
  onEditInvoice?: (invoice: Invoice) => void;
  onDeleteInvoice?: (invoice: Invoice) => void;
  activeTab?: TabType;
  onTabChange?: (tab: TabType) => void;
  isLoading?: boolean;
}

const InvoicesTable: React.FC<InvoicesTableProps> = ({
  invoices,
  itemsPerPage,
  onItemsPerPageChange,
  currentPageIndex,
  hasNextPage,
  onNextPage,
  onPreviousPage,
  onEditInvoice,
  onDeleteInvoice,
  activeTab = "manual",
  onTabChange,
  isLoading = false,
}) => {
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [invoiceToDelete, setInvoiceToDelete] = useState<Invoice | null>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);

  // Use local state if no onTabChange is provided
  const [localActiveTab, setLocalActiveTab] = useState<TabType>("manual");

  // Use either the prop or local state
  const currentTab = onTabChange ? activeTab : localActiveTab;

  // Handle tab change
  const handleTabChange = (tab: TabType) => {
    if (onTabChange) {
      onTabChange(tab);
    } else {
      setLocalActiveTab(tab);
    }
  };

  // Filter invoices based on the active tab
  const filteredInvoices = invoices.filter((invoice) => {
    if (currentTab === "stripe") return !invoice.is_manual;
    if (currentTab === "manual") return invoice.is_manual;
    return true; // Fallback, should not happen
  });

  // --- Calculate display range based on current page index and items per page ---
  const firstItemIndex = currentPageIndex * itemsPerPage + 1;
  const lastItemIndex =
    currentPageIndex * itemsPerPage + filteredInvoices.length;
  // --- End Calculation ---

  // Debug: Log invoices to see if payment_method is included
  console.log("Invoices received by InvoicesTable:", invoices);

  const formatCurrency = (amount: number | null, currency: string | null) => {
    if (amount === null || currency === null) return "N/A";
    // Stripe amounts are usually in cents
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "PPp"); // e.g., Sep 21, 2023, 10:30:00 AM
    } catch {
      return "Invalid Date";
    }
  };
  console.log("invoices", invoices);
  return (
    <div className="overflow-x-auto">
      {/* Tabs */}
      <div className="border-b border-gray-200 mb-4">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            onClick={() => handleTabChange("stripe")}
            className={`${
              currentTab === "stripe"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
          >
            <CreditCard className="h-5 w-5 mr-2" />
            Stripe Invoices
          </button>
          <button
            onClick={() => handleTabChange("manual")}
            className={`${
              currentTab === "manual"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
          >
            <FileText className="h-5 w-5 mr-2" />
            Manual Invoices
          </button>
        </nav>
      </div>

      <table className="min-w-full divide-y divide-gray-200">
        {/* ... Table Head ... */}
        <thead className="bg-gray-50">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Customer
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Amount Due
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Due Date
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Payment Method
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Created
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {isLoading ? (
            <tr>
              <td colSpan={7} className="px-6 py-4 text-center">
                <div className="flex justify-center items-center">
                  <Loader2 className="h-6 w-6 text-blue-600 animate-spin mr-2" />
                  <span className="text-gray-500">
                    Loading {currentTab} invoices...
                  </span>
                </div>
              </td>
            </tr>
          ) : filteredInvoices.length === 0 ? (
            <tr>
              <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                No {currentTab} invoices found.
              </td>
            </tr>
          ) : (
            filteredInvoices.map((invoice) => (
              <tr key={invoice.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {/* Display customer email or name */}
                  {invoice.customer_name || invoice.customer_email || "N/A"}
                  <div className="text-xs text-gray-500">
                    Ref: {invoice.ref || invoice.id.substring(0, 8)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <div className="flex items-center">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        invoice.status === "paid"
                          ? "bg-green-100 text-green-800"
                          : invoice.status === "open"
                          ? "bg-blue-100 text-blue-800"
                          : invoice.status === "draft"
                          ? "bg-gray-100 text-gray-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {invoice.status}
                    </span>
                    {invoice.is_manual && (
                      <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                        Manual
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatCurrency(invoice.amount_due, invoice.currency)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(invoice.due_date)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {invoice.payment_method ? (
                    <div>
                      <span className="font-medium">
                        {invoice.payment_method.type
                          .replace("_", " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </span>
                      {invoice.payment_method.type === "bank_transfer" &&
                        invoice.payment_method.bank_name && (
                          <div className="text-xs text-gray-500">
                            {invoice.payment_method.bank_name}
                          </div>
                        )}
                    </div>
                  ) : invoice.is_manual ? (
                    "Not specified"
                  ) : (
                    "Stripe"
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(invoice.created_at)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    {/* View Details Button */}
                    <button
                      onClick={() => {
                        setSelectedInvoice(invoice);
                        setDetailsModalOpen(true);
                      }}
                      className="p-1 rounded-full text-gray-500 hover:text-gray-700"
                      title="View Invoice Details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>

                    {/* PDF Download Button */}
                    {invoice.is_manual && invoice.line_items ? (
                      <InvoicePDFDownload
                        invoice={{
                          ...invoice,
                          status: invoice.status || "draft",
                          amount_due: invoice.amount_due || 0,
                          amount_paid: invoice.amount_paid || 0,
                          amount_remaining: invoice.amount_remaining || 0,
                          currency: invoice.currency || "usd",
                          due_date: invoice.due_date || "",
                          created_at: invoice.created_at,
                          line_items: invoice.line_items || [],
                          is_manual: invoice.is_manual,
                          payment_method: invoice.payment_method,
                          notes: invoice.notes,
                        }}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <span className="flex items-center p-1">
                          <Download className="h-4 w-4" />
                        </span>
                      </InvoicePDFDownload>
                    ) : (
                      invoice.hosted_invoice_url && (
                        <a
                          href={invoice.hosted_invoice_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-900"
                        >
                          View
                        </a>
                      )
                    )}

                    {/* Edit Button - Only for manual invoices */}
                    {invoice.is_manual && onEditInvoice && (
                      <button
                        onClick={() => onEditInvoice(invoice)}
                        className={`p-1 rounded-full ${
                          // If not draft, can only edit status
                          invoice.status !== "draft"
                            ? "text-gray-400 hover:text-gray-600"
                            : "text-blue-500 hover:text-blue-700"
                        }`}
                        title={
                          invoice.status !== "draft"
                            ? "Edit Status Only"
                            : "Edit Invoice"
                        }
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    )}

                    {/* Delete Button - Only for draft or paid invoices */}
                    {invoice.is_manual &&
                      onDeleteInvoice &&
                      (invoice.status === "draft" ||
                        invoice.status === "paid") && (
                        <button
                          onClick={() => {
                            setInvoiceToDelete(invoice);
                            setDeleteConfirmOpen(true);
                          }}
                          className="p-1 rounded-full text-red-500 hover:text-red-700"
                          title="Delete Invoice"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                  </div>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
      {/* --- Updated Pagination Controls --- */}
      <div className="py-3 px-6 flex items-center justify-between border-t border-gray-200">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={onPreviousPage}
            disabled={currentPageIndex === 0}
            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={onNextPage}
            disabled={!hasNextPage}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <label
              htmlFor="itemsPerPage"
              className="text-sm font-medium text-gray-700 mr-2"
            >
              Items per page:
            </label>
            <select
              id="itemsPerPage"
              value={itemsPerPage}
              onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option>10</option>
              <option>25</option>
              <option>50</option>
              <option>100</option>
            </select>
          </div>
          <div>
            {/* --- Updated Results Display --- */}
            <p className="text-sm text-gray-700">
              Showing{" "}
              <span className="font-medium">
                {filteredInvoices.length > 0 ? firstItemIndex : 0}
              </span>{" "}
              to <span className="font-medium">{lastItemIndex}</span>{" "}
              {currentTab} results
              {/* Removed total results count as it's not available with cursor pagination */}
            </p>
            {/* --- End Updated Results Display --- */}
          </div>
          <div>
            <nav
              className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
              aria-label="Pagination"
            >
              <button
                onClick={onPreviousPage}
                disabled={currentPageIndex === 0}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Previous</span>
                <ChevronLeft className="h-5 w-5" aria-hidden="true" />
              </button>
              <button
                onClick={onNextPage}
                disabled={!hasNextPage}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Next</span>
                <ChevronRight className="h-5 w-5" aria-hidden="true" />
              </button>
            </nav>
          </div>
        </div>
      </div>
      {/* --- End Updated Pagination Controls --- */}

      {/* Delete Confirmation Modal */}
      {deleteConfirmOpen && invoiceToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="flex items-center text-red-500 mb-4">
              <AlertTriangle className="h-6 w-6 mr-2" />
              <h3 className="text-lg font-medium">Confirm Delete</h3>
            </div>
            <p className="mb-4">
              Are you sure you want to delete invoice{" "}
              <span className="font-semibold">
                #{invoiceToDelete.ref || invoiceToDelete.id.substring(0, 8)}
              </span>
              ?
              {invoiceToDelete.status === "paid" && (
                <span className="block mt-2 text-red-600">
                  Warning: This invoice has been paid. Deleting it may affect
                  financial records.
                </span>
              )}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirmOpen(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  if (onDeleteInvoice && invoiceToDelete) {
                    onDeleteInvoice(invoiceToDelete);
                  }
                  setDeleteConfirmOpen(false);
                }}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Invoice Details Modal */}
      {detailsModalOpen && selectedInvoice && (
        <InvoiceDetailsModal
          invoice={selectedInvoice}
          onClose={() => {
            setDetailsModalOpen(false);
            setSelectedInvoice(null);
          }}
        />
      )}
    </div>
  );
};

export default InvoicesTable;
