import { ArrowRight } from 'lucide-react';
import act1Video from '../../assets/home/<USER>';
import { Link } from 'react-router-dom';
import OutBreakText from './OutBreakText';

function ActSection() {
    return (
        <section className="relative w-full mx-auto rounded-lg max-h-[1400px] bg-gradient-to-br from-slate-100 via-blue-50 to-teal-50 overflow-hidden">
            {/* Background Video and Pattern */}
            <div className="absolute inset-0">
                <video
                    src={act1Video}
                    autoPlay
                    loop
                    muted
                    className="w-full h-full object-cover"
                />
                {[...Array(8)].map((_, i) => (
                    <div
                        key={`triangle-${i}`}
                        className="absolute w-0 h-0 opacity-10 hidden lg:block"
                        style={{
                            left: `${Math.random() * 100}%`,
                            top: `${Math.random() * 100}%`,
                            borderLeft: `${Math.random() * 10 + 5}px solid transparent`,
                            borderRight: `${Math.random() * 10 + 5}px solid transparent`,
                            borderBottom: `${Math.random() * 15 + 10}px solid ${i % 2 === 0 ? '#3b82f6' : '#14b8a6'}`,
                            transform: `rotate(${Math.random() * 360}deg)`,
                        }}
                    />
                ))}
            </div>

            {/* Main Content */}
            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8  sm:py-10 lg:py-12">
                <div className="flex flex-col-reverse md:flex-col items-center justify-center py-10 lg:flex-row lg:items-center lg:justify-between">
                    {/* Left Content */}
                    <div className="space-y-4 sm:space-y-6 lg:space-y-8 w-full lg:w-1/2 text-center lg:text-left">
                        <div className="space-y-2 sm:space-y-3">
                            <h1 className="hidden md:block text-xl sm:text-2xl md:text3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 leading-tight">
                                PREDICT TOMORROW
                                <br />
                                <span className="text-gray-800">ACT TODAY</span>
                            </h1>
                        </div>
                        <div className="space-y-2 sm:space-y-3">
                            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-700 leading-relaxed max-w-3xl mx-auto lg:mx-0">
                                We are working to save lives and stop the spread of outbreaks using the latest forecasting models
                            </p>
                            <p className="text-sm sm:text-base md:text-lg text-gray-600 italic font-semibold">
                                Learn more about our partnership with the CDC
                            </p>
                        </div>
                        <div className="pt-2 sm:pt-3">
                            <Link
                                to="/book-a-demo"
                                className="group inline-flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 bg-white border-2 border-gray-300 rounded-lg text-gray-500 font-medium text-xs sm:text-sm md:text-base lg:text-base hover:border-gray-400 hover:shadow-md transition-all duration-300 hover:scale-105"
                            >
                                See A Demo
                                <ArrowRight className="w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 group-hover:translate-x-1 transition-transform duration-300" />
                            </Link>
                        </div>
                    </div>

                    {/* Right Content - OutBreakText */}
                    <div className="w-full lg:w-3/5 flex justify-center lg:justify-end">
                        <OutBreakText />
                    </div>
                </div>
            </div>

            {/* Bottom decorative wave */}
            <div className="absolute bottom-0 left-0 right-0">
                <svg
                    className="w-full h-12 sm:h-16 md:h-20 lg:h-24 fill-white"
                    viewBox="0 0 1200 120"
                    preserveAspectRatio="none"
                >
                    <path d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z" />
                </svg>
            </div>
        </section>
    );
}

export default ActSection;