-- Create storage bucket for whitepaper images if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('whitepaper-images', 'whitepaper-images', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for whitepaper images bucket
CREATE POLICY "Public can view whitepaper images"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'whitepaper-images');

CREATE POLICY "Ad<PERSON> can manage whitepaper images"
ON storage.objects FOR ALL
TO authenticated
USING (
  bucket_id = 'whitepaper-images' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);

-- Add image format validation
CREATE POLICY "Validate whitepaper image uploads"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'whitepaper-images' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ) AND
  storage.extension(name) IN ('jpg', 'jpeg', 'png', 'gif', 'webp')
);
