-- Create function to initiate password reset
CREATE OR R<PERSON>LACE FUNCTION request_password_reset(user_email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Check if user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = user_email) THEN
    RETURN false;
  END IF;

  -- Return true if user exists (actual reset email will be handled by Supabase)
  RETURN true;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION request_password_reset TO anon, authenticated;