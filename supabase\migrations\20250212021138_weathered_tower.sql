/*
  # Fix Admin User Setup

  1. Changes
    - Create admin_users table
    - Add RLS policies
    - Add admin verification functions
    - Create initial admin user safely

  2. Security
    - Enable RLS on admin_users table
    - Add policies for authenticated users
    - Add secure verification functions
*/

-- First clean up existing admin setup
DROP TABLE IF EXISTS admin_users CASCADE;

-- Create admin_users table
CREATE TABLE admin_users (
  id uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  role text NOT NULL CHECK (role IN ('admin', 'editor')),
  created_at timestamptz DEFAULT now(),
  last_login timestamptz
);

-- Enable RLS
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Admin users can view own data"
  ON admin_users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Admin users can update own data"
  ON admin_users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Function to verify admin status
CREATE OR REPLACE FUNCTION verify_admin(user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM admin_users
    WHERE id = user_id
    AND role = 'admin'
  );
END;
$$;

-- Function to check admin credentials
CREATE OR REPLACE FUNCTION check_admin_credentials(email text, password text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM auth.users u
    JOIN admin_users a ON u.id = a.id
    WHERE u.email = check_admin_credentials.email
    AND u.encrypted_password = crypt(check_admin_credentials.password, u.encrypted_password)
    AND a.role = 'admin'
  );
END;
$$;

-- Function to update last login
CREATE OR REPLACE FUNCTION update_admin_last_login()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE admin_users
  SET last_login = now()
  WHERE id = auth.uid();
  RETURN NEW;
END;
$$;

-- Create trigger for last login update
DROP TRIGGER IF EXISTS on_admin_login ON auth.sessions;
CREATE TRIGGER on_admin_login
  AFTER INSERT ON auth.sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_admin_last_login();