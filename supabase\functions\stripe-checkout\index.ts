import 'jsr:@supabase/functions-js/edge-runtime.d.ts';
import Stripe from 'npm:stripe@17.7.0';
import { createClient } from 'npm:@supabase/supabase-js@2.49.1';

// Validate environment variables
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const stripeSecret = Deno.env.get('STRIPE_SECRET_KEY');

if (!supabaseUrl || !supabaseKey || !stripeSecret) {
  console.error('Missing environment variables:', {
    supabaseUrl: !!supabaseUrl,
    supabaseKey: !!supabaseKey,
    stripeSecret: !!stripeSecret,
  });
  throw new Error('Missing required environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);
const stripe = new Stripe(stripeSecret, {
  appInfo: { name: 'Bolt Integration', version: '1.0.0' },
});

function corsResponse(body: unknown, status = 200) {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-application-name',
  };
  return new Response(status === 204 ? null : JSON.stringify(body), {
    status,
    headers: status === 204 ? headers : { ...headers, 'Content-Type': 'application/json' },
  });
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-application-name, apikey',
      },
    });
  }

  try {
    if (req.method !== 'POST') {
      return corsResponse({ error: 'Method not allowed' }, 405);
    }

    // Add authorization check
    const authHeader = req.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return corsResponse({ error: 'Missing authorization header' }, 401);
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return corsResponse({ error: 'Invalid authentication token' }, 401);
    }

    // Fetch the user's stripe_customer_id from Supabase
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Profile fetch error:', profileError);
      return corsResponse({ error: 'Could not fetch user profile' }, 500);
    }

    let stripeCustomerId = profile?.stripe_customer_id;
    let customerExistsInStripe = false;

    // If a stripe_customer_id exists in the profile, verify it with Stripe
    if (stripeCustomerId) {
      try {
        const existingCustomer = await stripe.customers.retrieve(stripeCustomerId);
        // Check if the customer is not deleted
        if (existingCustomer && !existingCustomer.deleted) {
          customerExistsInStripe = true;
        } else {
          console.log(`Stripe customer ${stripeCustomerId} found but marked as deleted. Will create a new one.`);
          stripeCustomerId = null; // Treat as non-existent
        }
      } catch (error) {
        // Handle specific error for 'resource_missing' (customer not found)
        if (error.code === 'resource_missing') {
          console.log(`Stripe customer ${stripeCustomerId} not found in Stripe. Will create a new one.`);
          stripeCustomerId = null; // Clear the invalid ID
        } else {
          // Log other Stripe API errors and return a server error
          console.error('Stripe customer retrieval error:', error);
          return corsResponse({ error: 'Could not verify Stripe customer' }, 500);
        }
      }
    }

    // If no valid stripe_customer_id exists (either not found in profile or invalid/deleted in Stripe), create a new Customer
    if (!stripeCustomerId) {
      try {
        const customer = await stripe.customers.create({
          email: user.email!, // Ensure email is non-null, handle if necessary
          metadata: { supabase_user_id: user.id }, // Link to Supabase user
        });
        stripeCustomerId = customer.id;
        customerExistsInStripe = true; // Newly created customer exists

        // Store the new stripe_customer_id in the profiles table
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ stripe_customer_id: stripeCustomerId })
          .eq('id', user.id);

        if (updateError) {
          // Log the error but proceed cautiously, maybe the checkout can still work
          console.error('Error updating profile with new stripe_customer_id:', updateError);
          // Depending on requirements, you might want to return an error here
          // return corsResponse({ error: 'Could not save new customer ID to profile' }, 500);
        } else {
           console.log(`Created new Stripe customer ${stripeCustomerId} and updated profile for user ${user.id}`);
        }
      } catch (error) {
         console.error('Stripe customer creation error:', error);
         return corsResponse({ error: 'Could not create Stripe customer' }, 500);
      }
    }

    // Ensure we have a valid customer ID before proceeding
    if (!stripeCustomerId) {
       console.error('Failed to obtain a valid Stripe customer ID for user:', user.id);
       return corsResponse({ error: 'Could not determine Stripe customer' }, 500);
    }


    const body = await req.json();
    console.log('Received request:', body);

    // Add request validation
    const validationError = validateParameters(body, {
      line_items: { values: ['array'] },
      mode: { values: ['payment', 'subscription'] },
      // Add validation for success_url and cancel_url if they become mandatory
      // success_url: 'string',
      // cancel_url: 'string',
    });

    if (validationError) {
      return corsResponse({ error: validationError }, 400);
    }

    // Create Stripe Checkout Session with the verified or new Customer ID
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: body.line_items,
      mode: body.mode || 'subscription', // Default to subscription if not provided
      success_url: body.success_url || `${new URL(req.url).origin}/payment-success`, // Provide sensible defaults
      cancel_url: body.cancel_url || `${new URL(req.url).origin}/cart`, // Provide sensible defaults
      customer: stripeCustomerId, // Use the verified or newly created Customer ID
      metadata: {
        user_id: user.id,
        // Consider if stringifying line_items here is still necessary or safe
        // cart_items: JSON.stringify(body.line_items),
      },
    });

    return corsResponse({ url: session.url });

  } catch (error) {
    console.error('Full error details:', error);
    // Check for Stripe-specific error structure
    const errorMessage = error.raw?.message || error.message || 'An unexpected error occurred';
    const statusCode = error.statusCode || 500;
    return corsResponse(
      { error: errorMessage },
      statusCode
    );
  }
});

type ExpectedType = 'string' | { values: string[] };
type Expectations<T> = { [K in keyof T]: ExpectedType };

function validateParameters<T extends Record<string, any>>(values: T, expected: Expectations<T>): string | undefined {
  // Check for required parameters first
  for (const parameter in expected) {
      if (!(parameter in values)) {
          // Allow 'mode' to be optional as it has a default
          if (parameter === 'mode') continue;
          // You might want to make success_url and cancel_url optional too depending on your logic
          // if (parameter === 'success_url' || parameter === 'cancel_url') continue;

          // Check if the expectation is just 'string' or an object like { values: [...] }
          const expectation = expected[parameter];
          if (typeof expectation === 'string' || (typeof expectation === 'object' && expectation !== null)) {
             // Only declare missing if it's truly required based on your logic
             // For now, let's assume line_items is always required
             if (parameter === 'line_items') {
                return `Missing required parameter ${parameter}`;
             }
          }
      }
  }

  // Then validate the types of provided parameters
  for (const parameter in values) {
    // Only validate parameters that are expected
    if (!(parameter in expected)) continue;

    const expectation = expected[parameter];
    const value = values[parameter];

    if (expectation === 'string') {
      // Allow null/undefined for optional string parameters if needed
      // if (value == null) continue; // Example if 'success_url' could be optional

      if (typeof value !== 'string') {
        return `Expected parameter ${parameter} to be a string, got ${typeof value}`;
      }
    } else if (expectation && typeof expectation === 'object' && 'values' in expectation) {
      if (expectation.values[0] === 'array') {
        if (!Array.isArray(value)) {
          return `Expected parameter ${parameter} to be an array`;
        }
        // You could add further validation for array items here if needed
      } else if (!expectation.values.includes(value)) {
        return `Expected parameter ${parameter} to be one of ${expectation.values.join(', ')}, got ${value}`;
      }
    }
  }
  return undefined;
}