import { useState, useRef } from 'react';
import { Mail, MapPin, Phone } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import HCaptcha from '@hcaptcha/react-hcaptcha';
import { supabase } from '../lib/supabase';
import Alert from '../components/ui/Alert';

const schema = yup.object().shape({
  name: yup.string().required('Full name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().matches(/^[0-9]+$/, 'Phone number must contain only numbers'),
  company: yup.string(),
  message: yup.string().required('Message is required')
});

export default function Contact() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const captcha = useRef<HCaptcha>(null);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    resolver: yupResolver(schema)
  });

  const onSubmit = async (data: any) => {
    if (!captchaToken) {
      setSubmitError('Please complete the hCaptcha verification');
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      // First verify the captcha with your backend (optional additional verification)
      const { error: verifyError } = await supabase
        .rpc('verify_captcha', { token: captchaToken });

      if (verifyError) throw verifyError;

      // Then insert the form data
      const { error: insertError } = await supabase
        .from('contact_submissions')
        .insert([{
          ...data,
          captcha_token: captchaToken,
          submitted_at: new Date().toISOString()
        }]);

      if (insertError) throw insertError;

      // Send email to admin
      const { error: emailError } = await supabase.functions.invoke('send-contact-email', {
        body: {
          to: '', // Updated recipient
          subject: `New Contact Submission from ${data.name}`,
          html: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
              <h2 style="color: #2c3e50;">New Contact Form IRS Website</h2>
  
              <table cellpadding="8" cellspacing="0" border="1" style="font-family: Arial, sans-serif; color: #333;">
                <tbody>
                  <tr>
                    <td><strong>Name:</strong></td>
                    <td>${data.name}</td>
                  </tr>
                  <tr>
                    <td><strong>Email:</strong></td>
                    <td><a href="mailto:${data.email}">${data.email}</a></td>
                  </tr>
                  ${data.phone ? `
                  <tr>
                    <td><strong>Phone:</strong></td>
                    <td><a href="tel:${data.phone}">${data.phone}</a></td>
                  </tr>` : ''}
                  ${data.company ? `
                  <tr>
                    <td><strong>Company:</strong></td>
                    <td>${data.company}</td>
                  </tr>` : ''}
                </tbody>
              </table>

  
              <p><strong>Message:</strong></p>
              <div style="background: #f9f9f9; padding: 10px; border-left: 4px solid #3498db; white-space: pre-wrap;">
                ${data.message}
              </div>
            </div>

          `
        }
      });

      if (emailError) throw emailError;

      // Reset form on success
      reset();
      captcha.current?.resetCaptcha();
      setCaptchaToken(null);
      setSubmitSuccess(true);
    } catch (error: any) {
      console.error('Full error details:', error);
      setSubmitError(error.message || 'Failed to send message. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="">      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-700 via-blue-500 to-indigo-900 text-white py-24 relative rounded-lg">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Small decorative dots */}
          <div className="absolute top-32 right-20 w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-32 w-3 h-3 bg-blue-200 rounded-full opacity-60 animate-pulse delay-75"></div>
          <div className="absolute bottom-32 right-24 w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-150"></div>
          <div className="absolute bottom-20 right-40 w-1 h-1 bg-blue-100 rounded-full animate-pulse delay-300"></div>

          {/* Geometric shapes */}
          <div className="absolute top-16 right-16 w-12 h-12 border border-white/20 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-24 right-64 w-8 h-8 border border-white/10 rotate-12 animate-bounce"></div>
        </div>

        {/* Creative illustration on the right - Contact themed */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block overflow-hidden">
          <div className="relative h-full flex items-center justify-center">
            <div className="relative scale-150">
              {/* Floating contact icons */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float">
                <div className="p-3 flex items-center justify-center h-full">
                  <MapPin className="h-8 w-8 text-white" />
                  {/* <MapPin className="h-6 w-6 absolute -bottom-3 transform" />  */}
                </div>
              </div>

              <div className="absolute -top-6 right-10 w-18 h-22 bg-white/15 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed">
                <div className="p-2.5 flex items-center justify-center h-full">
                  <Phone className="h-7 w-7 text-white" />
                </div>
              </div>

              {/* Central contact hub */}
              <div className="w-40 h-40 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center relative animate-pulse-slow">
                <div className="w-28 h-28 bg-white/30 rounded-full flex items-center justify-center">
                  <div className="w-16 h-16 bg-white/40 rounded-full flex items-center justify-center">
                    <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">                  <span className="text-blue-600 font-bold text-xl">
                      <div className="relative">
                        <Mail className="h-6 w-6 absolute -left-3 -top-3 transform -rotate-12" />
                        {/* <Phone className="h-6 w-6 absolute -right-4 -top-3 transform rotate-12" />
                          <MapPin className="h-6 w-6 absolute -bottom-3 transform" /> */}
                      </div>
                    </span>
                    </div>
                  </div>
                </div>

                {/* Orbiting elements */}
                <div className="absolute inset-0 animate-spin-slow">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-300 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -right-3 transform -translate-y-1/2 w-4 h-4 bg-blue-200 rounded-full opacity-60"></div>
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-400 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -left-3 transform -translate-y-1/2 w-4 h-4 bg-blue-100 rounded-full opacity-60"></div>
                </div>
              </div>

              {/* Network connection lines */}
              <div className="absolute inset-0 pointer-events-none scale-125">
                <svg className="w-full h-full opacity-20" viewBox="0 0 400 400">
                  <defs>
                    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="rgba(255,255,255,0)" />
                      <stop offset="50%" stopColor="rgba(255,255,255,0.3)" />
                      <stop offset="100%" stopColor="rgba(255,255,255,0)" />
                    </linearGradient>
                  </defs>
                  <path
                    d="M50,200 Q200,100 350,200 Q200,300 50,200"
                    fill="none"
                    stroke="url(#gradient1)"
                    strokeWidth="3"
                    className="animate-pulse"
                  />
                  <path
                    d="M100,150 Q200,50 300,150"
                    fill="none"
                    stroke="url(#gradient1)"
                    strokeWidth="2"
                    className="animate-pulse delay-500"
                  />
                  <path
                    d="M100,250 Q200,350 300,250"
                    fill="none"
                    stroke="url(#gradient1)"
                    strokeWidth="2"
                    className="animate-pulse delay-1000"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Contact Sales
              <span className="block text-3xl font-normal mt-2 text-blue-200">
                We're Here to Help
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Let's discuss how our solutions can transform your public health emergency response
            </p>
          </div>
        </div>
      </section>

      {/* Add custom animations */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(12deg); }
          50% { transform: translateY(-10px) rotate(12deg); }
        }
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0px) rotate(-6deg); }
          50% { transform: translateY(-8px) rotate(-6deg); }
        }
        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        @keyframes bounce-slow {
          0%, 100% { transform: translateY(0px) rotate(-12deg); }
          50% { transform: translateY(-5px) rotate(-12deg); }
        }
        @keyframes pulse-slow {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }
        .animate-float { animation: float 3s ease-in-out infinite; }
        .animate-float-delayed { animation: float-delayed 3s ease-in-out infinite 1s; }
        .animate-spin-slow { animation: spin-slow 20s linear infinite; }
        .animate-bounce-slow { animation: bounce-slow 2s ease-in-out infinite; }
        .animate-pulse-slow { animation: pulse-slow 4s ease-in-out infinite; }
      `}</style>

      {/* Contact Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-16">
            {/* Contact Information - unchanged */}
            <div>
              <h2 className="text-2xl font-bold mb-8">Get in Touch</h2>
              <div className="space-y-6">
                <div className="flex items-start">
                  <MapPin className="h-6 w-6 text-blue-600 mt-1" />
                  <div className="ml-4">
                    <h3 className="font-semibold mb-1">Address</h3>
                    <p className="text-gray-600">
                      INTERNATIONAL RESPONDER SYSTEMS LLC<br />
                      157 E Main St<br />
                      Elkton, MD 21921-5977<br />
                      United States
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Mail className="h-6 w-6 text-blue-600 mt-1" />
                  <div className="ml-4">
                    <h3 className="font-semibold mb-1">Email</h3>
                    <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700">
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>

              <div className="mt-12">
                <img
                  src="https://images.unsplash.com/photo-1577962917302-cd874c4e31d2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                  alt="Office"
                  className="rounded-lg shadow-xl"
                />
              </div>
            </div>

            {/* Contact Form */}
            <div className="bg-gray-50 p-8 rounded-lg shadow-lg">
              <h2 className="text-2xl font-bold mb-6">Send us a Message</h2>

              {submitSuccess && (
                <Alert
                  type="success"
                  message="Message sent successfully!"
                  onClose={() => setSubmitSuccess(false)}
                />
              )}

              {submitError && (
                <Alert
                  type="error"
                  message={submitError}
                  onClose={() => setSubmitError('')}
                />
              )}

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    {...register('name')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    {...register('email')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    {...register('phone')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name
                  </label>
                  <input
                    type="text"
                    id="company"
                    {...register('company')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    {...register('message')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  ></textarea>
                  {errors.message && (
                    <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
                  )}
                </div>

                <div className="mt-4">
                  <HCaptcha
                    ref={captcha}
                    sitekey={import.meta.env.VITE_HCAPTCHA_SITE_KEY}
                    onVerify={(token) => setCaptchaToken(token)}
                    onExpire={() => setCaptchaToken(null)}
                  />
                </div>

                {submitError && (
                  <p className="mt-2 text-sm text-red-600">{submitError}</p>
                )}

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-md font-semibold hover:bg-blue-700 transition duration-300 disabled:opacity-50"
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}