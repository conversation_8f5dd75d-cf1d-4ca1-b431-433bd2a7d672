import React, { useState, useRef } from 'react';
// import Swiper core and required modules
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from 'swiper/modules';
import type { Swiper as SwiperType } from 'swiper';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';

import NewsBannerCard from './NewsBannerCard';
import SocialsBannerCard from './SocialsBannerCard';

const HomeBannerSlider: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = useRef<SwiperType | null>(null);

  return (
    <div className="relative w-full font-inter">
      <div className="mx-auto px-0">
        <Swiper
          modules={[Pagination, Autoplay]}
          spaceBetween={0}
          slidesPerView={1}
          loop={false}
          speed={1000}
          autoplay={{
            delay: 6000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
          }}
          onSlideChange={(swiper) => {
            setActiveIndex(swiper.realIndex);
          }}
          pagination={{
            clickable: true,
            bulletClass: 'swiper-pagination-bullet custom-bullet',
            bulletActiveClass: 'swiper-pagination-bullet-active custom-bullet-active',
            renderBullet: function (index, className) {
              return '<span class="' + className + '" data-index="' + index + '"></span>';
            },
          }}
          className="home-banner-swiper h-[320px] sm:h-[360px] md:h-[450px] lg:h-[440px] xl:h-[450px]"
          style={{ background: 'linear-gradient(0deg, #170031 0.04%, #470097 100.04%)' }}
        >
          <SwiperSlide>
            <NewsBannerCard />
            {/* <SocialsBannerCard /> */}
          </SwiperSlide>
          <SwiperSlide>
            <SocialsBannerCard />
          </SwiperSlide>
        </Swiper>
      </div>

      {/* Custom Styles */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .home-banner-swiper .swiper-pagination {
            bottom: 15px !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            width: auto !important;
            position: absolute !important;
            z-index: 20 !important;
            display: flex !important;
            justify-content: center !important;
            gap: 16px !important;
          }

          /* Mobile responsive positioning */
          @media (min-width: 640px) {
            .home-banner-swiper .swiper-pagination {
              bottom: 30px !important;
            }
          }

          .home-banner-swiper .custom-bullet {
            width: 44px !important;
            height: 8px !important;
            border-radius: 0 !important;
            background: rgba(255, 255, 255, 0.4) !important;
            opacity: 1 !important;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
            transform: skewX(-20deg) !important;
            clip-path: polygon(15% 0%, 100% 0%, 85% 100%, 0% 100%) !important;
            position: relative !important;
          }

          /* Active state styling */
          .home-banner-swiper .swiper-pagination-bullet-active {
            background: white !important;
            transform: skewX(-20deg) scale(1.1) !important;
          }

          /* Inactive state styling */
          .home-banner-swiper .swiper-pagination-bullet:not(.swiper-pagination-bullet-active) {
            background: rgba(255, 255, 255, 0.4) !important;
            transform: skewX(-20deg) !important;
          }
        `
      }} />
    </div>
  );
};

export default HomeBannerSlider;