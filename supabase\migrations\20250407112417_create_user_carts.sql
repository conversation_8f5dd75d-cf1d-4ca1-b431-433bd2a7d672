-- Create user_carts table
CREATE TABLE user_carts (
  user_id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  items jsonb NOT NULL DEFAULT '[]'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE user_carts ENABLE ROW LEVEL SECURITY;

-- Create policies for user_carts
CREATE POLICY "Enable read access for own cart" 
ON user_carts FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Enable insert for authenticated users" 
ON user_carts FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update for own cart" 
ON user_carts FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable delete for own cart" 
ON user_carts FOR DELETE
USING (auth.uid() = user_id);

-- Grant permissions to authenticated role
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE user_carts TO authenticated;

-- <PERSON>reate index for faster lookups
CREATE INDEX user_carts_user_id_idx ON user_carts(user_id);