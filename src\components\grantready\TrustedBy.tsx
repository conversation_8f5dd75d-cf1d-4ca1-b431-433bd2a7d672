import React from 'react';
import trustedby1 from '../../assets/images/grantready/trustedby1.png';
import trustedby2 from '../../assets/images/grantready/trustedby2-new.png';
import trustedby3 from '../../assets/images/grantready/trustedby3.png';
import trustedby4 from '../../assets/images/grantready/trustedby4.png';

interface TrustedPartner {
    src: string;
    alt: string;
    size: number; // height in pixels
}

interface TrustedByProps {
    title: string;
    description: string;
}

const TrustedBy: React.FC<TrustedByProps> = ({ title, description }) => {
    const trustedPartners: TrustedPartner[] = [
        { src: trustedby2, alt: "California Department of Public Health", size: 80 },
        { src: trustedby3, alt: "Virginia Department of Health", size: 112 },
        { src: trustedby1, alt: "PHIG Partners", size: 112 },
        { src: trustedby3, alt: "Virginia Department of Health", size: 112 },
        { src: trustedby4, alt: "Public Health Emergency Preparedness", size: 44 }
    ];

    return (
      <div className="relative w-full bg-custom-gray mb-10 rounded-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20 flex flex-col justify-center items-center gap-6 sm:gap-8 md:gap-12">
          {/* Header Section */}
          <div className="flex flex-col justify-center items-center gap-3 sm:gap-4">
            <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl text-center">
              {title}
            </h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl font-medium text-black text-center leading-relaxed font-inter max-w-2xl px-2">
              {description}
            </p>
          </div>

          {/* Logos Section */}
          <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-5 gap-4 sm:gap-6 md:gap-8 lg:gap-10 w-full max-w-5xl">
            {trustedPartners.map((partner, index) => (
              <div
                key={index}
                className="flex justify-center items-center p-2 sm:p-3 md:p-4"
              >
                <img
                  src={partner.src}
                  alt={partner.alt}
                  style={{ width: `${partner.size}px` }}
                  className="max-w-full w-auto object-contain filter grayscale-0 hover:grayscale transition-all duration-300 hover:scale-105"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
};

export default TrustedBy;
