import { <PERSON>, <PERSON>R<PERSON> } from "lucide-react";
import DataModelingImg from "../../assets/home/<USER>";
import datamod1 from "../../assets/home/<USER>";
import datamod2 from "../../assets/home/<USER>";
import { Link } from "react-router-dom";
import ChangedText from "../ui/ChangedText.tsx";

function DataModeling() {
  return (
    <div className="max-w-7xl bg-[#FFFFFF] mx-auto p-4 md:p-6 lg:p-8">
      {/* Main container */}
      <div className="bg-white rounded-lg p-4 sm:p-6 md:p-8 lg:p-12">
        {/* Header Section */}
        <div className="text-center mb-6 sm:mb-8 md:mb-12">
          <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl text-center">
            Data Modeling and Analytics
          </h2>
          <p className="text-sm sm:text-base font-inter md:text-lg text-black max-w-xl mx-auto px-2">
            We are fine tuning models to push the limit on our prediction
            accuracy to best prepare for any outcome
          </p>
        </div>

        {/* Main Workflow Section */}
        <div className="flex flex-col lg:flex-row gap-4 sm:gap-6 md:gap-8 items-center mb-0">
          {/* Left Side - Inserting Data */}
          <div className="w-full lg:w-72 flex flex-col items-center">
            <video
              src={datamod1}
              className="w-10 h-10 md:w-14 md:h-14 border-0 outline-none object-cover"
              autoPlay
              loop
              muted
            ></video>
            <h3 className="text-base md:text-lg font-bold font-inter text-black mb-3 sm:mb-4 text-center">
              Inserting Data
            </h3>
            <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-3 sm:p-4 md:p-6 w-full">
              <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm md:text-sm text-gray-700">
                <li className="flex items-start">
                  <span className="text-gray-700 mr-2">•</span>
                  Public Health Systems
                </li>
                <li className="flex items-start">
                  <span className="text-gray-700 mr-2">•</span>
                  Environmental Sensors
                </li>
                <li className="flex items-start">
                  <span className="text-gray-700 mr-2">•</span>
                  IoT Devices
                </li>
                <li className="flex items-start">
                  <span className="text-gray-700 mr-2">•</span>
                  Government Databases
                </li>
              </ul>
            </div>
          </div>

          {/* Arrow 1 - Hidden on mobile */}
          <div className="hidden lg:flex justify-center">
            <ArrowRight className="h-8 w-8 text-gray-400" />
          </div>

          {/* Center - Modeling & Machine Learning */}
          <div className="w-full">
            <div className="w-full border-2 border-pink-200 rounded-lg p-4 md:p-6">
              <img
                src={DataModelingImg}
                alt="data modeling"
                className="w-full h-auto pointer-events-none"
              />
            </div>
          </div>

          {/* Arrow 2 - Hidden on mobile */}
          <div className="hidden lg:flex justify-center">
            <ArrowRight className="h-8 w-8 text-gray-400" />
          </div>

          {/* Right Side - End Application */}
          <div className="w-full lg:w-72 flex flex-col items-center">
            <video
              src={datamod2}
              className="w-16 h-16 sm:w-10 sm:h-10 md:w-14 md:h-14 border-0 outline-none object-cover"
              autoPlay
              loop
              muted
            ></video>
            <h3 className="text-base md:text-lg font-bold text-black font-inter mb-3 sm:mb-4 text-center">
              End Application
            </h3>
            <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-3 sm:p-4 md:p-6 w-full">
              <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm md:text-sm text-gray-700">
                <li className="flex items-start">
                  <span className="text-gray-700 mr-2">•</span>
                  Real-Time Alerts
                </li>
                <li className="flex items-start">
                  <span className="text-gray-700 mr-2">•</span>
                  Geospatial Visualization
                </li>
                <li className="flex items-start">
                  <span className="text-gray-700 mr-2">•</span>
                  Collaboration & Orchestration
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="hidden border max-w-4xl mx-auto items-center justify-center md:mb-6 md:mt-2 p-4 rounded-8">
          <p className="text-sm text-center uppercase sm:text-base md:text-lg text-black font-medium max-w-4xl mx-auto leading-relaxed px-2 sm:px-0">
            {/* @augment , this place must have a variable that contain the content of this p elements.
            so we we enable the the admin to insert and edit this content of this elemt from the admin panel. */}
            # under dev
            
          </p>
        </div>
        <ChangedText name={'soar'} />
        {/* Action Buttons */}
        <div className="flex  gap-4 justify-center items-center">
          <Link
            to="https://www.cdc.gov/forecast-outbreak-analytics/partners/insightnet/implementers.html"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-black text-white border-2 border-black py-0.5 px-0.5 pl-3.5 rounded-8 text-sm md:text-base font-medium cursor-pointer flex items-center justify-between transition-all duration-200 ease-in-out tracking-wide no-underline hover:bg-gray-800 hover:border-gray-800"
          >
            Learn More
            <span className="flex items-center justify-center w-10 h-10 bg-white ml-5 rounded-md">
              <ArrowRight className="h-5 w-5 text-black" />
            </span>
          </Link>
          <Link
            to={"/book-a-demo"}
            className="bg-black text-white border-2 border-black py-0.5 px-0.5 pl-3.5 rounded-8 text-sm md:text-base font-medium cursor-pointer flex items-center justify-between transition-all duration-200 ease-in-out tracking-wide no-underline hover:bg-gray-800 hover:border-gray-800"
          >
            Book A Demo
            <span className="flex items-center justify-center w-10 h-10 bg-white ml-5 rounded-md">
              <ArrowRight className="h-5 w-5 text-black" />
            </span>
          </Link>
        </div>
      </div>
    </div>
  );
}

export default DataModeling;
