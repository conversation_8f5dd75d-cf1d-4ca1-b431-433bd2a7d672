-- First check if table exists and drop if it does
DROP TABLE IF EXISTS subscribers CASCADE;

-- Create subscribers table
CREATE TABLE subscribers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  full_name text,
  status text NOT NULL CHECK (status IN ('active', 'unsubscribed')) DEFAULT 'active',
  subscribed_at timestamptz DEFAULT now(),
  last_email_sent timestamptz,
  CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Enable RLS
ALTER TABLE subscribers ENABLE ROW LEVEL SECURITY;

-- Create policies for subscribers
CREATE POLICY "Anyone can subscribe"
  ON subscribers
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Admins can manage subscribers"
  ON subscribers
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));

-- <PERSON><PERSON> function to handle subscription
CREATE OR REPLACE FUNCTION subscribe_to_newsletter(subscriber_email text, subscriber_name text DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  existing_subscriber subscribers;
  result json;
BEGIN
  -- Validate email format
  IF NOT (subscriber_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$') THEN
    RAISE EXCEPTION 'Invalid email format';
  END IF;

  -- Check for existing subscriber with proper locking
  SELECT * INTO existing_subscriber
  FROM subscribers
  WHERE email = subscriber_email
  FOR UPDATE;

  IF existing_subscriber IS NOT NULL THEN
    -- First check if email exists and is active
    PERFORM 1 FROM subscribers 
    WHERE email = subscriber_email 
    AND status = 'active'
    LIMIT 1;
    
    IF FOUND THEN
      RETURN json_build_object(
        'status', 'error',
        'message', 'This email is already subscribed'
      );
    ELSE
      -- Reactivate unsubscribed user
      UPDATE subscribers 
      SET 
        status = 'active',
        full_name = COALESCE(subscriber_name, existing_subscriber.full_name),
        subscribed_at = now()
      WHERE email = subscriber_email
      RETURNING 
        json_build_object(
          'id', id,
          'status', 'reactivated',
          'message', 'Thank you for resubscribing!'
        ) INTO result;
      RETURN result;
    END IF;
  ELSE
    -- Insert new subscriber
    BEGIN
      INSERT INTO subscribers (email, full_name, status)
      VALUES (subscriber_email, subscriber_name, 'active')
      RETURNING 
        json_build_object(
          'id', id,
          'status', 'new',
          'message', 'Thank you for subscribing!'
        ) INTO result;
      EXCEPTION WHEN unique_violation THEN
        -- Handle race condition where another transaction inserted the same email
        SELECT * INTO existing_subscriber
        FROM subscribers
        WHERE email = subscriber_email;
        
        IF existing_subscriber.status = 'active' THEN
          RETURN json_build_object(
            'status', 'error',
            'message', 'This email is already subscribed'
          );
        ELSE
          -- If somehow the existing record is unsubscribed, reactivate it
          UPDATE subscribers 
          SET 
            status = 'active',
            full_name = COALESCE(subscriber_name, existing_subscriber.full_name),
            subscribed_at = now()
          WHERE email = subscriber_email
          RETURNING 
            json_build_object(
              'id', id,
              'status', 'reactivated',
              'message', 'Thank you for resubscribing!'
            ) INTO result;
          RETURN result;
        END IF;
    END;
  END IF;
END;
$$;

-- Grant necessary permissions
GRANT ALL ON subscribers TO authenticated;
GRANT EXECUTE ON FUNCTION subscribe_to_newsletter TO public;

-- Insert sample subscribers
INSERT INTO subscribers (email, full_name, status) VALUES
  ('<EMAIL>', 'John Doe', 'active'),
  ('<EMAIL>', 'Jane Smith', 'active'),
  ('<EMAIL>', 'Bob Wilson', 'unsubscribed')
ON CONFLICT (email) DO NOTHING;