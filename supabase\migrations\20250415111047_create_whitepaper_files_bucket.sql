-- Create storage bucket for whitepaper PDF files if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('whitepaper-files', 'whitepaper-files', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for whitepaper files bucket
CREATE POLICY "Public can view whitepaper files"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'whitepaper-files');

CREATE POLICY "Admins can manage whitepaper files"
ON storage.objects FOR ALL 
TO authenticated
USING (
  bucket_id = 'whitepaper-files' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);

-- Add file format validation
CREATE POLICY "Validate whitepaper file uploads"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'whitepaper-files' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ) AND
  storage.extension(name) IN ('pdf')
);

-- Add file_url column to media_items table
ALTER TABLE media_items
ADD COLUMN IF NOT EXISTS file_url TEXT;
