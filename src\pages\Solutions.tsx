import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>, <PERSON>, Briefcase, Award, Brain, Building2, Zap, Phone, BookOpen, Download, Heart, Laptop2, ScreenShare, Gamepad2Icon, NotepadText, Globe2, ShieldCheck, BarChart3, <PERSON>tings, ShieldPlus } from 'lucide-react';
import { Link } from 'react-router-dom';
import Ssolutions from '../components/home/<USER>';
import Capabilities from '../components/home/<USER>';


const studioIcon = () => {
  return (
    <svg width="25" height="23" viewBox="0 0 25 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.9998 0.899951C4.9998 0.7143 5.07355 0.536252 5.20483 0.404977C5.33611 0.273701 5.51415 0.199951 5.6998 0.199951H9.8998C10.0855 0.199951 10.2635 0.273701 10.3948 0.404977C10.5261 0.536252 10.5998 0.7143 10.5998 0.899951V5.09995C10.5998 5.2856 10.5261 5.46365 10.3948 5.59493C10.2635 5.7262 10.0855 5.79995 9.8998 5.79995H5.6998C5.51415 5.79995 5.33611 5.7262 5.20483 5.59493C5.07355 5.46365 4.9998 5.2856 4.9998 5.09995V0.899951ZM14.0998 0.199951C13.9142 0.199951 13.7361 0.273701 13.6048 0.404977C13.4736 0.536252 13.3998 0.7143 13.3998 0.899951V5.09995C13.3998 5.2856 13.4736 5.46365 13.6048 5.59493C13.7361 5.7262 13.9142 5.79995 14.0998 5.79995H18.2998C18.4855 5.79995 18.6635 5.7262 18.7948 5.59493C18.9261 5.46365 18.9998 5.2856 18.9998 5.09995V0.899951C18.9998 0.7143 18.9261 0.536252 18.7948 0.404977C18.6635 0.273701 18.4855 0.199951 18.2998 0.199951H14.0998ZM14.7998 4.39995V1.59995H17.5998V4.39995H14.7998ZM0.799805 12.8C0.799805 11.3147 1.3898 9.89036 2.44001 8.84015C3.49021 7.78995 4.91459 7.19995 6.3998 7.19995H17.5998C19.5458 7.19995 21.258 8.19115 22.2618 9.69615C20.9482 8.90551 19.4276 8.52707 17.8966 8.60975C17.7978 8.60304 17.6988 8.59977 17.5998 8.59995H6.3998C5.2859 8.59995 4.21761 9.04245 3.42996 9.8301C2.6423 10.6178 2.1998 11.686 2.1998 12.8C2.1998 13.9139 2.6423 14.9821 3.42996 15.7698C4.21761 16.5575 5.2859 17 6.3998 17H10.6306C10.6735 17.4736 10.7602 17.9423 10.8896 18.4H6.3998C4.91459 18.4 3.49021 17.81 2.44001 16.7597C1.3898 15.7095 0.799805 14.2852 0.799805 12.8ZM11.9998 11.4C12.1118 11.4 12.2238 11.414 12.3288 11.4392C11.7556 12.141 11.311 12.9387 11.0156 13.7954C10.8183 13.6002 10.6834 13.3508 10.6281 13.0788C10.5728 12.8069 10.5996 12.5246 10.7051 12.2679C10.8106 12.0112 10.99 11.7916 11.2205 11.6371C11.451 11.4825 11.7223 11.4 11.9998 11.4ZM6.3998 14.2C6.77111 14.2 7.1272 14.0525 7.38975 13.7899C7.6523 13.5273 7.7998 13.1713 7.7998 12.8C7.7998 12.4286 7.6523 12.0726 7.38975 11.81C7.1272 11.5475 6.77111 11.4 6.3998 11.4C6.0285 11.4 5.67241 11.5475 5.40986 11.81C5.1473 12.0726 4.9998 12.4286 4.9998 12.8C4.9998 13.1713 5.1473 13.5273 5.40986 13.7899C5.67241 14.0525 6.0285 14.2 6.3998 14.2ZM24.5998 16.3C24.5998 17.9708 23.9361 19.5732 22.7546 20.7547C21.5731 21.9362 19.9707 22.6 18.2998 22.6C16.6289 22.6 15.0265 21.9362 13.845 20.7547C12.6636 19.5732 11.9998 17.9708 11.9998 16.3C11.9998 14.6291 12.6636 13.0267 13.845 11.8452C15.0265 10.6637 16.6289 9.99995 18.2998 9.99995C19.9707 9.99995 21.5731 10.6637 22.7546 11.8452C23.9361 13.0267 24.5998 14.6291 24.5998 16.3ZM20.6042 13.7044L16.8998 17.4102L15.9954 16.5044C15.864 16.3729 15.6857 16.2991 15.4998 16.2991C15.3139 16.2991 15.1356 16.3729 15.0042 16.5044C14.8728 16.6358 14.7989 16.8141 14.7989 17C14.7989 17.1858 14.8728 17.3641 15.0042 17.4956L16.4042 18.8956C16.4692 18.9607 16.5465 19.0125 16.6315 19.0477C16.7166 19.083 16.8077 19.1012 16.8998 19.1012C16.9919 19.1012 17.083 19.083 17.1681 19.0477C17.2531 19.0125 17.3304 18.9607 17.3954 18.8956L21.5954 14.6956C21.7268 14.5641 21.8007 14.3858 21.8007 14.2C21.8007 14.0141 21.7268 13.8358 21.5954 13.7044C21.464 13.5729 21.2857 13.4991 21.0998 13.4991C20.9139 13.4991 20.7356 13.5729 20.6042 13.7044Z" fill="#4F63D9" />
    </svg>

  )
}

const graphIcon = () => {
  return (
    <svg width="20" height="12" viewBox="0 0 20 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.99967 1.33337V6.00004M9.99967 6.00004H5.91634C3.99134 6.00004 3.02884 6.00004 2.43151 6.51337C1.83301 7.02437 1.83301 7.84921 1.83301 9.50004V10.6667M9.99967 6.00004H14.083C16.008 6.00004 16.9705 6.00004 17.5678 6.51337C18.1663 7.02437 18.1663 7.84921 18.1663 9.50004V10.6667" stroke="#4F63D9" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" />
    </svg>

  )
}

const bagIcon = () => {
  return (
    <svg width="26" height="24" viewBox="0 0 26 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0_1284_4770" style={{ maskType: "luminance" }} maskUnits="userSpaceOnUse" x="0" y="0" width="26" height="24">
        <path d="M3.66699 12.5834V20.1667C3.66699 20.6308 3.85137 21.076 4.17956 21.4041C4.50774 21.7323 4.95286 21.9167 5.41699 21.9167H20.5837C21.0478 21.9167 21.4929 21.7323 21.8211 21.4041C22.1493 21.076 22.3337 20.6308 22.3337 20.1667V12.5834" stroke="white" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M1.91699 6.75004C1.91699 6.44062 2.03991 6.14388 2.2587 5.92508C2.47749 5.70629 2.77424 5.58337 3.08366 5.58337H22.917C23.2264 5.58337 23.5232 5.70629 23.742 5.92508C23.9607 6.14388 24.0837 6.44062 24.0837 6.75004V11.4167C24.0837 11.7261 23.9607 12.0229 23.742 12.2417C23.5232 12.4605 23.2264 12.5834 22.917 12.5834H3.08366C2.77424 12.5834 2.47749 12.4605 2.2587 12.2417C2.03991 12.0229 1.91699 11.7261 1.91699 11.4167V6.75004Z" fill="white" stroke="white" stroke-width="2.33333" stroke-linejoin="round" />
        <path d="M17.0843 5.58337V3.25004C17.0843 2.94062 16.9614 2.64388 16.7426 2.42508C16.5238 2.20629 16.2271 2.08337 15.9176 2.08337H10.0843C9.77489 2.08337 9.47814 2.20629 9.25935 2.42508C9.04056 2.64388 8.91764 2.94062 8.91764 3.25004V5.58337M7.75098 11.4167V14.9167M18.251 11.4167V14.9167" stroke="white" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round" />
      </mask>
      <g mask="url(#mask0_1284_4770)">
        <path d="M-1 -2H27V26H-1V-2Z" fill="#4F63D9" />
      </g>
    </svg>
  )

}

const virusIcon = () => {
  return (
    <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M24.9377 14.793C23.6799 14.0638 22.34 13.763 20.9182 13.8906C20.9728 14.2552 21.0002 14.5742 21.0002 14.8477C21.0002 16.4154 20.5353 17.8145 19.6057 19.0449C18.676 20.2754 17.482 21.1094 16.0236 21.5469C16.4611 23.2969 17.3908 24.5911 18.8127 25.4297C20.4533 26.3776 22.9325 26.3503 26.2502 25.3477C25.6669 25.9492 24.8055 26.4141 23.6662 26.7422C22.5269 27.0703 21.2827 27.1888 19.9338 27.0977C18.5848 27.0065 17.3908 26.6693 16.3518 26.0859C15.4221 25.5573 14.6382 24.8737 14.0002 24.0352C13.3622 24.8737 12.5692 25.5573 11.6213 26.0859C10.6005 26.6693 9.411 27.0065 8.05293 27.0977C6.69485 27.1888 5.45071 27.0703 4.3205 26.7422C3.19029 26.4141 2.32441 25.9492 1.72285 25.3477C2.23326 25.5117 2.74824 25.6576 3.26777 25.7852C3.7873 25.9128 4.40253 26.0176 5.11347 26.0996C5.82441 26.1816 6.5399 26.168 7.25996 26.0586C7.98001 25.9492 8.61347 25.7396 9.16035 25.4297C10.5822 24.5911 11.521 23.2969 11.9768 21.5469C10.5184 21.1094 9.32441 20.2754 8.39472 19.0449C7.46503 17.8145 7.00019 16.4154 7.00019 14.8477C7.00019 14.5742 7.01842 14.2552 7.05488 13.8906C5.633 13.763 4.29316 14.0638 3.03535 14.793C2.16035 15.3034 1.43574 16.069 0.861518 17.0898C0.287299 18.1107 0.000189887 19.0221 0.000189887 19.8242C-0.0180393 16.7435 1.27623 14.474 3.883 13.0156C4.88561 12.4323 5.98847 12.1042 7.1916 12.0312C6.55358 10.901 6.23456 9.6888 6.23456 8.39453C6.23456 6.27995 6.80423 4.49349 7.94355 3.03516C9.08287 1.57682 10.5184 0.847656 12.2502 0.847656C11.284 0.847656 10.2997 1.46289 9.29707 2.69336C8.29446 3.92383 7.79316 5.32292 7.79316 6.89062C7.79316 8.07552 8.12128 9.16927 8.77753 10.1719C9.43378 9.44271 10.2131 8.87305 11.1154 8.46289C12.0178 8.05273 12.9748 7.84766 13.9865 7.84766C14.9982 7.84766 15.9553 8.05273 16.8576 8.46289C17.76 8.87305 18.5393 9.44271 19.1955 10.1719C19.87 9.16927 20.2072 8.07552 20.2072 6.89062C20.2072 5.32292 19.7014 3.92383 18.6896 2.69336C17.6779 1.46289 16.6981 0.847656 15.7502 0.847656C17.482 0.847656 18.913 1.57682 20.0432 3.03516C21.1734 4.49349 21.7385 6.27995 21.7385 8.39453C21.7385 9.6888 21.4286 10.901 20.8088 12.0312C21.9937 12.1042 23.0965 12.4323 24.1174 13.0156C25.4299 13.7448 26.4051 14.679 27.0432 15.8184C27.6812 16.9577 28.0002 18.3841 28.0002 20.0977C28.0002 19.168 27.7131 18.179 27.1389 17.1309C26.5646 16.0827 25.8309 15.3034 24.9377 14.793ZM19.2502 14.8477L19.1955 14.3008C18.0835 14.7747 17.2268 15.5404 16.6252 16.5977C16.0965 17.5273 15.8231 18.5846 15.8049 19.7695C16.8257 19.3867 17.6551 18.7487 18.2932 17.8555C18.9312 16.9622 19.2502 15.9596 19.2502 14.8477ZM8.75019 14.8477C8.75019 15.9596 9.06464 16.9622 9.69355 17.8555C10.3225 18.7487 11.1473 19.3867 12.1682 19.7695C12.1499 18.5664 11.8856 17.5091 11.3752 16.5977C10.7554 15.5404 9.89863 14.7747 8.80488 14.3008C8.76842 14.6289 8.75019 14.8112 8.75019 14.8477ZM14.0002 9.59766C12.396 9.59766 11.0562 10.2266 9.98066 11.4844C11.1656 12.4505 12.5008 12.9336 13.9865 12.9336C15.4722 12.9336 16.8075 12.4505 17.9924 11.4844C16.9351 10.2266 15.6044 9.59766 14.0002 9.59766Z" fill="#4F63D9" />
    </svg>

  )

}

const stockIcon = () => {
  return (
    <svg width="24" height="18" viewBox="0 0 24 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M23.0739 0.772275L17.627 1.42852C17.4466 1.4504 17.37 1.67188 17.4985 1.8004L19.12 3.42188L12.9403 9.60157L10.1567 6.82071C9.98445 6.64845 9.70827 6.65118 9.53874 6.82071L0.742258 15.6199C0.701548 15.661 0.678711 15.7166 0.678711 15.7744C0.678711 15.8323 0.701548 15.8878 0.742258 15.9289L1.97273 17.1649C2.05749 17.2496 2.19695 17.2496 2.28171 17.1649L9.84773 9.60157L12.6286 12.3824C12.8009 12.552 13.077 12.552 13.2466 12.3824L20.6676 4.96681L22.2891 6.58829C22.3182 6.6172 22.3548 6.63738 22.3947 6.64653C22.4347 6.65568 22.4764 6.65343 22.5152 6.64005C22.5539 6.62666 22.5881 6.60266 22.6139 6.57079C22.6397 6.53892 22.656 6.50046 22.661 6.45977L23.3173 1.0129C23.3364 0.873446 23.2161 0.753134 23.0739 0.772275Z" fill="#4F63D9" />
    </svg>

  )
}

import SolutionHeroBg from '../assets/images/solution-hero-bg.mp4';

export default function Solutions() {
  const services = [
    {
      title: "Public Health Disaster Response Planning and Training",
      icon: Heart
    },
    {
      title: "Public Health Emergency Training and Exercises",
      icon: Laptop2
    },
    {
      title: "Virtual and Online Training and webinars",
      icon: ScreenShare
    },
    {
      title: "Studio production, design, engineering and installation",
      icon: studioIcon

    },
    {
      title: "Response and Safety Plan Development",
      icon: Shield
    },

    {
      title: "ICS/EOC Position Specific Training",
      icon: graphIcon
    },
    {
      title: "NIMS and ICS Training",
      icon: bagIcon
    },
    {
      title: "HSEEP Compliant Exercises",
      icon: NotepadText
    },
    {
      title: "Just in Time Response Training",
      icon: Clock
    },
    {
      title: "Training in Spanish",
      icon: Globe2
    },
    {
      title: "Training in Hazard, Vulnerability and Health Risk Assessment",
      icon: stockIcon
    },
    {
      title: "NIMS Compliance",
      icon: ShieldCheck
    },
    {
      title: "Highly Infectious Disease Response Planning and Exercises",
      icon: virusIcon
    },
    {
      title: "Project and Program Management",
      icon: BarChart3
    },
    {
      title: "Role Player Training and Support",
      icon: Users
    },
    {
      title: "Operational Support and training in HAZMAT/Health Physics/Chemistry/Highly Infectious Disease Response and Law Enforcement",
      icon: ShieldPlus
    },
    {
      title: "Emergency Operations Facility Support, construction & Management",
      icon: Settings
    },
  ];

  return (
    <div className="">
      {/* Hero Section */}
      <section className="text-white py-4 sm:py-6 md:py-8 lg:py-10 xl:py-12 relative rounded-lg overflow-hidden">
        {/* Background Video */}
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
        >
          <source src={SolutionHeroBg} type="video/mp4" />
        </video>


        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            {/* Small title */}
            <p className="text-sm sm:text-base md:text-lg font-medium mb-2 sm:mb-4 text-purple-200 tracking-wide">
              Solutions
            </p>

            {/* Main heading */}
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 sm:mb-6 md:mb-8 leading-tight px-2 sm:px-0">
              World-class Solutions for Your Needs
            </h1>

            {/* Description */}
            <p className="text-sm sm:text-base md:text-lg lg:text-xl mb-6 sm:mb-8 md:mb-10 opacity-90 leading-relaxed max-w-sm sm:max-w-2xl md:max-w-3xl lg:max-w-4xl mx-auto px-2 sm:px-0">
              Our team has domain knowledge in government, public health, disaster and emergency response, and engineering. We use our expertise to deliver sound solutions to our clients spanning multiple fields and disciplines to fit their needs.
            </p>
          </div>
        </div>
      </section>

      {/* Primary Services Section */}
      <section id='primary-services' className="py-24 bg-gradient-to-br from-blue-50 via-white to-indigo-50 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">Primary Services</h2>
            <p className="text-xl text-gray-700 max-w-2xl mx-auto">
              We offer a comprehensive list of premium services tailored to our customers' unique requirements
            </p>
          </div>

          <div className="flex flex-wrap justify-center gap-6">
  {services.map((service, index) => {
    const IconComponent = service.icon;
    const isLastRow = index >= services.length - (services.length % 3);
    const isLastTwoItems = services.length % 3 === 2 && isLastRow;
    
    return (
      <div
        key={index}
        className="group bg-white border border-gray-200 p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 relative"
        style={{
          flexBasis: isLastTwoItems ? 'calc(50% - 0.75rem)' : 'calc(33.333% - 1rem)',
          minWidth: '280px',
          maxWidth: isLastTwoItems ? 'calc(50% - 0.75rem)' : 'calc(33.333% - 1rem)'
        }}
      >
        {/* Icon */}
        <div className="flex items-center justify-center w-12 h-12 mb-4">
          <IconComponent className="h-6 w-6 text-[#4F63D9]" />
        </div>

        {/* Service Title */}
        <h3 className="text-base font-semibold text-gray-900 leading-tight mb-2">
          {service.title}
        </h3>

        {/* Hover effect */}
        <div className="absolute inset-0 rounded-xl border-2 border-transparent group-hover:border-blue-200 transition-all duration-300 pointer-events-none"></div>
      </div>
    );
  })}
</div>
        </div>
      </section>

      {/* <OurSolutions />``
      <Ssolutions />
      <Capabilities /> */}

      {/* Resources Section
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Resources</h2>
            <p className="text-xl text-gray-600">
              Explore our collection of whitepapers, guides, and other resources to learn more about our solutions and industry best practices.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-blue-100 mr-4">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold">Whitepapers</h3>
                </div>
                <p className="text-gray-600 mb-6">
                  In-depth reports and analyses on public health, emergency management, and grant administration topics.
                </p>
                <Link to="/whitepapers" className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
                  Browse Whitepapers
                  <Download className="h-4 w-4 ml-2" />
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-green-100 mr-4">
                    <BookOpen className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="text-xl font-bold">Guides</h3>
                </div>
                <p className="text-gray-600 mb-6">
                  Step-by-step guides and best practices for implementing effective public health and emergency response systems.
                </p>
                <Link to="/guides" className="inline-flex items-center text-green-600 hover:text-green-700 font-medium">
                  View Guides
                  <Download className="h-4 w-4 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Operations?</h2>
          <p className="text-xl mb-8">Get in touch with our team to learn how we can help.</p>
          <Link to="/contact" className="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300">
            <Phone className="h-5 w-5 mr-2" />
            Contact Us
          </Link>
        </div>
      </section> */}
    </div>
  );
}
