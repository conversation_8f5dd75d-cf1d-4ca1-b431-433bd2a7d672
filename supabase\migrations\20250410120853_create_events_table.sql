-- Migration: Create events table and related functionality
-- Purpose: Add support for events management and registration

-- Create events table
CREATE TABLE events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text NOT NULL,
  date text NOT NULL, -- Store as ISO string
  time text NOT NULL,
  end_time text, -- Optional end time
  location text NOT NULL,
  address text, -- Optional detailed address
  type text NOT NULL, -- e.g., Workshop, Conference, Training
  capacity integer, -- Optional maximum capacity
  image_url text NOT NULL,
  is_featured boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create event_registrations table
CREATE TABLE event_registrations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id uuid NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE SET NULL, -- Optional, for logged-in users
  full_name text NOT NULL,
  email text NOT NULL,
  phone text DEFAULT NULL,
  company text DEFAULT NULL,
  registered_at timestamptz DEFAULT now(),
  status text NOT NULL CHECK (status IN ('registered', 'cancelled', 'attended')) DEFAULT 'registered',
  CONSTRAINT event_registrations_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Add RLS policies
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_registrations ENABLE ROW LEVEL SECURITY;

-- Events policies
CREATE POLICY "Anyone can view events"
ON events FOR SELECT
TO public
USING (true);

CREATE POLICY "Only admins can insert events"
ON events FOR INSERT
TO authenticated
WITH CHECK (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

CREATE POLICY "Only admins can update events"
ON events FOR UPDATE
TO authenticated
USING (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

CREATE POLICY "Only admins can delete events"
ON events FOR DELETE
TO authenticated
USING (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

-- Event registrations policies
CREATE POLICY "Anyone can register for events"
ON event_registrations FOR INSERT
TO public
WITH CHECK (true);

CREATE POLICY "Users can view their own registrations"
ON event_registrations FOR SELECT
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Admins can view all registrations"
ON event_registrations FOR SELECT
TO authenticated
USING (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

CREATE POLICY "Users can update their own registrations"
ON event_registrations FOR UPDATE
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Admins can update all registrations"
ON event_registrations FOR UPDATE
TO authenticated
USING (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

-- Create function to update event timestamps
CREATE OR REPLACE FUNCTION update_event_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_event_timestamp
  BEFORE UPDATE ON events
  FOR EACH ROW
  EXECUTE FUNCTION update_event_timestamp();

-- Grant permissions to anon and authenticated roles
GRANT SELECT ON TABLE events TO anon, authenticated;
GRANT SELECT, INSERT ON TABLE event_registrations TO anon, authenticated;
GRANT ALL ON TABLE events TO authenticated;
GRANT ALL ON TABLE event_registrations TO authenticated;

-- Add comments to explain the tables
COMMENT ON TABLE events IS 'Stores information about upcoming and past events';
COMMENT ON TABLE event_registrations IS 'Stores registrations for events';

-- Migration: Add captcha_token column to event_registrations table
-- Purpose: Support captcha verification for event registrations

-- Add captcha_token column to event_registrations table
ALTER TABLE event_registrations ADD COLUMN captcha_token text;

-- Add comment to explain the column
COMMENT ON COLUMN event_registrations.captcha_token IS 'Stores the captcha token for verification purposes';
