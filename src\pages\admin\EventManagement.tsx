import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import {
  Edit,
  Trash2,
  Plus,
  X,
  AlertCircle,
  Check,
  Users,
  Search,
  Download,
  Star,
  Upload,
  Image as ImageIcon,
  XCircle,
  MonitorPlay
} from 'lucide-react';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  end_time?: string;
  location: string;
  address?: string;
  type: string;
  capacity?: number;
  image_url: string;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

interface EventRegistration {
  id: string;
  event_id: string;
  user_id?: string;
  full_name: string;
  email: string;
  phone?: string;
  company?: string;
  registered_at: string;
  status: 'registered' | 'cancelled';
  captcha_token?: string;
}

export default function EventManagement() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [registrationSearchQuery, setRegistrationSearchQuery] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageInputType, setImageInputType] = useState<'upload' | 'url'>('upload');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const registrationSearchInputRef = useRef<HTMLInputElement>(null);
  const [formData, setFormData] = useState<Partial<Event>>({
    title: '',
    description: '',
    date: '',
    time: '',
    end_time: '',
    location: '',
    address: '',
    type: '',
    capacity: undefined,
    image_url: '',
    is_featured: false
  });
  const [isEditing, setIsEditing] = useState(false);
  const [viewingRegistrations, setViewingRegistrations] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const [registrations, setRegistrations] = useState<EventRegistration[]>([]);

  // Initial fetch handled by the fetchEvents useEffect

  // Function to handle escape key press
  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showForm) {
        setShowForm(false);
        setIsEditing(false);
        setSelectedEventId(null);
        resetForm();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [showForm]);

  // Use useCallback for fetchEvents to properly handle dependencies
  const fetchEvents = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Calculate pagination values
      const from = (currentPage - 1) * pageSize;
      const to = from + pageSize - 1;

      console.log(`Fetching events: page ${currentPage}, size ${pageSize}, from ${from}, to ${to}`);

      // Build the query with count option to get total
      let query = supabase
        .from('events')
        .select('*', { count: 'exact' })
        .order('date', { ascending: false })
        .range(from, to);

      // Apply search filter if provided
      if (searchQuery.trim() !== '') {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,location.ilike.%${searchQuery}%,type.ilike.%${searchQuery}%`);
      }

      // Execute the query
      const { data, error, count } = await query;

      if (error) throw error;

      console.log(`Fetched ${data?.length} events, total count: ${count}`);

      // Update state with the paginated data
      setEvents(data || []);

      // Update pagination information
      const totalCount = count || 0;
      const totalPages = Math.ceil(totalCount / pageSize);
      setTotalCount(totalCount);
      setTotalPages(totalPages);

    } catch (err: unknown) {
      console.error('Error fetching events:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchQuery]);

  // Fetch events when fetchEvents function changes (which happens when dependencies change)
  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const fetchRegistrations = async (eventId: string) => {
    try {
      setLoading(true);
      setRegistrationSearchQuery(''); // Reset search query when loading new registrations

      const { data, error } = await supabase
        .from('event_registrations')
        .select('*')
        .eq('event_id', eventId)
        .order('registered_at', { ascending: false });

      if (error) throw error;
      setRegistrations(data || []);
      setSelectedEventId(eventId);
      setViewingRegistrations(true);
    } catch (err: unknown) {
      console.error('Error fetching registrations:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Filter registrations based on search query
  const filteredRegistrations = registrations.filter(registration => {
    if (!registrationSearchQuery) return true;

    const searchLower = registrationSearchQuery.toLowerCase();
    return (
      registration.full_name.toLowerCase().includes(searchLower) ||
      registration.email.toLowerCase().includes(searchLower) ||
      registration.status.toLowerCase().includes(searchLower)
    );
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: value ? parseInt(value) : undefined }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Validate form data before submission
  const validateForm = () => {
    // Title validation
    if (!formData.title?.trim()) {
      setError('Title is required');
      return false;
    }

    // Type validation
    if (!formData.type?.trim()) {
      setError('Event type is required');
      return false;
    }

    // Date validation
    if (!formData.date) {
      setError('Date is required');
      return false;
    }

    // Date should not be in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day for date comparison
    const selectedDate = new Date(formData.date);
    selectedDate.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      setError('Date cannot be in the past');
      return false;
    }

    // Time validation
    if (!formData.time) {
      setError('Start time is required');
      return false;
    }

    // End time validation (if provided)
    if (formData.end_time && formData.time && formData.end_time <= formData.time) {
      setError('End time must be after start time');
      return false;
    }

    // Location validation
    if (!formData.location?.trim()) {
      setError('Location is required');
      return false;
    }

    // Image URL validation removed - no longer required

    // Description validation
    if (!formData.description?.trim()) {
      setError('Description is required');
      return false;
    }

    // Capacity validation (if provided)
    if (formData.capacity !== undefined && formData.capacity <= 0) {
      setError('Capacity must be a positive number');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // If the event is being marked as featured, unmark any other featured events
      if (formData.is_featured) {
        // First, check if there's already a featured event (different from the current one)
        const { data: featuredEvents, error: fetchError } = await supabase
          .from('events')
          .select('id')
          .eq('is_featured', true)
          .neq('id', selectedEventId || '00000000-0000-0000-0000-000000000000');

        if (fetchError) throw fetchError;

        // If there are other featured events, unmark them
        if (featuredEvents && featuredEvents.length > 0) {
          const { error: updateError } = await supabase
            .from('events')
            .update({ is_featured: false })
            .in('id', featuredEvents.map(e => e.id));

          if (updateError) throw updateError;
        }
      }

      if (isEditing && selectedEventId) {
        // Update existing event
        const { error } = await supabase
          .from('events')
          .update(formData)
          .eq('id', selectedEventId);

        if (error) throw error;
        setSuccess('Event updated successfully!');
        setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
      } else {
        // Create new event
        const { error } = await supabase
          .from('events')
          .insert([formData]);

        if (error) throw error;
        setSuccess('Event created successfully!');
        setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
      }

      setShowForm(false);
      resetForm();
      fetchEvents();
    } catch (err: unknown) {
      console.error('Error saving event:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setTimeout(() => setError(null), 5000); // Auto-dismiss after 5 seconds
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (event: Event) => {
    setFormData({
      title: event.title,
      description: event.description,
      date: event.date,
      time: event.time,
      end_time: event.end_time || '',
      location: event.location,
      address: event.address || '',
      type: event.type,
      capacity: event.capacity,
      image_url: event.image_url,
      is_featured: event.is_featured
    });
    setImagePreview(event.image_url);
    // Set the correct image input type based on the image URL
    if (event.image_url && event.image_url.includes('event-images')) {
      setImageInputType('upload');
    } else if (event.image_url) {
      setImageInputType('url');
    } else {
      setImageInputType('upload'); // Default to upload if no image
    }
    setSelectedEventId(event.id);
    setIsEditing(true);
    setShowForm(true);
  };

  // Extract file path from public URL
  const getFilePathFromUrl = (url: string): string | null => {
    if (!url || !url.includes('event-images')) return null;

    try {
      // Extract the path after the bucket name
      const pathMatch = url.match(/event-images\/(.*)/i);
      if (pathMatch && pathMatch[1]) {
        return pathMatch[1];
      }
      return null;
    } catch (err) {
      console.error('Error extracting file path from URL:', err);
      return null;
    }
  };

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setImageUploading(true);
      setError(null);

      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Please upload a valid image file (JPEG, PNG, GIF, or WEBP)');
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('File size should be less than 5MB');
      }

      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const fileName = `${Math.random().toString(36).substring(2, 9)}-${Date.now()}.${fileExt}`;
      const filePath = `events/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('event-images')
        .upload(filePath, file, { upsert: true });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('event-images')
        .getPublicUrl(filePath);

      // Set the image URL in the form data
      setFormData(prev => ({ ...prev, image_url: publicUrl }));
      setImagePreview(publicUrl);
      setSuccess('Image uploaded successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message);
      setTimeout(() => setError(null), 5000);
    } finally {
      setImageUploading(false);
    }
  };

  // Handle image removal from storage
  const handleRemoveImage = async (imageUrl: string) => {
    try {
      // Only attempt to remove from storage if it's an uploaded image
      if (imageUrl && imageUrl.includes('event-images')) {
        const filePath = getFilePathFromUrl(imageUrl);

        if (filePath) {
          setImageUploading(true);
          const { error } = await supabase.storage
            .from('event-images')
            .remove([filePath]);

          if (error) {
            console.error('Error removing image from storage:', error);
            setError('Failed to remove image from storage');
            setTimeout(() => setError(null), 5000);
            return;
          }

          setSuccess('Image removed successfully');
          setTimeout(() => setSuccess(null), 3000);
        }
      }

      // Clear the image URL from the form data
      setFormData(prev => ({ ...prev, image_url: '' }));
      setImagePreview(null);
    } catch (err: any) {
      console.error('Error in handleRemoveImage:', err);
      setError(err.message || 'Failed to remove image');
      setTimeout(() => setError(null), 5000);
    } finally {
      setImageUploading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this event?')) return;

    try {
      setLoading(true);

      // Get the event to check if it has an uploaded image
      const { data: event, error: fetchError } = await supabase
        .from('events')
        .select('image_url')
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;

      // If the event has an uploaded image, delete it from storage
      if (event?.image_url && event.image_url.includes('event-images')) {
        const filePath = getFilePathFromUrl(event.image_url);
        if (filePath) {
          const { error: storageError } = await supabase.storage
            .from('event-images')
            .remove([filePath]);

          if (storageError) {
            console.error('Error removing event image from storage:', storageError);
          }
        }
      }

      // Delete the event from the database
      const { error } = await supabase
        .from('events')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setSuccess('Event deleted successfully!');
      setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
      fetchEvents();
    } catch (err: unknown) {
      console.error('Error deleting event:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setTimeout(() => setError(null), 5000); // Auto-dismiss after 5 seconds
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      date: '',
      time: '',
      end_time: '',
      location: '',
      address: '',
      type: '',
      capacity: undefined,
      image_url: '',
      is_featured: false
    });
    setIsEditing(false);
    setSelectedEventId(null);
    setImagePreview(null);
    setImageInputType('upload');
  };

  const handleUpdateRegistrationStatus = async (registrationId: string, status: 'registered' | 'cancelled') => {
    try {
      setLoading(true);
      const { error } = await supabase
        .from('event_registrations')
        .update({ status })
        .eq('id', registrationId);

      if (error) throw error;

      if (selectedEventId) {
        fetchRegistrations(selectedEventId);
      }

      setSuccess(`Registration status updated to ${status}`);
      setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
    } catch (err: unknown) {
      console.error('Error updating registration status:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setTimeout(() => setError(null), 5000); // Auto-dismiss after 5 seconds
    } finally {
      setLoading(false);
    }
  };

  const exportRegistrations = () => {
    if (!filteredRegistrations.length) return;

    const csvContent = [
      // Header row
      ['ID', 'Full Name', 'Email', 'Phone', 'Company', 'Registered At', 'Status'].join(','),
      // Data rows
      ...filteredRegistrations.map(reg => [
        reg.id,
        `"${reg.full_name}"`,
        reg.email,
        reg.phone || '',
        `"${reg.company || ''}"`,
        `"${new Date(reg.registered_at).toLocaleDateString()} ${new Date(reg.registered_at).toLocaleTimeString()}"`,
        reg.status
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `event-registrations-${selectedEventId}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getEventById = (id: string | null) => {
    if (!id) return null;
    return events.find(event => event.id === id) || null;
  };

  const selectedEvent = getEventById(selectedEventId);

  return (
    <div className="p-6">
      {!viewingRegistrations ? (
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Event Management</h1>
          <div className="flex space-x-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                ref={searchInputRef}
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <button
              onClick={() => {
                resetForm();
                setShowForm(true);
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Event
            </button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <h1 className="text-2xl font-bold">
            Event Registrations: {selectedEvent?.title}
          </h1>

          <div className="flex flex-col sm:flex-row w-full md:w-auto gap-3">
            <div className="relative flex-grow sm:max-w-md">
              <input
                type="text"
                placeholder="Search registrations..."
                value={registrationSearchQuery}
                onChange={(e) => setRegistrationSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                ref={registrationSearchInputRef}
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setViewingRegistrations(false)}
                className="flex-1 sm:flex-none px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors whitespace-nowrap"
              >
                Back to Events
              </button>

              <button
                onClick={exportRegistrations}
                disabled={!filteredRegistrations.length}
                className="flex-1 sm:flex-none px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
          <button onClick={() => setError(null)} className="ml-auto">
            <X className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg flex items-start">
          <Check className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
          <button onClick={() => setSuccess(null)} className="ml-auto">
            <X className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Event Form Modal */}
      {showForm && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fadeIn"
          onClick={(e) => {
            // Close modal when clicking outside
            if (e.target === e.currentTarget) {
              setShowForm(false);
              setIsEditing(false);
              setSelectedEventId(null);
              resetForm();
            }
          }}
        >
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">{isEditing ? 'Edit Event' : 'Add New Event'}</h2>
                <button
                  onClick={() => {
                    setShowForm(false);
                    setIsEditing(false);
                    setSelectedEventId(null);
                    resetForm();
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                    <input
                      type="text"
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      placeholder="Workshop, Conference, Training, etc."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <input
                      type="date"
                      name="date"
                      min={new Date().toISOString().split('T')[0]}
                      value={formData.date}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border ${formData.date && new Date(formData.date) < new Date(new Date().toISOString().split('T')[0]) ? 'border-red-300 bg-red-50' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">Select a date in the future for the event</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                    <input
                      type="time"
                      name="time"
                      value={formData.time}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">Select the start time of the event (24-hour format)</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Time (Optional)</label>
                    <input
                      type="time"
                      name="end_time"
                      value={formData.end_time}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border ${formData.end_time && formData.time && formData.end_time <= formData.time ? 'border-red-300 bg-red-50' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                    />
                    <p className="text-xs text-gray-500 mt-1">Must be after the start time</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                    <input
                      type="text"
                      name="location"
                      value={formData.location}
                      onChange={handleInputChange}
                      placeholder="City, State"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Address (Optional)</label>
                    <input
                      type="text"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      placeholder="Full address"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Capacity (Optional)</label>
                    <input
                      type="number"
                      name="capacity"
                      value={formData.capacity || ''}
                      onChange={handleInputChange}
                      placeholder="Maximum attendees"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Event Image</label>
                    <div className="mb-4">
                      <div className="flex items-center space-x-6 mb-4">
                        <div className="flex items-center">
                          <input
                            type="radio"
                            id="image_upload_option"
                            name="image_source"
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            checked={imageInputType === 'upload'}
                            onChange={() => {
                              setImageInputType('upload');
                              if (formData.image_url && !formData.image_url.includes('event-images')) {
                                setFormData({ ...formData, image_url: '' });
                              }
                            }}
                          />
                          <label
                            htmlFor="image_upload_option"
                            className="ml-2 text-sm text-gray-700 cursor-pointer"
                            onClick={() => {
                              setImageInputType('upload');
                              if (formData.image_url && !formData.image_url.includes('event-images')) {
                                setFormData({ ...formData, image_url: '' });
                              }
                            }}
                          >
                            Upload Image
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="radio"
                            id="image_url_option"
                            name="image_source"
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            checked={imageInputType === 'url'}
                            onChange={() => {
                              setImageInputType('url');
                              if (formData.image_url && formData.image_url.includes('event-images')) {
                                setFormData({ ...formData, image_url: '' });
                              }
                            }}
                          />
                          <label
                            htmlFor="image_url_option"
                            className="ml-2 text-sm text-gray-700 cursor-pointer"
                            onClick={() => {
                              setImageInputType('url');
                              if (formData.image_url && formData.image_url.includes('event-images')) {
                                setFormData({ ...formData, image_url: '' });
                              }
                            }}
                          >
                            Image URL
                          </label>
                        </div>
                      </div>

                      {/* Show the appropriate input based on selection */}
                      {imageInputType === 'upload' ? (
                        // Upload button
                        <div className="w-full">
                          <label className="flex-1">
                            <div className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 cursor-pointer transition-colors">
                              <Upload className="h-5 w-5 mr-2 text-gray-500" />
                              {imageUploading ? 'Uploading...' : 'Choose File'}
                              <input
                                type="file"
                                accept="image/*"
                                onChange={handleImageUpload}
                                className="hidden"
                                disabled={imageUploading}
                              />
                            </div>
                          </label>
                        </div>
                      ) : (
                        // URL input
                        <div className="w-full">
                          <input
                            type="text"
                            name="image_url"
                            placeholder="https://example.com/image.jpg"
                            value={formData.image_url || ''}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                          />
                        </div>
                      )}
                    </div>

                    {/* Image preview */}
                    {(formData.image_url || imagePreview) && (
                      <div className="mt-2 flex justify-center bg-[#6c6696] bg-opacity-20 p-4 rounded-lg">
                        <div className="relative max-w-xs">
                          <div className="relative">
                            <img
                              src={formData.image_url || imagePreview || ''}
                              alt="Event image preview"
                              className="max-h-48 object-contain rounded border border-gray-200 shadow-sm bg-white"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'https://via.placeholder.com/300x200?text=Invalid+Image+URL';
                              }}
                            />
                            {/* Remove image button */}
                            <button
                              type="button"
                              onClick={() => handleRemoveImage(formData.image_url || imagePreview || '')}
                              className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100 transition-colors"
                              title="Remove image"
                              disabled={imageUploading}
                            >
                              <XCircle className="h-5 w-5 text-red-500" />
                            </button>
                          </div>
                          <div className="text-center text-xs text-gray-500 mt-1">
                            {formData.image_url && formData.image_url.includes('event-images')
                              ? 'Image uploaded'
                              : formData.image_url
                                ? 'Image URL'
                                : ''}
                          </div>
                        </div>
                      </div>
                    )}
                    <p className="text-xs text-gray-500 mt-2">Upload an image or provide a URL for the event image</p>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    ></textarea>
                  </div>

                  <div className="md:col-span-2 space-y-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="is_featured"
                        name="is_featured"
                        checked={formData.is_featured}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-700">
                        Featured Event
                      </label>
                    </div>
                    {formData.is_featured && (
                      <div className="flex items-start p-3 bg-amber-50 border border-amber-200 rounded-md">
                        <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5 mr-2 flex-shrink-0" />
                        <p className="text-xs text-amber-700">
                          Only one event can be featured at a time. Marking this event as featured will unmark any currently featured event.
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-2 mt-6">
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false);
                      setIsEditing(false);
                      setSelectedEventId(null);
                      resetForm();
                    }}
                    className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    disabled={loading}
                  >
                    {loading ? 'Saving...' : isEditing ? 'Update Event' : 'Create Event'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && !showForm && (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {/* Event Registrations Table */}
      {viewingRegistrations && !loading && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {filteredRegistrations.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              {registrationSearchQuery ? 'No registrations match your search.' : 'No registrations found for this event.'}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered At</th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRegistrations.map((registration) => (
                    <tr key={registration.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{registration.full_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{registration.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {new Date(registration.registered_at).toLocaleString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <select
                          value={registration.status}
                          onChange={(e) => handleUpdateRegistrationStatus(registration.id, e.target.value as 'registered' | 'cancelled')}
                          className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors border ${
                            registration.status === 'cancelled' ? 'bg-red-50 text-red-600 border-red-200' :
                            'bg-blue-50 text-blue-600 border-blue-200'
                          }`}
                        >
                          <option value="registered">Registered</option>
                          <option value="cancelled">Cancelled</option>
                        </select>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Events Table */}
      {!viewingRegistrations && !loading && !showForm && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {events.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              {searchQuery ? 'No events match your search.' : 'No events found. Create your first event by clicking the "Add Event" button.'}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Featured</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="px-4 py-8 text-center">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                          <span className="text-gray-500">{searchQuery ? 'Searching...' : 'Loading events...'}</span>
                        </div>
                      </td>
                    </tr>
                  ) : events.map((event) => (
                    <tr key={event.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {event.image_url ? (
                              <img
                                className="h-10 w-10 rounded-full object-cover"
                                src={event.image_url}
                                alt={event.title}
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.onerror = null;
                                  target.style.display = 'none';
                                  target.parentElement!.innerHTML = `<div class="h-10 w-10 flex items-center justify-center bg-blue-100 rounded-full">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                      <rect x="2" y="6" width="20" height="12" rx="2" ry="2"></rect>
                                      <path d="M12 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                      <path d="M17 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                      <path d="M7 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                    </svg>
                                  </div>`;
                                }}
                              />
                            ) : (
                              <div className="h-10 w-10 flex items-center justify-center bg-blue-100 rounded-full">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                                  <rect x="2" y="6" width="20" height="12" rx="2" ry="2"></rect>
                                  <path d="M12 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                  <path d="M17 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                  <path d="M7 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                </svg>
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{event.title}</div>
                            <div className="text-sm text-gray-500 truncate max-w-xs">{event.description}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{event.date}</div>
                        <div className="text-sm text-gray-500">{event.time}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{event.location}</div>
                        {event.address && <div className="text-sm text-gray-500">{event.address}</div>}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          {event.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {event.is_featured ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <Star className="h-3 w-3 mr-1" />
                            Featured
                          </span>
                        ) : (
                          <span className="text-gray-400">No</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => fetchRegistrations(event.id)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="View Registrations"
                          >
                            <Users className="h-5 w-5" />
                          </button>
                          <button
                            onClick={() => handleEdit(event)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Edit Event"
                          >
                            <Edit className="h-5 w-5" />
                          </button>
                          <button
                            onClick={() => handleDelete(event.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Event"
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination Controls */}
          <div className="border-t border-gray-200 bg-white px-4 py-3 mt-4">
            {/* Results info and page size selector */}
            <div className="flex flex-wrap items-center justify-between mb-4">
              <div className="text-sm text-gray-700 mb-2 sm:mb-0 flex items-center">
                Showing <span className="font-medium mx-1">{events.length > 0 && !loading ? (currentPage - 1) * pageSize + 1 : 0}</span> to{' '}
                <span className="font-medium mx-1">{!loading ? Math.min(currentPage * pageSize, totalCount) : 0}</span> of{' '}
                <span className="font-medium mx-1">{totalCount}</span> results
                {searchQuery && <span className="ml-2 text-blue-600">for "{searchQuery}"</span>}
              </div>

              <div className="flex items-center">
                <span className="mr-2 text-sm text-gray-700">Rows per page:</span>
                <div className="relative">
                  <select
                    value={pageSize}
                    onChange={(e) => {
                      const newSize = Number(e.target.value);
                      setPageSize(newSize);
                      // Adjust current page to maintain position in data as much as possible
                      const firstItemIndex = (currentPage - 1) * pageSize;
                      const newPage = Math.floor(firstItemIndex / newSize) + 1;
                      setCurrentPage(Math.min(newPage, Math.ceil(totalCount / newSize) || 1));
                    }}
                    className="py-1 text-sm bg-white border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 pl-3 pr-8 appearance-none"
                  >
                    <option value="5">5</option>
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2 text-gray-700">
                    <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Simplified Pagination controls */}
            <div className="flex items-center justify-center">
              <nav className="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {/* Previous page button */}
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage <= 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </button>

                {/* Page number display */}
                <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-700">
                  Page <span className="font-bold mx-1 text-blue-600">{currentPage}</span> of <span className="font-bold mx-1">{totalPages || 1}</span>
                </span>

                {/* Next page button */}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages || 1))}
                  disabled={currentPage >= (totalPages || 1)}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
