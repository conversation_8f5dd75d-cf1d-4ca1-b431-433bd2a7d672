-- Create storage bucket for invoice PDFs if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('invoice-pdfs', 'invoice-pdfs', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for invoice PDFs bucket
CREATE POLICY "Public can view invoice PDFs"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'invoice-pdfs');

CREATE POLICY "Admins can manage invoice PDFs"
ON storage.objects FOR ALL 
TO authenticated
USING (
  bucket_id = 'invoice-pdfs' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);
