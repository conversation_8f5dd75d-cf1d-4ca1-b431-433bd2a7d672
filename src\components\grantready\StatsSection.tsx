import React from 'react';
import './StatsSection.css';

const StatsSection: React.FC = () => {
    return (
        <div className="relative w-full max-w-[1940px] mx-auto">
            {/* Full Width Container until XL */}
            <div className="w-full p-2">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    {/* Grant Funding Management */}
                    <div className="rounded-md bg-white shadow-lg px-4 py-1 md:px-6 md:py-3 text-gray-900">
                        <div className="flex items-center justify-between">
                            <div>
                                <div className="mb-2 text-lg md:text-[40px]" style={{ fontFamily: 'Zenith Trial', fontWeight: 400, fontStyle: 'italic', letterSpacing: '0%', verticalAlign: 'middle' }}>
                                    <span className="text-gray-900">$500M</span>
                                    <span className="text-blue-600 text-lg md:text-[36px]">+</span>
                                </div>
                                <p className="text-gray-700 text-sm md:text-base font-medium">
                                    Grant Funding Management
                                </p>
                            </div>
                            <div className="flex-shrink-0 ml-4">
                                <div className="w-12 h-12 md:w-16 md:h-16 rounded-xl flex items-center justify-center">
                                    <svg width="68" height="67" viewBox="0 0 68 67" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-12 h-12 md:w-16 md:h-16">
                                        <path d="M37.2598 3.1875V8.25195C42.0454 8.78433 45.8537 10.3622 48.5342 12.8076C51.3986 15.421 52.8931 18.9522 52.9629 23.0137L52.9805 24.0312H41.5723L41.542 23.0625C41.4986 21.6902 41.0522 20.5224 40.2227 19.6631C39.5605 18.9773 38.5977 18.4307 37.2598 18.1592V28.2588L40.5059 29.0264H40.5068C44.8606 29.9792 48.2509 31.6085 50.5557 34.0322C52.8806 36.4773 54.0098 39.6355 54.0098 43.4453C54.0098 47.8987 52.4862 51.65 49.4346 54.3662C46.575 56.9114 42.4671 58.4637 37.2598 58.9365V63.8125H31.0723V58.9766C26.1477 58.5499 22.0749 57.0444 19.165 54.5928C16.1471 52.0499 14.4482 48.5437 14.3291 44.3779L14.3223 43.9727L14.3184 42.9688H25.6787L25.7422 43.9014C25.9139 46.4573 27.8676 48.6165 31.0723 49.333V38.459L28.3164 37.7393V37.7383C24.2056 36.7787 20.9482 35.0894 18.7129 32.6592C16.4586 30.2084 15.3154 27.082 15.3154 23.4131C15.3155 19.0916 16.8727 15.4499 19.7959 12.7969C22.5249 10.3203 26.3715 8.76972 31.0723 8.25098V3.1875H37.2598ZM37.2598 49.4326C39.0649 49.2293 40.3372 48.705 41.1748 47.9961C42.1353 47.1831 42.6377 46.0348 42.6377 44.5225C42.6377 43.1941 42.16 42.134 41.1768 41.3066C40.3265 40.5912 39.0485 40.0112 37.2598 39.6523V49.4326ZM31.0723 18.1367C28.4545 18.6321 26.8867 20.3907 26.8867 22.5771C26.8867 23.7587 27.3071 24.6956 28.1377 25.4531C28.8064 26.0629 29.7736 26.5821 31.0723 26.9766V18.1367Z" stroke="url(#paint0_linear_899_4345)" strokeWidth="2"/>
                                        <defs>
                                            <linearGradient id="paint0_linear_899_4345" x1="34.166" y1="4.1875" x2="34.166" y2="62.8125" gradientUnits="userSpaceOnUse">
                                                <stop stopColor="#6683FF"/>
                                                <stop offset="1" stopColor="#5CF0FE"/>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Public Health Jurisdictions */}
                    <div className="rounded-md bg-white shadow-lg px-4 py-0 md:px-6 md:py-3 text-gray-900">
                        <div className="flex items-center justify-between">
                            <div>
                                <div className="mb-2 text-lg md:text-[40px]" style={{ fontFamily: 'Zenith Trial', fontWeight: 400, fontStyle: 'italic', letterSpacing: '0%', verticalAlign: 'middle' }}>
                                    <span className="text-gray-900">100</span>
                                    <span className="text-blue-600 text-lg md:text-[36px]">+</span>
                                </div>
                                <p className="text-gray-700 text-sm md:text-base font-medium">
                                    Public Health Jurisdictions
                                </p>
                            </div>
                            <div className="flex-shrink-0 ml-4">
                                <div className="w-12 h-12 md:w-16 md:h-16 rounded-xl flex items-center justify-center">
                                    <svg width="69" height="69" viewBox="0 0 69 69" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-12 h-12 md:w-16 md:h-16">
                                        <path opacity="0.4" d="M51.3267 20.7866C51.1567 20.7583 50.9584 20.7583 50.7884 20.7866C46.8784 20.645 43.7617 17.4433 43.7617 13.4766C43.7617 9.42496 47.0201 6.16663 51.0717 6.16663C55.1234 6.16663 58.3817 9.45329 58.3817 13.4766C58.3534 17.4433 55.2367 20.645 51.3267 20.7866Z" stroke="url(#paint0_linear_899_4352)" strokeWidth="4.25" strokeLinecap="round" strokeLinejoin="round"/>
                                        <path opacity="0.4" d="M48.4073 41.4138C52.2889 42.0655 56.5673 41.3855 59.5706 39.3738C63.5656 36.7105 63.5656 32.3472 59.5706 29.6838C56.5389 27.6721 52.2039 26.9921 48.3223 27.6721" stroke="url(#paint1_linear_899_4352)" strokeWidth="4.25" strokeLinecap="round" strokeLinejoin="round"/>
                                        <path opacity="0.4" d="M17.2366 20.7866C17.4066 20.7583 17.605 20.7583 17.775 20.7866C21.685 20.645 24.8016 17.4433 24.8016 13.4766C24.8016 9.42496 21.5433 6.16663 17.4916 6.16663C13.44 6.16663 10.1816 9.45329 10.1816 13.4766C10.21 17.4433 13.3266 20.645 17.2366 20.7866Z" stroke="url(#paint2_linear_899_4352)" strokeWidth="4.25" strokeLinecap="round" strokeLinejoin="round"/>
                                        <path opacity="0.4" d="M20.1576 41.4138C16.276 42.0655 11.9976 41.3855 8.9943 39.3738C4.9993 36.7105 4.9993 32.3472 8.9943 29.6838C12.026 27.6721 16.361 26.9921 20.2426 27.6721" stroke="url(#paint3_linear_899_4352)" strokeWidth="4.25" strokeLinecap="round" strokeLinejoin="round"/>
                                        <path d="M34.3326 41.952C34.1626 41.9237 33.9643 41.9237 33.7943 41.952C29.8843 41.8103 26.7676 38.6087 26.7676 34.642C26.7676 30.5903 30.026 27.332 34.0776 27.332C38.1293 27.332 41.3876 30.6187 41.3876 34.642C41.3593 38.6087 38.2426 41.8387 34.3326 41.952Z" stroke="url(#paint4_linear_899_4352)" strokeWidth="4.25" strokeLinecap="round" strokeLinejoin="round"/>
                                        <path d="M26.09 50.8776C22.095 53.541 22.095 57.904 26.09 60.5673C30.6233 63.599 38.0467 63.599 42.58 60.5673C46.575 57.904 46.575 53.541 42.58 50.8776C38.075 47.8743 30.6233 47.8743 26.09 50.8776Z" stroke="url(#paint5_linear_899_4352)" strokeWidth="4.25" strokeLinecap="round" strokeLinejoin="round"/>
                                        <defs>
                                            <linearGradient id="paint0_linear_899_4352" x1="51.0717" y1="6.16663" x2="51.0717" y2="20.7866" gradientUnits="userSpaceOnUse">
                                                <stop stopColor="#6583FF"/>
                                                <stop offset="1" stopColor="#6BC2D5"/>
                                            </linearGradient>
                                            <linearGradient id="paint1_linear_899_4352" x1="55.4446" y1="27.4166" x2="55.4446" y2="41.653" gradientUnits="userSpaceOnUse">
                                                <stop stopColor="#6583FF"/>
                                                <stop offset="1" stopColor="#6BC2D5"/>
                                            </linearGradient>
                                            <linearGradient id="paint2_linear_899_4352" x1="17.4916" y1="6.16663" x2="17.4916" y2="20.7866" gradientUnits="userSpaceOnUse">
                                                <stop stopColor="#6583FF"/>
                                                <stop offset="1" stopColor="#6BC2D5"/>
                                            </linearGradient>
                                            <linearGradient id="paint3_linear_899_4352" x1="13.1203" y1="27.4166" x2="13.1203" y2="41.653" gradientUnits="userSpaceOnUse">
                                                <stop stopColor="#6583FF"/>
                                                <stop offset="1" stopColor="#6BC2D5"/>
                                            </linearGradient>
                                            <linearGradient id="paint4_linear_899_4352" x1="34.0776" y1="27.332" x2="34.0776" y2="41.952" gradientUnits="userSpaceOnUse">
                                                <stop stopColor="#6583FF"/>
                                                <stop offset="1" stopColor="#6BC2D5"/>
                                            </linearGradient>
                                            <linearGradient id="paint5_linear_899_4352" x1="34.335" y1="48.6251" x2="34.335" y2="62.8411" gradientUnits="userSpaceOnUse">
                                                <stop stopColor="#6583FF"/>
                                                <stop offset="1" stopColor="#6BC2D5"/>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Active Grants */}
                    <div className="rounded-md bg-white shadow-lg px-4 py-0 md:px-6 md:py-3 text-gray-900">
                        <div className="flex items-center justify-between">
                            <div>
                                <div className="mb-2 text-lg md:text-[40px]" style={{ fontFamily: 'Zenith Trial', fontWeight: 400, fontStyle: 'italic', letterSpacing: '0%', verticalAlign: 'middle' }}>
                                    <span className="text-gray-900">15</span>
                                    <span className="text-blue-600 text-lg text-[36px]">+</span>
                                </div>
                                <p className="text-gray-700 text-sm md:text-base font-medium">
                                    Active Grants
                                </p>
                            </div>
                            <div className="flex-shrink-0 ml-4">
                                <div className="w-12 h-12 md:w-16 md:h-16 rounded-xl flex items-center justify-center">
                                    <svg width="60" height="61" viewBox="0 0 60 61" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_899_4364)">
<path d="M8.75 30.5C7.09301 30.498 5.50445 29.8389 4.33278 28.6672C3.1611 27.4956 2.50198 25.907 2.5 24.25C2.50846 23.6859 2.59425 23.1257 2.755 22.585C3.1653 22.8986 3.6638 23.0753 4.18 23.09C4.4659 23.0827 4.74839 23.026 5.015 22.9225C5.015 22.95 5 22.975 5 23V25.5C5 26.163 5.26339 26.7989 5.73223 27.2678C6.20107 27.7366 6.83696 28 7.5 28H10C10.663 28 11.2989 27.7366 11.7678 27.2678C12.2366 26.7989 12.5 26.163 12.5 25.5C13.084 25.4952 13.6473 25.2833 14.0896 24.9019C14.5319 24.5205 14.8244 23.9944 14.915 23.4175C14.9622 23.6927 14.9906 23.9709 15 24.25C15 24.285 14.99 24.32 14.99 24.355L25.555 26.705C25.9086 25.9225 26.4197 25.2211 27.0562 24.6448C27.6928 24.0684 28.4413 23.6294 29.255 23.355L28.265 15.45C26.9791 15.3514 25.7562 14.8532 24.7675 14.025C24.9177 13.704 24.997 13.3544 25 13V10.5C25 11.163 25.2634 11.7989 25.7322 12.2678C26.2011 12.7366 26.837 13 27.5 13H30C30.663 13 31.2989 12.7366 31.7678 12.2678C32.2366 11.7989 32.5 11.163 32.5 10.5V8C32.5 7.33696 32.2366 6.70107 31.7678 6.23223C31.2989 5.76339 30.663 5.5 30 5.5H27.5C26.837 5.5 26.2011 5.76339 25.7322 6.23223C25.2634 6.70107 25 7.33696 25 8V10.5C24.9981 9.86129 24.7503 9.24785 24.308 8.787C23.8658 8.32615 23.2631 8.05327 22.625 8.025C22.786 7.215 23.106 6.44497 23.5665 5.75948C24.027 5.07399 24.619 4.48664 25.308 4.03145C25.9971 3.57625 26.7696 3.26224 27.5808 3.1076C28.392 2.95295 29.2259 2.96074 30.0341 3.13051C30.8423 3.30029 31.6088 3.62867 32.2892 4.09666C32.9696 4.56465 33.5505 5.16294 33.9981 5.85692C34.4458 6.5509 34.7514 7.32677 34.8972 8.13963C35.0429 8.9525 35.0261 9.7862 34.8475 10.5925L46.175 15.625C46.7982 14.7583 47.6336 14.0661 48.6011 13.6147C49.5685 13.1633 50.6356 12.9679 51.7003 13.0472C52.7649 13.1265 53.7913 13.4778 54.6812 14.0675C55.5711 14.6572 56.2947 15.4655 56.7827 16.415C57.2707 17.3645 57.5067 18.4234 57.4681 19.4903C57.4295 20.5572 57.1177 21.5963 56.5624 22.508C56.0071 23.4198 55.227 24.1738 54.2969 24.6977C53.3667 25.2215 52.3176 25.4978 51.25 25.5C51.1975 25.5 51.1475 25.485 51.095 25.485L46.3425 46.0825C47.3544 46.5428 48.225 47.2651 48.8642 48.1747C49.5034 49.0843 49.8879 50.1481 49.9781 51.2561C50.0683 52.3642 49.8609 53.4762 49.3772 54.4772C48.8936 55.4781 48.1513 56.3317 47.2272 56.9497C46.303 57.5676 45.2306 57.9274 44.1207 57.9919C43.0109 58.0564 41.904 57.8232 40.9145 57.3165C39.925 56.8098 39.0888 56.048 38.4925 55.1097C37.8961 54.1715 37.5613 53.091 37.5225 51.98L19.5675 48.9875C19.2005 49.9463 18.6027 50.7997 17.827 51.4721C17.0513 52.1445 16.1217 52.6151 15.1205 52.8423C14.1194 53.0695 13.0777 53.0461 12.0877 52.7743C11.0978 52.5025 10.1902 51.9907 9.44536 51.2842C8.70056 50.5777 8.14157 49.6983 7.81795 48.7241C7.49433 47.7499 7.41606 46.7108 7.5901 45.6991C7.76414 44.6874 8.1851 43.7342 8.81567 42.9241C9.44624 42.114 10.2669 41.472 11.205 41.055L8.855 30.49C8.82 30.49 8.7875 30.5 8.75 30.5ZM17.5 48V45.5C17.5 44.837 17.2366 44.2011 16.7678 43.7322C16.2989 43.2634 15.663 43 15 43H12.5C11.837 43 11.2011 43.2634 10.7322 43.7322C10.2634 44.2011 10 44.837 10 45.5V48C10 48.663 10.2634 49.2989 10.7322 49.7678C11.2011 50.2366 11.837 50.5 12.5 50.5H15C15.663 50.5 16.2989 50.2366 16.7678 49.7678C17.2366 49.2989 17.5 48.663 17.5 48ZM18.96 43.3075C19.5889 44.264 19.941 45.3758 19.9775 46.52L37.9325 49.5125C38.3065 48.5518 38.913 47.6989 39.6975 47.03L33.115 35.185C32.2345 35.4826 31.297 35.572 30.376 35.4462C29.4551 35.3204 28.5759 34.9829 27.8075 34.46L18.96 43.3075ZM45 48H42.5C41.837 48 41.2011 48.2634 40.7322 48.7322C40.2634 49.2011 40 49.837 40 50.5V53C40 53.663 40.2634 54.2989 40.7322 54.7678C41.2011 55.2366 41.837 55.5 42.5 55.5H45C45.663 55.5 46.2989 55.2366 46.7678 54.7678C47.2366 54.2989 47.5 53.663 47.5 53V50.5C47.5 49.837 47.2366 49.2011 46.7678 48.7322C46.2989 48.2634 45.663 48 45 48ZM48.6575 24.92C47.7569 24.5028 46.9685 23.8772 46.3575 23.095L37.2625 27.64C37.5896 28.7712 37.5782 29.9735 37.2299 31.0984C36.8816 32.2232 36.2116 33.2215 35.3025 33.97L41.885 45.815C42.4868 45.6147 43.1158 45.5084 43.75 45.5C43.8025 45.5 43.85 45.515 43.905 45.515L48.6575 24.92ZM47.5 18V20.5C47.5 21.163 47.7634 21.7989 48.2322 22.2678C48.7011 22.7366 49.337 23 50 23H52.5C53.163 23 53.7989 22.7366 54.2678 22.2678C54.7366 21.7989 55 21.163 55 20.5V18C55 17.337 54.7366 16.7011 54.2678 16.2322C53.7989 15.7634 53.163 15.5 52.5 15.5H50C49.337 15.5 48.7011 15.7634 48.2322 16.2322C47.7634 16.7011 47.5 17.337 47.5 18ZM30.745 15.1425L31.735 23.05C32.5929 23.116 33.4278 23.3597 34.1865 23.7655C34.9453 24.1714 35.6114 24.7305 36.1425 25.4075L45.2375 20.8575C44.9532 19.8983 44.9239 18.8815 45.1525 17.9075L33.825 12.875C33.0622 13.9342 31.9869 14.728 30.75 15.145L30.745 15.1425ZM27.5 28V30.5C27.5 31.163 27.7634 31.7989 28.2322 32.2678C28.7011 32.7366 29.337 33 30 33H32.5C33.163 33 33.7989 32.7366 34.2678 32.2678C34.7366 31.7989 35 31.163 35 30.5V28C35 27.337 34.7366 26.7011 34.2678 26.2322C33.7989 25.7634 33.163 25.5 32.5 25.5H30C29.337 25.5 28.7011 25.7634 28.2322 26.2322C27.7634 26.7011 27.5 27.337 27.5 28ZM11.295 29.945L13.645 40.51C13.68 40.51 13.7125 40.5 13.75 40.5C14.975 40.4994 16.1728 40.8613 17.1925 41.54L26.0425 32.69C25.3635 31.6712 25.0008 30.4744 25 29.25C25 29.2125 25.01 29.18 25.01 29.145L14.445 26.795C13.8158 28.195 12.6951 29.3158 11.295 29.945ZM4.18 20.59L2.41 18.82L6.04 15.195C5.59639 14.5291 5.28569 13.7838 5.125 13H0V10.5H5.125C5.286 9.71707 5.59669 8.97261 6.04 8.3075L2.41 4.68L4.18 2.91L7.805 6.54C8.47086 6.09639 9.2162 5.78569 10 5.625V0.5H12.5V5.625C13.2829 5.786 14.0274 6.09669 14.6925 6.54L18.32 2.91L20.09 4.68L16.46 8.305C16.9036 8.97086 17.2143 9.7162 17.375 10.5H22.5V13H17.375C17.214 13.7829 16.9033 14.5274 16.46 15.1925L20.09 18.82L18.32 20.59L14.695 16.96C14.0292 17.4037 13.2838 17.7144 12.5 17.875V23H10V17.875C9.21705 17.7141 8.47258 17.4034 7.8075 16.96L4.18 20.59ZM7.5 11.75C7.5 12.4917 7.71993 13.2167 8.13199 13.8334C8.54404 14.4501 9.12971 14.9307 9.81494 15.2145C10.5002 15.4984 11.2542 15.5726 11.9816 15.4279C12.709 15.2833 13.3772 14.9261 13.9017 14.4017C14.4261 13.8772 14.7833 13.209 14.9279 12.4816C15.0726 11.7542 14.9984 11.0002 14.7145 10.3149C14.4307 9.62971 13.9501 9.04404 13.3334 8.63199C12.7167 8.21993 11.9917 8 11.25 8C10.2558 8.00132 9.30279 8.39684 8.59981 9.09981C7.89684 9.80279 7.50132 10.7558 7.5 11.75Z" fill="url(#paint0_linear_899_4364)"/>
</g>
<defs>
<linearGradient id="paint0_linear_899_4364" x1="28.7361" y1="0.5" x2="28.7361" y2="58.0024" gradientUnits="userSpaceOnUse">
<stop stop-color="#6583FF"/>
<stop offset="1" stop-color="#6CC2D5"/>
</linearGradient>
<clipPath id="clip0_899_4364">
<rect width="60" height="60" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StatsSection;
