Stack trace:
Frame         Function      Args
0007FFFF3BC0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF3BC0, 0007FFFF2AC0) msys-2.0.dll+0x2118E
0007FFFF3BC0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF3BC0  0002100469F2 (00021028DF99, 0007FFFF3A78, 0007FFFF3BC0, 000000000000) msys-2.0.dll+0x69F2
0007FFFF3BC0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF3BC0  00021006A545 (0007FFFF3BD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF3BD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8FB590000 ntdll.dll
7FF8F98B0000 KERNEL32.DLL
7FF8F9040000 KERNELBASE.dll
7FF8F9A20000 USER32.dll
7FF8F8D30000 win32u.dll
7FF8FA610000 GDI32.dll
7FF8F94A0000 gdi32full.dll
7FF8F8D60000 msvcp_win.dll
7FF8F8C30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8F9690000 advapi32.dll
7FF8F9980000 msvcrt.dll
7FF8FA3C0000 sechost.dll
7FF8F9D50000 RPCRT4.dll
7FF8F9340000 bcrypt.dll
7FF8F8420000 CRYPTBASE.DLL
7FF8F8E00000 bcryptPrimitives.dll
7FF8F97E0000 IMM32.DLL
