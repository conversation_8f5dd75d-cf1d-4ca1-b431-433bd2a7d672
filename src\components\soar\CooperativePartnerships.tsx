import React from 'react';
import soarBrainImage from '../../assets/home/<USER>';
import soarDashboardImage from '../../assets/home/<USER>';
import pacificRimLogo from '../../assets/home/<USER>';
import { Link } from 'react-router-dom';

const CooperativePartnerships: React.FC = () => {
  const CircleIcon = () => (
    <div className="relative w-32 h-32">
      {/* Gradient Border */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-orange-400 via-pink-400 to-purple-500 p-1">
        {/* Image Container */}
        <div className="w-full h-full rounded-full overflow-hidden bg-white">
          <img
            src={soarBrainImage}
            alt="SOAR Brain"
            className="w-full h-full object-cover"
          />
        </div>
      </div>
      {/* Shadow */}
      <div className="absolute inset-0 rounded-full shadow-lg pointer-events-none"></div>
    </div>
  );

  const ArrowIcon = () => (
    <svg width="89" height="21" viewBox="0 0 89 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M57.6232 14.8022C63.6588 12.8611 69.8791 11.2447 76.2865 10.4938C79.6974 10.0938 83.1351 9.95803 86.5688 10.0877L87.0367 10.1L86.3089 13.3498C79.6943 10.6473 72.9333 8.36398 66.0054 6.91963C59.0857 5.42422 52.0021 4.95065 44.9366 5.10552L45.347 0.925056C46.9177 1.2542 48.4959 1.60062 50.0818 2.00343C50.8734 2.20368 51.6631 2.4202 52.4247 2.63027C53.1872 2.84014 53.9282 3.03968 54.6792 3.22625C55.8006 3.50658 56.941 3.7629 58.0886 4.01174L58.9789 4.20676C59.351 4.29156 59.6694 4.3839 59.9882 4.4796C60.6206 4.66966 61.2098 4.87939 61.8012 5.096C62.9759 5.53472 64.1205 6.00706 65.2607 6.54459C66.4731 7.09158 67.6341 7.74618 68.7295 8.50046C69.1036 8.7655 69.3833 9.14248 69.528 9.57643C69.6728 10.0104 69.6749 10.4786 69.534 10.9129C69.3932 11.3471 69.1168 11.7247 68.7452 11.9905C68.3735 12.2563 67.9261 12.3965 67.468 12.3907L67.4379 12.39C63.2051 12.3094 58.9704 12.3829 54.7392 12.5527C50.5094 12.7516 46.2864 13.0611 42.0701 13.4812C39.9663 13.7219 37.8589 13.9375 35.7617 14.2337C33.6608 14.5039 31.574 14.8669 29.4825 15.1975C25.3096 15.9213 21.1593 16.7571 17.0314 17.7047C15.4472 18.0964 13.8398 18.4271 12.2448 18.7917C11.4462 18.9708 10.6494 19.1497 9.85428 19.3281C9.05603 19.4941 8.26546 19.6787 7.47921 19.8634C4.45994 20.5432 2.414 20.7012 1.03148 20.4707C0.0268414 20.307 -0.114375 19.8669 0.522715 19.2731C1.16147 18.6872 2.57026 17.9153 4.67601 17.1817C5.34288 16.9525 6.02836 16.7271 6.72543 16.5065C7.42201 16.284 8.13071 16.0682 8.84963 15.8774C9.56786 15.6845 10.2906 15.497 11.0111 15.3153L12.0892 15.0469C12.4493 14.967 12.8075 14.8874 13.1636 14.8082C19.7164 13.3412 26.3012 12.1016 32.9183 11.0894C39.5342 10.0704 46.1824 9.26775 52.863 8.68136C54.7057 8.50082 56.5913 8.38303 58.4649 8.25923C59.4021 8.20411 60.3375 8.18211 61.2633 8.15851C62.1893 8.13973 63.106 8.12565 64.0073 8.15435L67.4682 8.22932L66.2658 11.8497C65.558 11.3366 64.8085 10.8835 64.0252 10.4951C63.1967 10.0698 62.3305 9.6746 61.4542 9.31043C60.5794 8.94832 59.6831 8.61125 58.8207 8.34105C58.7112 8.30469 58.6092 8.2774 58.505 8.24731C58.4008 8.21722 58.2945 8.18569 58.204 8.16566C58.0395 8.12236 57.7714 8.06482 57.5294 8.01159L56.059 7.68418C55.0779 7.46161 54.0919 7.22912 53.1062 6.97364C52.1185 6.72019 51.1479 6.44237 50.2102 6.18898C48.3406 5.68001 46.4445 5.25372 44.5197 4.83744L44.482 4.82929C44.0247 4.73047 43.6199 4.46616 43.3457 4.08748C43.0716 3.7088 42.9477 3.24266 42.9979 2.77924C43.0482 2.31582 43.2691 1.88804 43.6179 1.57866C43.9667 1.26928 44.4185 1.10028 44.8861 1.10435C47.287 1.12488 49.6883 1.18812 52.081 1.3982C56.3982 1.74337 60.6923 2.34061 64.9412 3.18684C69.1837 4.03824 73.3676 5.162 77.4666 6.5511C80.8802 7.69859 84.2324 8.99537 87.5335 10.384L87.5522 10.3919C87.8834 10.5314 88.1549 10.7829 88.3188 11.1021C88.4827 11.4213 88.5284 11.7876 88.4479 12.1363C88.3673 12.485 88.1657 12.7937 87.8787 13.0079C87.5916 13.2221 87.2375 13.3279 86.879 13.3067C81.2544 12.9723 75.5677 13.6447 69.9995 14.8849C64.4226 16.1236 58.9462 17.909 53.5197 19.8721L52.7085 20.1625L52.6723 20.1754C52.3928 20.2755 52.0859 20.2697 51.8096 20.1593C51.5334 20.0488 51.307 19.8413 51.1733 19.576C51.0397 19.3107 51.0081 19.0061 51.0845 18.7199C51.1609 18.4336 51.34 18.1856 51.5879 18.0228C52.8248 17.2102 54.0669 16.503 55.3013 15.8426C56.5344 15.1813 57.7645 14.5811 58.9812 14.0225C60.1971 13.4622 61.3965 12.9385 62.5794 12.4514C63.7546 11.9516 64.9365 11.5308 66.0622 11.0737C67.1546 10.6403 67.7183 10.516 67.7599 10.7506C67.7957 10.9739 67.3157 11.5798 66.2515 12.4654C65.8724 12.7728 65.4912 13.0821 65.1078 13.3933C64.7172 13.6917 64.2954 13.9366 63.8868 14.2092C63.0614 14.7339 62.2299 15.2625 61.3923 15.7948C59.73 16.8331 58.0504 17.8821 56.3751 18.9285C55.4017 19.5441 54.4193 20.1566 53.4864 20.7903L51.5166 17.0291C53.5217 16.2806 55.5713 15.568 57.6154 14.8709C57.6178 14.849 57.6202 14.8284 57.6232 14.8022Z" fill="#F75849" />
    </svg>
  );

  return (
    <section className="py-8 sm:py-12 lg:py-16 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-10 lg:mb-12">
          <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">
            Cooperative Partnerships
          </h2>
          <p className="text-sm sm:text-base lg:text-lg text-gray-800 font-medium max-w-4xl mx-auto leading-relaxed px-2 sm:px-0">
            Building a platform to nowcast and forecast outcomes is not a trivial task, it comes with a host of
            barriers to clear and challenges to solve. We are partnering with various stakeholders to ensure
            we hit the mark. This will be a network of networks learning and sharing information.
          </p>
        </div>

        {/* Process Flow - Desktop */}
        {/* <div className="hidden md:flex items-start justify-center space-x-8 lg:space-x-16">
          {/* Innovate */}
          {/* <div className="flex flex-col items-center">
            <div className="mb-4">
              <CircleIcon />
            </div>
            <h3 className="text-xl font-bold text-gray-900">Innovate</h3>
          </div>

          {/* Arrow 1 */}
          {/* <div className="mt-16">
            <ArrowIcon />
          </div>

          {/* Integrate */}
          {/* <div className="flex flex-col items-center">
            <div className="mb-4">
              <CircleIcon />
            </div>
            <h3 className="text-xl font-bold text-gray-900">Integrate</h3>
          </div>

          {/* Arrow 2 */}
          {/* <div className="mt-16">
            <ArrowIcon />
          </div>

          {/* Implement */}
          {/* <div className="flex flex-col items-center">
            <div className="mb-4">
              <CircleIcon />
            </div>
            <h3 className="text-xl font-bold text-gray-900">Implement</h3>
          </div>
        </div> */}

        {/* Process Flow - Mobile */}
        {/* <div className="md:hidden space-y-8">
          {/* Innovate */}
          {/* <div className="flex flex-col items-center">
            <div className="mb-4">
              <CircleIcon />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-6">Innovate</h3>
          </div>

          {/* Mobile Arrow Down */}
          {/* <div className="flex justify-center">
            <svg width="89" height="21" viewBox="0 0 89 21" fill="none" xmlns="http://www.w3.org/2000/svg" className="transform rotate-90">
              <path d="M57.6232 14.8022C63.6588 12.8611 69.8791 11.2447 76.2865 10.4938C79.6974 10.0938 83.1351 9.95803 86.5688 10.0877L87.0367 10.1L86.3089 13.3498C79.6943 10.6473 72.9333 8.36398 66.0054 6.91963C59.0857 5.42422 52.0021 4.95065 44.9366 5.10552L45.347 0.925056C46.9177 1.2542 48.4959 1.60062 50.0818 2.00343C50.8734 2.20368 51.6631 2.4202 52.4247 2.63027C53.1872 2.84014 53.9282 3.03968 54.6792 3.22625C55.8006 3.50658 56.941 3.7629 58.0886 4.01174L58.9789 4.20676C59.351 4.29156 59.6694 4.3839 59.9882 4.4796C60.6206 4.66966 61.2098 4.87939 61.8012 5.096C62.9759 5.53472 64.1205 6.00706 65.2607 6.54459C66.4731 7.09158 67.6341 7.74618 68.7295 8.50046C69.1036 8.7655 69.3833 9.14248 69.528 9.57643C69.6728 10.0104 69.6749 10.4786 69.534 10.9129C69.3932 11.3471 69.1168 11.7247 68.7452 11.9905C68.3735 12.2563 67.9261 12.3965 67.468 12.3907L67.4379 12.39C63.2051 12.3094 58.9704 12.3829 54.7392 12.5527C50.5094 12.7516 46.2864 13.0611 42.0701 13.4812C39.9663 13.7219 37.8589 13.9375 35.7617 14.2337C33.6608 14.5039 31.574 14.8669 29.4825 15.1975C25.3096 15.9213 21.1593 16.7571 17.0314 17.7047C15.4472 18.0964 13.8398 18.4271 12.2448 18.7917C11.4462 18.9708 10.6494 19.1497 9.85428 19.3281C9.05603 19.4941 8.26546 19.6787 7.47921 19.8634C4.45994 20.5432 2.414 20.7012 1.03148 20.4707C0.0268414 20.307 -0.114375 19.8669 0.522715 19.2731C1.16147 18.6872 2.57026 17.9153 4.67601 17.1817C5.34288 16.9525 6.02836 16.7271 6.72543 16.5065C7.42201 16.284 8.13071 16.0682 8.84963 15.8774C9.56786 15.6845 10.2906 15.497 11.0111 15.3153L12.0892 15.0469C12.4493 14.967 12.8075 14.8874 13.1636 14.8082C19.7164 13.3412 26.3012 12.1016 32.9183 11.0894C39.5342 10.0704 46.1824 9.26775 52.863 8.68136C54.7057 8.50082 56.5913 8.38303 58.4649 8.25923C59.4021 8.20411 60.3375 8.18211 61.2633 8.15851C62.1893 8.13973 63.106 8.12565 64.0073 8.15435L67.4682 8.22932L66.2658 11.8497C65.558 11.3366 64.8085 10.8835 64.0252 10.4951C63.1967 10.0698 62.3305 9.6746 61.4542 9.31043C60.5794 8.94832 59.6831 8.61125 58.8207 8.34105C58.7112 8.30469 58.6092 8.2774 58.505 8.24731C58.4008 8.21722 58.2945 8.18569 58.204 8.16566C58.0395 8.12236 57.7714 8.06482 57.5294 8.01159L56.059 7.68418C55.0779 7.46161 54.0919 7.22912 53.1062 6.97364C52.1185 6.72019 51.1479 6.44237 50.2102 6.18898C48.3406 5.68001 46.4445 5.25372 44.5197 4.83744L44.482 4.82929C44.0247 4.73047 43.6199 4.46616 43.3457 4.08748C43.0716 3.7088 42.9477 3.24266 42.9979 2.77924C43.0482 2.31582 43.2691 1.88804 43.6179 1.57866C43.9667 1.26928 44.4185 1.10028 44.8861 1.10435C47.287 1.12488 49.6883 1.18812 52.081 1.3982C56.3982 1.74337 60.6923 2.34061 64.9412 3.18684C69.1837 4.03824 73.3676 5.162 77.4666 6.5511C80.8802 7.69859 84.2324 8.99537 87.5335 10.384L87.5522 10.3919C87.8834 10.5314 88.1549 10.7829 88.3188 11.1021C88.4827 11.4213 88.5284 11.7876 88.4479 12.1363C88.3673 12.485 88.1657 12.7937 87.8787 13.0079C87.5916 13.2221 87.2375 13.3279 86.879 13.3067C81.2544 12.9723 75.5677 13.6447 69.9995 14.8849C64.4226 16.1236 58.9462 17.909 53.5197 19.8721L52.7085 20.1625L52.6723 20.1754C52.3928 20.2755 52.0859 20.2697 51.8096 20.1593C51.5334 20.0488 51.307 19.8413 51.1733 19.576C51.0397 19.3107 51.0081 19.0061 51.0845 18.7199C51.1609 18.4336 51.34 18.1856 51.5879 18.0228C52.8248 17.2102 54.0669 16.503 55.3013 15.8426C56.5344 15.1813 57.7645 14.5811 58.9812 14.0225C60.1971 13.4622 61.3965 12.9385 62.5794 12.4514C63.7546 11.9516 64.9365 11.5308 66.0622 11.0737C67.1546 10.6403 67.7183 10.516 67.7599 10.7506C67.7957 10.9739 67.3157 11.5798 66.2515 12.4654C65.8724 12.7728 65.4912 13.0821 65.1078 13.3933C64.7172 13.6917 64.2954 13.9366 63.8868 14.2092C63.0614 14.7339 62.2299 15.2625 61.3923 15.7948C59.73 16.8331 58.0504 17.8821 56.3751 18.9285C55.4017 19.5441 54.4193 20.1566 53.4864 20.7903L51.5166 17.0291C53.5217 16.2806 55.5713 15.568 57.6154 14.8709C57.6178 14.849 57.6202 14.8284 57.6232 14.8022Z" fill="#F75849" />
            </svg>
          </div>

          {/* Integrate */}
          {/* <div className="flex flex-col items-center">
            <div className="mb-4">
              <CircleIcon />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-6">Integrate</h3>
          </div>

          {/* Mobile Arrow Down */}
          {/* <div className="flex justify-center">
            <svg width="89" height="21" viewBox="0 0 89 21" fill="none" xmlns="http://www.w3.org/2000/svg" className="transform rotate-90">
              <path d="M57.6232 14.8022C63.6588 12.8611 69.8791 11.2447 76.2865 10.4938C79.6974 10.0938 83.1351 9.95803 86.5688 10.0877L87.0367 10.1L86.3089 13.3498C79.6943 10.6473 72.9333 8.36398 66.0054 6.91963C59.0857 5.42422 52.0021 4.95065 44.9366 5.10552L45.347 0.925056C46.9177 1.2542 48.4959 1.60062 50.0818 2.00343C50.8734 2.20368 51.6631 2.4202 52.4247 2.63027C53.1872 2.84014 53.9282 3.03968 54.6792 3.22625C55.8006 3.50658 56.941 3.7629 58.0886 4.01174L58.9789 4.20676C59.351 4.29156 59.6694 4.3839 59.9882 4.4796C60.6206 4.66966 61.2098 4.87939 61.8012 5.096C62.9759 5.53472 64.1205 6.00706 65.2607 6.54459C66.4731 7.09158 67.6341 7.74618 68.7295 8.50046C69.1036 8.7655 69.3833 9.14248 69.528 9.57643C69.6728 10.0104 69.6749 10.4786 69.534 10.9129C69.3932 11.3471 69.1168 11.7247 68.7452 11.9905C68.3735 12.2563 67.9261 12.3965 67.468 12.3907L67.4379 12.39C63.2051 12.3094 58.9704 12.3829 54.7392 12.5527C50.5094 12.7516 46.2864 13.0611 42.0701 13.4812C39.9663 13.7219 37.8589 13.9375 35.7617 14.2337C33.6608 14.5039 31.574 14.8669 29.4825 15.1975C25.3096 15.9213 21.1593 16.7571 17.0314 17.7047C15.4472 18.0964 13.8398 18.4271 12.2448 18.7917C11.4462 18.9708 10.6494 19.1497 9.85428 19.3281C9.05603 19.4941 8.26546 19.6787 7.47921 19.8634C4.45994 20.5432 2.414 20.7012 1.03148 20.4707C0.0268414 20.307 -0.114375 19.8669 0.522715 19.2731C1.16147 18.6872 2.57026 17.9153 4.67601 17.1817C5.34288 16.9525 6.02836 16.7271 6.72543 16.5065C7.42201 16.284 8.13071 16.0682 8.84963 15.8774C9.56786 15.6845 10.2906 15.497 11.0111 15.3153L12.0892 15.0469C12.4493 14.967 12.8075 14.8874 13.1636 14.8082C19.7164 13.3412 26.3012 12.1016 32.9183 11.0894C39.5342 10.0704 46.1824 9.26775 52.863 8.68136C54.7057 8.50082 56.5913 8.38303 58.4649 8.25923C59.4021 8.20411 60.3375 8.18211 61.2633 8.15851C62.1893 8.13973 63.106 8.12565 64.0073 8.15435L67.4682 8.22932L66.2658 11.8497C65.558 11.3366 64.8085 10.8835 64.0252 10.4951C63.1967 10.0698 62.3305 9.6746 61.4542 9.31043C60.5794 8.94832 59.6831 8.61125 58.8207 8.34105C58.7112 8.30469 58.6092 8.2774 58.505 8.24731C58.4008 8.21722 58.2945 8.18569 58.204 8.16566C58.0395 8.12236 57.7714 8.06482 57.5294 8.01159L56.059 7.68418C55.0779 7.46161 54.0919 7.22912 53.1062 6.97364C52.1185 6.72019 51.1479 6.44237 50.2102 6.18898C48.3406 5.68001 46.4445 5.25372 44.5197 4.83744L44.482 4.82929C44.0247 4.73047 43.6199 4.46616 43.3457 4.08748C43.0716 3.7088 42.9477 3.24266 42.9979 2.77924C43.0482 2.31582 43.2691 1.88804 43.6179 1.57866C43.9667 1.26928 44.4185 1.10028 44.8861 1.10435C47.287 1.12488 49.6883 1.18812 52.081 1.3982C56.3982 1.74337 60.6923 2.34061 64.9412 3.18684C69.1837 4.03824 73.3676 5.162 77.4666 6.5511C80.8802 7.69859 84.2324 8.99537 87.5335 10.384L87.5522 10.3919C87.8834 10.5314 88.1549 10.7829 88.3188 11.1021C88.4827 11.4213 88.5284 11.7876 88.4479 12.1363C88.3673 12.485 88.1657 12.7937 87.8787 13.0079C87.5916 13.2221 87.2375 13.3279 86.879 13.3067C81.2544 12.9723 75.5677 13.6447 69.9995 14.8849C64.4226 16.1236 58.9462 17.909 53.5197 19.8721L52.7085 20.1625L52.6723 20.1754C52.3928 20.2755 52.0859 20.2697 51.8096 20.1593C51.5334 20.0488 51.307 19.8413 51.1733 19.576C51.0397 19.3107 51.0081 19.0061 51.0845 18.7199C51.1609 18.4336 51.34 18.1856 51.5879 18.0228C52.8248 17.2102 54.0669 16.503 55.3013 15.8426C56.5344 15.1813 57.7645 14.5811 58.9812 14.0225C60.1971 13.4622 61.3965 12.9385 62.5794 12.4514C63.7546 11.9516 64.9365 11.5308 66.0622 11.0737C67.1546 10.6403 67.7183 10.516 67.7599 10.7506C67.7957 10.9739 67.3157 11.5798 66.2515 12.4654C65.8724 12.7728 65.4912 13.0821 65.1078 13.3933C64.7172 13.6917 64.2954 13.9366 63.8868 14.2092C63.0614 14.7339 62.2299 15.2625 61.3923 15.7948C59.73 16.8331 58.0504 17.8821 56.3751 18.9285C55.4017 19.5441 54.4193 20.1566 53.4864 20.7903L51.5166 17.0291C53.5217 16.2806 55.5713 15.568 57.6154 14.8709C57.6178 14.849 57.6202 14.8284 57.6232 14.8022Z" fill="#F75849" />
            </svg>
          </div>

          {/* Implement */}
          {/* <div className="flex flex-col items-center">
            <div className="mb-4">
              <CircleIcon />
            </div>
            <h3 className="text-lg font-bold text-gray-900">Implement</h3>
          </div>
        </div> */}

        {/* SOAR Dashboard Image */}
        <div className="mt-16 mx-8 lg:mx-16">
          <img
            src={soarDashboardImage}
            alt="SOAR Dashboard"
            className="w-full rounded-2xl"
            style={{
              boxShadow: '0 0 40px rgba(251, 146, 60, 0.3)'
            }}
          />
        </div>

        {/* Partnership Cards */}
        {/* <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-6 mx-8 lg:mx-16">
          {/* CDC Partnership Card */}
          {/* <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 flex flex-col h-full">
            {/* Avatar */}
            {/* <div className="w-16 h-16 mb-4 flex items-center justify-center">
              <svg width="93" height="59" viewBox="0 0 93 59" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-auto">
                <path d="M2.53711 57.1984H20.8026H82.5742C87.0121 57.1984 90.6668 53.8251 91.1033 49.5014C91.1318 49.2118 91.1482 48.9222 91.1482 48.6244V2.0918H21.9978H11.1151C8.15382 2.0918 5.54737 3.59285 4.00553 5.87298C3.69553 6.32982 3.4304 6.81522 3.21422 7.32917C2.78185 8.35706 2.54119 9.48285 2.54119 10.6657V57.1984H2.53711Z" fill="#0055B8" />
                <path d="M11.1152 0.374512C5.44134 0.374512 0.823975 4.99188 0.823975 10.6657V58.9115H13.7216H16.5565H82.5742C88.2481 58.9115 92.8654 54.2941 92.8654 48.6203V0.374512H11.1152ZM78.1241 32.3453C78.0425 32.2515 77.8957 32.2433 77.8059 32.3249C77.2675 32.8103 75.4238 34.2665 72.6624 34.3154C69.1831 34.3725 65.6588 31.4887 65.6588 26.4839C65.6588 21.479 69.2973 18.6482 72.7032 18.6482C75.2321 18.6482 76.8637 19.8392 77.3573 20.2594C77.4511 20.3369 77.5898 20.3287 77.6713 20.2349L80.7591 16.8045C80.8325 16.7229 80.8407 16.5965 80.7673 16.5108C80.2615 15.9031 78.0874 13.7004 73.2171 13.7004C72.7644 13.7004 72.2994 13.7249 71.8262 13.7698L90.4303 2.09175H91.1482V27.5648L78.4423 32.7002L78.1241 32.3453ZM44.1873 33.6832H41.589C41.4625 33.6832 41.3605 33.5812 41.3605 33.4548V19.2682C41.3605 19.1417 41.4625 19.0398 41.589 19.0398H44.5707C49.1717 19.0398 52.2269 21.3077 52.2269 26.1494C52.2269 31.7661 49.2411 33.6832 44.1873 33.6832ZM43.2165 14.0227H36.1517C36.0253 14.0227 35.9233 14.1246 35.9233 14.2511V38.48C35.9233 38.5127 35.9315 38.5412 35.9396 38.5698L9.32858 57.2024H6.84042L23.1766 39.0592C24.0046 39.1979 24.8204 39.2673 25.5954 39.2673C30.429 39.2673 32.9906 36.722 33.5698 36.0571C33.6473 35.9715 33.6473 35.841 33.5698 35.7553L30.5024 32.3453C30.4208 32.2515 30.274 32.2433 30.1842 32.3249C29.6458 32.8103 27.8021 34.2665 25.0407 34.3154C21.5613 34.3725 18.0371 31.4887 18.0371 26.4839C18.0371 21.479 21.6756 18.6482 25.0815 18.6482C27.6104 18.6482 29.242 19.8392 29.7355 20.2594C29.8294 20.3369 29.9681 20.3287 30.0496 20.2349L33.1374 16.8045C33.2108 16.7229 33.219 16.5965 33.1455 16.5108C32.6398 15.9031 30.4657 13.7004 25.5954 13.7004C23.1807 13.7004 20.4192 14.3245 18.0575 15.7807L23.3112 2.09175H54.5804L43.2165 14.0227ZM3.21424 7.32912C3.43042 6.81517 3.69555 6.32977 4.00555 5.87293C5.5474 3.5928 8.15792 2.09175 11.1152 2.09175H21.8632L15.9936 17.3837C13.9092 19.415 12.4775 22.3723 12.4775 26.4798C12.4775 26.5002 12.4775 26.5206 12.4775 26.541L2.53713 52.4382V10.6657C2.53713 9.4828 2.77779 8.35293 3.21424 7.32912ZM2.53713 56.2316L12.7753 29.5512C13.8603 34.8253 17.723 37.6316 21.6552 38.7166L5.01305 57.1983H2.53713V56.2316ZM11.7025 57.1983L38.1178 38.7044H44.8195C52.1738 38.7044 57.8762 34.4174 57.8762 26.4349C57.8762 17.7875 52.5165 14.3204 45.0765 14.0471L56.4649 2.09583H87.8769L67.6738 14.7773C63.6357 16.4089 60.0951 20.0269 60.0951 26.4839C60.0951 32.227 62.9219 35.7227 66.393 37.5745L17.8087 57.2024H11.7025V57.1983ZM91.1033 49.5013C90.6628 53.825 87.0121 57.1983 82.5742 57.1983H21.4308L68.1062 38.3414C69.8234 38.9736 71.6059 39.2632 73.2171 39.2632C78.0507 39.2632 80.6123 36.7179 81.1915 36.0531C81.269 35.9674 81.269 35.8369 81.1915 35.7512L79.409 33.7729L91.1482 29.0291V48.6203C91.1482 48.9181 91.1359 49.2117 91.1033 49.5013Z" fill="white" />
              </svg>
            </div>

            <h3 className="text-lg font-bold text-gray-900 mb-3">CDC Partnership</h3>
            <p className="text-gray-600 text-sm leading-relaxed flex-grow">
              Learn more about our collaboration with the CDC in disease modeling and prediction.
            </p>

            <div className="flex justify-end mt-4">
              <Link
                to="https://www.cdc.gov/media/releases/2023/p0922-disease-modeling.html"
                target='_blank'
                className="inline-flex items-center px-4 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-100 transition duration-300 text-sm">
                Learn More
                <svg className="ml-2 w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Pacific Rim Forecasting Card */}
          {/* <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 flex flex-col h-full">
            {/* Avatar */}
            {/* <div className="w-16 h-16 mb-4 flex items-center justify-center">
              <img
                src={pacificRimLogo}
                alt="Pacific Rim Forecasting Logo"
                className="w-full h-full object-contain"
              />
            </div>

            <h3 className="text-lg font-bold text-gray-900 mb-3">Pacific Rim Forecasting</h3>
            <p className="text-gray-600 text-sm leading-relaxed flex-grow">
              Explore our Pacific Rim forecasting initiatives and partnerships.
            </p>

            <div className="flex justify-end mt-4">
              <Link to="/pacific-rim" target='_blank' className="inline-flex items-center px-4 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-100 transition duration-300 text-sm">
                Learn More
                <svg className="ml-2 w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          </div>
        </div> */}
      </div>
    </section>
  );
};

export default CooperativePartnerships;
