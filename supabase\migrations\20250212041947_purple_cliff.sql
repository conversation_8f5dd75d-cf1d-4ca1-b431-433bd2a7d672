-- First drop existing policies
DROP POLICY IF EXISTS "blog_posts_read_all" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_insert" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_update" ON blog_posts;
DROP POLICY IF EXISTS "blog_posts_admin_delete" ON blog_posts;

-- Drop existing constraints
ALTER TABLE blog_posts
  DROP CONSTRAINT IF EXISTS title_not_empty,
  DROP CONSTRAINT IF EXISTS content_not_empty,
  DROP CONSTRAINT IF EXISTS valid_status;

-- Add NOT NULL constraints and validation checks
ALTER TABLE blog_posts
  ALTER COLUMN title SET NOT NULL,
  ALTER COLUMN content SET NOT NULL,
  ALTER COLUMN status SET NOT NULL,
  ADD CONSTRAINT title_not_empty CHECK (length(trim(title)) > 0),
  ADD CONSTRAINT content_not_empty CHECK (length(trim(content)) > 0),
  ADD CONSTRAINT valid_status CHECK (status IN ('draft', 'published', 'archived'));

-- Create new policies for blog_posts
CREATE POLICY "blog_posts_read_all"
  ON blog_posts
  FOR SELECT
  TO public
  USING (true);

-- Create a single policy for all admin operations
CREATE POLICY "blog_posts_admin_all"
  ON blog_posts
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Grant necessary permissions
GRANT ALL ON blog_posts TO authenticated;
GRANT ALL ON blog_posts TO anon;
GRANT ALL ON blog_posts TO service_role;

-- Ensure RLS is enabled
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;