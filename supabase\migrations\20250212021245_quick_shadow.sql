/*
  # Add Admin User
  
  1. Changes
    - Add a new admin user to admin_users table
    
  2. Security
    - Only adds user to admin_users table
    - Relies on existing RLS policies
*/

-- Add the user to admin_users table
INSERT INTO admin_users (id, email, role)
SELECT 
  id,
  email,
  'admin'
FROM auth.users
WHERE email = '<EMAIL>'  -- Replace with your actual email
AND NOT EXISTS (
  SELECT 1 FROM admin_users WHERE email = '<EMAIL>'  -- Replace with your actual email
);