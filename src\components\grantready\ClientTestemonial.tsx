import React, { useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight } from 'lucide-react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, A11y, Autoplay } from 'swiper/modules';
import type { Swiper as SwiperType } from 'swiper';
import doubleQuotes from '../../assets/images/double-quotes-testimonial.svg';
import { Colors } from '../../constants/Colors';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/a11y';

import Test1 from '../../assets/home/<USER>';
import Test2 from '../../assets/home/<USER>';
import Test3 from '../../assets/home/<USER>';

import Phig from '../../assets/home/<USER>'

const TESTIMONIAL_CAROUSEL_STYLES = `
  .testimonial-carousel {
    padding: 1rem 4rem 4rem 4rem;
    overflow: hidden;
    max-width: 100%;
    margin: 0 auto;
  }

  .testimonial-carousel .swiper-wrapper {
    display: flex;
    align-items: stretch;
  }

  .testimonial-carousel .swiper-slide {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: auto;
    min-height: 360px;
    width: clamp(300px, 80vw, 800px);
    background: #ffffff;
    border-radius: 1rem;
    border: 1px solid #e5e7eb;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 2rem;
    opacity: 0.6;
    transform: scale(0.95);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .testimonial-carousel .swiper-slide-active {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border-color: #d1d5db;
  }

  .carousel-nav-button {
    width: 3rem;
    height: 3rem;
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    transform: translateY(-50%);
  }

  .carousel-nav-button:hover:not(.swiper-button-disabled) {
    background: #f3f4f6;
    border-color: #9ca3af;
    transform: translateY(-50%) scale(1.05);
  }

  .carousel-nav-button:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  .carousel-nav-button.swiper-button-disabled {
    opacity: 0.4;
    pointer-events: none;
    cursor: not-allowed;
    background: #f9fafb;
  }

  .carousel-nav-button.prev {
    left: 1rem;
  }

  .carousel-nav-button.next {
    right: 1rem;
  }

  @media (max-width: 1024px) {
    .testimonial-carousel {
      padding: 1rem 2.5rem 4rem 2.5rem;
    }

    .carousel-nav-button.prev {
      left: 0.5rem;
    }

    .carousel-nav-button.next {
      right: 0.5rem;
    }
  }

  @media (max-width: 640px) {
    .testimonial-carousel {
      padding: 1rem 1.5rem 3rem 1.5rem;
    }

    .testimonial-carousel .swiper-slide {
      width: 90vw;
      min-height: 280px;
      padding: 1.5rem;
    }

    .carousel-nav-button {
      width: 2.25rem;
      height: 2.25rem;
    }

    .carousel-nav-button.prev {
      left: 0.25rem;
    }

    .carousel-nav-button.next {
      right: 0.25rem;
    }
  }
`;

interface Testimonial {
  id: number;
  quote: string;
  name: string;
  title: string;
  organization: string;
  organizationLogo?: string;
  profileImage: string;
}

interface ClientTestimonialProps {
  color: string;
  title: string;
  description: string;
}

const ClientTestimonial: React.FC<ClientTestimonialProps> = ({ color, title, description }) => {
  const swiperRef = useRef<SwiperType | null>(null);

  // Color mapping for Tailwind CSS classes
  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { text: string; quote: string }> = {
      '#F75849': { text: 'text-[#F75849]', quote: 'text-[#F75849]' },
      '#4F63D9': { text: 'text-[#4F63D9]', quote: 'text-[#4F63D9]' },
      '#2F8AFF': { text: 'text-[#2F8AFF]', quote: 'text-[#2F8AFF]' },
      '#3B82F6': { text: 'text-blue-500', quote: 'text-blue-500' },
      '#EF4444': { text: 'text-red-500', quote: 'text-red-500' },
      '#10B981': { text: 'text-green-500', quote: 'text-green-500' },
      '#F59E0B': { text: 'text-yellow-500', quote: 'text-yellow-500' },
      '#8B5CF6': { text: 'text-purple-500', quote: 'text-purple-500' },
    };

    return colorMap[color] || { text: 'text-gray-600', quote: 'text-gray-600' };
  };

  const colorClasses = getColorClasses(color);

  const testimonials: Testimonial[] = [
    {
      id: 1,
      quote: "GrantReady has revolutionized how we manage our public health grants. We’ve reduced administrative time by 45% and improved our reporting accuracy substantially.",
      name: "Dr. Michael Rodriguez",
      title: "Director of Public Health Programs",
      organization: "State Department of Health",
      organizationLogo: "",
      profileImage: Test1
    },
    {
      id: 2,
      quote: "The comprehensive dashboard gives us real-time insights into our grant performance. It’s made coordination between state and local jurisdictions seamless.",
      name: "Dr. Emily Parker",
      title: "Chief Grants Officer",
      organization: "County Health Department",
      organizationLogo: "",
      profileImage: Test2
    },
    {
      id: 3,
      quote: "The real-time reporting capabilities have revolutionized our decision-making process. We can now respond to funding opportunities much more efficiently.",
      name: "Dr. Amanda Chen",
      title: "Emergency Preparedness Director",
      organization: "Regional Health Authority",
      organizationLogo: "",
      profileImage: Test3
    },
  ];

  return (
    <section className="m-auto max-w-full py-10 bg-gray-50 relative overflow-hidden">
      <style>{TESTIMONIAL_CAROUSEL_STYLES}</style>    
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-10 pb-8">        <div className="flex justify-between items-start mb-20">
          <div className="space-y-4 flex-1">
            <h2 className="text-2xl md:text-4xl font-medium text-black font-platform max-w-2xl">
              {title}
            </h2>
            <p className="text-sm sm:text-base md:text-lg text-gray-600 max-w-2xl font-inter">
              {description}
            </p>
          </div>
          {/* <img 
            src={doubleQuotes} 
            alt="Double quotes" 
            className="w-24 h-24 ml-8 flex-shrink-0"            style={{ filter: `brightness(0) saturate(100%) ${color === Colors.GRANTREADY ? 'invert(36%) sepia(14%) saturate(1966%) hue-rotate(182deg) brightness(96%) contrast(89%)' : 
              color === Colors.SOAR ? 'invert(45%) sepia(90%) saturate(385%) hue-rotate(327deg) brightness(97%) contrast(95%)' :
              'invert(35%) sepia(58%) saturate(3516%) hue-rotate(213deg) brightness(98%) contrast(93%)'}`
            }}
          /> */}
        </div>

        <div className="relative max-w-7xl mx-auto">
          <Swiper
            onSwiper={(swiper) => (swiperRef.current = swiper)}
            modules={[Navigation, A11y, Autoplay]}
            grabCursor
            loop
            slidesPerView="auto"
            centeredSlides
            spaceBetween={24}
            autoplay={{ delay: 5000, disableOnInteraction: true }}
            navigation={{
              prevEl: '.carousel-nav-button.prev',
              nextEl: '.carousel-nav-button.next',
            }}
            a11y={{
              prevSlideMessage: 'Previous testimonial',
              nextSlideMessage: 'Next testimonial',
              slideLabelMessage: 'Testimonial {{index}} of {{slidesCount}}',
            }}
            className="testimonial-carousel"
            onProgress={(swiper) => {
              for (let i = 0; i < swiper.slides.length; i++) {
                const slide = swiper.slides[i] as HTMLElement;
                const slideProgress = (slide as any).progress;
                const absProgress = Math.abs(slideProgress);
                const scale = 1 - absProgress * 0.08;
                const opacity = 1 - absProgress * 0.5;
                const translateX = slideProgress * 120;

                slide.style.transform = `translateX(${translateX}px) scale(${scale})`;
                slide.style.opacity = opacity.toString();
                slide.style.zIndex = (100 - Math.abs(Math.round(slideProgress))).toString();
              }
            }}
            onSetTransition={(swiper, speed) => {
              for (let i = 0; i < swiper.slides.length; i++) {
                const slide = swiper.slides[i] as HTMLElement;
                slide.style.transitionDuration = `${speed}ms`;
              }
            }}
          >
            {testimonials.map((testimonial) => (
              <SwiperSlide key={testimonial.id}>
                {({ isActive }) => (
                  <div className="h-full flex flex-col justify-between">
                    <blockquote className="flex-grow mb-6">
                      <p className={`text-lg text-gray-800 leading-relaxed ${isActive ? 'font-medium' : 'font-normal'}`}>
                        "{testimonial.quote}"
                      </p>
                    </blockquote>
                    <div className="flex justify-between items-center gap-4">
                      
                      <div>
                        <h4 className={`text-2xl md:text-3xl font-semibold ${colorClasses.text}`}>{testimonial.name}</h4>
                        <p className="text-lg md:text-xl font-bold text-gray-600">{testimonial.title}</p>
                        <p className="text-sm md:text-base text-gray-600">{testimonial.organization}</p>
                        <div className="inline-flex items-center mt-10">
                          <img className='w-48 h-12' src={Phig} alt='phig parteners' />
                        </div>
                      </div>
                      {/* <div className="flex-shrink-0">
                        <img
                          src={testimonial.profileImage}
                          alt={`${testimonial.name}, ${testimonial.title} at ${testimonial.organization}`}
                          className="w-36 h-36 rounded-lg object-cover border-2 shadow-sm"
                          loading="lazy"
                        />
                      </div> */}
                    </div>
                  </div>
                )}
              </SwiperSlide>
            ))}
          </Swiper>

          <div className='flex items-center justify-between'>
          <button
            className="
            carousel-nav-button prev"
            aria-label="Previous testimonial"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <button
            className="carousel-nav-button next"
            aria-label="Next testimonial"
          >
            <ArrowRight className="h-5 w-5 text-gray-600" />
          </button>
          </div>
        </div>
      </div>
    </section>
  );
}

export default ClientTestimonial;