import React, { useState, useEffect } from "react";
import ReactD<PERSON> from "react-dom";
import { supabase } from "../../../lib/supabase";
import { X, Plus, Trash2, Save, Search, UserPlus, Loader2 } from "lucide-react";
import { Combobox, Switch } from "@headlessui/react";
import { generateRef } from "../../../utils/generateRef";
// Remove the createClient import

interface InvoiceFormProps {
  isAdding: boolean;
  setIsAdding: (isAdding: boolean) => void;
  users: User[];
  products: Plan[]; // Assuming Plan includes necessary price info or you fetch prices separately
  onAddInvoice: (newInvoiceData: any) => void; // Adjust 'any' to the expected return type
  setError: (error: string | null) => void;
  onAddUser: (newUser: User) => void; // Add this function to update the users list
  invoice?: any; // The invoice to edit
  isEditingDraft?: boolean; // Whether the invoice being edited is a draft
}

const InvoiceForm: React.FC<InvoiceFormProps> = ({
  isAdding,
  setIsAdding,
  users,
  products,
  onAddInvoice,
  setError: parentSetError, // Rename to avoid confusion
  onAddUser,
  invoice,
  isEditingDraft = true,
}) => {
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(
    null
  );
  const [items, setItems] = useState<InvoiceItemForm[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<string>("");
  const [quantity, setQuantity] = useState<number>(1);
  const [globalBillingCycle, setGlobalBillingCycle] = useState<
    "monthly" | "yearly"
  >("yearly");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userQuery, setUserQuery] = useState("");
  const [productQuery, setProductQuery] = useState("");
  const [useStripe, setUseStripe] = useState<boolean>(false);
  const [notes, setNotes] = useState<string>("");

  // New state for user creation
  const [showCreateUserModal, setShowCreateUserModal] = useState(false);
  const [creatingUser, setCreatingUser] = useState(false);
  const [newUser, setNewUser] = useState({
    email: "",
    password: "",
    full_name: "",
    auto_confirm: true,
  });
  const [success, setSuccess] = useState<string | null>(null);

  // Local error state
  const [error, setError] = useState<string | null>(null);

  // Add payment method state
  const [paymentMethod, setPaymentMethod] = useState({
    type: "bank_transfer",
    bank_name: "",
    account_number: "",
    routing_number: "",
  });

  // Add invoice status state (for manual invoices)
  const [invoiceStatus, setInvoiceStatus] = useState<string>("draft");

  // Use a combined error handler that updates both local and parent error states
  const handleError = (errorMessage: string | null) => {
    setError(errorMessage);
    parentSetError(errorMessage);
  };

  // Filter users based on search query
  const filteredUsers =
    userQuery === ""
      ? users
      : users.filter(
          (user) =>
            user.email.toLowerCase().includes(userQuery.toLowerCase()) ||
            (user.full_name &&
              user.full_name.toLowerCase().includes(userQuery.toLowerCase()))
        );

  // Filter products based on search query
  const filteredProducts =
    productQuery === ""
      ? products
      : products.filter(
          (product) =>
            product.name.toLowerCase().includes(productQuery.toLowerCase()) ||
            product.description
              .toLowerCase()
              .includes(productQuery.toLowerCase())
        );

  useEffect(() => {
    // If editing an invoice, populate the form with the invoice data
    if (invoice) {
      // Find the user ID based on customer email or name
      const user = users.find(
        (u) =>
          u.email === invoice.customer_email ||
          (u.full_name &&
            invoice.customer_name &&
            u.full_name === invoice.customer_name)
      );

      if (user) {
        setSelectedUserId(user.id);
        setSelectedCustomerId(user.stripe_customer_id);
      }

      // Set payment method if available
      if (invoice.payment_method) {
        setPaymentMethod(invoice.payment_method);
      }

      // Set notes if available
      if (invoice.notes) {
        setNotes(invoice.notes);
      }

      // Set invoice status if available
      if (invoice.status) {
        setInvoiceStatus(invoice.status);
      }

      // Set items if available
      if (invoice.line_items && Array.isArray(invoice.line_items)) {
        const formattedItems = invoice.line_items.map((item) => ({
          productId: item.product_id,
          priceId: "", // We don't have this info in the line items
          quantity: item.quantity,
          billingCycle: item.billing_cycle || "yearly",
          price: item.price / 100, // Convert from cents to dollars for display
        }));
        setItems(formattedItems);

        // Set the global billing cycle based on the first item's billing cycle
        if (formattedItems.length > 0 && formattedItems[0].billingCycle) {
          setGlobalBillingCycle(
            formattedItems[0].billingCycle as "monthly" | "yearly"
          );
        }
      }

      // Set use Stripe flag
      setUseStripe(!invoice.is_manual);
    } else if (!isAdding) {
      // Reset form when closing without an invoice
      setSelectedUserId("");
      setSelectedCustomerId(null);
      setItems([]);
      setSelectedProduct("");
      setQuantity(1);
      setGlobalBillingCycle("yearly");
      setIsSubmitting(false);
      setUserQuery("");
      setProductQuery("");
      setNotes("");
      setInvoiceStatus("draft"); // Reset invoice status
      setPaymentMethod({
        type: "bank_transfer",
        bank_name: "",
        account_number: "",
        routing_number: "",
      });
    }
  }, [isAdding, invoice, users]);

  useEffect(() => {
    // Find the stripe_customer_id when a user is selected
    const user = users.find((u) => u.id === selectedUserId);
    setSelectedCustomerId(user?.stripe_customer_id ?? null);
  }, [selectedUserId, users]);

  // --- Add Price Fetching Logic Here if needed ---
  // useEffect(() => {
  //     if (selectedProduct) {
  //         // Fetch prices associated with the selected product
  //         // Example: fetchPricesForProduct(selectedProduct).then(setPrices);
  //         setSelectedPrice(''); // Reset price selection
  //     } else {
  //         setPrices([]);
  //         setSelectedPrice('');
  //     }
  // }, [selectedProduct]);
  // --- End Price Fetching Logic ---

  const handleAddItem = () => {
    if (!selectedProduct || quantity <= 0) {
      handleError("Please select a product and ensure quantity is positive.");
      return;
    }

    // Find the selected product object from the products prop
    const product = products.find((p) => p.id === selectedProduct);

    if (!product) {
      handleError("Selected product not found.");
      return;
    }

    // Get the correct price based on billing cycle
    // For manual invoices, we'll use the base price from the product
    const basePrice = product.price;
    console.log(
      `Adding product: ${product.name}, Base price: ${basePrice}, Billing cycle: ${globalBillingCycle}`
    );

    // Add the item with the correct price
    setItems([
      ...items,
      {
        productId: selectedProduct,
        priceId:
          globalBillingCycle === "monthly"
            ? product.stripe_monthly_price_id
            : product.stripe_yearly_price_id,
        quantity,
        billingCycle: globalBillingCycle,
        price: basePrice, // Store the base price for display
      },
    ]);

    // Reset inputs
    setSelectedProduct("");
    setQuantity(1);
    setError(null);
  };

  const handleRemoveItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    // Get the selected user
    const selectedUser = users.find((u) => u.id === selectedUserId);
    if (!selectedUser) {
      handleError("Please select a valid user.");
      return;
    }

    // Only require items for new invoices or when editing a draft
    if ((isAdding || (invoice && isEditingDraft)) && items.length === 0) {
      handleError("Please add at least one item to the invoice.");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // If we're editing a non-draft invoice, we can only update the status
      if (invoice && !isEditingDraft) {
        // Update only the status of the invoice
        const { error } = await supabase
          .from("invoices")
          .update({
            status: invoiceStatus, // Use the selected status
            amount_paid: invoiceStatus === "paid" ? invoice.amount_due : 0,
            amount_remaining: invoiceStatus === "paid" ? 0 : invoice.amount_due,
          })
          .eq("id", invoice.id);

        if (error) throw error;

        onAddInvoice({
          ...invoice,
          status: invoiceStatus,
          amount_paid: invoiceStatus === "paid" ? invoice.amount_due : 0,
          amount_remaining: invoiceStatus === "paid" ? 0 : invoice.amount_due,
        });
      }
      // If we're editing a draft invoice or creating a new one
      else if (useStripe && !invoice) {
        // Existing Stripe invoice creation logic
        const { data, error } = await supabase.functions.invoke(
          "create-stripe-invoice",
          {
            body: {
              customerId: selectedUser.stripe_customer_id || "",
              userId: selectedUser.id,
              userEmail: selectedUser.email,
              subscriptionItems: items.map((item) => ({
                priceId: item.priceId,
                quantity: item.quantity,
              })),
              daysUntilDue: 30, // Default to 30 days
              description: notes || undefined, // Keep this for Stripe
            },
          }
        );

        if (error) throw error;
        onAddInvoice(data);
      } else {
        // Calculate totals
        let totalAmount = 0;
        const lineItems = items.map((item) => {
          const product = products.find((p) => p.id === item.productId);

          // Get the correct price based on billing cycle
          // First, convert the product price to cents (Stripe uses cents)
          const basePrice = product?.price || 0;
          const priceInCents = Math.round(basePrice * 100); // Convert dollars to cents

          // Calculate the item total
          const itemTotal = priceInCents * item.quantity;
          totalAmount += itemTotal;

          console.log(
            `Item: ${product?.name}, Price: ${basePrice}, In cents: ${priceInCents}, Quantity: ${item.quantity}, Total: ${itemTotal}`
          );

          return {
            product_id: item.productId,
            product_name: product?.name || "Unknown Product",
            price: priceInCents, // Store price in cents
            quantity: item.quantity,
            billing_cycle: item.billingCycle,
            amount: itemTotal, // Store amount in cents
          };
        });

        console.log(`Total amount in cents: ${totalAmount}`);

        if (invoice && isEditingDraft) {
          // Update the existing invoice
          const { data, error } = await supabase
            .from("invoices")
            .update({
              user_id: selectedUser.id,
              stripe_customer_id: selectedUser.stripe_customer_id,
              status: invoiceStatus, // Use the selected status
              amount_due: totalAmount, // In cents
              amount_paid: invoiceStatus === "paid" ? totalAmount : 0, // Set amount_paid based on status
              amount_remaining: invoiceStatus === "paid" ? 0 : totalAmount, // Set amount_remaining based on status
              line_items: lineItems, // Store line items as JSON
              customer_email: selectedUser.email,
              customer_name: selectedUser.full_name,
              notes: notes || null,
              payment_method: paymentMethod,
              // Keep existing ref if it exists, otherwise generate a new one
              ref: invoice.ref || generateRef(8),
            })
            .eq("id", invoice.id)
            .select()
            .single();

          if (error) throw error;
          console.log("Invoice updated:", data);

          onAddInvoice(data);
        } else {
          // Create a new manual invoice
          const { data, error } = await supabase
            .from("invoices")
            .insert({
              user_id: selectedUser.id,
              stripe_invoice_id: `manual-${Date.now()}`, // Generate a unique ID
              stripe_customer_id: selectedUser.stripe_customer_id,
              status: invoiceStatus, // Use the selected status
              amount_due: totalAmount, // In cents
              amount_paid: invoiceStatus === "paid" ? totalAmount : 0, // Set amount_paid based on status
              amount_remaining: invoiceStatus === "paid" ? 0 : totalAmount, // Set amount_remaining based on status
              currency: "usd", // Default currency
              due_date: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(), // 30 days from now
              is_manual: true, // Flag to identify manual invoices
              line_items: lineItems, // Store line items as JSON
              customer_email: selectedUser.email,
              customer_name: selectedUser.full_name,
              notes: notes || null,
              payment_method: paymentMethod,
              ref: generateRef(8), // Generate a new 8-character reference
            })
            .select()
            .single();

          if (error) throw error;
          console.log("Invoice created:", data);

          onAddInvoice(data);
        }
      }

      setIsAdding(false);
    } catch (err: any) {
      console.error("Error with invoice:", err);
      handleError(
        `Failed to ${invoice ? "update" : "create"} invoice: ${
          err.message || err.details || "Unknown error"
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCreateUser = async (e: React.FormEvent & { localUser?: any }) => {
    // Only call preventDefault if it exists (it's a real event)
    if (e && typeof e.preventDefault === "function") {
      e.preventDefault();
    }

    setCreatingUser(true);
    setError(null); // Clear local error

    // Use the local state from the modal if available, otherwise use the parent state
    const userToCreate = e.localUser || newUser;

    try {
      // Validation is now handled in the modal component

      // Get the current session for the auth token
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session) {
        throw new Error("You must be logged in to perform this action");
      }

      // Call the Edge Function to create the user with auto-confirmation
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-user`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({
            email: userToCreate.email,
            password: userToCreate.password,
            full_name: userToCreate.full_name,
            auto_confirm: userToCreate.auto_confirm,
          }),
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to create user");
      }

      setSuccess(
        userToCreate.auto_confirm
          ? "User created successfully (email auto-confirmed)"
          : "User created successfully"
      );

      // Set the newly created user as the selected user
      if (result.user && result.user.id) {
        // Add the new user to the users array
        const newUserObj = {
          id: result.user.id,
          email: userToCreate.email,
          full_name: userToCreate.full_name,
          stripe_customer_id: null,
        };

        // Update the users list and select the new user
        onAddUser(newUserObj);
        setSelectedUserId(result.user.id);
      }

      // Reset the form
      setNewUser({
        email: "",
        password: "",
        full_name: "",
        auto_confirm: true,
      });

      // Hide the form after successful creation
      setShowCreateUserModal(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      handleError(errorMessage);

      // Clear error message after 5 seconds
      setTimeout(() => {
        handleError(null);
      }, 5000);
    } finally {
      setCreatingUser(false);
    }
  };

  const CreateUserModal = ({
    showModal,
    setShowModal,
    newUser,
    setNewUser,
    handleCreateUser,
    creatingUser,
    error,
    setError,
  }) => {
    if (!showModal) return null;

    // Create local state to avoid re-renders from parent component
    const [localUser, setLocalUser] = useState(newUser);

    // Sync local state with parent state when modal opens
    useEffect(() => {
      setLocalUser(newUser);
    }, [newUser]);

    // Handle local input changes
    const handleInputChange = (e) => {
      const { name, value, type, checked } = e.target;
      setLocalUser({
        ...localUser,
        [name]: type === "checkbox" ? checked : value,
      });
    };

    // Handle form submission with local state
    const handleSubmit = (e) => {
      e.preventDefault();

      // Validate form locally before submitting
      if (!localUser.email || !localUser.password || !localUser.full_name) {
        setError("All fields are required");
        return;
      }

      if (localUser.password.length < 8) {
        setError("Password must be at least 8 characters long");
        return;
      }

      // Update parent state before submitting
      setNewUser(localUser);

      // Call the parent's handleCreateUser function with just the local state
      // Don't try to extend the event object
      handleCreateUser({ localUser });
    };

    return ReactDOM.createPortal(
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100]">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Create New User
            </h3>
            <button
              onClick={() => setShowModal(false)}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Display error message if present */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 text-sm rounded-md">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="mb-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700"
              >
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={localUser.email}
                onChange={handleInputChange}
                className="mt-1 block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"
                required
              />
            </div>
            <div className="mb-4">
              <label
                htmlFor="full_name"
                className="block text-sm font-medium text-gray-700"
              >
                Full Name
              </label>
              <input
                type="text"
                id="full_name"
                name="full_name"
                value={localUser.full_name}
                onChange={handleInputChange}
                className="mt-1 block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"
                required
              />
            </div>
            <div className="mb-4">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={localUser.password}
                onChange={handleInputChange}
                className="mt-1 block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"
                required
              />
            </div>
            <div className="mb-4">
              <label
                htmlFor="auto_confirm"
                className="block text-sm font-medium text-gray-700"
              >
                Auto Confirm Email
              </label>
              <input
                type="checkbox"
                id="auto_confirm"
                name="auto_confirm"
                checked={localUser.auto_confirm}
                onChange={handleInputChange}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
              />
            </div>
            <div className="flex justify-end">
              <button
                type="button"
                onClick={() => setShowModal(false)}
                className="px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 ml-3"
                disabled={creatingUser}
              >
                {creatingUser ? (
                  <>
                    <Loader2 className="animate-spin h-4 w-4 mr-2" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Create User
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>,
      document.body
    );
  };

  return (
    <div>
      <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex justify-center items-start pt-20 pb-20">
        <div className="relative bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">
              {invoice
                ? isEditingDraft
                  ? "Edit Invoice"
                  : "Update Invoice Status"
                : "Create New Invoice"}
            </h2>
            <button
              onClick={() => setIsAdding(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Add the toggle switch - only show for new invoices, not when editing */}
          {!invoice && (
            <div className="mb-6 flex items-center justify-end">
              <span
                className={`mr-3 text-sm ${
                  !useStripe ? "font-medium" : "text-gray-500"
                }`}
              >
                Manual Invoice
              </span>
              <Switch
                checked={useStripe}
                onChange={setUseStripe}
                className={`${
                  useStripe ? "bg-blue-600" : "bg-gray-200"
                } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
              >
                <span
                  className={`${
                    useStripe ? "translate-x-6" : "translate-x-1"
                  } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                />
              </Switch>
              <span
                className={`ml-3 text-sm ${
                  useStripe ? "font-medium" : "text-gray-500"
                }`}
              >
                Stripe Invoice
              </span>
            </div>
          )}

          {/* Show message when editing non-draft invoice */}
          {invoice && !isEditingDraft && (
            <div className="mb-6 p-4 bg-yellow-50 text-yellow-700 rounded-md">
              <p className="text-sm">
                This invoice is not in draft status. You can only update its
                status.
              </p>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* For non-draft invoice editing, only show the status field */}
            {invoice && !isEditingDraft ? (
              <div className="mb-4 bg-blue-50 p-5 rounded-lg border border-blue-100">
                <div className="mb-4 text-blue-700 text-sm font-medium flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                  You can only update the status of this invoice
                </div>

                <div className="mb-2">
                  <p className="text-sm text-gray-600">
                    <strong>Invoice Ref:</strong>{" "}
                    {invoice.ref || invoice.id.substring(0, 8)}
                  </p>
                  <p className="text-sm text-gray-600">
                    <strong>Customer:</strong>{" "}
                    {invoice.customer_name || invoice.customer_email || "N/A"}
                  </p>
                  <p className="text-sm text-gray-600">
                    <strong>Amount:</strong>{" "}
                    {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: invoice.currency || "usd",
                    }).format((invoice.amount_due || 0) / 100)}
                  </p>
                </div>

                <label
                  htmlFor="invoice_status"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Update Invoice Status
                </label>
                <div className="relative">
                  <select
                    id="invoice_status"
                    name="invoice_status"
                    className="appearance-none block w-full px-3 py-2.5 border border-blue-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                    value={invoiceStatus}
                    onChange={(e) => setInvoiceStatus(e.target.value)}
                    disabled={isSubmitting}
                  >
                    <option value="draft">Draft</option>
                    <option value="open">Open</option>
                    <option value="paid">Paid</option>
                    <option value="void">Void</option>
                    <option value="uncollectible">Uncollectible</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <svg
                      className="h-4 w-4"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>

                {/* Add Update Button for non-draft invoice status changes */}
                <div className="mt-4 flex justify-end">
                  <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="animate-spin h-4 w-4 mr-2" />
                        Updating...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Update Status
                      </>
                    )}
                  </button>
                </div>
              </div>
            ) : (
              <>
                {/* Customer Selection - Searchable Combobox */}
                <div className="mb-4 relative">
                  <div className="flex justify-between items-center mb-1">
                    <label
                      htmlFor="customer"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Customer
                    </label>
                    {(!invoice || (invoice && isEditingDraft)) && (
                      <button
                        type="button"
                        onClick={() => {
                          console.log("Opening create user modal");
                          setShowCreateUserModal(true);
                        }}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700"
                        disabled={invoice && !isEditingDraft}
                      >
                        <UserPlus className="h-3 w-3 mr-1" />
                        New User
                      </button>
                    )}
                  </div>
                  <Combobox
                    as="div"
                    value={selectedUserId}
                    onChange={(value) => setSelectedUserId(value)}
                    disabled={isSubmitting || (invoice && !isEditingDraft)}
                  >
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Search className="h-4 w-4 text-gray-400" />
                      </div>
                      <Combobox.Input
                        className="w-full pl-10 pr-10 py-2 text-base border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        onChange={(event) => setUserQuery(event.target.value)}
                        displayValue={(userId: string) => {
                          const user = users.find((u) => u.id === userId);
                          return user
                            ? `${user.full_name || ""} (${user.email})`
                            : "";
                        }}
                        placeholder="Search users..."
                      />
                      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
                        <X
                          className={`h-5 w-5 text-gray-400 ${
                            selectedUserId ? "visible" : "invisible"
                          }`}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedUserId("");
                            setUserQuery("");
                          }}
                        />
                      </Combobox.Button>
                    </div>
                    <div className="relative">
                      <Combobox.Options className="absolute z-50 mt-1 max-h-40 w-full overflow-y-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                        {filteredUsers.length === 0 && userQuery !== "" ? (
                          <div className="relative cursor-default select-none py-2 px-4 text-gray-700">
                            No users found.
                          </div>
                        ) : (
                          filteredUsers.map((user) => (
                            <Combobox.Option
                              key={user.id}
                              value={user.id}
                              className={({ active }) =>
                                `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                  active
                                    ? "bg-indigo-600 text-white"
                                    : "text-gray-900"
                                }`
                              }
                            >
                              {({ active, selected }) => (
                                <>
                                  <div className="flex items-center">
                                    <span
                                      className={`block truncate ${
                                        selected
                                          ? "font-semibold"
                                          : "font-normal"
                                      }`}
                                    >
                                      {user.full_name || "Unnamed"} (
                                      {user.email})
                                      {!user.stripe_customer_id
                                        ? " (No Stripe ID)"
                                        : ""}
                                    </span>
                                  </div>
                                </>
                              )}
                            </Combobox.Option>
                          ))
                        )}
                      </Combobox.Options>
                    </div>
                  </Combobox>
                  {!selectedCustomerId && selectedUserId && (
                    <p className="mt-1 text-xs text-gray-500">
                      A Stripe Customer ID will be created for this user.
                    </p>
                  )}
                </div>

                {/* Invoice Status Selection - Only for manual invoices when not editing a non-draft invoice */}
                {!useStripe && !(invoice && !isEditingDraft) && (
                  <div className="mb-4">
                    <label
                      htmlFor="invoice_status"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Invoice Status
                    </label>
                    <div className="relative">
                      <select
                        id="invoice_status"
                        name="invoice_status"
                        className="appearance-none block w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                        value={invoiceStatus}
                        onChange={(e) => setInvoiceStatus(e.target.value)}
                        disabled={isSubmitting}
                      >
                        <option value="draft">Draft</option>
                        <option value="open">Open</option>
                        <option value="paid">Paid</option>
                        <option value="void">Void</option>
                        <option value="uncollectible">Uncollectible</option>
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <svg
                          className="h-4 w-4"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                )}

                {/* Global Billing Cycle Toggle - show for all invoices except non-draft edits */}
                {!(invoice && !isEditingDraft) && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Billing Cycle
                    </label>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="monthly"
                          name="billingCycle"
                          value="monthly"
                          checked={globalBillingCycle === "monthly"}
                          onChange={() => {
                            setGlobalBillingCycle("monthly");
                            // Update all existing items to use the new billing cycle
                            if (items.length > 0) {
                              const updatedItems = items.map((item) => {
                                const product = products.find(
                                  (p) => p.id === item.productId
                                );
                                return {
                                  ...item,
                                  billingCycle: "monthly",
                                  priceId:
                                    product?.stripe_monthly_price_id || "",
                                };
                              });
                              setItems(updatedItems);
                            }
                          }}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                          disabled={isSubmitting}
                        />
                        <label
                          htmlFor="monthly"
                          className="text-sm text-gray-700"
                        >
                          Monthly
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="yearly"
                          name="billingCycle"
                          value="yearly"
                          checked={globalBillingCycle === "yearly"}
                          onChange={() => {
                            setGlobalBillingCycle("yearly");
                            // Update all existing items to use the new billing cycle
                            if (items.length > 0) {
                              const updatedItems = items.map((item) => {
                                const product = products.find(
                                  (p) => p.id === item.productId
                                );
                                return {
                                  ...item,
                                  billingCycle: "yearly",
                                  priceId:
                                    product?.stripe_yearly_price_id || "",
                                };
                              });
                              setItems(updatedItems);
                            }
                          }}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                          disabled={isSubmitting}
                        />
                        <label
                          htmlFor="yearly"
                          className="text-sm text-gray-700"
                        >
                          Yearly
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {/* Invoice Items Section */}
                <div className="border-t border-gray-200 pt-4 mt-6">
                  <h4 className="text-lg font-medium text-gray-800 mb-3">
                    Invoice Items
                  </h4>

                  {/* Only show item input for new invoices or draft edits */}
                  {(!invoice || (invoice && isEditingDraft)) && (
                    <div className="flex items-end space-x-3 mb-4">
                      {/* Product Selection - Searchable Combobox */}
                      <div className="flex-grow relative">
                        <label
                          htmlFor="product"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Product
                        </label>
                        <Combobox
                          as="div"
                          value={selectedProduct}
                          onChange={(value) =>
                            setSelectedProduct(value as string)
                          }
                          disabled={
                            isSubmitting || (invoice && !isEditingDraft)
                          }
                        >
                          <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <Search className="h-4 w-4 text-gray-400" />
                            </div>
                            <Combobox.Input
                              className="w-full pl-10 pr-10 py-2 text-base border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                              onChange={(event) =>
                                setProductQuery(event.target.value)
                              }
                              displayValue={(productId: string) => {
                                const product = products.find(
                                  (p) => p.id === productId
                                );
                                return product ? product.name : "";
                              }}
                              placeholder="Search products..."
                            />
                            <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
                              <X
                                className={`h-5 w-5 text-gray-400 ${
                                  selectedProduct ? "visible" : "invisible"
                                }`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedProduct("");
                                  setProductQuery("");
                                }}
                              />
                            </Combobox.Button>
                          </div>
                          <div className="relative">
                            <Combobox.Options className="absolute z-50 mt-1 max-h-40 w-full overflow-y-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                              {filteredProducts.length === 0 &&
                              productQuery !== "" ? (
                                <div className="relative cursor-default select-none py-2 px-4 text-gray-700">
                                  No products found.
                                </div>
                              ) : (
                                filteredProducts.map((product) => (
                                  <Combobox.Option
                                    key={product.id}
                                    value={product.id}
                                    className={({ active }) =>
                                      `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                        active
                                          ? "bg-indigo-600 text-white"
                                          : "text-gray-900"
                                      }`
                                    }
                                  >
                                    {({ active, selected }) => (
                                      <>
                                        <div className="flex flex-col">
                                          <span
                                            className={`block truncate ${
                                              selected
                                                ? "font-semibold"
                                                : "font-normal"
                                            }`}
                                          >
                                            {product.name}
                                          </span>
                                          <span
                                            className={`block truncate text-xs ${
                                              active
                                                ? "text-indigo-200"
                                                : "text-gray-500"
                                            }`}
                                          >
                                            {product.description?.substring(
                                              0,
                                              50
                                            )}
                                            {product.description?.length > 50
                                              ? "..."
                                              : ""}
                                          </span>
                                        </div>
                                      </>
                                    )}
                                  </Combobox.Option>
                                ))
                              )}
                            </Combobox.Options>
                          </div>
                        </Combobox>
                      </div>

                      {/* Quantity Input */}
                      <div className="w-24">
                        <label
                          htmlFor="quantity"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Quantity
                        </label>
                        <input
                          type="number"
                          id="quantity"
                          min="1"
                          value={quantity}
                          onChange={(e) =>
                            setQuantity(parseInt(e.target.value) || 1)
                          }
                          className="mt-1 block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"
                          disabled={isSubmitting}
                        />
                      </div>

                      {/* Add Button */}
                      <button
                        type="button"
                        onClick={handleAddItem}
                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                        disabled={
                          !selectedProduct || quantity <= 0 || isSubmitting
                        }
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add
                      </button>
                    </div>
                  )}

                  {/* Added Items List */}
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {items.map((item, index) => {
                      const product = products.find(
                        (p) => p.id === item.productId
                      );
                      return (
                        <div
                          key={index}
                          className="flex justify-between items-center p-2 bg-gray-50 rounded"
                        >
                          <div className="flex flex-col">
                            <span className="text-sm text-gray-700">
                              {product?.name ?? "Unknown Product"} (Qty:{" "}
                              {item.quantity})
                            </span>
                            <span className="text-xs text-gray-500">
                              Billing:{" "}
                              {item.billingCycle === "monthly"
                                ? "Monthly"
                                : "Yearly"}
                            </span>
                          </div>
                          <button
                            type="button"
                            onClick={() => handleRemoveItem(index)}
                            className="text-red-500 hover:text-red-700"
                            disabled={isSubmitting}
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Payment Method Details - Only show for manual invoices */}
                {!useStripe && (
                  <div className="mt-6 border border-gray-200 rounded-lg p-5 bg-white shadow-sm">
                    <div className="flex items-center mb-4">
                      <div className="bg-blue-100 p-2 rounded-full mr-3">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-blue-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                          <path
                            fillRule="evenodd"
                            d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-800">
                        Payment Method Details
                      </h3>
                    </div>

                    <div className="mb-4">
                      <label
                        htmlFor="payment_type"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Payment Type
                      </label>
                      <div className="relative">
                        <select
                          id="payment_type"
                          name="payment_type"
                          className="appearance-none block w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                          value={paymentMethod.type}
                          onChange={(e) =>
                            setPaymentMethod({
                              ...paymentMethod,
                              type: e.target.value,
                            })
                          }
                          disabled={isSubmitting}
                        >
                          <option value="bank_transfer">Bank Transfer</option>
                          <option value="check">Check</option>
                          <option value="other">Other</option>
                        </select>
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                          <svg
                            className="h-4 w-4"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden="true"
                          >
                            <path
                              fillRule="evenodd"
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>

                    {paymentMethod.type === "bank_transfer" && (
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-4">
                        <div className="mb-4">
                          <label
                            htmlFor="bank_name"
                            className="block text-sm font-medium text-gray-700 mb-1"
                          >
                            Bank Name
                          </label>
                          <input
                            type="text"
                            id="bank_name"
                            name="bank_name"
                            placeholder="Enter bank name"
                            className="block w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                            value={paymentMethod.bank_name}
                            onChange={(e) =>
                              setPaymentMethod({
                                ...paymentMethod,
                                bank_name: e.target.value,
                              })
                            }
                            disabled={isSubmitting}
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label
                              htmlFor="account_number"
                              className="block text-sm font-medium text-gray-700 mb-1"
                            >
                              Account Number
                            </label>
                            <input
                              type="text"
                              id="account_number"
                              name="account_number"
                              placeholder="Enter account number"
                              className="block w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                              value={paymentMethod.account_number}
                              onChange={(e) =>
                                setPaymentMethod({
                                  ...paymentMethod,
                                  account_number: e.target.value,
                                })
                              }
                              disabled={isSubmitting}
                            />
                          </div>

                          <div>
                            <label
                              htmlFor="routing_number"
                              className="block text-sm font-medium text-gray-700 mb-1"
                            >
                              Routing Number
                            </label>
                            <input
                              type="text"
                              id="routing_number"
                              name="routing_number"
                              placeholder="Enter routing number"
                              className="block w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                              value={paymentMethod.routing_number}
                              onChange={(e) =>
                                setPaymentMethod({
                                  ...paymentMethod,
                                  routing_number: e.target.value,
                                })
                              }
                              disabled={isSubmitting}
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {paymentMethod.type === "check" && (
                      <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                        <p className="text-sm text-gray-600">
                          Check payments will be recorded manually. No
                          additional details are required.
                        </p>
                      </div>
                    )}

                    {paymentMethod.type === "cash" && (
                      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                        <p className="text-sm text-gray-600">
                          Cash payments will be recorded manually. No additional
                          details are required.
                        </p>
                      </div>
                    )}

                    {paymentMethod.type === "other" && (
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <p className="text-sm text-gray-600">
                          Other payment methods will be handled according to
                          your company's policies.
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Notes Field - Only show for manual invoices */}
                {!useStripe && (
                  <div className="mt-6 border border-gray-200 rounded-lg p-5 bg-white shadow-sm">
                    <div className="flex items-center mb-4">
                      <div className="bg-green-100 p-2 rounded-full mr-3">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-green-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-800">
                        Invoice Notes
                      </h3>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <label
                        htmlFor="notes"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Add notes or special instructions for this invoice
                        (Optional)
                      </label>
                      <textarea
                        id="notes"
                        name="notes"
                        rows={4}
                        className="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full text-sm border-gray-300 rounded-md bg-white"
                        placeholder="These notes will appear on the invoice PDF between the customer information and the line items"
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        disabled={isSubmitting}
                      ></textarea>
                    </div>
                  </div>
                )}

                {/* Form Actions */}
                <div className="mt-6 pt-4 border-t border-gray-200 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setIsAdding(false)}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    disabled={
                      items.length === 0 || !selectedUserId || isSubmitting
                    }
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="animate-spin h-4 w-4 mr-2" />
                        {invoice ? "Updating..." : "Creating..."}
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        {invoice
                          ? isEditingDraft
                            ? "Update Invoice"
                            : "Update Status"
                          : "Create Invoice"}
                      </>
                    )}
                  </button>
                </div>
              </>
            )}
          </form>
        </div>
      </div>

      {/* User creation modal */}
      <CreateUserModal
        showModal={showCreateUserModal}
        setShowModal={setShowCreateUserModal}
        newUser={newUser}
        setNewUser={setNewUser}
        handleCreateUser={handleCreateUser}
        creatingUser={creatingUser}
        error={error}
        setError={setError}
      />
    </div>
  );
};

export default InvoiceForm;
