import React, { useState } from 'react';
import OurAprochesImg from '../../assets/home/<USER>';
import DataModelingImg from '../../assets/images/soar/Data Modeling and Analytics.png'; // Import the new image
import PacificRimImg from '../../assets/images/soar/Pacific Rim Partners.png'; // Import the Pacific Rim image
import { ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';

function OurAproches() {
  const [currentSlide, setCurrentSlide] = useState(0);

  const approaches = [
    {
      title: "Data Modeling and Analytics",
      description: "Ingesting data, checking its integrity, training and testing models. We are fine tuning models to push the limit on our prediction accuracy to best prepare for any outcome.",
      image: DataModelingImg, // Use the new image here
      link: "https://www.cdc.gov/forecast-outbreak-analytics/partners/insightnet/implementers.html"
    },
    {
      title: "Directing Public Health Workforce",
      description: "Designing and implementing a continuous education program to upskill our current public health workforce and increasing the pipeline of trained workers.",
      image: OurAprochesImg,
      link: "https://soar-forecasting.internationalrespondersystems.net/ "
    },
    {
      title: "Pacific Rim Partners",
      description: "Collaborating with key stakeholders across the Pacific Rim to enhance our forecasting capabilities and response strategies.",
      image: PacificRimImg, // Use the Pacific Rim image here
      link: "/pacific-rim"
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % approaches.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + approaches.length) % approaches.length);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  return (
    <div className="relative py-20 bg-gray-50">
      <div className="max-w-[1500px] mx-auto px-6">
        {/* Header Section */}
        <div className="text-center mb-16">
          <h1 className="text-3xl md:text-5xl font-bold text-gray-800 mb-5">Our Approach</h1>
          <p className="text-lg md:text-2xl text-gray-600 font-medium">Innovate • Integrate • Implement</p>
        </div>

        {/* Desktop Grid - Hidden on mobile */}
        <div className="hidden md:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16">
          {approaches.map((approach, index) => (
            <div
              key={index}
              className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 flex flex-col h-full"
            >
              {/* Image Section */}
              <div className="h-64 overflow-hidden flex-shrink-0">
                <img
                  src={approach.image}
                  alt={approach.title}
                  className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>

              {/* Content Section */}
              <div className="p-8 flex flex-col flex-grow">
                <h3 className="text-xl font-bold text-gray-800 mb-4 leading-tight">
                  {approach.title}
                </h3>
                <p className="text-gray-600 leading-relaxed mb-6 text-base flex-grow">
                  {approach.description}
                </p>
                <Link to={approach.link} target="_blank" className="inline-flex items-center gap-2 px-6 py-3 border-2 border-gray-500 font-semibold rounded-md transition-all duration-300 hover:translate-x-1 self-start mt-auto">
                  Learn More
                  <span className="transition-transform duration-300 group-hover:translate-x-1">
                    <ArrowRight size={20} />
                  </span>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile Carousel - Visible only on mobile */}
        <div className="md:hidden mt-16">
          <div className="relative">
            {/* Carousel Container */}
            <div className="overflow-hidden rounded-xl">
              <div
                className="flex transition-transform duration-300 ease-in-out"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
              >
                {approaches.map((approach, index) => (
                  <div key={index} className="w-full flex-shrink-0">
                    <div className="bg-white rounded-xl overflow-hidden shadow-lg mx-2">
                      {/* Image Section */}
                      <div className="h-48 overflow-hidden">
                        <img
                          src={approach.image}
                          alt={approach.title}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Content Section */}
                      <div className="p-6">
                        <h3 className="text-lg font-bold text-gray-800 mb-3 leading-tight">
                          {approach.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed mb-4 text-sm">
                          {approach.description}
                        </p>
                        <Link to={approach.link} className="inline-flex items-center gap-2 px-4 py-2 border-2 border-gray-500 font-semibold rounded-md transition-all duration-300 text-sm">
                          Learn More
                          <ArrowRight size={16} />
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={prevSlide}
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-all duration-300"
              disabled={currentSlide === 0}
            >
              <ChevronLeft size={24} className={currentSlide === 0 ? 'text-gray-400' : 'text-gray-700'} />
            </button>

            <button
              onClick={nextSlide}
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-all duration-300"
              disabled={currentSlide === approaches.length - 1}
            >
              <ChevronRight size={24} className={currentSlide === approaches.length - 1 ? 'text-gray-400' : 'text-gray-700'} />
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-6 space-x-2">
            {approaches.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${currentSlide === index
                  ? 'bg-blue-500 scale-110'
                  : 'bg-gray-300 hover:bg-gray-400'
                  }`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default OurAproches;