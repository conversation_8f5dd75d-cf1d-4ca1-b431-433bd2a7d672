import React, { useState, useEffect } from 'react';
import { Library, Search, Tag, Clock, ArrowRight, MapPin, Download } from 'lucide-react';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';
import VedioBanner from '../assets/video/resources.mp4';

interface FeaturedItem {
  id: string;
  title: string;
  description?: string;
  excerpt?: string;
  content?: string;
  category: string;
  created_at: string;
  image?: string;
  featured_image?: string;
  image_url?: string;
  organization?: string;
  impact?: string;
  date?: string;
  time?: string;
  location?: string;
  type?: string;
  pdf_url?: string;
  download_count?: number;
}

export default function Resources() {
  const [featuredItems, setFeaturedItems] = useState<FeaturedItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const resourceCategories = [
    {
      name: 'Blogs',
      path: '/blog',
      description: 'Latest articles and insights from our experts',
      table: 'blog_posts'
    },
    {
      name: 'Guides',
      path: '/guides',
      description: 'Step-by-step instructions and best practices',
      table: 'guides'
    },
    {
      name: 'Case Studies',
      path: '/case-studies',
      description: 'Real-world examples of our solutions in action',
      table: 'case_studies'
    },
    {
      name: 'Webinars',
      path: '/webinars',
      description: 'Educational sessions and presentations',
      table: 'webinars'
    },
    {
      name: 'Whitepapers',
      path: '/whitepapers',
      description: 'In-depth reports and research documents',
      table: 'whitepapers'
    },
    {
      name: 'Events',
      path: '/events',
      description: 'Upcoming conferences and training',
      table: 'events'
    }
  ];

  useEffect(() => {
    const fetchFeaturedItems = async () => {
      try {
        const items: FeaturedItem[] = [];

        for (const category of resourceCategories) {
          let query = supabase
            .from(category.table)
            .select('*')
            .order('created_at', { ascending: false })
            .limit(1);

          // Special handling for each resource type
          if (category.table === 'blog_posts') {
            query = query.eq('status', 'published');
          } else if (category.table === 'case_studies') {
            query = query.eq('is_public', true);
          }

          const { data, error } = await query;

          if (data && data.length > 0) {
            const item = {
              ...data[0],
              category: category.name,
              path: category.path
            };

            // Map different image field names to a common 'image' field
            if (item.featured_image) {
              item.image = item.featured_image;
            } else if (item.image_url) {
              item.image = item.image_url;
            }

            items.push(item);
          }
        }

        setFeaturedItems(items);
      } catch (error) {
        console.error('Error fetching featured items:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedItems();
  }, []);

  const [searchResults, setSearchResults] = useState<FeaturedItem[]>([]);
  const [showResults, setShowResults] = useState(false);

  // Add debounce function
  const debounce = (func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  };

  // Debounced search function
  const debouncedSearch = debounce(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    try {
      const searchPromises = resourceCategories.map(category =>
        supabase
          .from(category.table)
          .select('*')
          .ilike('title', `%${query}%`)
          .limit(5)
      );

      const results = await Promise.all(searchPromises);

      const foundItems: FeaturedItem[] = [];
      results.forEach(({ data }, i) => {
        if (data && data.length > 0) {
          data.forEach(item => {
            foundItems.push({
              ...item,
              category: resourceCategories[i].name,
              path: resourceCategories[i].path
            });
          });
        }
      });

      setSearchResults(foundItems);
      setShowResults(foundItems.length > 0);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
      setShowResults(false);
    }
  }, 300); // 300ms delay

  // Update the input onChange handler
  return (
    <div className=" bg-gray-50">
      {/* Hero Section */}
      <section className="text-white py-24 relative rounded-lg" style={{ background: 'radial-gradient(50% 55% at 0% 0%, #3B82F6 0%, #6366F1 100%)' }}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Small decorative dots */}
          <div className="absolute top-32 right-20 w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-32 w-3 h-3 bg-blue-200 rounded-full opacity-60 animate-pulse delay-75"></div>
          <div className="absolute bottom-32 right-24 w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-150"></div>
          <div className="absolute bottom-20 right-40 w-1 h-1 bg-blue-100 rounded-full animate-pulse delay-300"></div>

          {/* Geometric shapes */}
          <div className="absolute top-16 right-16 w-12 h-12 border border-white/20 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-24 right-64 w-8 h-8 border border-white/10 rotate-12 animate-bounce"></div>

          {/* Additional decorative lines */}
          <div className="absolute top-1/3 right-0 w-px h-20 bg-gradient-to-b from-transparent via-white/20 to-transparent"></div>
          <div className="absolute top-2/3 right-12 w-px h-16 bg-gradient-to-b from-transparent via-white/15 to-transparent"></div>
        </div>

        {/* Creative illustration on the right - Made bigger */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block overflow-hidden">
          {/* Main illustration container */}
          <div className="relative h-full flex items-center justify-center">
            {/* Digital library visualization - Scaled up */}
            <div className="relative scale-150">
              {/* Floating documents/books - Made bigger */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float">
                <div className="p-3">
                  <div className="w-full h-2.5 bg-blue-300/50 rounded mb-1.5"></div>
                  <div className="w-3/4 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-2/3 h-1 bg-blue-100/50 rounded"></div>
                </div>
              </div>

              <div className="absolute -top-6 right-10 w-18 h-22 bg-white/15 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed">
                <div className="p-2.5">
                  <div className="w-full h-2 bg-blue-400/50 rounded mb-1.5"></div>
                  <div className="w-2/3 h-1.5 bg-blue-300/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-300/50 rounded mb-1"></div>
                  <div className="w-3/4 h-1 bg-blue-200/50 rounded"></div>
                </div>
              </div>

              {/* Central knowledge hub - Made bigger */}
              <div className="w-40 h-40 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center relative animate-pulse-slow">
                <div className="w-28 h-28 bg-white/30 rounded-full flex items-center justify-center">
                  <Library className="h-14 w-14 text-white" />
                </div>

                {/* Orbiting elements - Made bigger */}
                <div className="absolute inset-0 animate-spin-slow">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-300 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -right-3 transform -translate-y-1/2 w-4 h-4 bg-blue-200 rounded-full opacity-60"></div>
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-400 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -left-3 transform -translate-y-1/2 w-4 h-4 bg-blue-100 rounded-full opacity-60"></div>
                </div>
              </div>

              {/* Floating resource icons - Made bigger */}
              <div className="absolute -bottom-8 -left-8 w-16 h-16 bg-white/15 rounded-lg backdrop-blur-sm flex items-center justify-center transform -rotate-12 animate-bounce-slow">
                <Search className="h-8 w-8 text-white" />
              </div>

              <div className="absolute bottom-3 right-6 w-14 h-14 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center animate-float">
                <Download className="h-7 w-7 text-white" />
              </div>

              <div className="absolute top-10 -left-10 w-18 h-18 bg-white/10 rounded-xl backdrop-blur-sm flex items-center justify-center transform rotate-45 animate-pulse">
                <Tag className="h-8 w-8 text-white transform -rotate-45" />
              </div>
            </div>

            {/* Data flow lines - Made bigger */}
            <div className="absolute inset-0 pointer-events-none scale-125">
              <svg className="w-full h-full opacity-20" viewBox="0 0 400 400">
                <defs>
                  <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="rgba(255,255,255,0)" />
                    <stop offset="50%" stopColor="rgba(255,255,255,0.3)" />
                    <stop offset="100%" stopColor="rgba(255,255,255,0)" />
                  </linearGradient>
                </defs>
                <path
                  d="M50,200 Q200,100 350,200 Q200,300 50,200"
                  fill="none"
                  stroke="url(#gradient1)"
                  strokeWidth="3"
                  className="animate-pulse"
                />
                <path
                  d="M100,150 Q200,50 300,150"
                  fill="none"
                  stroke="url(#gradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-500"
                />
                <path
                  d="M100,250 Q200,350 300,250"
                  fill="none"
                  stroke="url(#gradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-1000"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Professional icons on the right side (adjusted position) */}
        <div className="absolute right-8 top-1/2 transform -translate-y-1/2 hidden xl:flex flex-col space-y-6 opacity-15 z-10">
          <div className="p-3 bg-white/10 rounded-full backdrop-blur-sm animate-float">
            <Clock className="h-6 w-6 text-white" />
          </div>
          <div className="p-3 bg-white/10 rounded-full backdrop-blur-sm animate-float-delayed">
            <MapPin className="h-6 w-6 text-white" />
          </div>
        </div>

        {/* Content pushed to the left */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Resources Library
              <span className="block text-3xl font-normal mt-2 text-blue-200">
                Your Knowledge Hub
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Discover our comprehensive library of resources to enhance your public health emergency response capabilities
            </p>

            {/* Search Bar with dropdown */}
            <form onSubmit={(e) => {
              e.preventDefault();
              debouncedSearch(searchQuery);
            }} className="relative max-w-xl">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-400 z-50" />
                <input
                  type="text"
                  placeholder="Search resources..."
                  className="w-full pl-12 pr-4 py-4 rounded-lg bg-white/20 backdrop-blur-sm border border-white/30 text-white placeholder-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300 focus:bg-white/30"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    debouncedSearch(e.target.value);
                  }}
                  onFocus={() => searchResults.length > 0 && setShowResults(true)}
                  onBlur={() => setTimeout(() => setShowResults(false), 200)}
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-300 hover:text-white transition-colors duration-300"
                  aria-label="Search"
                >
                  <ArrowRight className="h-5 w-5" />
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Search results dropdown - moved outside hero section */}
      {showResults && searchResults.length > 0 && (
        <div className="relative z-50 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-14">
          <div className="max-w-xl bg-white rounded-lg shadow-lg max-h-96 overflow-y-auto">
            {searchResults.map((result) => (
              <div
                key={`${result.category}-${result.id}`}
                className="p-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                onMouseDown={(e) => {
                  e.preventDefault();
                  navigate(`${result.path}/${result.id}`);
                  setShowResults(false);
                }}
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0 mr-3">
                    {result.image && (
                      <img
                        src={result.image}
                        alt={result.title}
                        className="h-10 w-10 rounded object-cover"
                      />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{result.title}</p>
                    <p className="text-sm text-gray-500">{result.category}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add custom animations */}
      <style jsx>{`
                                                        @keyframes float {
                                                          0%, 100% { transform: translateY(0px) rotate(12deg); }
                                                          50% { transform: translateY(-10px) rotate(12deg); }
                                                        }
                                                        @keyframes float-delayed {
                                                          0%, 100% { transform: translateY(0px) rotate(-6deg); }
                                                          50% { transform: translateY(-8px) rotate(-6deg); }
                                                        }
                                                        @keyframes spin-slow {
                                                          from { transform: rotate(0deg); }
                                                          to { transform: rotate(360deg); }
                                                        }
                                                        @keyframes bounce-slow {
                                                          0%, 100% { transform: translateY(0px) rotate(-12deg); }
                                                          50% { transform: translateY(-5px) rotate(-12deg); }
                                                        }
                                                        @keyframes pulse-slow {
                                                          0%, 100% { opacity: 1; }
                                                          50% { opacity: 0.7; }
                                                        }
                                                        .animate-float { animation: float 3s ease-in-out infinite; }
                                                        .animate-float-delayed { animation: float-delayed 3s ease-in-out infinite 1s; }
                                                        .animate-spin-slow { animation: spin-slow 20s linear infinite; }
                                                        .animate-bounce-slow { animation: bounce-slow 2s ease-in-out infinite; }
                                                        .animate-pulse-slow { animation: pulse-slow 4s ease-in-out infinite; }
                                                      `}</style>
      {/* Categories Section - More visual hierarchy */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Browse by Category</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore our resources organized by topic
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {resourceCategories.map((category) => (
              <Link
                key={category.name}
                to={category.path}
                className="group p-8 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-100"
              >
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-lg bg-blue-50 text-blue-600 mr-4">
                    <Tag className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {category.name}
                  </h3>
                </div>
                <p className="text-gray-600 pl-14">{category.description}</p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Items Section - Enhanced card design */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Featured Resources</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Handpicked content from our experts to get you started
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {featuredItems.map((item) => (
                <div
                  key={`${item.category}-${item.id}`}
                  className="relative bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1 flex flex-col"
                >
                  <span className="absolute inline-flex items-center w-auto top-1 right-1 px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-3">
                    {item.category}
                  </span>
                  {item.image && (
                    <div className="h-48 overflow-hidden">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-full object-cover transition-transform duration-500 "
                      />
                    </div>
                  )}
                  <div className="p-6 flex-1 flex flex-col">

                    <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>

                    <p className="text-gray-600 mb-4 line-clamp-2 flex-1">
                      {item.description || item.excerpt}
                    </p>

                    {/* Metadata section */}
                    <div className="space-y-2 mb-4">
                      {item.date && (
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-2" />
                          {item.date}
                        </div>
                      )}
                      {item.location && (
                        <div className="flex items-center text-sm text-gray-500">
                          <MapPin className="h-4 w-4 mr-2" />
                          {item.location}
                        </div>
                      )}
                    </div>

                    <div className="mt-auto">
                      {item.category === 'Guides' && item.pdf_url ? (
                        <button
                          onClick={() => {
                            const link = document.createElement('a');
                            link.href = item.pdf_url;
                            link.download = item.pdf_url.split('/').pop() || 'guide.pdf';
                            link.target = '_blank';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                          className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download PDF
                        </button>
                      ) : item.category === 'Events' ? (
                        <Link
                          to="/events"
                          className="w-full flex items-center justify-center px-4 py-2 bg-white border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          View Events
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Link>
                      ) : (
                        <Link
                          to={`${item.path}/${item.id}`}
                          className="w-full flex items-center justify-center px-4 py-2 bg-white border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          View Details
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>


    </div>
  );
}