import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

// Import the new images
import DoctorOfficeImage from '../../assets/images/capabilities/capability-1.png';
import VRTrainingImage from '../../assets/images/capabilities/capability-2.png';
import TeamMeetingImage from '../../assets/images/capabilities/capability-3.png';

import { ArrowRight } from 'lucide-react';

const Capabilities: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const [allCardsExpanded, setAllCardsExpanded] = useState(false);

  // Handle window resize to detect mobile view
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Touch handlers for swipe functionality
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && isMobile) {
      nextSlide();
    }
    if (isRightSwipe && isMobile) {
      prevSlide();
    }
  };

  // Toggle all cards expansion
  const toggleAllCardsExpansion = () => {
    setAllCardsExpanded(prev => !prev);
  };

  const capabilities = [
    {
      id: 1,
      title: "Public Health Development",
      image: DoctorOfficeImage,
      shortDescription: "Advanced public health system development with cutting-edge technology solutions. Our expert team creates robust, scalable platforms...",
      fullDescription: "Advanced public health system development with cutting-edge technology solutions. Our expert team creates robust, scalable platforms that enhance population health management and streamline public health workflows. We specialize in building comprehensive public health management systems, electronic health records (EHR), population monitoring solutions, and telehealth platforms. Our development process follows industry best practices and regulatory compliance standards including HIPAA, ensuring data security and privacy.",
      readMore: "READ MORE"
    },
    {
      id: 2,
      title: "Virtual Training",
      image: VRTrainingImage,
      shortDescription: "Immersive virtual reality training programs for public health professionals. Experience realistic scenarios in a safe, controlled environment...",
      fullDescription: "Immersive virtual reality training programs for public health professionals. Experience realistic scenarios in a safe, controlled environment that enhances learning and skill development. Our VR training modules cover emergency response procedures, public health interventions, community engagement protocols, and equipment operation. The platform provides hands-on experience without real-world risks, allowing public health workers to practice complex procedures repeatedly until they achieve mastery.",
      readMore: "READ MORE"
    },
    {
      id: 3,
      title: "Team Management",
      image: TeamMeetingImage,
      shortDescription: "Comprehensive public health team coordination and management solutions. Streamline communication, optimize workflows, and ensure seamless collaboration...",
      fullDescription: "Comprehensive public health team coordination and management solutions. Streamline communication, optimize workflows, and ensure seamless collaboration across all departments. Our team management platform includes real-time communication tools, task assignment and tracking, shift scheduling, resource allocation, and performance monitoring. The system integrates with existing public health infrastructure to provide a unified view of operations.",
      readMore: "READ MORE"
    },
    {
      id: 4,
      title: "System Assessment",
      image: DoctorOfficeImage,
      shortDescription: "Thorough evaluation and assessment of public health systems and processes. Identify optimization opportunities and implement data-driven improvements...",
      fullDescription: "Thorough evaluation and assessment of public health systems and processes. Identify optimization opportunities and implement data-driven improvements for better population outcomes. Our assessment services include comprehensive audits of existing systems, workflow analysis, security evaluations, and compliance reviews. We use advanced analytics and industry benchmarks to identify areas for improvement and provide detailed recommendations.",
      readMore: "READ MORE"
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % capabilities.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + capabilities.length) % capabilities.length);
  };

  const getVisibleCards = () => {
    const visibleCards = [];
    const cardsToShow = isMobile ? 1 : 3;

    for (let i = 0; i < cardsToShow; i++) {
      const index = (currentSlide + i) % capabilities.length;
      visibleCards.push(capabilities[index]);
    }
    return visibleCards;
  };

  return (
    <section className="py-16 md:py-24 bg-[#EDEFFE] font-inter">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-12">
          <div className="lg:max-w-2xl">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-black mb-4">
              Capabilities
            </h2>
            <p className="text-lg text-gray-600 leading-relaxed">
              We support a diverse range of public and private sector client services with responsive
              and resilient systems at competitive
            </p>
          </div>

          {/* Navigation Arrow */}
          <div className="mt-6 lg:mt-0">
            <button
              onClick={nextSlide}
              className="p-3"
            >
              <svg width="87" height="31" viewBox="0 0 87 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M46.8884 18.9713C34.6184 20.34 2.37234 23.0936 1.36735 22.8585C0.139699 22.5716 -0.122223 21.2388 0.903631 20.4984C2.15374 19.5959 7.7863 18.7397 29.6338 16.1297C50.2404 13.668 59.8876 12.3469 66.4875 11.0837L70.4594 10.3238L67.5417 8.48551C64.2579 6.41668 59.3738 2.45547 59.1702 1.69579C59.0965 1.42075 59.1613 1.06266 59.3142 0.900621C59.7865 0.399103 68.3407 3.00737 74.9077 5.65542C78.4077 7.0663 81.857 8.17846 82.7474 8.18246C86.4851 8.2002 87.904 11.155 85.3102 13.5189C83.9319 14.7751 64.431 26.1162 59.1962 28.7064C54.5007 31.0293 53.993 31.0918 52.4576 29.5345C51.7224 28.7887 51.6766 28.5617 52.039 27.4612C52.5967 25.7685 53.8434 24.8875 62.4111 20.1332C70.8917 15.4278 70.9225 15.5052 61.2017 17.0689C57.7891 17.6175 51.3482 18.474 46.8884 18.9713Z" fill="black" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile swipe hint */}
        {isMobile && (
          <div className="text-center mb-4">
            <p className="text-sm text-gray-500">← Swipe to navigate →</p>
          </div>
        )}

        {/* Cards Container */}
        <div
          className="relative overflow-hidden"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div className={`grid gap-6 mb-8 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`}>
            {getVisibleCards().map((capability, index) => (
              <div
                key={`${capability.id}-${currentSlide}`}
                className={`bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 ease-in-out transform hover:-translate-y-1 flex flex-col ${allCardsExpanded ? 'h-auto' : 'h-full'
                  }`}
                style={{
                  animationDelay: `${index * 0.1}s`,
                  animation: 'fadeInUp 0.6s ease-out forwards'
                }}
              >
                {/* Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={capability.image}
                    alt={capability.title}
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  />
                </div>

                {/* Content */}
                <div className="p-6 flex flex-col flex-grow">
                  <h3 className="text-xl font-bold text-black mb-3">
                    {capability.title}
                  </h3>
                  <div className="text-gray-600 text-sm leading-relaxed mb-4 flex-grow">
                    <div className="overflow-hidden">
                      <p className="transition-all duration-500 ease-in-out">
                        {allCardsExpanded ? capability.fullDescription : capability.shortDescription}
                      </p>
                      {/* {allCardsExpanded && (
                        <div className="mt-4 animate-slideDown">
                          <div className="p-4 bg-gray-50 rounded-lg transform transition-all duration-500 ease-in-out">
                            <h4 className="font-semibold text-black mb-2">Key Features:</h4>
                            <ul className="list-disc list-inside space-y-1 text-gray-600">
                              <li>Advanced technology integration</li>
                              <li>Regulatory compliance standards</li>
                              <li>Real-time monitoring and analytics</li>
                              <li>Scalable and secure architecture</li>
                            </ul>
                          </div>
                        </div>
                      )} */}
                    </div>
                  </div>

                  {/* Read More Button - Aligned to bottom */}
                  <button
                    onClick={toggleAllCardsExpansion}
                    className="flex items-center text-black font-semibold text-sm hover:text-gray-700 transition-all duration-300 group mt-auto transform hover:scale-105"
                  >
                    <span className="transition-all duration-300 ease-in-out">
                      {allCardsExpanded ? 'READ LESS' : capability.readMore}
                    </span>
                    <div className="ml-2 w-6 h-6 bg-black rounded-full flex items-center justify-center group-hover:bg-gray-700 transition-all duration-300 transform group-hover:scale-110">
                      <svg
                        className={`w-3 h-3 text-white transition-all duration-500 ease-in-out transform ${allCardsExpanded ? 'rotate-180 scale-110' : 'rotate-0 scale-100'
                          }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Navigation and CTA */}
        <div className="flex flex-col sm:flex-row items-center justify-between">
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6 sm:mb-0">
            <Link
              to="/solutions"
              className="bg-black text-white p-1 pl-2 capitalize rounded-lg font-normal text-sm md:text-base hover:bg-gray-800 transition-colors duration-200 flex items-center justify-center sm:justify-start text-center">

              <span className='flex items-center justify-center p-2'>
                Browse Our Services
              </span>
              <span className='bg-white text-black h-full py-2 px-3 rounded-md flex items-center justify-center'>
                <ArrowRight size={22} />
              </span>
            </Link>
            <Link
              to="/book-a-demo"
              className="bg-transparent text-sm md:text-base border-black border-2 text-black p-1 pl-2 capitalize rounded-lg font-normal hover:bg-gray-800 transition-colors hover:text-white duration-200 flex items-center justify-center sm:justify-start text-center">

              <span className='flex items-center justify-center p-2 capitalize'>
                Book a demo
              </span>
              <span className='bg-white text-black h-full py-2 px-3 rounded-md flex items-center justify-center'>
                <ArrowRight className="" size={22} />
              </span>
            </Link>
          </div>

          {/* Slide Navigation */}
          <div className="flex items-center gap-4">
            {/* Mobile navigation with larger touch targets */}
            {isMobile ? (
              <>
                <button
                  onClick={prevSlide}
                  className="p-3 bg-gray-200 text-gray-600 hover:bg-gray-300 hover:text-black rounded-full transition-colors duration-200"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>

                {/* Larger indicators for mobile */}
                <div className="flex gap-3">
                  {capabilities.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`w-3 h-3 rounded-full transition-colors duration-200 ${index === currentSlide ? 'bg-black' : 'bg-gray-300'
                        }`}
                    />
                  ))}
                </div>

                <button
                  onClick={nextSlide}
                  className="p-3 bg-gray-200 text-gray-600 hover:bg-gray-300 hover:text-black rounded-full transition-colors duration-200"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </>
            ) : (
              /* Desktop navigation */
              <>
                <button
                  onClick={prevSlide}
                  className="p-2 text-gray-400 hover:text-black transition-colors duration-200"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>

                {/* Slide Indicators */}
                <div className="flex gap-2">
                  {capabilities.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`w-2 h-2 rounded-full transition-colors duration-200 ${index === currentSlide ? 'bg-black' : 'bg-gray-300'
                        }`}
                    />
                  ))}
                </div>

                <button
                  onClick={nextSlide}
                  className="p-2 text-gray-400 hover:text-black transition-colors duration-200"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Animation Styles */}
      <style>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
            max-height: 0;
          }
          to {
            opacity: 1;
            transform: translateY(0);
            max-height: 200px;
          }
        }

        .animate-slideDown {
          animation: slideDown 0.5s ease-in-out forwards;
        }
      `}</style>
    </section>
  );
};

export default Capabilities;
