import React, { useState } from 'react';
import { Combobox } from '@headlessui/react';
import { supabase } from '../../../lib/supabase';
import { User, Plan, Subscription } from './types';

interface SubscriptionFormProps {
    isAdding: boolean;
    setIsAdding: (value: boolean) => void;
    users: User[];
    products: Plan[];
    onAddSubscription: (newSub: Subscription) => void;
    setError: (error: string | null) => void;
}

const SubscriptionForm: React.FC<SubscriptionFormProps> = ({
    isAdding,
    setIsAdding,
    users,
    products,
    onAddSubscription, // Renamed from handleAddNew to match prop name
    setError
}) => {
    // Move state inside the component
    const [newSubscription, setNewSubscription] = useState<Partial<Subscription> & { product_ids?: string[] }>({
        status: 'active',
        period: 'monthly',
        quantity: 1,
        cancel_at_period_end: false,
        deleted: false,
        product_ids: [], // Use an array for product IDs
        stripe_subscription_id: '', // Add stripe subscription ID field
    });
    const [userQuery, setUserQuery] = useState('');

    // Helper function to generate random string (if needed within this component)
    const generateRef = (length: number): string => {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        const charactersLength = characters.length;
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * charactersLength));
        }
        return result;
    };


    const filteredUsers = userQuery === ''
        ? users
        : users.filter((user) =>
            user.email.toLowerCase().includes(userQuery.toLowerCase())
        );

    // This function now handles the logic previously in the parent
    const handleSave = async () => {
        try {
            setError(null); // Clear previous errors
            // Validate required fields
            if (!newSubscription.user_id || !newSubscription.product_ids || newSubscription.product_ids.length === 0 || !newSubscription.period) {
                setError('User, at least one Plan, and Period are required');
                return;
            }

            // 1. Generate Ref
            const subscriptionRef = generateRef(8);

            // 2. Calculate Dates
            const currentPeriodStart = new Date();
            let currentPeriodEnd = new Date(currentPeriodStart);
            if (newSubscription.period === 'monthly') {
                currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1);
            } else if (newSubscription.period === 'yearly') {
                currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1);
            }

            // 3. Prepare Subscription Data (omit product_id)
            const subscriptionToInsert = {
                user_id: newSubscription.user_id,
                status: newSubscription.status || 'active',
                period: newSubscription.period,
                current_period_start: currentPeriodStart.toISOString(),
                current_period_end: currentPeriodEnd.toISOString(),
                cancel_at_period_end: newSubscription.cancel_at_period_end || false,
                quantity: newSubscription.quantity || 1, // Quantity for the main subscription
                deleted: newSubscription.deleted || false,
                ref: subscriptionRef, // Add generated ref
                stripe_subscription_id: newSubscription.stripe_subscription_id || null, // Add stripe ID (or null)
            };

            // 4. Insert into subscriptions table
            const { data: insertedSubData, error: subInsertError } = await supabase
                .from('subscriptions')
                .insert(subscriptionToInsert)
                .select()
                .single();

            if (subInsertError) throw subInsertError;
            if (!insertedSubData) throw new Error("Subscription data not returned after insert.");

            const newSubscriptionId = insertedSubData.id;

            // 5. Fetch Product Details (including Stripe Price IDs) for selected products
            const { data: selectedProductsData, error: productsFetchError } = await supabase
                .from('products')
                .select('id, name, stripe_monthly_price_id, stripe_yearly_price_id')
                .in('id', newSubscription.product_ids);

            if (productsFetchError) throw productsFetchError;
            if (!selectedProductsData || selectedProductsData.length !== newSubscription.product_ids.length) {
                throw new Error("Could not fetch all selected product details.");
            }

            // 6. Prepare Subscription Items Data
            const itemsToInsert = selectedProductsData.map(product => {
                const stripe_price_id = newSubscription.period === 'monthly'
                    ? product.stripe_monthly_price_id
                    : product.stripe_yearly_price_id;

                if (!stripe_price_id) {
                    console.warn(`Stripe Price ID for period '${newSubscription.period}' not found for product ${product.name} (${product.id}). Skipping item.`);
                    return null; // Skip this item if price ID is missing
                }

                return {
                    subscription_id: newSubscriptionId,
                    product_id: product.id,
                    stripe_price_id: stripe_price_id,
                    quantity: 1, // Default quantity for each item to 1
                };
            }).filter(item => item !== null);

            if (itemsToInsert.length === 0 && newSubscription.product_ids.length > 0) {
                await supabase.from('subscriptions').delete().eq('id', newSubscriptionId); // Rollback subscription insert
                throw new Error("Could not create subscription items. Check if products have valid Stripe Price IDs for the selected period.");
            }

            // 7. Insert into subscription_items table
            if (itemsToInsert.length > 0) {
                const { error: itemsInsertError } = await supabase
                    .from('subscription_items')
                    .insert(itemsToInsert as any);

                if (itemsInsertError) {
                    await supabase.from('subscriptions').delete().eq('id', newSubscriptionId);
                    throw itemsInsertError;
                }
            }

            // 8. Fetch User Email for display
            const { data: userData } = await supabase
                .from('profiles')
                .select('email')
                .eq('id', newSubscription.user_id)
                .single();

            // 9. Construct the full new subscription object for callback
            const finalNewSub: Subscription = {
                ...insertedSubData,
                user_id: newSubscription.user_id,
                period: newSubscription.period,
                user_email: userData?.email || 'Unknown',
                plan_name: selectedProductsData.map(p => p.name).join(', ') || 'N/A',
                items: itemsToInsert.map(item => ({
                    ...item,
                    id: '', // Placeholder
                    created_at: new Date().toISOString(), // Placeholder
                    plan_name: selectedProductsData.find(p => p.id === item?.product_id)?.name || 'Unknown'
                })) as any[], // Cast needed
            };

            // 10. Call the callback prop
            onAddSubscription(finalNewSub);
            setIsAdding(false); // Close the form

            // 11. Reset Form state
            setNewSubscription({
                status: 'active',
                period: 'monthly',
                quantity: 1,
                cancel_at_period_end: false,
                deleted: false,
                product_ids: [],
                stripe_subscription_id: '',
                user_id: undefined,
            });
            setUserQuery('');
        } catch (err) {
            console.error('Error adding subscription:', err);
            const message = err instanceof Error ? err.message : 'Failed to add subscription';
            setError(`Failed to add subscription: ${message}`);
        }
    };


    // Only render the form if isAdding is true
    if (!isAdding) {
        return null;
    }

    return (
        <div className="mb-6 p-4 border rounded-lg bg-gray-50">
            <h3 className="text-lg font-semibold mb-4">Add New Subscription</h3>
            {/* Use the grid layout and inputs from previous version */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                {/* User Combobox */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">User</label>
                    <Combobox
                        as="div"
                        value={newSubscription.user_id || ''}
                        onChange={(value) => setNewSubscription({ ...newSubscription, user_id: value })}
                    >
                        <div className="relative">
                            <Combobox.Input
                                className="w-full p-2 border rounded-lg"
                                onChange={(event) => setUserQuery(event.target.value)}
                                displayValue={(userId) => users.find(u => u.id === userId)?.email || ''}
                                placeholder="Search users..."
                            />
                            <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                {filteredUsers.length === 0 && userQuery !== '' ? (
                                    <div className="relative cursor-default select-none py-2 px-4 text-gray-700">
                                        Nothing found.
                                    </div>
                                ) : (
                                    filteredUsers.map((user) => (
                                        <Combobox.Option
                                            key={user.id}
                                            value={user.id}
                                            className={({ active }) =>
                                                `relative cursor-default select-none py-2 pl-3 pr-9 ${active ? 'bg-blue-600 text-white' : 'text-gray-900'}`
                                            }
                                        >
                                            {user.email}
                                        </Combobox.Option>
                                    ))
                                )}
                            </Combobox.Options>
                        </div>
                    </Combobox>
                </div>

                {/* Plan Selection (Checkboxes) */}
                <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Plans</label>
                    <div className="max-h-40 overflow-y-auto border rounded-lg p-2 space-y-1 bg-white">
                        {products.length > 0 ? products.map(plan => (
                            <div key={plan.id} className="flex items-center">
                                <input
                                    id={`plan-${plan.id}`}
                                    type="checkbox"
                                    value={plan.id}
                                    checked={newSubscription.product_ids?.includes(plan.id)}
                                    onChange={(e) => {
                                        const productId = e.target.value;
                                        const isChecked = e.target.checked;
                                        setNewSubscription(prevState => {
                                            const currentProductIds = prevState.product_ids || [];
                                            let updatedProductIds: string[];
                                            if (isChecked) {
                                                updatedProductIds = [...currentProductIds, productId];
                                            } else {
                                                updatedProductIds = currentProductIds.filter(id => id !== productId);
                                            }
                                            return { ...prevState, product_ids: updatedProductIds };
                                        });
                                    }}
                                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                />
                                <label htmlFor={`plan-${plan.id}`} className="ml-2 block text-sm text-gray-900">
                                    {plan.name}
                                </label>
                            </div>
                        )) : (
                            <p className="text-sm text-gray-500">No plans available.</p>
                        )}
                    </div>
                    {(!newSubscription.product_ids || newSubscription.product_ids.length === 0) && (
                        <p className="text-xs text-red-600 mt-1">Please select at least one plan.</p>
                    )}
                </div>

                {/* Period Select */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Period</label>
                    <select
                        className="w-full p-2 border rounded-lg"
                        value={newSubscription.period || 'monthly'}
                        onChange={(e) => setNewSubscription({ ...newSubscription, period: e.target.value as 'monthly' | 'yearly' })}
                        required
                    >
                        <option value="monthly">Monthly</option>
                        <option value="yearly">Yearly</option>
                    </select>
                </div>

                {/* Status Select */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                        className="w-full p-2 border rounded-lg"
                        value={newSubscription.status || 'active'}
                        onChange={(e) => setNewSubscription({ ...newSubscription, status: e.target.value })}
                    >
                        <option value="active">Active</option>
                        <option value="trialing">Trialing</option>
                        <option value="canceled">Canceled</option>
                        <option value="pending cancellation">Pending Cancellation</option>
                        <option value="past due">Past Due</option>
                    </select>
                </div>

                {/* Quantity Input */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                    <input
                        type="number"
                        min="1"
                        className="w-full p-2 border rounded-lg"
                        value={newSubscription.quantity || 1}
                        onChange={(e) => setNewSubscription({
                            ...newSubscription,
                            quantity: parseInt(e.target.value, 10) || 1
                        })}
                    />
                </div>

                {/* Stripe Subscription ID Input */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Stripe Subscription ID (Optional)</label>
                    <input
                        type="text"
                        className="w-full p-2 border rounded-lg"
                        placeholder="sub_..."
                        value={newSubscription.stripe_subscription_id || ''}
                        onChange={(e) => setNewSubscription({
                            ...newSubscription,
                            stripe_subscription_id: e.target.value
                        })}
                    />
                </div>
            </div>
            <div className="flex justify-end space-x-2">
                <button
                    onClick={() => {
                        setIsAdding(false); // Close the form
                        // Reset local form state
                        setNewSubscription({
                            status: 'active',
                            period: 'monthly',
                            quantity: 1,
                            cancel_at_period_end: false,
                            deleted: false,
                            product_ids: [],
                            stripe_subscription_id: '',
                        });
                        setUserQuery('');
                        setError(null); // Clear any errors shown by the form
                    }}
                    className="px-4 py-2 border rounded-lg text-gray-700 hover:bg-gray-100"
                >
                    Cancel
                </button>
                <button
                    onClick={handleSave} // Call the internal save handler
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                    Save
                </button>
            </div>
        </div>
    );
};

// Add the export default line
export default SubscriptionForm;
