import act2Video from '../../assets/home/<USER>';

function OutBreakText() {
    return (
        <div className="flex items-center justify-center">
            <div className="relative">
                <h2 className="relative z-10 text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold leading-none">
                    <div className="space-y-2 sm:space-y-3 items-center justify-center flex md:hidden">
                        <h1 className="text-xl text-center sm:text-2xl md:text3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 leading-tight">
                            PREDICT TOMORROW
                            <br />
                            <span className="text-gray-800">ACT TODAY</span>
                        </h1>
                    </div>
                    <div className="relative">
                        {/* Container for masked video and text */}
                        <div className="relative">
                            {/* SVG for text mask and visible text */}
                            <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <mask id="text-mask">
                                        <rect x="0" y="0" width="100%" height="100%" fill="black" />
                                        <text
                                            x="50%"
                                            y="25%"
                                            dominantBaseline="middle"
                                            textAnchor="middle"
                                            className="fill-white font-bold"
                                            style={{ fontSize: 'inherit' }}
                                        >
                                            OUTBREAK
                                        </text>
                                        <text
                                            x="50%"
                                            y="50%"
                                            dominantBaseline="middle"
                                            textAnchor="middle"
                                            className="fill-white font-bold"
                                            style={{ fontSize: 'inherit' }}
                                        >
                                            DISEASE
                                        </text>
                                        <text
                                            x="50%"
                                            y="75%"
                                            dominantBaseline="middle"
                                            textAnchor="middle"
                                            className="fill-white font-bold"
                                            style={{ fontSize: 'inherit' }}
                                        >
                                            RESOURCES
                                        </text>
                                    </mask>
                                </defs>
                            </svg>
                            {/* Video masked by text */}
                            <div
                                className="relative"
                                style={{
                                    WebkitMaskImage: 'url(#text-mask)',
                                    maskImage: 'url(#text-mask)',
                                }}
                            >
                                <video
                                    autoPlay
                                    loop
                                    muted
                                    className="w-full h-full object-cover"
                                >
                                    <source src={act2Video} type="video/mp4" />
                                </video>
                            </div>
                        </div>
                    </div>
                </h2>
            </div>
        </div>
    );
}

export default OutBreakText;