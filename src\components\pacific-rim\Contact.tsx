import { Users, Mail } from 'lucide-react';

interface ContactProps {
  investigator: {
    name: string;
    email: string;
  };
}

export const Contact = ({ investigator }: ContactProps) => {
  return (
    <section className="py-16 bg-gradient-to-br from-orange-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-8">Get Involved & Stay Connected</h2>
          <div className="bg-white p-8 rounded-2xl shadow-lg">
            <div className="flex flex-col items-center space-y-4">
              <Users className="h-12 w-12 text-orange-600 mb-2" />
              <h3 className="text-xl font-semibold">Principal Investigator</h3>
              <p className="text-lg text-gray-600">{investigator.name}</p>
              <a
                href={`mailto:${investigator.email}`}
                className="inline-flex items-center px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-300"
              >
                <Mail className="h-5 w-5 mr-2" />
                Contact Us
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
