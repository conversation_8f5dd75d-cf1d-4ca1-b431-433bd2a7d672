-- First drop all existing objects
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;

-- Grant necessary permissions for the public schema
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO anon;
GRANT ALL ON SCHEMA public TO authenticated;
GRANT ALL ON SCHEMA public TO service_role;

-- Create a minimal profiles table
CREATE TABLE profiles (
  id uuid PRIMARY KEY,
  email text,
  avatar_url text,
  full_name text,
  created_at timestamptz DEFAULT now()
);

-- Create standalone admins table
CREATE TABLE standalone_admins (
  username text PRIMARY KEY,
  role text NOT NULL CHECK (role IN ('admin', 'editor')),
  created_at timestamptz DEFAULT now(),
  last_login timestamptz
);

-- Disable R<PERSON> completely for maximum compatibility
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE standalone_admins DISABLE ROW LEVEL SECURITY;

-- Simple function to handle new user signup
CREATE OR REPLACE FUNCTION handle_new_user()
R<PERSON>URNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  INSERT INTO profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1))
  );
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error in handle_new_user: %', SQLERRM;
    RETURN NEW;
END;
$$;

-- Create trigger for new user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();

-- Function to update admin last login
CREATE OR REPLACE FUNCTION update_admin_last_login(admin_username text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE standalone_admins
  SET last_login = now()
  WHERE username = admin_username;
END;
$$;

-- Insert default admin into standalone_admins
INSERT INTO standalone_admins (username, role)
VALUES (
  '<EMAIL>',
  'admin'
);

-- Create admin user in auth.users if it doesn't exist
DO $$
DECLARE
  admin_exists boolean;
BEGIN
  -- Check if admin user already exists
  SELECT EXISTS (
    SELECT 1 FROM auth.users WHERE email = '<EMAIL>'
  ) INTO admin_exists;

  -- Only create admin user if it doesn't exist
  IF NOT admin_exists THEN
    INSERT INTO auth.users (
      id,
      instance_id,
      email,
      encrypted_password,
      email_confirmed_at,
      created_at,
      updated_at,
      raw_app_meta_data,
      raw_user_meta_data,
      is_super_admin,
      role,
      aud,
      confirmation_token
    )
    VALUES (
      gen_random_uuid(),
      '00000000-0000-0000-0000-000000000000'::uuid,
      '<EMAIL>',
      crypt('11111111', gen_salt('bf')),
      now(),
      now(),
      now(),
      '{"provider":"email","providers":["email"]}'::jsonb,
      '{"name":"Admin User"}'::jsonb,
      false,
      'authenticated',
      'authenticated',
      'confirmed'
    );
  END IF;
END $$;

-- Grant all permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, anon, authenticated, service_role;

-- Grant auth schema permissions
GRANT USAGE ON SCHEMA auth TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA auth TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA auth TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA auth TO postgres, anon, authenticated, service_role;