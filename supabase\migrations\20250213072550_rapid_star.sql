-- First drop existing foreign key constraints
ALTER TABLE support_tickets 
DROP CONSTRAINT IF EXISTS support_tickets_user_id_fkey,
DROP CONSTRAINT IF EXISTS support_tickets_assigned_to_fkey,
DROP CONSTRAINT IF EXISTS support_tickets_resolved_by_fkey;

-- Recreate foreign key constraints with explicit names
ALTER TABLE support_tickets
ADD CONSTRAINT support_tickets_user_id_fkey 
FOREIGN KEY (user_id) 
REFERENCES profiles(id) 
ON DELETE CASCADE,
ADD CONSTRAINT support_tickets_assigned_to_fkey 
FOREIGN KEY (assigned_to) 
REFERENCES profiles(id) 
ON DELETE SET NULL,
ADD CONSTRAINT support_tickets_resolved_by_fkey 
FOREIGN KEY (resolved_by) 
REFERENCES profiles(id) 
ON DELETE SET NULL;

-- Create views for cleaner queries
CREATE OR REPLACE VIEW support_tickets_with_users AS
SELECT 
  t.*,
  creator.email as creator_email,
  creator.full_name as creator_full_name,
  assignee.email as assignee_email,
  assignee.full_name as assignee_full_name,
  resolver.email as resolver_email,
  resolver.full_name as resolver_full_name
FROM support_tickets t
LEFT JOIN profiles creator ON t.user_id = creator.id
LEFT JOIN profiles assignee ON t.assigned_to = assignee.id
LEFT JOIN profiles resolver ON t.resolved_by = resolver.id;

-- Grant access to the view
GRANT SELECT ON support_tickets_with_users TO authenticated;