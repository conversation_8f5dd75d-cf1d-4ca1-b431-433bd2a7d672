import AccelerateVid from '../../assets/home/<USER>'

function Accelerate() {
  return (
    <section className="m-auto w-full bg-[#0A0F20] text-white rounded-lg py-6 md:py-10">
      <div className="flex flex-col items-center justify-center px-4 md:px-6 lg:px-8">
        <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-semibold py-2 w-full max-w-[600px] text-center">
          Accelerate Data,  
          Amplify Action
        </h1>
        <video
          src={AccelerateVid}
          autoPlay
          loop
          muted
          className="w-full max-w-7xl h-auto"
        ></video>
        <span className='text-base sm:text-lg font-semibold text-center px-4'>
          Elenor Emergency Management Platform
        </span>
        <p className="text-center text-sm sm:text-base w-full max-w-[90%] sm:max-w-[80%] md:max-w-[70%] lg:w-[40%] mt-4 px-4">
          Elenor empowers health organizations to harness data faster and more efficiently, delivering real-time, comprehensive insights that drive better decisions and protect public health during emergencies and daily operations.
        </p>
      </div>
    </section>
  );
}

export default Accelerate;