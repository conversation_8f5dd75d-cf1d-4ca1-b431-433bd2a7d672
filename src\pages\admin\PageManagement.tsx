import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { FileText, Edit, Save, X, Plus, Trash2, AlertCircle, CheckCircle, Code, Eye, FileCode, Layout, Search, Filter, Bar<PERSON><PERSON>, Clock, ArrowRight, FileSearch } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { Link } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';

interface Page {
  id: string;
  slug: string;
  title: string;
  content: string;
  html_content: string | null;
  custom_css: string | null;
  custom_js: string | null;
  meta_description: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

export default function PageManagement() {
  const navigate = useNavigate();
  const [pages, setPages] = useState<Page[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState(false);
  const [editMode, setEditMode] = useState<'markdown' | 'html'>('markdown');
  const [activeTab, setActiveTab] = useState<'content' | 'styles' | 'scripts'>('content');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft'>('all');
  const [previewPage, setPreviewPage] = useState<Page | null>(null);
  const [formData, setFormData] = useState({
    slug: '',
    title: '',
    content: '',
    html_content: '',
    custom_css: '',
    custom_js: '',
    meta_description: '',
    is_published: false
  });

  useEffect(() => {
    fetchPages();
  }, []);

  const fetchPages = async () => {
    try {
      const { data, error } = await supabase
        .from('pages')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPages(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const filteredPages = pages.filter(page => {
    const matchesSearch = page.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         page.slug.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' ? true :
                         statusFilter === 'published' ? page.is_published :
                         !page.is_published;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (isPublished: boolean) => {
    return isPublished ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    try {
      if (!formData.title.trim()) {
        throw new Error('Title is required');
      }

      const pageData = {
        slug: formData.slug.trim(),
        title: formData.title.trim(),
        content: formData.content,
        html_content: formData.html_content,
        custom_css: formData.custom_css,
        custom_js: formData.custom_js,
        meta_description: formData.meta_description.trim(),
        is_published: formData.is_published
      };

      if (editingId) {
        const { error } = await supabase
          .from('pages')
          .update(pageData)
          .eq('id', editingId);

        if (error) throw error;
        setSuccess('Page updated successfully!');
      } else {
        const { error } = await supabase
          .from('pages')
          .insert([pageData]);

        if (error) throw error;
        setSuccess('Page created successfully!');
      }

      setFormData({
        slug: '',
        title: '',
        content: '',
        html_content: '',
        custom_css: '',
        custom_js: '',
        meta_description: '',
        is_published: false
      });
      setShowForm(false);
      setEditingId(null);
      setPreviewMode(false);
      fetchPages();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const renderPreview = () => {
    const previewData = {
      title: formData.title,
      content: editMode === 'markdown' ? formData.content : formData.html_content,
      slug: formData.slug,
      custom_css: formData.custom_css,
      custom_js: formData.custom_js
    };

    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="prose max-w-none">
          {editMode === 'markdown' ? (
            <ReactMarkdown>{previewData.content}</ReactMarkdown>
          ) : (
            <div dangerouslySetInnerHTML={{ __html: previewData.content || '' }} />
          )}
        </div>
        {previewData.custom_css && (
          <style dangerouslySetInnerHTML={{ __html: previewData.custom_css }} />
        )}
        {previewData.custom_js && (
          <script dangerouslySetInnerHTML={{ __html: previewData.custom_js }} />
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  const handlePreviewPage = (page: Page) => {
    setPreviewPage(page);
  };

  return (
    <div className="space-y-6">
      {/* Preview Modal */}
      {previewPage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold">Preview: {previewPage.title}</h3>
                <button 
                  onClick={() => setPreviewPage(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="prose max-w-none p-4 border border-gray-300 rounded-lg bg-gray-50">
                <ReactMarkdown>{previewPage.content}</ReactMarkdown>
              </div>
              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => navigate(`/${previewPage.slug}`)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                >
                  View Live Page
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Total Pages */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300 cursor-pointer">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Total Pages</p>
              <h3 className="text-3xl font-bold">{pages.length}</h3>
            </div>
            <FileText className="h-8 w-8 text-blue-100" />
          </div>
          <div className="mt-4 text-sm text-blue-100">
            Last updated: {pages.length > 0 ? new Date(Math.max(...pages.map(p => new Date(p.updated_at).getTime()))).toLocaleDateString() : 'N/A'}
          </div>
        </div>

        {/* Published Pages */}
        <div className="bg-gradient-to-br from-green-500 to-green-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300 cursor-pointer">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Published</p>
              <h3 className="text-3xl font-bold">
                {pages.filter(page => page.is_published).length}
              </h3>
            </div>
            <CheckCircle className="h-8 w-8 text-green-100" />
          </div>
          <div className="mt-4 text-sm text-green-100">
            {((pages.filter(page => page.is_published).length / pages.length) * 100).toFixed(1)}% of total
          </div>
        </div>

        {/* Draft Pages */}
        <div className="bg-gradient-to-br from-yellow-500 to-yellow-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300 cursor-pointer">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-100 text-sm">Drafts</p>
              <h3 className="text-3xl font-bold">
                {pages.filter(page => !page.is_published).length}
              </h3>
            </div>
            <Edit className="h-8 w-8 text-yellow-100" />
          </div>
          <div className="mt-4 text-sm text-yellow-100">
            {((pages.filter(page => !page.is_published).length / pages.length) * 100).toFixed(1)}% of total
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-gradient-to-br from-purple-500 to-purple-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300 cursor-pointer">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Recent Activity</p>
              <h3 className="text-lg font-bold">
                {pages.length > 0 
                  ? new Date(Math.max(...pages.map(p => new Date(p.updated_at).getTime()))).toLocaleDateString()
                  : 'No activity'
                }
              </h3>
            </div>
            <Clock className="h-8 w-8 text-purple-100" />
          </div>
          <div className="mt-4 text-sm text-purple-100">
            Last page update
          </div>
        </div>
      </div>

      {/* Search and Controls */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between bg-white p-6 rounded-xl shadow-lg">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search pages..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as 'all' | 'published' | 'draft')}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
            </select>
          </div>
          <button
            onClick={() => {
              setShowForm(true);
              setEditingId(null);
              setFormData({
                slug: '',
                title: '',
                content: '',
                html_content: '',
                custom_css: '',
                custom_js: '',
                meta_description: '',
                is_published: false
              });
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center transform hover:scale-105 transition-all duration-300"
          >
            <Plus className="h-5 w-5 mr-2" />
            Add New Page
          </button>
        </div>
      </div>

      {/* Pages Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPages.map((page, index) => (
          <div 
            key={page.id} 
            className={`bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-all duration-300 hover:shadow-xl animate-fade-in`}
            style={{
              animationDelay: `${index * 100}ms`
            }}
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(page.is_published)}`}>
                  {page.is_published ? 'Published' : 'Draft'}
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      setFormData({
                        slug: page.slug,
                        title: page.title,
                        content: page.content,
                        html_content: page.html_content || '',
                        custom_css: page.custom_css || '',
                        custom_js: page.custom_js || '',
                        meta_description: page.meta_description,
                        is_published: page.is_published
                      });
                      setEditingId(page.id);
                      setShowForm(true);
                    }}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transform hover:scale-110 transition-transform"
                  >
                    <Edit className="h-5 w-5" />
                  </button>
                  <button
                    onClick={async () => {
                      if (window.confirm('Are you sure you want to delete this page?')) {
                        try {
                          const { error } = await supabase
                            .from('pages')
                            .delete()
                            .eq('id', page.id);

                          if (error) throw error;
                          fetchPages();
                        } catch (err: any) {
                          setError(err.message);
                        }
                      }
                    }}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transform hover:scale-110 transition-transform"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handlePreviewPage(page)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transform hover:scale-110 transition-transform"
                  >
                    <Eye className="h-5 w-5" />
                  </button>
                </div>
              </div>
              <h3 className="text-xl font-bold mb-2">{page.title}</h3>
              <p className="text-gray-600 mb-4">/{page.slug}</p>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  {new Date(page.updated_at).toLocaleDateString()}
                </div>
                <Link
                  to={`/${page.slug}`}
                  target="_blank"
                  className="flex items-center text-blue-600 hover:text-blue-700"
                >
                  View
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-100 opacity-100">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold">
                  {editingId ? 'Edit Page' : 'Add New Page'}
                </h3>
                <div className="flex items-center space-x-4">
                  <div className="flex rounded-lg border border-gray-300 p-1">
                    <button
                      type="button"
                      onClick={() => setEditMode('markdown')}
                      className={`px-3 py-1 rounded ${
                        editMode === 'markdown'
                          ? 'bg-blue-100 text-blue-600'
                          : 'text-gray-600'
                      }`}
                    >
                      <Code className="h-4 w-4" />
                    </button>
                    <button
                      type="button"
                      onClick={() => setEditMode('html')}
                      className={`px-3 py-1 rounded ${
                        editMode === 'html'
                          ? 'bg-blue-100 text-blue-600'
                          : 'text-gray-600'
                      }`}
                    >
                      <FileCode className="h-4 w-4" />
                    </button>
                    <button
                      type="button"
                      onClick={() => setPreviewMode(!previewMode)}
                      className={`px-3 py-1 rounded ${
                        previewMode
                          ? 'bg-blue-100 text-blue-600'
                          : 'text-gray-600'
                      }`}
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>
                  <button
                    onClick={() => {
                      setShowForm(false);
                      setEditingId(null);
                      setPreviewMode(false);
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Page Title
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="About Us"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      URL Slug
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.slug}
                      onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="about-us"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Meta Description
                  </label>
                  <input
                    type="text"
                    value={formData.meta_description}
                    onChange={(e) => setFormData({ ...formData, meta_description: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Brief description for search engines"
                  />
                </div>

                {previewMode ? (
                  renderPreview()
                ) : (
                  <div className="space-y-4">
                    <div className="flex border-b border-gray-200">
                      <button
                        type="button"
                        onClick={() => setActiveTab('content')}
                        className={`px-4 py-2 border-b-2 ${
                          activeTab === 'content'
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        Content
                      </button>
                      <button
                        type="button"
                        onClick={() => setActiveTab('styles')}
                        className={`px-4 py-2 border-b-2 ${
                          activeTab === 'styles'
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        Styles
                      </button>
                      <button
                        type="button"
                        onClick={() => setActiveTab('scripts')}
                        className={`px-4 py-2 border-b-2 ${
                          activeTab === 'scripts'
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        Scripts
                      </button>
                    </div>

                    {activeTab === 'content' && (
                      <div className="space-y-4">
                        {editMode === 'markdown' ? (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Markdown Content
                            </label>
                            <textarea
                              rows={12}
                              value={formData.content}
                              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                              placeholder="# Page Title

Write your content here using Markdown formatting..."
                            />
                          </div>
                        ) : (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              HTML Content
                            </label>
                            <textarea
                              rows={12}
                              value={formData.html_content}
                              onChange={(e) => setFormData({ ...formData, html_content: e.target.value })}
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                              placeholder="<div class='container'>
  <h1>Page Title</h1>
  <p>Write your content here using HTML...</p>
</div>"
                            />
                          </div>
                        )}
                      </div>
                    )}

                    {activeTab === 'styles' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Custom CSS
                        </label>
                        <textarea
                          rows={12}
                          value={formData.custom_css}
                          onChange={(e) => setFormData({ ...formData, custom_css: e.target.value })}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                          placeholder="/* Add your custom CSS styles here */
.custom-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}"
                        />
                      </div>
                    )}

                    {activeTab === 'scripts' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Custom JavaScript
                        </label>
                        <textarea
                          rows={12}
                          value={formData.custom_js}
                          onChange={(e) => setFormData({ ...formData, custom_js: e.target.value })}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                          placeholder="// Add your custom JavaScript here
document.addEventListener('DOMContentLoaded', function() {
  // Your code here
});"
                        />
                      </div>
                    )}
                  </div>
                )}

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_published"
                    checked={formData.is_published}
                    onChange={(e) => setFormData({ ...formData, is_published: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_published" className="ml-2 block text-sm text-gray-900">
                    Publish this page
                  </label>
                </div>

                <div className="flex justify-end space-x-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false);
                      setEditingId(null);
                      setPreviewMode(false);
                    }}
                    className="text-gray-600 hover:text-gray-900"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700"
                  >
                    {editingId ? 'Update Page' : 'Create Page'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}