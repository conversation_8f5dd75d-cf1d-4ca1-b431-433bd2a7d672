import React, { useEffect, useState } from 'react';
import { supabase } from '../../lib/supabase';
import { 
  Plus, Edit, Trash2, Eye, Check, X, 
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>, Building2, <PERSON>Left,
  FileText
} from 'lucide-react';
import { Link } from 'react-router-dom';
import MdEditor from 'react-markdown-editor-lite';
import 'react-markdown-editor-lite/lib/index.css';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface CaseStudy {
  id: string;
  title: string;
  organization: string;
  impact: string;
  category: string;
  image_url: string;
  content: string;
  is_public: boolean;
}

export default function CaseStudiesManagement() {
  const [caseStudies, setCaseStudies] = useState<CaseStudy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    organization: '',
    impact: '',
    category: '',
    image_url: '',
    content: '',
    is_public: true
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [caseStudiesPerPage, setCaseStudiesPerPage] = useState(10);
  const [totalCaseStudies, setTotalCaseStudies] = useState(0);

  useEffect(() => {
    fetchCaseStudies();
  }, [currentPage, caseStudiesPerPage]);

  const fetchCaseStudies = async () => {
    try {
      setLoading(true);
      const { data, error, count } = await supabase
        .from('case_studies')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })
        .range((currentPage - 1) * caseStudiesPerPage, currentPage * caseStudiesPerPage - 1);

      if (error) throw error;
      setCaseStudies(data || []);
      setTotalCaseStudies(count || 0);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const togglePublicStatus = async (id: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('case_studies')
        .update({ is_public: !currentStatus })
        .eq('id', id);

      if (error) throw error;
      fetchCaseStudies();
    } catch (err) {
      setError(err.message);
    }
  };

  const deleteCaseStudy = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this case study?')) return;
    
    try {
      const { error } = await supabase
        .from('case_studies')
        .delete()
        .eq('id', id);

      if (error) throw error;
      fetchCaseStudies();
    } catch (err) {
      setError(err.message);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined;
    
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const resetForm = () => {
    setFormData({
      title: '',
      organization: '',
      impact: '',
      category: '',
      image_url: '',
      content: '',
      is_public: true
    });
    setEditingId(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (editingId) {
        const { error } = await supabase
          .from('case_studies')
          .update(formData)
          .eq('id', editingId);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('case_studies')
          .insert(formData);
        if (error) throw error;
      }

      fetchCaseStudies();
      setShowForm(false);
      resetForm();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (study: CaseStudy) => {
    setFormData({
      title: study.title,
      organization: study.organization,
      impact: study.impact,
      category: study.category,
      image_url: study.image_url,
      content: study.content,
      is_public: study.is_public
    });
    setEditingId(study.id);
    setShowForm(true);
  };

  if (loading) return <div className="flex justify-center py-20">Loading...</div>;
  if (error) return <div className="flex justify-center py-20 text-red-500">Error: {error}</div>;

  return (
    <div className="p-8">
      {showForm ? (
        <div>
          <button 
            onClick={() => {
              setShowForm(false);
              resetForm();
            }}
            className="flex items-center text-blue-600 mb-6"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Case Studies
          </button>

          <h1 className="text-2xl font-bold mb-8">
            {editingId ? 'Edit Case Study' : 'Add New Case Study'}
          </h1>

          <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
            <div className="grid md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-gray-700 mb-2">Title*</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border rounded-lg"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">Organization*</label>
                <input
                  type="text"
                  name="organization"
                  value={formData.organization}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border rounded-lg"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">Impact*</label>
                <input
                  type="text"
                  name="impact"
                  value={formData.impact}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border rounded-lg"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">Category*</label>
                <input
                  type="text"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border rounded-lg"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">Image URL*</label>
                <input
                  type="url"
                  name="image_url"
                  value={formData.image_url}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border rounded-lg"
                  required
                />
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="is_public"
                  checked={formData.is_public}
                  onChange={handleChange}
                  className="h-5 w-5 mr-2"
                />
                <label className="text-gray-700">Public</label>
              </div>
            </div>
            <div className="mb-6">
              <label className="block text-gray-700 mb-2">Content*</label>
              <MdEditor
                name="content"
                value={formData.content}
                onChange={({ text }) => setFormData({...formData, content: text})}
                style={{ height: '400px' }}
                className="w-full border rounded-lg"
                renderHTML={(text) => (
                  <ReactMarkdown 
                    remarkPlugins={[remarkGfm]}
                    className="prose max-w-none p-4"
                  >
                    {text}
                  </ReactMarkdown>
                )}
              />
            </div>
            <button
              type="submit"
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-blue-400"
            >
              {loading ? 'Saving...' : 'Save Case Study'}
            </button>
          </form>
        </div>
      ) : (
        <div>
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-2xl font-bold">Manage Case Studies</h1>
            <button 
              onClick={() => setShowForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add New
            </button>
          </div>

          <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organization</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Public</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {caseStudies.length === 0 && (
                  <tr>
                    <td colSpan={6} className="px-6 py-8 text-center">
                      <div className="flex flex-col items-center justify-center space-y-2">
                        <FileText className="h-12 w-12 text-gray-400" />
                        <h3 className="text-lg font-medium text-gray-900">
                          No Case Studies found
                        </h3>
                        <p className="text-sm text-gray-500">
                          Get started by creating a new Case studies
                        </p>
                      </div>
                    </td>
                  </tr>
                )}
                {caseStudies.map((study) => (
                  <tr key={study.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{study.title}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-gray-500">{study.organization}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                        {study.category}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button 
                        onClick={() => togglePublicStatus(study.id, study.is_public)}
                        className={`p-1 rounded-full ${study.is_public ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}
                      >
                        {study.is_public ? <Check className="h-4 w-4" /> : <X className="h-4 w-4" />}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link 
                          to={`/case-studies/${study.id}`}
                          className="text-blue-600 hover:text-blue-900 p-1"
                          title="View"
                        >
                          <Eye className="h-5 w-5" />
                        </Link>
                        <button 
                          onClick={() => handleEdit(study)}
                          className="text-yellow-600 hover:text-yellow-900 p-1"
                          title="Edit"
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                        <button 
                          onClick={() => deleteCaseStudy(study.id)}
                          className="text-red-600 hover:text-red-900 p-1"
                          title="Delete"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            <div className="flex items-center justify-between px-6 py-4 bg-gray-50 border-t border-gray-200">
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-500">
                  Showing {(currentPage - 1) * caseStudiesPerPage + 1} to {Math.min(currentPage * caseStudiesPerPage, totalCaseStudies)} of {totalCaseStudies} case studies
                </div>
                <div className="flex items-center">
                  <label className="text-sm text-gray-500 mr-2">Items per page:</label>
                  <select
                    value={caseStudiesPerPage}
                    onChange={(e) => {
                      setCaseStudiesPerPage(Number(e.target.value));
                      setCurrentPage(1);
                    }}
                    className="px-2 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="5">5</option>
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                  </select>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className={`px-4 py-2 border rounded-lg ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'}`}
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => prev + 1)}
                  disabled={currentPage * caseStudiesPerPage >= totalCaseStudies}
                  className={`px-4 py-2 border rounded-lg ${currentPage * caseStudiesPerPage >= totalCaseStudies ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'}`}
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}