import React, { useState, useRef } from 'react';
import { Calendar, Clock, Users, Building2, CheckCircle, ArrowRight, ArrowLeft, Sparkles } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import <PERSON>aptcha from '@hcaptcha/react-hcaptcha';
import { supabase } from '../lib/supabase';
import Alert from '../components/ui/Alert';
import { isBusinessEmail } from '../utils/emailBlacklist';

const schema = yup.object().shape({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup
    .string()
    .email('Invalid email format')
    .required('Email is required')
    .test('business-email', 'Please use a business email address. Personal email providers (Gmail, Yahoo, etc.) and temporary emails are not allowed.', (value) => {
      if (!value) return false;
      return isBusinessEmail(value);
    }),
  phone: yup.string().optional().matches(/^[0-9+\-\s()]*$/, 'Invalid phone number'),
  company: yup.string().required('Company name is required'),
  jobTitle: yup.string().required('Job title is required'),
  teamSize: yup.string().required('Team size is required'),
  useCase: yup.string().required('Use case is required'),
  preferredDate: yup.string().required('Preferred date is required'),
  preferredTime: yup.string().required('Preferred time is required'),
  additionalInfo: yup.string().optional()
});

type FormData = yup.InferType<typeof schema>;

const BookDemo: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const captcha = useRef<HCaptcha>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    trigger
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    mode: 'onChange'
  });

  const totalSteps = 3;

  const nextStep = async () => {
    let fieldsToValidate: (keyof FormData)[] = [];

    switch (currentStep) {
      case 1:
        fieldsToValidate = ['firstName', 'lastName', 'email', 'phone', 'company', 'jobTitle', 'teamSize'];
        break;
      case 2:
        fieldsToValidate = ['useCase', 'preferredDate', 'preferredTime'];
        break;
    }

    const isValid = await trigger(fieldsToValidate);
    if (isValid && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = async (data: FormData) => {
    if (!captchaToken) {
      setSubmitError('Please complete the captcha verification');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { error } = await supabase
        .from('demo_requests')
        .insert([{
          first_name: data.firstName,
          last_name: data.lastName,
          email: data.email,
          phone: data.phone || null,
          company: data.company,
          job_title: data.jobTitle,
          team_size: data.teamSize,
          use_case: data.useCase,
          preferred_date: data.preferredDate,
          preferred_time: data.preferredTime,
          additional_info: data.additionalInfo || null,
          status: 'pending'
        }]);

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      setSubmitSuccess(true);
      setCurrentStep(totalSteps + 1); // Move to success step
    } catch (error: any) {
      console.error('Error submitting demo request:', error);
      if (error?.message?.includes('permission denied')) {
        setSubmitError('There was an issue submitting your request. Please try again or contact support.');
      } else {
        setSubmitError('Failed to submit demo request. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
      if (captcha.current) {
        captcha.current.resetCaptcha();
      }
      setCaptchaToken(null);
    }
  };

  const teamSizeOptions = [
    '1-10 employees',
    '11-50 employees',
    '51-200 employees',
    '201-1000 employees',
    '1000+ employees'
  ];

  const useCaseOptions = [
    'Grant Management',
    'Emergency Response',
    'Public Health Operations',
    'Public Health Coordination',
    'Compliance & Reporting',
    'Other'
  ];

  const timeSlots = [
    '9:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
    '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM'
  ];

  if (submitSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#170031] via-[#2D1B69] to-[#470097] flex items-center justify-center px-4 pt-32 md:pt-20 xl:pt-32">
        <div className="max-w-2xl mx-auto text-center">
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 md:p-12 border border-white/20">
            <div className="mb-8">
              <div className="w-20 h-20 bg-gradient-to-r from-[#79D68F] to-[#7A8EDB] rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
                <CheckCircle className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Demo Request Submitted!
              </h1>
              <p className="text-lg text-white/80 leading-relaxed">
                Thank you for your interest in International Responder Systems. Our team will review your request and contact you within 24 hours to schedule your personalized demo.
              </p>
            </div>

            <div className="space-y-4 text-left bg-white/5 rounded-2xl p-6 mb-8">
              <h3 className="text-xl font-semibold text-white mb-4">What happens next?</h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-[#79D68F] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                  <p className="text-white/80">Our team reviews your demo request and use case</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-[#7A8EDB] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                  <p className="text-white/80">We'll contact you to confirm the demo time and prepare a customized presentation</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-[#79D68F] to-[#7A8EDB] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">3</span>
                  </div>
                  <p className="text-white/80">Experience a live demo tailored to your organization's needs</p>
                </div>
              </div>
            </div>

            <button
              onClick={() => window.location.href = '/'}
              className="bg-white text-black font-semibold px-8 py-4 rounded-lg flex items-center gap-2 mx-auto micro-button transition-all duration-300 hover:shadow-xl"
            >
              Return to Home
              <ArrowRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#170031] via-[#2D1B69] to-[#470097] pt-20 2xl:pt-32 pb-8 2xl:pb-12 px-4">
      {/* Hero Section */}
      <div className="max-w-4xl mx-auto text-center mb-12">
        <div className="mb-8">
          <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6 animate-fadeInDown">
            <Sparkles className="w-4 h-4 text-[#79D68F]" />
            <span className="text-white/90 text-sm font-medium">Book Your Demo</span>
          </div>

          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fadeInDown animation-delay-200">
            Experience the Future of
            <span className="block bg-gradient-to-r from-[#79D68F] to-[#7A8EDB] bg-clip-text text-transparent">
              Public Health Response
            </span>
          </h1>

          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed animate-fadeInDown animation-delay-400">
            Get a personalized demonstration of our cutting-edge solutions and see how International Responder Systems can transform your organization's emergency response capabilities.
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center justify-center mb-8 animate-fadeInDown animation-delay-600">
          <div className="flex items-center gap-2">
            {[1, 2, 3].map((step) => (
              <React.Fragment key={step}>
                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${step <= currentStep
                  ? 'bg-gradient-to-r from-[#79D68F] to-[#7A8EDB] text-white'
                  : 'bg-white/20 text-white/60'
                  }`}>
                  {step === 3 ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : (
                    step
                  )}
                </div>
                {step < 3 && (
                  <div className={`w-12 h-1 rounded-full transition-all duration-300 ${step < currentStep ? 'bg-gradient-to-r from-[#79D68F] to-[#7A8EDB]' : 'bg-white/20'
                    }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>

      {/* Form Container */}
      <div className="max-w-2xl mx-auto">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 md:p-12 border border-white/20">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Step 1: Personal & Company Information */}
            {currentStep === 1 && (
              <div className="space-y-8 animate-fadeInDown">
                <div className="text-center mb-8">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <Users className="w-10 h-10 text-[#79D68F]" />
                    <Building2 className="w-10 h-10 text-[#7A8EDB]" />
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-2">Personal & Company Information</h2>
                  <p className="text-white/70">Tell us about yourself and your organization</p>
                </div>

                {/* Personal Information Section */}
                <div className="space-y-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Users className="w-5 h-5 text-[#79D68F]" />
                    <h3 className="text-lg font-semibold text-white">Personal Details</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-white mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        {...register('firstName')}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:ring-2 focus:ring-[#79D68F] focus:border-transparent transition-all duration-300"
                        placeholder="Enter your first name"
                      />
                      {errors.firstName && (
                        <p className="mt-1 text-sm text-red-400">{errors.firstName.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-white mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        {...register('lastName')}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:ring-2 focus:ring-[#79D68F] focus:border-transparent transition-all duration-300"
                        placeholder="Enter your last name"
                      />
                      {errors.lastName && (
                        <p className="mt-1 text-sm text-red-400">{errors.lastName.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-white mb-2">
                        Work Email Address *
                      </label>
                      <input
                        type="email"
                        {...register('email')}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:ring-2 focus:ring-[#79D68F] focus:border-transparent transition-all duration-300"
                        placeholder="Enter your work email address"
                      />
                      {errors.email && (
                        <p className="mt-1 text-sm text-red-400">{errors.email.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-white mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        {...register('phone')}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:ring-2 focus:ring-[#79D68F] focus:border-transparent transition-all duration-300"
                        placeholder="Enter your phone number (optional)"
                      />
                      {errors.phone && (
                        <p className="mt-1 text-sm text-red-400">{errors.phone.message}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Company Information Section */}
                <div className="space-y-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Building2 className="w-5 h-5 text-[#7A8EDB]" />
                    <h3 className="text-lg font-semibold text-white">Company Details</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-white mb-2">
                        Company Name *
                      </label>
                      <input
                        type="text"
                        {...register('company')}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:ring-2 focus:ring-[#7A8EDB] focus:border-transparent transition-all duration-300"
                        placeholder="Enter your company name"
                      />
                      {errors.company && (
                        <p className="mt-1 text-sm text-red-400">{errors.company.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-white mb-2">
                        Job Title *
                      </label>
                      <input
                        type="text"
                        {...register('jobTitle')}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:ring-2 focus:ring-[#7A8EDB] focus:border-transparent transition-all duration-300"
                        placeholder="Enter your job title"
                      />
                      {errors.jobTitle && (
                        <p className="mt-1 text-sm text-red-400">{errors.jobTitle.message}</p>
                      )}
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-white mb-2">
                        Team Size *
                      </label>
                      <select
                        {...register('teamSize')}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:ring-2 focus:ring-[#7A8EDB] focus:border-transparent transition-all duration-300"
                      >
                        <option value="" className="bg-gray-800">Select team size</option>
                        {teamSizeOptions.map((option) => (
                          <option key={option} value={option} className="bg-gray-800">
                            {option}
                          </option>
                        ))}
                      </select>
                      {errors.teamSize && (
                        <p className="mt-1 text-sm text-red-400">{errors.teamSize.message}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Demo Preferences */}
            {currentStep === 2 && (
              <div className="space-y-6 animate-fadeInDown">
                <div className="text-center mb-8">
                  <Calendar className="w-12 h-12 text-[#79D68F] mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-white mb-2">Demo Preferences</h2>
                  <p className="text-white/70">Choose your preferred demo details</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Primary Use Case *
                  </label>
                  <select
                    {...register('useCase')}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:ring-2 focus:ring-[#79D68F] focus:border-transparent transition-all duration-300"
                  >
                    <option value="" className="bg-gray-800">Select your primary use case</option>
                    {useCaseOptions.map((option) => (
                      <option key={option} value={option} className="bg-gray-800">
                        {option}
                      </option>
                    ))}
                  </select>
                  {errors.useCase && (
                    <p className="mt-1 text-sm text-red-400">{errors.useCase.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      Preferred Date *
                    </label>
                    <input
                      type="date"
                      {...register('preferredDate')}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:ring-2 focus:ring-[#79D68F] focus:border-transparent transition-all duration-300"
                    />
                    {errors.preferredDate && (
                      <p className="mt-1 text-sm text-red-400">{errors.preferredDate.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      Preferred Time *
                    </label>
                    <select
                      {...register('preferredTime')}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:ring-2 focus:ring-[#79D68F] focus:border-transparent transition-all duration-300"
                    >
                      <option value="" className="bg-gray-800">Select time</option>
                      {timeSlots.map((time) => (
                        <option key={time} value={time} className="bg-gray-800">
                          {time}
                        </option>
                      ))}
                    </select>
                    {errors.preferredTime && (
                      <p className="mt-1 text-sm text-red-400">{errors.preferredTime.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Additional Information
                  </label>
                  <textarea
                    {...register('additionalInfo')}
                    rows={4}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:ring-2 focus:ring-[#79D68F] focus:border-transparent transition-all duration-300 resize-none"
                    placeholder="Tell us more about your specific needs or questions..."
                  />
                </div>
              </div>
            )}

            {/* Step 3: Review & Submit */}
            {currentStep === 3 && (
              <div className="space-y-6 animate-fadeInDown">
                <div className="text-center mb-8">
                  <CheckCircle className="w-12 h-12 text-[#7A8EDB] mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-white mb-2">Review & Submit</h2>
                  <p className="text-white/70">Please review your information before submitting</p>
                </div>

                <div className="bg-white/5 rounded-2xl p-6 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-white/60">Name:</span>
                      <p className="text-white font-medium">{watch('firstName')} {watch('lastName')}</p>
                    </div>
                    <div>
                      <span className="text-white/60">Email:</span>
                      <p className="text-white font-medium">{watch('email')}</p>
                    </div>
                    <div>
                      <span className="text-white/60">Company:</span>
                      <p className="text-white font-medium">{watch('company')}</p>
                    </div>
                    <div>
                      <span className="text-white/60">Job Title:</span>
                      <p className="text-white font-medium">{watch('jobTitle')}</p>
                    </div>
                    <div>
                      <span className="text-white/60">Team Size:</span>
                      <p className="text-white font-medium">{watch('teamSize')}</p>
                    </div>
                    <div>
                      <span className="text-white/60">Use Case:</span>
                      <p className="text-white font-medium">{watch('useCase')}</p>
                    </div>
                    <div>
                      <span className="text-white/60">Preferred Date:</span>
                      <p className="text-white font-medium">{watch('preferredDate')}</p>
                    </div>
                    <div>
                      <span className="text-white/60">Preferred Time:</span>
                      <p className="text-white font-medium">{watch('preferredTime')}</p>
                    </div>
                  </div>

                  {watch('additionalInfo') && (
                    <div>
                      <span className="text-white/60">Additional Information:</span>
                      <p className="text-white font-medium mt-1">{watch('additionalInfo')}</p>
                    </div>
                  )}
                </div>

                <div className="flex justify-center">
                  <HCaptcha
                    ref={captcha}
                    sitekey={import.meta.env.VITE_HCAPTCHA_SITE_KEY}
                    onVerify={(token) => setCaptchaToken(token)}
                    onExpire={() => setCaptchaToken(null)}
                    theme="dark"
                  />
                </div>

                {submitError && (
                  <Alert type="error" message={submitError} />
                )}
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-8">
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={prevStep}
                  className="flex items-center gap-2 px-6 py-3 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-all duration-300 micro-button"
                >
                  <ArrowLeft className="w-5 h-5" />
                  Previous
                </button>
              )}

              <div className="ml-auto">
                {currentStep < totalSteps ? (
                  <button
                    type="button"
                    onClick={nextStep}
                    className="flex items-center gap-2 px-8 py-3 bg-gradient-to-r from-[#79D68F] to-[#7A8EDB] text-white rounded-lg font-semibold hover:shadow-xl transition-all duration-300 micro-button"
                  >
                    Next Step
                    <ArrowRight className="w-5 h-5" />
                  </button>
                ) : (
                  <button
                    type="submit"
                    disabled={isSubmitting || !captchaToken}
                    className="flex items-center gap-2 px-8 py-3 bg-gradient-to-r from-[#79D68F] to-[#7A8EDB] text-white rounded-lg font-semibold hover:shadow-xl transition-all duration-300 micro-button disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        Submit Demo Request
                        <CheckCircle className="w-5 h-5" />
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BookDemo;
