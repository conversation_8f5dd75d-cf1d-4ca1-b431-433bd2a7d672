import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import ReactMarkdown from 'react-markdown';

interface Page {
  id: string;
  slug: string;
  title: string;
  content: string;
  html_content: string | null;
  custom_css: string | null;
  custom_js: string | null;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

const PagePreview: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [page, setPage] = useState<Page | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPage = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('pages')
          .select('*')
          .eq('slug', slug)
          .eq('is_published', true)
          .single();

        if (error) throw error;
        setPage(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchPage();
  }, [slug]);

  if (loading) return <div className="text-center py-8">Loading...</div>;
  if (error) return <div className="text-center py-8 text-red-500">Error: {error}</div>;
  if (!page) return <div className="text-center py-8">Page not found</div>;

  return (
    <div className="max-w-4xl mx-auto mt-24 p-6">
      {/* Render custom CSS */}
      {page.custom_css && (
        <style dangerouslySetInnerHTML={{ __html: page.custom_css }} />
      )}

      <article className="prose max-w-none">
        <h1 className="text-3xl font-bold mb-6">{page.title}</h1>
        
        {/* Render Markdown content */}
        {page.content && (
          <div className="mb-8">
            <ReactMarkdown>{page.content}</ReactMarkdown>
          </div>
        )}
        
        {/* Render HTML content */}
        {page.html_content && (
          <div 
            className="html-content mb-8"
            dangerouslySetInnerHTML={{ __html: page.html_content }} 
          />
        )}
      </article>

      {/* Render custom JS */}
      {page.custom_js && (
        <script dangerouslySetInnerHTML={{ __html: page.custom_js }} />
      )}
    </div>
  );
};

export default PagePreview;