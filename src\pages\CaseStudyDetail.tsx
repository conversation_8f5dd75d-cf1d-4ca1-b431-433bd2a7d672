import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { FilePieChart as File<PERSON>hart, Building2, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import rehypeRaw from 'rehype-raw';
import { useAuthStore } from '../store/authStore';
import { useNavigate } from 'react-router-dom';
import defaultImage from '../assets/images/default-image.jpg';

interface CaseStudy {
  id: string;
  title: string;
  organization: string;
  impact: string;
  category: string;
  image_url: string;
  content: string;
}

export default function CaseStudyDetail() {
  const user = useAuthStore((state) => state.user);
  const { id } = useParams();
  const [caseStudy, setCaseStudy] = useState<CaseStudy | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to login if not authenticated
    if (user === null) {
      navigate('/login', { replace: true });
    }
  }, [user, navigate]);

  useEffect(() => {
    const fetchCaseStudy = async () => {
      try {
        const { data, error } = await supabase
          .from('case_studies')
          .select('*')
          .eq('id', id)
          .single();

        if (error) throw error;
        setCaseStudy(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchCaseStudy();
  }, [id]);

  if (loading) return <div className=" flex justify-center py-20">Loading...</div>;
  if (error) return <div className=" flex justify-center py-20 text-red-500">Error: {error}</div>;
  if (!caseStudy) return <div className=" flex justify-center py-20">Case study not found</div>;

  return (
    <div className="">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <Link to="/case-studies" className="flex items-center text-blue-600 mb-6">
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Case Studies
        </Link>
        
        <div className="bg-white rounded-lg shadow-xl overflow-hidden">
          {caseStudy.image_url ? ( <img
            src={caseStudy.image_url}
            alt={caseStudy.title}
            className="w-full h-64 object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.onerror = null;
              target.src = defaultImage;
            }}
          />
          ) : (
            <img
                      src={defaultImage}
                      alt="Default Case Study"
                      className="w-full h-full object-cover"
                    />
          )}
          <div className="p-8">
            <span className="inline-block px-4 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mb-4">
              {caseStudy.category}
            </span>
            <h1 className="text-3xl font-bold mb-4">{caseStudy.title}</h1>
            
            <div className="space-y-4 mb-8">
              <div className="flex items-center text-gray-600">
                <Building2 className="h-5 w-5 mr-2" />
                <span>{caseStudy.organization}</span>
              </div>
              <div className="flex items-center text-gray-600">
                <FileChart className="h-5 w-5 mr-2" />
                <span>{caseStudy.impact}</span>
              </div>
            </div>
            
            {/* Replace the ReactMarkdown component in the return statement with: */}
            <div className="prose max-w-none">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
                components={{
                  h1: ({ children }) => <h1 className="text-3xl font-bold mt-10 mb-6">{children}</h1>,
                  h2: ({ children }) => (
                    <h2 className="text-2xl font-bold mt-8 mb-4 border-b pb-2">{children}</h2>
                  ),
                  h3: ({ children }) => <h3 className="text-xl font-semibold mt-6 mb-3">{children}</h3>,
                  p: ({ children }) => <p className="mb-6 text-gray-700 leading-relaxed">{children}</p>,
                  ul: ({ children }) => <ul className="list-disc pl-8 mb-6 space-y-2">{children}</ul>,
                  ol: ({ children }) => <ol className="list-decimal pl-8 mb-6 space-y-2">{children}</ol>,
                  li: ({ children }) => <li className="mb-2">{children}</li>,
                  blockquote: ({ children }) => (
                    <blockquote className="border-l-4 border-blue-500 pl-4 italic bg-gray-50 py-2 mb-6 text-gray-600">
                      {children}
                    </blockquote>
                  ),
                  a: ({ children, href }) => (
                    <a href={href} className="text-blue-600 hover:text-blue-800 underline">
                      {children}
                    </a>
                  ),
                  img: ({ src, alt }) => (
                    <img src={src} alt={alt} className="my-6 rounded-lg shadow-md max-w-full h-auto" />
                  ),
                  table: ({ children }) => (
                    <table className="w-full mb-6 border-collapse">{children}</table>
                  ),
                  th: ({ children }) => (
                    <th className="py-2 px-4 border bg-gray-100 font-semibold text-left">{children}</th>
                  ),
                  td: ({ children }) => <td className="py-2 px-4 border">{children}</td>,
                  code: ({ inline, className, children }) => {
                    const match = /language-(\w+)/.exec(className || '');
                    return !inline ? (
                      <SyntaxHighlighter
                        style={atomDark}
                        language={match?.[1] || 'javascript'}
                        className="rounded-lg mb-6 text-sm"
                        showLineNumbers
                        wrapLines
                      >
                        {String(children).replace(/\n$/, '')}
                      </SyntaxHighlighter>
                    ) : (
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                        {children}
                      </code>
                    );
                  },
                  u: ({ children }) => <u className="underline">{children}</u>,
                }}
              >
                {
                  // Preprocess to convert ++text++ to <u>text</u>
                  caseStudy.content.replace(/\+\+([^\+]+)\+\+/g, '<u>$1</u>')
                }
              </ReactMarkdown>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}