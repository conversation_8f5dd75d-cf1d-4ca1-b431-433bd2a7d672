import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.21.0';
import Stripe from 'https://esm.sh/stripe@12.4.0?target=deno';
// Set up CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json'
};
serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({
        error: 'Method not allowed'
      }), {
        status: 405,
        headers: {
          ...corsHeaders
        }
      });
    }
    // Get the request body
    const { userId } = await req.json();
    if (!userId) {
      return new Response(JSON.stringify({
        error: 'User ID is required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders
        }
      });
    }
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '', {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    // Create Stripe instance
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
      apiVersion: '2023-10-16',
      httpClient: Stripe.createFetchHttpClient()
    });
    // Get the user making the request
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({
        error: 'Missing authorization header'
      }), {
        status: 401,
        headers: {
          ...corsHeaders
        }
      });
    }
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    if (userError || !user) {
      return new Response(JSON.stringify({
        error: 'Unauthorized'
      }), {
        status: 401,
        headers: {
          ...corsHeaders
        }
      });
    }
    // Check if the user is an admin
    const { data: profile, error: profileError } = await supabaseAdmin.from('profiles').select('role').eq('id', user.id).single();
    if (profileError || !profile || profile.role !== 'admin') {
      return new Response(JSON.stringify({
        error: 'Unauthorized - Admin privileges required'
      }), {
        status: 403,
        headers: {
          ...corsHeaders
        }
      });
    }
    // --- Start cleanup process ---
    const cleanupResults = [];
    try {
      // 1. Handle Stripe subscriptions first
      const { data: activeSubscriptions, error: subsError } = await supabaseAdmin.from('subscriptions').select('id, stripe_subscription_id, status').eq('user_id', userId).in('status', [
        'active',
        'trialing',
        'past_due'
      ]);
      if (subsError) {
        cleanupResults.push({
          success: false,
          table: 'subscriptions',
          error: subsError.message
        });
      } else if (activeSubscriptions) {
        for (const sub of activeSubscriptions){
          if (sub.stripe_subscription_id) {
            try {
              // Cancel the subscription in Stripe
              await stripe.subscriptions.cancel(sub.stripe_subscription_id, {
                invoice_now: true,
                prorate: true
              });
            } catch (stripeError) {
              cleanupResults.push({
                success: false,
                table: 'stripe_subscription',
                error: stripeError.message
              });
            }
          }
          // Update subscription status in database
          const { error: updateError } = await supabaseAdmin.from('subscriptions').update({
            status: 'canceled',
            deleted: true,
            deleted_at: new Date().toISOString()
          }).eq('id', sub.id);
          if (updateError) {
            cleanupResults.push({
              success: false,
              table: 'subscription_update',
              error: updateError.message
            });
          }
        }
      }
      // 2. Clean up subscription_items
      const { error: itemsError } = await supabaseAdmin.from('subscription_items').update({
        status: 'canceled'
      }).eq('subscription_id', activeSubscriptions?.map((s)=>s.id) || []);
      if (itemsError) {
        cleanupResults.push({
          success: false,
          table: 'subscription_items',
          error: itemsError.message
        });
      }
      // 3. Delete user's cart
      const { error: cartError } = await supabaseAdmin.from('user_carts').delete().eq('user_id', userId);
      if (cartError) {
        cleanupResults.push({
          success: false,
          table: 'user_carts',
          error: cartError.message
        });
      }
      // 4. Delete blog posts and comments
      const { error: postsError } = await supabaseAdmin.from('blog_posts').delete().eq('author_id', userId);
      if (postsError) {
        cleanupResults.push({
          success: false,
          table: 'blog_posts',
          error: postsError.message
        });
      }
      const { error: commentsError } = await supabaseAdmin.from('blog_comments').delete().eq('user_id', userId);
      if (commentsError) {
        cleanupResults.push({
          success: false,
          table: 'blog_comments',
          error: commentsError.message
        });
      }
      // 5. Cancel event registrations
      const { error: eventsError } = await supabaseAdmin.from('event_registrations').update({
        status: 'cancelled'
      }).eq('user_id', userId);
      if (eventsError) {
        cleanupResults.push({
          success: false,
          table: 'event_registrations',
          error: eventsError.message
        });
      }
      // 6. Update support tickets
      const { error: ticketsError } = await supabaseAdmin.from('support_tickets').update({
        status: 'closed'
      }).eq('user_id', userId);
      if (ticketsError) {
        cleanupResults.push({
          success: false,
          table: 'support_tickets',
          error: ticketsError.message
        });
      }
      // If any cleanup failed, log it but continue with deletion
      if (cleanupResults.some((r)=>!r.success)) {
        console.error('Some cleanup operations failed:', cleanupResults);
      }
      // Finally, delete the user from auth.users (this will cascade to profiles)
      const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(userId);
      if (deleteError) {
        return new Response(JSON.stringify({
          error: deleteError.message,
          cleanupResults
        }), {
          status: 400,
          headers: {
            ...corsHeaders
          }
        });
      }
      // Return success with cleanup results
      return new Response(JSON.stringify({
        success: true,
        message: 'User and related data deleted successfully',
        cleanupResults
      }), {
        status: 200,
        headers: {
          ...corsHeaders
        }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Failed to delete user and related data',
        details: error.message,
        cleanupResults
      }), {
        status: 500,
        headers: {
          ...corsHeaders
        }
      });
    }
  } catch (error) {
    return new Response(JSON.stringify({
      error: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders
      }
    });
  }
});
