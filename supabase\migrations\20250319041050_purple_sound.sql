-- Add role column to profiles table
DO $$ 
BEGIN
  -- Add role column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' 
    AND column_name = 'role'
  ) THEN
    ALTER TABLE profiles 
    ADD COLUMN role text DEFAULT 'user';

    -- Add check constraint
    ALTER TABLE profiles 
    ADD CONSTRAINT profiles_role_check 
    CHECK (role IN ('admin', 'user'));
  END IF;
END $$;

-- Create admin check function
CREATE OR REPLACE FUNCTION is_admin(user_email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles
    WHERE email = user_email
    AND role = 'admin'
  );
END;
$$;

-- Create function to update last login
CREATE OR REPLACE FUNCTION update_last_login(user_email text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE profiles
  SET updated_at = now()
  WHERE email = user_email;
END;
$$;

-- <PERSON> execute permissions
GRANT EXECUTE ON FUNCTION is_admin TO authenticated;
GRANT EXECUTE ON FUNCTION update_last_login TO authenticated;

-- Update existing admin users
UPDATE profiles 
SET role = 'admin'
WHERE email IN ('<EMAIL>', '<EMAIL>');