-- First check and drop existing objects if they exist
DO $$ 
BEGIN
  -- Drop existing functions
  DROP FUNCTION IF EXISTS create_user(text,text,text);
  DROP FUNCTION IF EXISTS verify_user(text,text);
  DROP FUNCTION IF EXISTS update_user_last_login(text);
  DROP FUNCTION IF EXISTS request_password_reset(text);
  DROP FUNCTION IF EXISTS validate_reset_token(text,text);
  DROP FUNCTION IF EXISTS reset_password(text,text,text);

  -- Drop existing table if it exists
  DROP TABLE IF EXISTS users;
END $$;

-- Create users table
CREATE TABLE users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  password_hash text NOT NULL,
  full_name text,
  created_at timestamptz DEFAULT now(),
  last_login timestamptz,
  reset_token text,
  reset_token_expires timestamptz,
  CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can read own data"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own data"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid()::text = id::text);

-- Function to create new user
CREATE OR REPLACE FUNCTION create_user(
  user_email text,
  user_password text,
  user_full_name text
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id uuid;
BEGIN
  -- Validate email format
  IF NOT (user_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$') THEN
    RAISE EXCEPTION 'Invalid email format';
  END IF;

  -- Check if email already exists
  IF EXISTS (SELECT 1 FROM users WHERE email = user_email) THEN
    RAISE EXCEPTION 'Email already registered';
  END IF;

  -- Create new user
  INSERT INTO users (email, password_hash, full_name)
  VALUES (
    user_email,
    crypt(user_password, gen_salt('bf')),
    user_full_name
  )
  RETURNING id INTO new_user_id;

  RETURN new_user_id;
END;
$$;

-- Function to verify user credentials
CREATE OR REPLACE FUNCTION verify_user(
  user_email text,
  user_password text
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM users
    WHERE email = user_email
    AND password_hash = crypt(user_password, password_hash)
  );
END;
$$;

-- Function to update last login
CREATE OR REPLACE FUNCTION update_user_last_login(user_email text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE users
  SET last_login = now()
  WHERE email = user_email;
END;
$$;

-- Function to initiate password reset
CREATE OR REPLACE FUNCTION request_password_reset(user_email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  reset_token_value text;
BEGIN
  -- Check if user exists
  IF NOT EXISTS (SELECT 1 FROM users WHERE email = user_email) THEN
    RETURN false;
  END IF;

  -- Generate reset token
  reset_token_value := encode(gen_random_bytes(32), 'hex');

  -- Update user with reset token
  UPDATE users
  SET 
    reset_token = reset_token_value,
    reset_token_expires = now() + interval '1 hour'
  WHERE email = user_email;

  RETURN true;
END;
$$;

-- Function to validate reset token
CREATE OR REPLACE FUNCTION validate_reset_token(
  user_email text,
  token text
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM users
    WHERE email = user_email
    AND reset_token = token
    AND reset_token_expires > now()
  );
END;
$$;

-- Function to reset password
CREATE OR REPLACE FUNCTION reset_password(
  user_email text,
  token text,
  new_password text
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Validate token first
  IF NOT EXISTS (
    SELECT 1
    FROM users
    WHERE email = user_email
    AND reset_token = token
    AND reset_token_expires > now()
  ) THEN
    RETURN false;
  END IF;

  -- Update password and clear reset token
  UPDATE users
  SET 
    password_hash = crypt(new_password, gen_salt('bf')),
    reset_token = NULL,
    reset_token_expires = NULL
  WHERE email = user_email;

  RETURN true;
END;
$$;

-- Grant necessary permissions
GRANT ALL ON users TO authenticated;
GRANT EXECUTE ON FUNCTION create_user TO anon;
GRANT EXECUTE ON FUNCTION verify_user TO anon;
GRANT EXECUTE ON FUNCTION update_user_last_login TO authenticated;
GRANT EXECUTE ON FUNCTION request_password_reset TO anon;
GRANT EXECUTE ON FUNCTION validate_reset_token TO anon;
GRANT EXECUTE ON FUNCTION reset_password TO anon;