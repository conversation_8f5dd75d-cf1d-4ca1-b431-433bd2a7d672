import React, { useRef } from 'react';
import { Link } from 'react-router-dom';
import soarBgVideoPart1 from '../../assets/video/soar-bgbanner-part1.mp4';
import soarBgVideoPart2 from '../../assets/video/soar-bgbanner-part2.mp4';
import { ArrowRight } from 'lucide-react';

interface ButtonProps {
    to: string;
    variant: 'primary' | 'secondary';
    label: string;
    icon?: React.ReactNode;
}

const Banner: React.FC = () => {
    const videoRef = useRef<HTMLVideoElement | null>(null);
    return (
      <div className="relative w-full overflow-hidden rounded-lg">
        {/* Full-width background container for 2xl+ screens */}
        <div
          className="relative w-full h-[500px] sm:h-[380px] md:h-[460px] lg:h-[520px] xl:h-[580px] 2xl:h-[620px] rounded-lg md:rounded-[20px] 2xl:rounded-none shadow-2xl 2xl:shadow-none"
          style={{ background: "linear-gradient(to right, #522b1a, #000021)" }}
        >
          {/* Content container with max-width constraint */}
          <div className="relative max-w-[1900px] mx-auto h-full overflow-hidden shadow-2xl 2xl:shadow-2xl">
            {/* Responsive Video Background - Mobile optimized, Desktop split */}
            <div className="absolute inset-0 w-full h-full">
              {/* Mobile: Single Video (Full Width) */}
              <div className="block md:hidden w-full h-full overflow-hidden relative">
                <video
                  autoPlay
                  loop
                  muted
                  playsInline
                  className="w-full h-full object-cover"
                >
                  <source src={soarBgVideoPart1} type="video/mp4" />
                </video>
              </div>

              {/* Desktop: Split Video Layout */}
              <div className="hidden md:flex w-full h-full">
                {/* Left Side - Video Part 1 (Larger) */}
                <div className="w-2/3 h-full overflow-hidden relative">
                  <video
                    autoPlay
                    loop
                    muted
                    playsInline
                    className="w-full h-full object-cover"
                  >
                    <source src={soarBgVideoPart1} type="video/mp4" />
                  </video>
                </div>

                {/* Right Side - Video Part 2 (Smaller) */}
                <div className="w-1/3 h-full overflow-hidden relative">
                  <video
                    autoPlay
                    loop
                    muted
                    playsInline
                    className="w-full h-full object-cover"
                  >
                    <source src={soarBgVideoPart2} type="video/mp4" />
                  </video>
                </div>
              </div>
            </div>

            {/* Main Content Container - Responsive */}
            <div className="relative z-10 flex items-center justify-center lg:justify-start h-full px-6 sm:px-8 md:px-10 lg:px-12 xl:px-16 2xl:px-20">
              {/* Content Section - Responsive */}
              <div className="space-y-4 max-w-xs sm:max-w-md md:max-w-lg lg:max-w-2xl xl:max-w-3xl 2xl:max-w-4xl text-center lg:text-left sm:space-y-6 lg:space-y-8">
                {/* Main Heading - Responsive Typography */}
                <div
                  className="space-y-1 sm:space-y-2 animate-fadeInUp"
                  style={{ animationDelay: "0.2s" }}
                >
                  <h1 className="font-platform text-black font-medium mb-2 leading-tight tracking-tight">
                    <span className="block">
                      <span
                        className="bg-clip-text text-transparent"
                        style={{
                          fontSize: "clamp(25px, 5vw, 60px)",
                          background:
                            "linear-gradient(88.01deg, #FD584A 2.18%, #EE8149 37.85%)",
                          WebkitBackgroundClip: "text",
                          backgroundClip: "text",
                        }}
                      >
                        Predict
                      </span>
                      <span
                        style={{
                          fontSize: "clamp(25px, 5vw, 60px)",
                          WebkitBackgroundClip: "text",
                          backgroundClip: "text",
                        }}
                        className="text-white ml-2 font-normal"
                      >
                        Tomorrow
                      </span>
                    </span>
                    <span
                      style={{
                        fontSize: "clamp(25px, 5vw, 60px)",
                        WebkitBackgroundClip: "text",
                        backgroundClip: "text",
                      }}
                      className="text-white font-normal"
                    >
                      Act Today
                    </span>
                  </h1>
                </div>

                {/* Description - Responsive */}
                <div
                  className="flex flex-col gap-y-8 animate-fadeInUp w-fit"
                  style={{ animationDelay: "0.4s" }}
                >
                  <p
                    className="text-white font-inter max-w-xs sm:max-w-sm md:max-w-md lg:max-w-xl xl:max-w-xl"
                    style={{
                      fontWeight: 500,
                      fontSize: "clamp(16px, 2.5vw, 20px)",
                      lineHeight: "clamp(20px, 3.1vw, 30px)",
                      letterSpacing: "0%",
                    }}
                  >
                    We are working to save lives and stop the spread of
                    outbreaks using the latest forecasting models
                  </p>

                  <a
                    href="https://www.cdc.gov/insight-net/php/about/index.html?CDC_AA_refVal=https%3A%2F%2Fwww.cdc.gov%2Fforecast-outbreak-analytics%2Fabout%2Foadm-network.html"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white w-fit px-4 py-3 rounded-md flex items-center gap-2 relative overflow-hidden group transition-all duration-300 cursor-pointer"
                  >
                    {/* Parallelogram sliding effect */}
                    <div
                      className="absolute inset-0 bg-gradient-to-r from-[#7EAEED] to-[#79B8AEA8] transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-500 ease-out"
                      style={{ width: "200px" }}
                    ></div>

                    {/* Button content */}
                    <span className="relative z-10 font-semibold">
                      Learn more about our partnership with the CDC
                    </span>
                    <span className="relative z-10">
                      <ArrowRight className="transition-all duration-300 group-hover:translate-x-1" />
                    </span>
                  </a>
                </div>
              </div>
            </div>

            {/* Custom CSS for animations */}
            <style
              dangerouslySetInnerHTML={{
                __html: `
                        @keyframes fadeInUp {
                            0% {
                                opacity: 0;
                                transform: translateY(30px);
                            }
                            100% {
                                opacity: 1;
                                transform: translateY(0);
                            }
                        }

                        .animate-fadeInUp {
                            animation: fadeInUp 0.8s ease-out forwards;
                            opacity: 0;
                        }
                    `,
              }}
            />
          </div>
        </div>
      </div>
    );
};

// Button Component - Responsive
const ButtonLink: React.FC<ButtonProps> = ({ to, variant, label, icon }) => {
    const baseStyles = 'group inline-flex items-center justify-center px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-semibold rounded-lg transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-lg w-full sm:w-auto';

    const variants = {
        primary: 'bg-white text-black hover:bg-gray-100 focus:ring-white/50 shadow-white/25',
        secondary: 'bg-transparent border-2 border-[#F75849] text-white hover:bg-[#F75849]/10 focus:ring-[#F75849]/50',
    };

    return (
        <Link
            to={to}
            className={`${baseStyles} ${variants[variant]}`}
            aria-label={label}
        >
            {label}
            {icon && icon}
        </Link>
    );
};

export default Banner;