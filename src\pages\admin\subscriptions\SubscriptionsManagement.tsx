import React, { useState, useEffect } from 'react';
import { supabase } from '../../../lib/supabase';
import {
  X,
  RefreshCw,
  AlertCircle,
} from 'lucide-react';
import SubscriptionsTable from './SubscriptionsTable';
import SubscriptionDetails from './SubscriptionDetails';

interface Subscription {
  id: string;
  user_id: string;
  product_id: string;
  status: string;
  period: 'monthly' | 'yearly';
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  quantity: number;
  deleted: boolean;
  deleted_at?: string | null;
  ref?: string | null;
  stripe_subscription_id?: string | null;
  user_email?: string;
  plan_name?: string;
}

interface Plan {
  id: string;
  name: string;
  description: string;
  price: number;
  features: string[];
}

interface User {
  id: string;
  email: string;
}

interface SubscriptionItem {
  id: string;
  product_id: string;
  stripe_price_id: string;
  quantity: number;
  status: string;
  product_name?: string;
  stripe_subscription_item_id?: string;
  created_at?: string;
}

// Enhance StripeDetails to match the new Edge Function response
interface StripeDetails {
  // Existing
  payment_method_details?: string;
  recent_invoices?: Array<{
    id: string;
    created: number;
    total: number;
    status: string;
    invoice_pdf?: string;
    due_date?: number | null; // Added
  }>;
  // New fields from Edge Function
  status?: string; // Stripe's status
  current_period_start?: number;
  current_period_end?: number;
  cancel_at_period_end?: boolean;
  canceled_at?: number | null;
  created?: number;
  customer_email?: string | null;
  schedule?: any | null;
  pending_update?: any | null;
  stripe_items?: Array<{
    id: string;
    stripe_price_id: string;
    product_name: string;
    product_id: string | null;
    quantity: number;
  }>;
  latest_invoice_id?: string | null;
}


export default function SubscriptionsManagement() {
  // --- Ensure ALL state declarations are here, at the top of the function ---
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [products, setProducts] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState<Subscription | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [isViewingDetails, setIsViewingDetails] = useState(false); // Make sure this line exists and is not commented out
  const [selectedSubscriptionDetails, setSelectedSubscriptionDetails] = useState<Subscription | null>(null);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [selectedSubscriptionItems, setSelectedSubscriptionItems] = useState<SubscriptionItem[]>([]); // Assuming SubscriptionItem interface exists
  const [selectedStripeDetails, setSelectedStripeDetails] = useState<StripeDetails | null>(null); // Ensure this uses the updated interface
  const [detailsError, setDetailsError] = useState<string | null>(null);
  // --- Add pagination state ---
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [statusFilter, setStatusFilter] = useState('all');
  // --- Update billing cycle filter state type and default ---
  const [billingCycleFilter, setBillingCycleFilter] = useState<'all' | 'month' | 'year'>('all');
  // --- End of state declarations ---

  useEffect(() => {
    fetchSubscriptionsCount();
    fetchSubscriptions();
    fetchProducts();
  }, [currentPage, itemsPerPage, searchTerm, statusFilter, billingCycleFilter]);

  // Add the missing fetchSubscriptionsCount function
  const fetchSubscriptionsCount = async () => {
    try {
      console.log(`fetchSubscriptionsCount: statusFilter='${statusFilter}', billingCycleFilter='${billingCycleFilter}', searchTerm='${searchTerm}'`);

      let query = supabase
        .from('subscriptions')
        .select('id', { count: 'exact', head: true });

      // Apply status filter
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      // --- Apply billing cycle filter (using 'month'/'year') ---
      if (billingCycleFilter !== 'all') {
        query = query.eq('period', billingCycleFilter); // Compare with 'month' or 'year'
      }

      // Apply search filter
      if (searchTerm) {
        query = query
          .or(`profiles.email.ilike.%${searchTerm}%,profiles.full_name.ilike.%${searchTerm}%`);
      }

      const { count, error } = await query;

      console.log(`fetchSubscriptionsCount Result: count=${count}, error=`, error);

      if (error) throw error;

      setTotalItems(count || 0);
      setTotalPages(Math.ceil((count || 0) / itemsPerPage));
    } catch (err: any) {
      console.error("Error fetching subscription count:", err);
      setError(`Failed to load subscription count: ${err.message}`);
    }
  };

  // --- Add status filter handler ---
  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset to first page on filter change
  };

  // --- Add billing cycle filter handler ---
  const handleBillingCycleFilterChange = (cycle: 'all' | 'month' | 'year') => { // Use 'month'/'year'
    setBillingCycleFilter(cycle);
    setCurrentPage(1); // Reset to first page on filter change
  };


  // Add pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('id, email');

        if (error) throw error;
        setUsers(data || []);
      } catch (err) {
        console.error('Error fetching users:', err);
      }
    };
    fetchUsers();
  }, []);

  // Inside SubscriptionsManagement component

  const fetchSubscriptions = async () => {
    setLoading(true);
    setError(null);
    try {
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;

      let query = supabase
        .from('subscriptions')
        .select(`
          *,
          products ( name ),
          profiles ( email, full_name )
        `)
        .order('created_at', { ascending: false })
        .range(from, to);

      // Apply status filter
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      // --- Apply billing cycle filter (using 'month'/'year') ---
      if (billingCycleFilter !== 'all') {
        query = query.eq('period', billingCycleFilter); // Compare with 'month' or 'year'
      }

      const { data, error } = await query;
      console.log(`fetchSubscriptions Result: data=`, data, `error=`, error);
      if (error) throw error;

      // --- Process data using 'products' ---
      const processedData = data.map(sub => ({
        ...sub,
        user_email: (sub.profiles as any)?.email || null,
        user_full_name: (sub.profiles as any)?.full_name || null,
        plan_name: (sub.products as any)?.name || 'Unknown Product', // <<< Use 'products' here and maybe adjust fallback text
        profiles: undefined, // Clean up nested object
        products: undefined, // <<< Clean up nested 'products' object
      }));

      setSubscriptions(processedData as Subscription[]);
    } catch (err: any) {
      console.error("Error fetching subscriptions:", err);
      setError(`Failed to load subscriptions: ${err.message}`);
      setSubscriptions([]);
      console.log(`fetchSubscriptions Error Caught:`, err); // <-- Add log
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*');

      if (error) throw error;
      setProducts(data || []);
    } catch (err) {
      console.error('Error fetching products:', err);
      setError('Failed to load products');
    }
  };

  const handleEdit = (subscription: Subscription) => {
    setCurrentSubscription(subscription);
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setCurrentSubscription(null);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this subscription?')) {
      try {
        const { error } = await supabase
          .from('subscriptions')
          .delete()
          .eq('id', id);

        if (error) throw error;

        setSubscriptions(subscriptions.filter(sub => sub.id !== id));
      } catch (err) {
        console.error('Error deleting subscription:', err);
        setError('Failed to delete subscription');
      }
    }
  };

  const handleSaveEdit = async () => {
    if (!currentSubscription) return;

    try {
      // Prepare only the updatable fields
      const updateData: Partial<Subscription> = {
        product_id: currentSubscription.product_id,
        status: currentSubscription.status,
        current_period_end: currentSubscription.current_period_end,
        // Add other fields you allow editing here
      };

      const { error } = await supabase
        .from('subscriptions')
        .update(updateData)
        .eq('id', currentSubscription.id);

      if (error) throw error;

      // Refetch or update local state more accurately
      // Option 1: Update local state (make sure plan_name is updated if product_id changed)
      const updatedSub = {
        ...currentSubscription,
        plan_name: products.find(p => p.id === currentSubscription.product_id)?.name || 'Unknown'
      };
      setSubscriptions(subscriptions.map(sub =>
        sub.id === currentSubscription.id ? updatedSub : sub
      ));

      setIsEditing(false);
      setCurrentSubscription(null);
    } catch (err) {
      console.error('Error updating subscription:', err);
      setError('Failed to update subscription');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <RefreshCw className="h-8 w-8 text-blue-600 animate-spin" />
      </div>
    );
  }

  // Function to fetch extra details (ensure this exists if you implemented it)
  const fetchSubscriptionExtraDetails = async (subscription: Subscription) => {
    if (!subscription) return;

    setDetailsLoading(true);
    setDetailsError(null);
    setSelectedSubscriptionItems([]);
    setSelectedStripeDetails(null);

    try {
      // Fetch items
      const { data: itemsData, error: itemsError } = await supabase
        .from('subscription_items') // Adjust table name if needed
        .select('*, products(name)')
        .eq('subscription_id', subscription.id);

      if (itemsError) throw new Error(`Failed to fetch subscription items: ${itemsError.message}`);

      const itemsWithProductNames = itemsData?.map(item => ({
        ...item,
        product_name: (item.products as any)?.name || 'Unknown Product'
      })) || [];
      setSelectedSubscriptionItems(itemsWithProductNames);

      // Fetch Stripe details via Edge Function
      if (subscription.stripe_subscription_id) {
        const { data: stripeData, error: functionError } = await supabase.functions.invoke(
          'get-stripe-subscription-details',
          { body: { stripeSubscriptionId: subscription.stripe_subscription_id } }
        );

        if (functionError) {
          console.error('Edge Function Error:', functionError);
          setDetailsError('Could not fetch details from Stripe.');
        } else {
          setSelectedStripeDetails(stripeData as StripeDetails);
        }
      }
    } catch (err: any) {
      console.error('Error fetching subscription details:', err);
      setDetailsError(err.message || 'Failed to load subscription details');
    } finally {
      setDetailsLoading(false);
    }
  };

  // Handler to open the details view
  const handleViewDetails = (subscription: Subscription) => {
    setSelectedSubscriptionDetails(subscription);
    setIsViewingDetails(true); // Uses setIsViewingDetails
    setIsEditing(false);
    setIsAdding(false);
    setCurrentSubscription(null);
    fetchSubscriptionExtraDetails(subscription); // Fetch details when opening
  };

  // Handler to close the details view
  const handleCloseDetails = () => {
    setIsViewingDetails(false); // Uses setIsViewingDetails
    setSelectedSubscriptionDetails(null);
    setSelectedSubscriptionItems([]);
    setSelectedStripeDetails(null);
    setDetailsError(null);
  };



  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-800">Subscriptions</h2>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
          <button
            className="ml-auto text-red-700 hover:text-red-900"
            onClick={() => setError(null)}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
      )}

      {isViewingDetails && selectedSubscriptionDetails && (
        <SubscriptionDetails
          subscription={selectedSubscriptionDetails}
          items={selectedSubscriptionItems} // Pass DB items
          stripeDetails={selectedStripeDetails} // Pass enhanced Stripe details
          isLoading={detailsLoading}
          error={detailsError}
          onClose={handleCloseDetails}
        />
      )}

      {/* Render the SubscriptionsTable component */}
      <div className="mt-6">
        <SubscriptionsTable
          subscriptions={subscriptions}
          products={products}
          isEditing={isEditing}
          currentSubscription={currentSubscription}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onSaveEdit={handleSaveEdit}
          onCancelEdit={handleCancelEdit}
          setCurrentSubscription={setCurrentSubscription}
          onViewDetails={handleViewDetails} // Ensure this prop is passed
          // Add pagination props
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
          // --- Pass filter state and handler ---
          statusFilter={statusFilter}
          onStatusFilterChange={handleStatusFilterChange}
          // --- Pass updated billing cycle filter props ---
          billingCycleFilter={billingCycleFilter} // Pass the state value ('all', 'month', 'year')
          onBillingCycleFilterChange={handleBillingCycleFilterChange} // Pass the updated handler
        />
      </div>

    </div>
  );
}