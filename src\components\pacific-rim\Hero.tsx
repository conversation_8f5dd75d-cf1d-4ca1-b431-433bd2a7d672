import { Award, ArrowRight, FileDown, Sparkles, Globe, Microscope, Check } from 'lucide-react';
import heroImage from '../../assets/images/pacific-rim-heroimg.jpg';
import narrativePDF from '../../assets/docs/pacific-rim-narrative.pdf';

export const Hero = () => {
  return (
    <section className="relative min-h-[50vh] 2xl:min-h-[70vh] flex items-center bg-gradient-to-br from-blue-600 via-purple-500 to-orange-500 text-white overflow-hidden">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-[url('/assets/images/pattern-grid.svg')] opacity-5"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/90 via-purple-500/90 to-orange-500/90"></div>
        
        {/* Floating particles effect */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-3/4 left-3/4 w-48 h-48 bg-orange-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 w-56 h-56 bg-purple-300/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative py-8 2xl:py-12">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8 animate-[fadeIn_1s_ease-out]">
            <div className="flex gap-4 items-center">
              <div className="inline-flex items-center px-4 py-2 bg-white/10 rounded-full backdrop-blur-sm border border-white/20">
                <Award className="h-5 w-5 mr-2 text-orange-300" />
                <span>CDC Cooperative Agreement Recipient</span>
              </div>
              <div className="animate-bounce">
                <Sparkles className="h-6 w-6 text-orange-300" />
              </div>
            </div>
            
            <div className="space-y-4">
              <h1 className="text-6xl font-bold leading-tight tracking-tight">
                Pacific Rim
                <span className="block bg-gradient-to-r from-orange-300 via-white to-purple-300 bg-clip-text text-transparent">
                  Forecasting Initiative
                </span>
              </h1>
              
              <p className="text-xl text-gray-200 leading-relaxed max-w-xl">
                Revolutionizing public health through scalable metagenomic surveillance and advanced analytics in laboratories across the Pacific Rim region.
              </p>
            </div>
            
            <div className="flex flex-wrap gap-4">
              <a
                href="https://www.cdc.gov/forecast-outbreak-analytics/partners/insightnet/index.html"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center bg-white text-purple-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 group"
              >
                Explore Insight Net
                <ArrowRight className="ml-2 h-5 w-5 transform group-hover:translate-x-1 transition-transform" />
              </a>
              <a
                href={narrativePDF}
                download="pacific-rim-narrative.pdf"
                className="inline-flex items-center bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/20 transition-all duration-300 group border border-white/20"
              >
                Download Narrative
                <FileDown className="ml-2 h-5 w-5 transform group-hover:translate-y-0.5 transition-transform" />
              </a>
            </div>
          </div>

          {/* Right Content - Hero Image */}
          <div className="relative hidden lg:block">
            <div className="relative animate-[float_6s_ease-in-out_infinite] will-change-transform">
              <div className="absolute -inset-0.5 bg-gradient-to-r from-orange-300 to-purple-300 rounded-3xl blur opacity-30 group-hover:opacity-100 transition duration-1000"></div>
              <div className="relative">
                <img
                  src={heroImage}
                  alt="Pacific Rim Initiative Visualization"
                  className="w-full h-auto rounded-3xl shadow-2xl"
                />
                <div className="absolute -left-8 top-1/4 bg-white/10 backdrop-blur-lg p-4 rounded-2xl border border-white/20 animate-[float_4s_ease-in-out_infinite] shadow-xl">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100/10 rounded-lg">
                      <Globe className="h-6 w-6 text-orange-300" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-300">Coverage</p>
                      <p className="font-semibold text-white">25+ State Labs</p>
                    </div>
                  </div>
                </div>
                <div className="absolute -right-8 bottom-1/4 bg-white/10 backdrop-blur-lg p-4 rounded-2xl border border-white/20 animate-[float_5s_ease-in-out_infinite] shadow-xl">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100/10 rounded-lg">
                      <Microscope className="h-6 w-6 text-purple-300" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-300">Analysis</p>
                      <p className="font-semibold text-white">Real-time Data</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
