name: Deploy IRS to PROD Environment

on:
  push:
    branches: [v4prod]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies
        run: |
          npm ci
          npm list # Debug: Show installed dependencies

      - name: Build the frontend
        run: |
          npm run build
          sync # Ensure file writes are complete
          ls -la dist # Debug: Verify build output

      - name: Create deployment package
        run: |
          tar -czf deploy.tar.gz dist
          ls -lh deploy.tar.gz

      - name: Install lftp
        run: sudo apt-get update && sudo apt-get install -y lftp

      - name: Debug before FTP upload
        run: |
          pwd
          ls -lh deploy.tar.gz

      - name: Transfer deployment package to server via FTP
        env:
          FTP_HOST: ${{ secrets.SSH_HOST }}
          FTP_USER: ${{ secrets.SSH_USER }}
          FTP_PASS: ${{ secrets.ROOT_PASS }}
          DEPLOY_PATH: ${{ secrets.DEPLOY_PATH }}
        run: |
          lftp -u $FTP_USER,$FTP_PASS $FTP_HOST <<EOF
          set ssl:verify-certificate no
          ls
          put deploy.tar.gz
          bye
          EOF
        

  deploy:
    needs: build
    runs-on: ["self-hosted", "IRS"]

    steps:
      - name: Debug deploy environment
        run: |
          pwd
          ls -la

      - name: Clean workspace except deploy.tar.gz
        run: |
          find . -mindepth 1 ! -name 'deploy.tar.gz' -exec rm -rf {} +

      - name: Verify deployment package presence
        run: |
          if [ ! -f deploy.tar.gz ]; then
            echo "ERROR: deploy.tar.gz not found in $(pwd)!"
            exit 1
          fi
          ls -lh deploy.tar.gz

      - name: Extract deployment package
        run: |
          tar -xzf deploy.tar.gz
          rm deploy.tar.gz
          ls -la

      - name: Final deployment steps
        run: |
          # Add any additional deployment commands here, e.g., restart services
          echo "Deployment completed successfully"
