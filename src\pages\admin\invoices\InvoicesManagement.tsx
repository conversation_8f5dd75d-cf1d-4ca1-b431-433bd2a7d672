import { useState, useEffect } from "react";
import { supabase } from "../../../lib/supabase"; // Adjust path if needed
import InvoicesTable from "./InvoicesTable";
import InvoiceForm from "./InvoiceForm";
// Define interfaces directly or import if they exist elsewhere
interface User {
  id: string;
  email: string;
  full_name: string | null;
  stripe_customer_id: string | null;
}
interface Plan {
  id: string;
  name: string /* add other relevant fields */;
}

// --- Updated Invoice Interface ---
interface Invoice {
  id: string; // Stripe Invoice ID
  stripe_invoice_id: string;
  stripe_customer_id: string | null;
  customer_email: string | null; // Added
  customer_name: string | null; // Added
  status: string | null;
  amount_due: number | null;
  amount_paid: number | null;
  amount_remaining: number | null;
  currency: string | null;
  due_date: string | null; // ISO string
  invoice_pdf: string | null;
  hosted_invoice_url: string | null;
  created_at: string; // ISO string
  is_manual?: boolean;
  line_items?: Array<{
    product_name: string;
    price: number;
    quantity: number;
    billing_cycle: string;
    amount: number;
  }>;
  payment_method?: {
    type: string;
    bank_name?: string;
    account_number?: string;
    routing_number?: string;
  };
  notes?: string;
}
// --- End Updated Invoice Interface ---

import { PlusCircle, AlertCircle, X } from "lucide-react";

export default function InvoicesManagement() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [users, setUsers] = useState<User[]>([]); // For the form
  const [products, setProducts] = useState<Plan[]>([]); // For the form
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentInvoice, setCurrentInvoice] = useState<Invoice | null>(null);
  // --- Updated Pagination State for Cursor Pagination ---
  const [itemsPerPage, setItemsPerPage] = useState(10); // Limit per request
  const [hasNextPage, setHasNextPage] = useState(false); // Replaces totalPages
  const [lastInvoiceIdStack, setLastInvoiceIdStack] = useState<
    (string | undefined)[]
  >([undefined]); // Stack to store 'starting_after' IDs for previous pages
  const [currentPageIndex, setCurrentPageIndex] = useState(0); // Index for the stack

  // Track active tab
  const [activeTab, setActiveTab] = useState<"stripe" | "manual">("manual");
  // --- End Updated Pagination State ---

  useEffect(() => {
    // Fetch invoices when component mounts or when tab, page index, or items per page changes
    fetchInvoices(
      lastInvoiceIdStack[currentPageIndex],
      itemsPerPage,
      activeTab
    );
    fetchUsersAndProducts(); // Fetch data needed for the form
  }, [currentPageIndex, itemsPerPage, activeTab]); // Refetch when tab, page index, or size changes

  const fetchInvoices = async (
    startingAfter: string | undefined,
    limit: number,
    tabType: "stripe" | "manual" = "stripe"
  ) => {
    setLoading(true);
    setError(null);
    try {
      // For manual invoices, use direct Supabase query
      if (tabType === "manual") {
        // Fetch manual invoices directly from the database
        const { data: manualInvoices, error: manualError } = await supabase
          .from("invoices")
          .select("*")
          .eq("is_manual", true)
          .order("created_at", { ascending: false })
          .limit(limit);

        if (manualError) {
          throw manualError;
        }

        // Format manual invoices to match the expected structure
        const formattedInvoices = manualInvoices.map((invoice) => ({
          id: invoice.id,
          stripe_invoice_id: invoice.stripe_invoice_id,
          stripe_customer_id: invoice.stripe_customer_id,
          customer_email: invoice.customer_email,
          customer_name: invoice.customer_name,
          status: invoice.status,
          amount_due: invoice.amount_due,
          amount_paid: invoice.amount_paid,
          amount_remaining: invoice.amount_remaining,
          currency: invoice.currency,
          due_date: invoice.due_date,
          invoice_pdf: null,
          hosted_invoice_url: null,
          created_at: invoice.created_at,
          is_manual: true,
          line_items: invoice.line_items,
          payment_method: invoice.payment_method,
          notes: invoice.notes,
        }));

        setInvoices(formattedInvoices);
        // For now, we don't have pagination for manual invoices
        setHasNextPage(false);
      } else {
        // For Stripe invoices, use the Edge Function
        const { data: functionData, error: functionError } =
          await supabase.functions.invoke("get-stripe-invoices", {
            // --- Pass cursor pagination params and tab filter ---
            body: JSON.stringify({
              limit: limit,
              starting_after: startingAfter,
              include_manual: false,
              include_stripe: true,
            }),
          });

        if (functionError) {
          console.error(
            "Edge function invocation error object:",
            functionError
          );
          throw new Error(`Edge function error: ${functionError.message}`);
        }

        // --- Adjust data validation for cursor pagination ---
        if (
          !functionData ||
          !Array.isArray(functionData.invoices) ||
          typeof functionData.has_more !== "boolean" ||
          (functionData.invoices.length > 0 &&
            typeof functionData.last_invoice_id !== "string" &&
            functionData.last_invoice_id !== null)
        ) {
          console.error(
            "Unexpected data format from Edge Function:",
            functionData
          );
          throw new Error("Received invalid data format from the server.");
        }
        // --- End Adjust data validation ---

        setInvoices(functionData.invoices as Invoice[]);
        // --- Set cursor pagination state ---
        setHasNextPage(functionData.has_more);

        // If navigating forward and there's a next page, add the last ID to the stack
        if (
          startingAfter === lastInvoiceIdStack[currentPageIndex] &&
          functionData.has_more &&
          functionData.last_invoice_id
        ) {
          // Only add if it's a new page fetch and not already the last known ID
          if (lastInvoiceIdStack.length === currentPageIndex + 1) {
            setLastInvoiceIdStack((prev) => [
              ...prev,
              functionData.last_invoice_id,
            ]);
          }
        }
      }
    } catch (err: any) {
      console.error("Error fetching invoices:", err);
      setError(`Failed to load invoices: ${err.message}`);
      setInvoices([]); // Clear invoices on error
      setHasNextPage(false); // Reset pagination on error
    } finally {
      setLoading(false);
    }
  };

  const fetchUsersAndProducts = async () => {
    try {
      const { data: usersData, error: usersError } = await supabase
        .from("profiles")
        .select("id, email, full_name, stripe_customer_id");
      if (usersError) throw usersError;
      setUsers(usersData || []);

      const { data: productsData, error: productsError } = await supabase
        .from("products")
        .select("*"); // Fetch necessary product/price details
      if (productsError) throw productsError;
      setProducts(productsData || []);
    } catch (err: any) {
      console.error("Error fetching users/products:", err);
      setError(
        error
          ? `${error}, Failed to load data for form.`
          : "Failed to load data for form."
      );
    }
  };

  const handleAddInvoice = (_: any) => {
    // Reset to first page and refetch after adding
    setCurrentPageIndex(0);
    setLastInvoiceIdStack([undefined]);
    fetchInvoices(undefined, itemsPerPage, activeTab);
  };

  const handleEditInvoice = async (invoice: Invoice) => {
    setCurrentInvoice(invoice);
    setIsEditing(true);
  };

  const handleUpdateInvoice = (_: any) => {
    // Reset to first page and refetch after updating
    setCurrentPageIndex(0);
    setLastInvoiceIdStack([undefined]);
    fetchInvoices(undefined, itemsPerPage, activeTab);
    setIsEditing(false);
    setCurrentInvoice(null);
  };

  const handleDeleteInvoice = async (invoice: Invoice) => {
    try {
      setLoading(true);

      // Only allow deletion of draft or paid invoices
      if (
        invoice.status !== "draft" &&
        invoice.status !== "paid" &&
        invoice.status !== "open"
      ) {
        throw new Error("Only draft, open, or paid invoices can be deleted");
      }

      // For manual invoices, delete from your database
      if (invoice.is_manual) {
        // For non-draft invoices, show a confirmation dialog
        if (invoice.status !== "draft") {
          const confirmDelete = window.confirm(
            `Are you sure you want to delete this ${invoice.status} invoice? This action cannot be undone.`
          );
          if (!confirmDelete) {
            setLoading(false);
            return;
          }
        }

        const { error: deleteError } = await supabase
          .from("invoices")
          .delete()
          .eq("id", invoice.id);

        if (deleteError) throw deleteError;
      }

      // Refresh the invoice list with the current active tab
      setCurrentPageIndex(0);
      setLastInvoiceIdStack([undefined]);
      fetchInvoices(undefined, itemsPerPage, activeTab);
    } catch (err: any) {
      console.error("Error deleting invoice:", err);
      setError(`Failed to delete invoice: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Add a function to handle adding a new user to the users list
  const handleAddUser = (newUser: User) => {
    setUsers((prevUsers) => [...prevUsers, newUser]);
  };

  // --- Updated Pagination Handlers for Cursor Pagination ---
  const handleNextPage = () => {
    if (hasNextPage) {
      setCurrentPageIndex((prev) => prev + 1);
      // Fetching is handled by the useEffect hook reacting to currentPageIndex change
    }
  };

  const handlePreviousPage = () => {
    if (currentPageIndex > 0) {
      setCurrentPageIndex((prev) => prev - 1);
      // Fetching is handled by the useEffect hook reacting to currentPageIndex change
    }
  };

  const handleItemsPerPageChange = (size: number) => {
    setItemsPerPage(size);
    setCurrentPageIndex(0); // Reset to first page
    setLastInvoiceIdStack([undefined]); // Reset cursor stack
    // Fetching is handled by the useEffect hook reacting to itemsPerPage change
  };
  // Remove: handlePageChange
  // --- End Updated Pagination Handlers ---

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Invoice Management</h2>
        <button
          onClick={() => setIsAdding(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150"
        >
          <PlusCircle className="h-5 w-5 mr-2" />
          Create Invoice
        </button>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
          <button
            className="ml-auto text-red-700 hover:text-red-900"
            onClick={() => setError(null)}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
      )}

      {isAdding && (
        <InvoiceForm
          isAdding={isAdding}
          setIsAdding={setIsAdding}
          users={users}
          products={products}
          onAddInvoice={handleAddInvoice}
          setError={setError}
          onAddUser={handleAddUser}
        />
      )}

      {isEditing && currentInvoice && (
        <InvoiceForm
          isAdding={false}
          setIsAdding={setIsEditing}
          users={users}
          products={products}
          onAddInvoice={handleUpdateInvoice}
          setError={setError}
          onAddUser={handleAddUser}
          invoice={currentInvoice}
          isEditingDraft={currentInvoice.status === "draft"}
        />
      )}

      {!isAdding && !isEditing && (
        <div className="mt-6">
          <InvoicesTable
            invoices={invoices}
            // --- Pass updated/new pagination props ---
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            // Pass handlers and state for Prev/Next buttons
            currentPageIndex={currentPageIndex}
            hasNextPage={hasNextPage}
            onNextPage={handleNextPage}
            onPreviousPage={handlePreviousPage}
            // Pass edit and delete handlers
            onEditInvoice={handleEditInvoice}
            onDeleteInvoice={handleDeleteInvoice}
            // Pass tab state and handler
            activeTab={activeTab}
            onTabChange={(tab) => {
              setActiveTab(tab);
              // Reset pagination when changing tabs
              setCurrentPageIndex(0);
              setLastInvoiceIdStack([undefined]);
              // Fetch invoices for the selected tab
              fetchInvoices(undefined, itemsPerPage, tab);
            }}
            // Pass loading state
            isLoading={loading}
          />
        </div>
      )}
    </div>
  );
}
