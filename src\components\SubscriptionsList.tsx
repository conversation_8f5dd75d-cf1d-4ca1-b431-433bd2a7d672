import React, { useState, useEffect } from "react";
import { useAuthStore } from "../store/authStore";
import { supabase } from "../lib/supabase";
import {
  CreditCard,
  Trash2,
  ShoppingCart,
  AlertCircle,
  CheckCircle,
  X,
} from "lucide-react";
import { Modal } from "./Modal"; // Assuming Modal component is in './Modal'
import { Link } from "react-router-dom";

// Interface for a single subscription item (product details nested)
interface SubscriptionItem {
  quantity: number;
  status: string;
  amount: number; // Add the amount field
  products: {
    // Corresponds to the 'products' table joined via 'subscription_items'
    id: string;
    name: string;
    price: number;
    monthly_discount: number;
    yearly_discount: number;
  } | null; // Product might be null if join fails or product deleted
}

// Updated Subscription interface
interface Subscription {
  id: string;
  ref: string;
  status: string;
  period: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  deleted: boolean;
  subscription_items: SubscriptionItem[]; // Array of items
}

// Define the component function correctly
export default function SubscriptionsList() {
  const { user } = useAuthStore(); // Get user from store
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(false); // Loading state for fetch/delete/cancel
  const [portalLoading, setPortalLoading] = useState(false); // Separate loading for portal redirect
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelSubscriptionId, setCancelSubscriptionId] = useState<
    string | null
  >(null);

  // Fetch subscriptions when the component mounts or user changes
  useEffect(() => {
    if (user?.id) {
      fetchSubscriptions();
    } else {
      // Clear subscriptions if user logs out or is not available
      setSubscriptions([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id]); // Depend only on user.id

  const fetchSubscriptions = async () => {
    if (!user?.id) return; // Guard clause

    setLoading(true);
    setError(null);
    // Don't clear success message here, let it persist briefly
    try {
      const { data, error: fetchError } = await supabase
        .from("subscriptions")
        .select(
          `
          id,
          ref,
          status,
          period,
          current_period_end,
          cancel_at_period_end,
          deleted,
          subscription_items (
            quantity,
            status,
            amount,
            products (
              id,
              name,
              price,
              monthly_discount,
              yearly_discount
            )
          )
        `
        )
        .eq("user_id", user.id)
        .eq("deleted", false) // Filter out deleted subscriptions
        .order("created_at", { ascending: false });

      if (fetchError) throw fetchError;

      if (!data) {
        console.warn("Subscription data received is null or undefined.");
        setSubscriptions([]);
      } else {
        setSubscriptions(data as Subscription[]);
      }
    } catch (err: any) {
      console.error("Subscription fetch error:", err);
      setError(err.message || "Failed to load subscriptions.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!cancelSubscriptionId) return;

    setLoading(true); // Use main loading state
    setError(null);
    setSuccess(null);
    try {
      // --- Updated Supabase Call ---
      // Set cancel_at_period_end to true, keep status active
      const { error: updateError } = await supabase
        .from("subscriptions")
        .update({
          status: "pending cancellation", // Keep status active
          cancel_at_period_end: true, // Set this flag
        })
        .eq("id", cancelSubscriptionId);
      // --- End Update ---

      // Option 2: Call a serverless function to cancel via Stripe API (if needed)
      // This function should also set cancel_at_period_end=true in Stripe
      // const { error: functionError } = await supabase.functions.invoke('cancel-stripe-subscription-at-period-end', {
      //   body: { subscriptionId: cancelSubscriptionId } // Pass the DB subscription ID
      // });
      // if (functionError) throw functionError;

      if (updateError) throw updateError;

      // --- Updated Optimistic Update ---
      // Update local state optimistically
      setSubscriptions((prev) =>
        prev.map((sub) =>
          sub.id === cancelSubscriptionId
            ? { ...sub, cancel_at_period_end: true /* status remains active */ }
            : sub
        )
      );
      // --- End Update ---
      // Or re-fetch for certainty: await fetchSubscriptions();

      setSuccess("Subscription set to cancel at period end."); // Updated success message
    } catch (err: any) {
      console.error("Cancel subscription error:", err);
      setError(err.message || "Failed to schedule subscription cancellation."); // Updated error message
    } finally {
      setLoading(false);
      setShowCancelModal(false);
      setCancelSubscriptionId(null);
    }
  };

  const handleDeleteSubscription = async (subscriptionId: string) => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      const { error: deleteError } = await supabase
        .from("subscriptions")
        .update({ deleted: true, deleted_at: new Date().toISOString() })
        .eq("id", subscriptionId);

      if (deleteError) throw deleteError;

      // Remove from local state
      setSubscriptions((prev) =>
        prev.filter((sub) => sub.id !== subscriptionId)
      );
      setSuccess("Subscription record hidden.");
    } catch (err: any) {
      console.error("Delete subscription error:", err);
      setError(err.message || "Failed to hide subscription record.");
    } finally {
      setLoading(false);
    }
  };

  const handleManageSubscription = async () => {
    setPortalLoading(true);
    setError(null);
    // setSuccess(null); // Not needed, redirects

    try {
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();
      if (sessionError || !session?.access_token) {
        throw new Error(sessionError?.message || "Could not get user session.");
      }

      const { data, error: functionError } = await supabase.functions.invoke(
        "create-customer-portal",
        {
          method: "POST",
          headers: { Authorization: `Bearer ${session.access_token}` },
        }
      );

      if (functionError) throw functionError;

      if (data?.url) {
        window.location.href = data.url;
        // No need to set portalLoading false if redirecting
        return;
      } else {
        throw new Error("Could not retrieve billing portal URL.");
      }
    } catch (err: any) {
      console.error("Error managing subscription:", err);
      setError(err.message || "Failed to open subscription management.");
      setPortalLoading(false); // Stop loading on error
    }
  };

  return (
    <div className="bg-white p-6 rounded-xl border border-gray-200">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Subscriptions</h2>

      {error && (
        <div className="mb-4 bg-red-50 text-red-600 p-3 rounded-lg flex items-center text-sm border border-red-100">
          <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
          <span className="flex-grow">{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-auto p-1 text-red-500 hover:text-red-700 hover:bg-red-100 rounded-md focus:outline-none focus:ring-2 focus:ring-red-300"
            aria-label="Dismiss error"
          >
            <X className="w-4 h-4 flex-shrink-0" />
          </button>
        </div>
      )}
      {success && (
        <div className="mb-4 bg-green-50 text-green-600 p-3 rounded-lg flex items-center text-sm border border-green-100">
          <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
          <span className="flex-grow">{success}</span>
          <button
            onClick={() => setSuccess(null)}
            className="ml-auto p-1 text-green-500 hover:text-green-700 hover:bg-green-100 rounded-md focus:outline-none focus:ring-2 focus:ring-green-300"
            aria-label="Dismiss success message"
          >
            <X className="w-4 h-4 flex-shrink-0" />
          </button>
        </div>
      )}

      {/* Global Manage Button */}
      {subscriptions.some(
        (sub) =>
          sub.status === "active" || sub.status === "pending cancellation"
      ) && (
        <div className="mb-6 flex justify-end">
          <button
            onClick={handleManageSubscription}
            disabled={portalLoading}
            className="px-6 py-3 bg-blue-700 text-white rounded-xl hover:bg-blue-800 disabled:opacity-50 transition-colors text-sm font-medium shadow-md inline-flex items-center gap-2"
          >
            <CreditCard className="h-5 w-5" />
            {portalLoading ? "Preparing Portal..." : "Manage All Subscriptions"}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 ml-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      )}

      {loading && subscriptions.length === 0 && (
        <p className="text-center text-gray-500 py-4">
          Loading subscriptions...
        </p>
      )}
      {!loading && subscriptions.length === 0 && (
        <div className="text-center py-10 px-6 bg-gray-50 rounded-lg border border-dashed border-gray-300">
          <ShoppingCart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-700 mb-2">
            No Active Subscriptions
          </h3>
          <p className="text-sm text-gray-500 mb-6">
            Ready to unlock powerful features? Explore our plans and find the
            perfect fit for your needs.
          </p>
          <Link
            to="/products" // Link to your products/pricing page
            className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium shadow-sm"
          >
            Browse Plans
          </Link>
        </div>
      )}

      {subscriptions.length > 0 && (
        <div className="space-y-4 max-h-[500px] overflow-y-auto pr-4">
          {subscriptions.map((subscription) => {
            // Determine the display status text and style
            const isPendingCancellation =
              subscription.status === "pending cancellation";
            const endDate = new Date(
              subscription.current_period_end
            ).toLocaleDateString();
            let statusText = `Renews on ${endDate}`;
            let statusColorClass = "bg-green-100 text-green-800"; // Default active

            if (isPendingCancellation) {
              statusText = `Cancels on ${endDate}`;
              statusColorClass = "bg-yellow-100 text-yellow-800"; // Pending cancellation style
            } else if (subscription.status === "canceled") {
              statusText = `Canceled - Access ended ${endDate}`; // Or just 'Canceled'
              statusColorClass = "bg-red-100 text-red-800";
            } else if (subscription.status !== "active") {
              // Handle other statuses like incomplete, past_due etc.
              statusText = `Status: ${subscription.status} - Ended ${endDate}`;
              statusColorClass = "bg-gray-100 text-gray-800";
            }

            // Separate active and scheduled items
            const activeItems = subscription.subscription_items.filter(
              (item) => item.status === "active"
            );
            const canceledItems = subscription.subscription_items.filter(
              (item) => item.status === "canceled"
            );
            const scheduledItems = subscription.subscription_items.filter(
              (item) => item.status === "scheduled"
            );

            return (
              <div
                key={subscription.id}
                className={`p-4 border rounded-xl transition-colors ${
                  subscription.status === "active" ||
                  subscription.status === "pending cancellation"
                    ? "border-gray-200 hover:border-blue-200"
                    : "border-gray-100 bg-gray-50 opacity-70"
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  {/* Icon and Basic Info */}
                  <div className="flex items-start space-x-4">
                    <div
                      className={`${
                        subscription.status === "active" ||
                        subscription.status === "pending cancellation"
                          ? "bg-blue-50"
                          : "bg-gray-200"
                      } p-2 rounded-lg mt-1`}
                    >
                      <CreditCard
                        className={`h-5 w-5 ${
                          subscription.status === "active" ||
                          subscription.status === "pending cancellation"
                            ? "text-blue-600"
                            : "text-gray-500"
                        }`}
                      />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">
                        Ref: #{subscription.ref}
                      </p>
                      <p className="text-sm text-gray-500">{statusText}</p>
                      <span
                        className={`text-xs px-2 py-1 rounded-full mt-1 inline-block capitalize ${statusColorClass}`}
                      >
                        {subscription.status}
                      </span>
                    </div>
                  </div>
                  {/* Hide Button */}
                  {subscription.status === "canceled" &&
                    !subscription.deleted && (
                      <button
                        onClick={() =>
                          handleDeleteSubscription(subscription.id)
                        }
                        disabled={loading}
                        className="text-gray-400 hover:text-red-600 disabled:opacity-50 p-1 rounded-md hover:bg-red-50 transition-colors flex-shrink-0"
                        title="Hide this subscription record"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                </div>

                {/* --- Improved Item Display --- */}
                <div className="mt-4 space-y-3">
                  {/* Current Active Items */}
                  {activeItems?.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-600 mb-1">
                        Current Plan:
                      </h4>
                      <ul className="list-disc list-inside space-y-1 pl-1">
                        {activeItems.map((item, index) => {
                          const productName =
                            item?.products?.name || "Unknown Product";
                          const quantity = item?.quantity || 1;
                          const period = subscription?.period || "month";
                          const itemAmount = item?.amount || 0;

                          // Handle null products
                          if (!item?.products) {
                            return (
                              <li
                                key={`active-${index}`}
                                className="text-sm text-gray-800"
                              >
                                {productName}{" "}
                                {quantity > 1 ? ` (x${quantity})` : ""} - $0/
                                {period} (No product data)
                              </li>
                            );
                          }

                          return (
                            <li
                              key={`active-${index}`}
                              className="text-sm text-gray-800"
                            >
                              {productName}
                              {quantity > 1 ? ` (x${quantity})` : ""}
                              {` - $${itemAmount.toFixed(2)}/${period}`}
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  )}

                  {/* Canceled Items */}
                  {canceledItems?.length > 0 &&
                    subscription?.status === "canceled" && (
                      <div className="mt-3 pt-3 border-t border-dashed border-gray-200">
                        <h4 className="text-sm font-medium text-red-700 mb-1">
                          Canceled Items:
                        </h4>
                        <ul className="list-disc list-inside space-y-1 pl-1">
                          {canceledItems.map((item, index) => {
                            const productName =
                              item?.products?.name || "Unknown Product";
                            const quantity = item?.quantity || 1;
                            const period = subscription?.period || "month";
                            const basePrice = item?.products?.price || 0;
                            const monthlyDiscount =
                              (item?.products?.monthly_discount || 0) / 100;
                            const yearlyDiscount =
                              (item?.products?.yearly_discount || 0) / 100;

                            // Handle null products
                            if (!item?.products) {
                              return (
                                <li
                                  key={`canceled-${index}`}
                                  className="text-sm text-red-600 italic"
                                >
                                  {productName}{" "}
                                  {quantity > 1 ? ` (x${quantity})` : ""} - $0/
                                  {period} (No product data)
                                </li>
                              );
                            }

                            // Calculate final price based on period
                            const finalPrice =
                              period === "month"
                                ? (basePrice * (1 - monthlyDiscount)).toFixed(2)
                                : (
                                    basePrice *
                                    12 *
                                    (1 - yearlyDiscount)
                                  ).toFixed(2);

                            return (
                              <li
                                key={`canceled-${index}`}
                                className="text-sm text-red-600 italic"
                              >
                                {productName}
                                {quantity > 1 ? ` (x${quantity})` : ""}
                                {` - $${finalPrice}/${period}`}
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                    )}

                  {/* Scheduled Items */}
                  {scheduledItems?.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-dashed border-gray-200">
                      <h4 className="text-sm font-medium text-blue-700 mb-1">
                        Scheduled Changes:
                      </h4>
                      <ul className="list-disc list-inside space-y-1 pl-1">
                        {scheduledItems.map((item, index) => {
                          const productName =
                            item?.products?.name || "Unknown Product";
                          const quantity = item?.quantity || 1;
                          const period = subscription?.period || "month";
                          const itemAmount = item?.amount || 0;

                          return (
                            <li
                              key={`scheduled-${index}`}
                              className="text-sm text-gray-800"
                            >
                              {productName}
                              {quantity > 1 ? ` (x${quantity})` : ""}
                              {` - $${itemAmount.toFixed(2)}/${period}`}
                            </li>
                          );
                        })}
                      </ul>

                      {/* Calculate and display scheduled total if multiple items */}
                      {scheduledItems.length > 1 && (
                        <div className="mt-2 pt-2 border-t border-gray-100">
                          <p className="text-sm font-medium text-gray-800 text-right">
                            Scheduled Total: $
                            {scheduledItems
                              .reduce(
                                (total, item) => total + (item.amount || 0),
                                0
                              )
                              .toFixed(2)}
                            /{subscription.period}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Cancellation Modal */}
      {showCancelModal && (
        <Modal
          title="Confirm Cancellation"
          onClose={() => setShowCancelModal(false)}
          actions={
            <>
              <button
                onClick={() => setShowCancelModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
                disabled={loading} // Disable during action
              >
                Back
              </button>
              <button
                onClick={handleCancelSubscription}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                disabled={loading} // Disable during action
              >
                {loading ? "Processing..." : "Confirm Cancellation"}
              </button>
            </>
          }
        >
          <p>Are you sure you want to cancel this subscription?</p>
          <p className="text-sm text-gray-500 mt-2">
            Your access will continue until the end of the current billing
            period (
            {subscriptions.find((s) => s.id === cancelSubscriptionId)
              ?.current_period_end
              ? new Date(
                  subscriptions.find(
                    (s) => s.id === cancelSubscriptionId
                  )!.current_period_end
                ).toLocaleDateString()
              : ""}
            ).
          </p>
        </Modal>
      )}
    </div>
  );
}
