-- First check if tables exist and drop them if they do
DROP TABLE IF EXISTS campaign_logs CASCADE;
DROP TABLE IF EXISTS email_campaigns CASCADE;
DROP TABLE IF EXISTS subscribers CASCADE;

-- Create email_campaigns table
CREATE TABLE email_campaigns (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  subject text NOT NULL,
  content text NOT NULL,
  status text NOT NULL CHECK (status IN ('draft', 'scheduled', 'sent')),
  scheduled_for timestamptz,
  sent_at timestamptz,
  opens integer DEFAULT 0,
  clicks integer DEFAULT 0,
  recipients integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create subscribers table
CREATE TABLE subscribers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  full_name text,
  status text NOT NULL CHECK (status IN ('active', 'unsubscribed')) DEFAULT 'active',
  subscribed_at timestamptz DEFAULT now(),
  last_email_sent timestamptz,
  CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Create campaign_logs table for tracking email events
CREATE TABLE campaign_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid REFERENCES email_campaigns(id) ON DELETE CASCADE,
  subscriber_id uuid REFERENCES subscribers(id) ON DELETE CASCADE,
  event_type text NOT NULL CHECK (event_type IN ('sent', 'opened', 'clicked', 'bounced', 'complained', 'unsubscribed')),
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE email_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for email_campaigns
CREATE POLICY "Admins can manage campaigns"
  ON email_campaigns
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));

-- Create policies for subscribers
CREATE POLICY "Anyone can subscribe"
  ON subscribers
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Admins can manage subscribers"
  ON subscribers
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));

-- Create policies for campaign_logs
CREATE POLICY "Admins can view campaign logs"
  ON campaign_logs
  FOR SELECT
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_email_campaigns_updated_at
  BEFORE UPDATE ON email_campaigns
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data
INSERT INTO subscribers (email, full_name, status) VALUES
  ('<EMAIL>', 'John Doe', 'active'),
  ('<EMAIL>', 'Jane Smith', 'active'),
  ('<EMAIL>', 'Bob Wilson', 'unsubscribed')
ON CONFLICT (email) DO NOTHING;

INSERT INTO email_campaigns (name, subject, content, status, opens, clicks, recipients) VALUES
  (
    'March Newsletter',
    'Latest Updates from IRS',
    'Here are the latest updates from International Responder Systems...',
    'sent',
    245,
    89,
    1000
  ),
  (
    'Product Launch',
    'Introducing New Features',
    'We''re excited to announce new features in our products...',
    'draft',
    0,
    0,
    0
  );

-- Grant necessary permissions
GRANT ALL ON email_campaigns TO authenticated;
GRANT ALL ON subscribers TO authenticated;
GRANT ALL ON campaign_logs TO authenticated;