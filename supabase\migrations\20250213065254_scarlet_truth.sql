-- Add name and phone columns to support_tickets
ALTER TABLE support_tickets 
ADD COLUMN contact_name text,
ADD COLUMN contact_phone text;

-- Update existing tickets to use profile data
UPDATE support_tickets
SET 
  contact_name = p.full_name,
  contact_phone = p.phone
FROM profiles p
WHERE support_tickets.user_id = p.id;

-- Create function to get user contact info
CREATE OR REPLACE FUNCTION get_user_contact_info(user_id uuid)
RETURNS TABLE (
  full_name text,
  phone text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.full_name,
    p.phone
  FROM profiles p
  WHERE p.id = user_id;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_user_contact_info TO authenticated;