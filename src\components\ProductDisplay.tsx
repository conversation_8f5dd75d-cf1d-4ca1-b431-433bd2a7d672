import { useState } from 'react';
import { ShoppingCart, Check } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useCartStore } from '../store/cartStore';
import { useAuthStore } from '../store/authStore';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  pages?: string[];
  specifications: string[];
  theme_color?: string;
  monthly_discount?: number;
  yearly_discount?: number;
  created_at: string;
  updated_at?: string;
}

interface ProductDisplayProps {
  products: Product[];
  pageName: string;
  billingInterval?: 'monthly' | 'yearly';
}

export default function ProductDisplay({ products, pageName, billingInterval = 'monthly' }: ProductDisplayProps) {
  const addItem = useCartStore((state) => state.addItem);
  const user = useAuthStore((state) => state.user);
  const [expandedDescriptions, setExpandedDescriptions] = useState<{[key: string]: boolean}>({});
  const [addedProducts, setAddedProducts] = useState<{[key: string]: boolean}>({});

  // Function to get color classes based on theme color
  const getColorClasses = (themeColor: string = 'blue') => {
    const colorMap = {
      blue: {
        gradient: 'from-blue-600 via-blue-700 to-blue-800',
        gradientLight: 'from-blue-400/30',
        gradientMedium: 'from-blue-500/30',
        gradientOverlay: 'from-blue-600/80 via-blue-700/80 to-blue-800/80',
        button: 'bg-blue-600 hover:bg-blue-700',
        border: 'border-blue-600',
        hoverBorder: 'hover:border-blue-100',
        text: 'text-blue-600',
        badge: 'bg-blue-100 text-blue-800',
        highlight: 'bg-blue-600'
      },
      orange: {
        gradient: 'from-orange-600 via-orange-700 to-orange-800',
        gradientLight: 'from-orange-400/30',
        gradientMedium: 'from-orange-500/30',
        gradientOverlay: 'from-orange-600/80 via-orange-700/80 to-orange-800/80',
        button: 'bg-orange-600 hover:bg-orange-700',
        border: 'border-orange-600',
        hoverBorder: 'hover:border-orange-100',
        text: 'text-orange-600',
        badge: 'bg-orange-100 text-orange-800',
        highlight: 'bg-orange-600'
      },
      purple: {
        gradient: 'from-purple-600 via-purple-700 to-purple-800',
        gradientLight: 'from-purple-400/30',
        gradientMedium: 'from-purple-500/30',
        gradientOverlay: 'from-purple-600/80 via-purple-700/80 to-purple-800/80',
        button: 'bg-purple-600 hover:bg-purple-700',
        border: 'border-purple-600',
        hoverBorder: 'hover:border-purple-100',
        text: 'text-purple-600',
        badge: 'bg-purple-100 text-purple-800',
        highlight: 'bg-purple-600'
      },
      yellow: {
        gradient: 'from-yellow-600 via-yellow-700 to-yellow-800',
        gradientLight: 'from-yellow-400/30',
        gradientMedium: 'from-yellow-500/30',
        gradientOverlay: 'from-yellow-600/80 via-yellow-700/80 to-yellow-800/80',
        button: 'bg-yellow-600 hover:bg-yellow-700',
        border: 'border-yellow-600',
        hoverBorder: 'hover:border-yellow-100',
        text: 'text-yellow-600',
        badge: 'bg-yellow-100 text-yellow-800',
        highlight: 'bg-yellow-600'
      }
    };

    return colorMap[themeColor as keyof typeof colorMap] || colorMap.blue;
  };

  const handleAddToCart = (product: Product) => {
    console.log('Adding to cart:', product, 'with billing interval:', billingInterval);

    // Add the product to cart with billing interval
    addItem(product.id, billingInterval);

    // Set this product as recently added
    setAddedProducts(prev => ({
      ...prev,
      [product.id]: true
    }));

    // Reset the added state after 800 milliseconds
    setTimeout(() => {
      setAddedProducts(prev => ({
        ...prev,
        [product.id]: false
      }));
    }, 800);
  };

  const toggleDescription = (productId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  // Function to truncate text
  const truncateText = (text: string, wordCount: number = 12) => {
    if (!text) return '';
    const words = text.split(' ');
    if (words.length <= wordCount) return text;
    return words.slice(0, wordCount).join(' ') + '...';
  };

  if (products.length === 0) {
    return (
      <div className="col-span-3 text-center py-12">
        <div className="bg-gray-50 rounded-lg p-8 max-w-md mx-auto">
          <h3 className="text-xl font-bold mb-4 text-gray-700">No Products Available</h3>
          <p className="text-gray-600 mb-6">
            There are currently no products available for {pageName}. Please check back later or contact our sales team for more information.
          </p>
          <Link
            to="/contact"
            className="inline-block bg-blue-600 text-white py-2 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Contact Sales
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="grid md:grid-cols-3 gap-8">
      {products.map((product) => (
        <div
          key={product.id}
          className={`bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 ${getColorClasses(product.theme_color).hoverBorder} transition-colors`}
        >
          <div className="p-8">
            <h3 className="text-2xl font-bold mb-2">{product.name}</h3>
            <div className="text-gray-600 mb-6 min-h-[4.5rem]">
              {expandedDescriptions[product.id] ? (
                <>
                  <p>{product.description}</p>
                  <button
                    onClick={() => toggleDescription(product.id)}
                    className={`${getColorClasses(product.theme_color).text} text-sm mt-2 hover:underline focus:outline-none`}
                  >
                    Read less
                  </button>
                </>
              ) : (
                <>
                  <p>{truncateText(product.description)}</p>
                  {product.description && product.description.split(' ').length > 12 && (
                    <button
                      onClick={() => toggleDescription(product.id)}
                      className={`${getColorClasses(product.theme_color).text} text-sm mt-1 hover:underline focus:outline-none`}
                    >
                      Read more
                    </button>
                  )}
                </>
              )}
            </div>
            <div className="mb-6 h-14 flex items-end">
              <div>
                {billingInterval === 'yearly' ? (
                  product.yearly_discount ? (
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <span className="text-4xl font-bold">
                          ${((product.price * 12) * (1 - (product.yearly_discount || 0) / 100)).toFixed(2)}
                        </span>
                        <span className="text-gray-600">/year</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-sm text-gray-500 line-through mr-2">
                          ${(product.price * 12).toFixed(2)}
                        </span>
                        <span className="text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                          Save {product.yearly_discount}%
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <span className="text-4xl font-bold">
                        ${(product.price * 12).toFixed(2)}
                      </span>
                      <span className="text-gray-600">/year</span>
                    </div>
                  )
                ) : (
                  product.monthly_discount ? (
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <span className="text-4xl font-bold">
                          ${(product.price * (1 - (product.monthly_discount || 0) / 100)).toFixed(2)}
                        </span>
                        <span className="text-gray-600">/month</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-sm text-gray-500 line-through mr-2">
                          ${product.price.toFixed(2)}
                        </span>
                        <span className="text-sm bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                          Save {product.monthly_discount}%
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <span className="text-4xl font-bold">
                        ${product.price.toFixed(2)}
                      </span>
                      <span className="text-gray-600">/month</span>
                    </div>
                  )
                )}
              </div>
            </div>
            {user ? (
              <button
                onClick={() => handleAddToCart(product)}
                className={`w-full ${addedProducts[product.id] ? 'bg-green-600 hover:bg-green-700' : getColorClasses(product.theme_color).button} text-white py-3 px-6 rounded-lg font-semibold transition-colors`}
                disabled={addedProducts[product.id]}
              >
                {addedProducts[product.id] ? (
                  <>
                    <Check className="inline-block h-5 w-5 mr-2" />
                    Added to Cart
                  </>
                ) : (
                  <>
                    <ShoppingCart className="inline-block h-5 w-5 mr-2" />
                    Add to Cart
                  </>
                )}
              </button>
            ) : (
              <Link
                to="/signup"
                className={`block w-full text-center ${getColorClasses(product.theme_color).button} text-white py-3 px-6 rounded-lg font-semibold transition-colors`}
              >
                Sign Up to Subscribe
              </Link>
            )}
          </div>
          <div className="bg-white p-8">
            <ul className="space-y-4">
              {product.specifications.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      ))}
    </div>
  );
}
