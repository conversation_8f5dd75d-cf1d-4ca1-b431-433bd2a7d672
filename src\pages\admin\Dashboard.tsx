import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Package,
  FileText,
  LogOut,
  Settings,
  Calendar,
  Mail,
  MessageSquare,
  Briefcase,
  Contact2,
  Video,
  Book,
  Info,
  CreditCardIcon,
  Loader,
  Users2,
  UserRound,
  Calendar as CalendarIcon,
  Building2
} from 'lucide-react';
import { supabase } from '../../lib/supabase';
import ProductManagement from './ProductManagement';
import BlogManagement from './BlogManagement';
import SettingsManagement from './SettingsManagement';
import SupportManagement from './SupportManagement';
import JobManagement from './JobManagement';
import MarketingManagement from './MarketingManagement';
import ContactSubmissions from './ContactSubmissions';
import WebinarManagement from './WebinarManagement';
import EventManagement from './EventManagement';
import GuideManagement from './GuideManagement';
import CaseStudiesManagement from './CaseStudiesManagement';
import DashboardOverview from './DashboardOverview';
import SubscriptionsManagement from './subscriptions/SubscriptionsManagement';
import TeamManagement from './TeamManagement';
import UserManagement from './UserManagement';
import InvoicesManagement from './invoices/InvoicesManagement';
import DemoRequestsManagement from './DemoRequestsManagement';
import ConsultingServicesManagement from './ConsultingServicesManagement';


interface DashboardStats {
  admins: number;
  users: number;
  products: number;
  posts: number;
  media: number;
  support: number;
  jobs: number;
  webinars: number;
  events: number;
  revenue: number;
  guides: number;
  caseStudies: number;
  teams: number;
  contactSubmissions: number;
  subscriptions: number;
  invoices: number;
  demoRequests: number;
  consultingServices: number;
}

export default function Dashboard() {
  const scrollbarStyles = `
    .sidebar-nav::-webkit-scrollbar {
      width: 6px;
    }
    .sidebar-nav::-webkit-scrollbar-track {
      background: transparent;
    }
    .sidebar-nav::-webkit-scrollbar-thumb {
      background-color: #CBD5E0;
      border-radius: 3px;
    }
    .sidebar-nav::-webkit-scrollbar-thumb:hover {
      background-color: #A0AEC0;
    }
  `;

  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DashboardStats>({
    admins: 0,
    users: 0,
    products: 0,
    subscriptions: 0,
    invoices: 0,
    // invoicesDisplay: "0",
    posts: 0,
    media: 0,
    support: 0,
    jobs: 0,
    webinars: 0,
    events: 0,
    revenue: 0,
    guides: 0,
    caseStudies: 0,
    teams: 0,
    contactSubmissions: 0,
    demoRequests: 0,
    consultingServices: 0
  });

  useEffect(() => {
    checkAdminAccess();
    fetchStats();
  }, [])

  const checkAdminAccess = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        navigate('/admin/login');
        return;
      }

      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || profile.role !== 'admin') {
        setError('Access denied. Admin privileges required.');
        navigate('/admin/login');
      }
    } catch (err) {
      console.error('Error checking admin access:', err);
      navigate('/admin/login');
    }
  };
  const fetchStats = async () => {
    try {
      const [
        { count: admins },
        { count: users },
        { count: products },
        { count: posts },
        { count: media },
        { count: support },
        { count: jobs },
        { count: webinars },
        { count: events },
        { count: guides },
        { count: caseStudies },
        { count: teams },
        { count: contactSubmissions },
        { count: invoices },
        { count: subscriptions },
        { count: demoRequests },
        { count: consultingServices },
      ] = await Promise.all([
        supabase.from('profiles').select('*', { count: 'exact', head: true }).eq('role', 'admin'),
        supabase.from('profiles').select('*', { count: 'exact', head: true }),
        supabase.from('products').select('*', { count: 'exact', head: true }),
        supabase.from('blog_posts').select('*', { count: 'exact', head: true }),
        supabase.from('media_items').select('*', { count: 'exact', head: true }),
        supabase.from('support_tickets').select('*', { count: 'exact', head: true }),
        supabase.from('job_listings').select('*', { count: 'exact', head: true }),
        supabase.from('webinars').select('*', { count: 'exact', head: true }),
        supabase.from('events').select('*', { count: 'exact', head: true }),
        supabase.from('guides').select('*', { count: 'exact', head: true }),
        supabase.from('case_studies').select('*', { count: 'exact', head: true }),
        supabase.from('teams').select('*', { count: 'exact', head: true }),
        supabase.from('contact_submissions').select('*', { count: 'exact', head: true }),
        supabase.from('invoices').select('*', { count: 'exact', head: true }),
        supabase.from('subscriptions').select('*', { count: 'exact', head: true }),
        supabase.from('demo_requests').select('*', { count: 'exact', head: true }),
        supabase.from('consulting_services').select('*', { count: 'exact', head: true }),
      ]);

      // Calculate revenue from active subscriptions
      const { data: subscriptionsForRevenue } = await supabase
        .from('subscriptions')
        .select('amount, status')
        .or('status.eq.active,status.eq.trialing');

      // Calculate total revenue from subscription amounts
      const revenue = subscriptionsForRevenue?.reduce((sum, sub) => {
        return sum + (sub.amount || 0);
      }, 0) || 0;

      // Fetch invoices count
      // const { data: invoicesData } = await supabase.functions.invoke('get-stripe-invoices', {
      //   body: JSON.stringify({ count_only: true })
      // });

      // // Use the displayCount for UI display (will be "99+" if over 99)
      // const invoicesDisplayCount = invoicesData?.displayCount || "0";

      setStats({
        admins: admins || 0,
        users: users || 0,
        products: products || 0,
        posts: posts || 0,
        media: media || 0,
        support: support || 0,
        jobs: jobs || 0,
        webinars: webinars || 0,
        events: events || 0,
        revenue,
        guides: guides || 0,
        caseStudies: caseStudies || 0,
        teams: teams || 0,
        contactSubmissions: contactSubmissions || 0,
        subscriptions: subscriptions || 0,
        invoices: invoices || 0,
        demoRequests: demoRequests || 0,
        consultingServices: consultingServices || 0
      });
    } catch (err) {
      console.error('Error fetching stats:', err);
    } finally {
      setLoading(false);
    }
  };

  const menuItems = [
    { icon: LayoutDashboard, label: 'Overview', value: 'overview', count: null },
    { icon: UserRound, label: 'Users', value: 'users', count: stats.users },
    { icon: Package, label: 'Products', value: 'products', count: stats.products },
    { icon: Building2, label: 'Consulting Services', value: 'consulting-services', count: stats.consultingServices },
    { icon: CreditCardIcon, label: 'Subscriptions', value: 'subscriptions', count: stats.subscriptions },
    { icon: CreditCardIcon, label: 'Invoices', value: 'invoices', count: stats.invoices },
    { icon: FileText, label: 'Blog & News', value: 'posts', count: stats.posts },
    { icon: MessageSquare, label: 'Support', value: 'support', count: stats.support },
    { icon: Book, label: 'Guides & Whitepapers', value: 'guides', count: stats.guides },
    { icon: Info, label: 'Case Studies', value: 'case-studies', count: stats.caseStudies },
    { icon: Users2, label: 'Teams', value: 'teams', count: stats.teams },
    { icon: Briefcase, label: 'Jobs', value: 'jobs', count: stats.jobs },
    { icon: Video, label: 'Webinars', value: 'webinars', count: stats.webinars },
    { icon: Calendar, label: 'Events', value: 'events', count: stats.events },
    { icon: CalendarIcon, label: 'Demo Requests', value: 'demo-requests', count: stats.demoRequests },
    { icon: Contact2, label: 'Contact Submissions', value: 'contactSubmissions', count: stats.contactSubmissions },
    { icon: Mail, label: 'Marketing', value: 'marketing', count: null },
    { icon: Settings, label: 'Settings', value: 'settings', count: null }
  ];

  const handleLogout = async () => {
    await supabase.auth.signOut();
    navigate('/login');
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <DashboardOverview stats={stats} />;
      case 'users':
        return <UserManagement />;
      case 'products':
        return <ProductManagement />;
      case 'consulting-services':
        return <ConsultingServicesManagement />;
      case 'posts':
        return <BlogManagement />;
      case 'support':
        return <SupportManagement />;
      case 'jobs':
        return <JobManagement />;
      case 'marketing':
        return <MarketingManagement />;
      case 'settings':
        return <SettingsManagement />;
      case 'contactSubmissions':
        return <ContactSubmissions />;
      case 'webinars':
        return <WebinarManagement />;
      case 'subscriptions':
        return <SubscriptionsManagement />;
      case 'invoices':
        return <InvoicesManagement />;
      case 'events':
        return <EventManagement />;
      case 'demo-requests':
        return <DemoRequestsManagement />;
      case 'guides':
        return <GuideManagement />;
      case 'case-studies':
        return <CaseStudiesManagement />;
      case 'teams':
        return <TeamManagement />
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loader className="h-8 w-8 text-blue-600 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{error}</h2>
          <button
            onClick={() => navigate('/admin/login')}
            className="text-blue-600 hover:text-blue-700"
          >
            Return to login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <style>{scrollbarStyles}</style>
      <div className="fixed inset-y-0 left-0 w-64 bg-white border-r z-30  lg:pt-28">
        <div className="flex flex-col h-full">
          {/* Navigation - Scrollable */}
          <nav className="flex-1 px-4 py-6 overflow-y-auto sidebar-nav" style={{ scrollbarWidth: 'thin', scrollbarColor: '#CBD5E0 transparent' }}>
            <div className="space-y-1">
              {menuItems.map((item) => (
                <button
                  key={item.value}
                  onClick={() => setActiveTab(item.value)}
                  className={`flex items-center w-full px-4 py-3 rounded-lg text-left ${activeTab === item.value
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                >
                  <item.icon className="h-5 w-5 mr-3" />
                  <span className="flex-1">{item.label}</span>
                  {item.count !== null && (
                    <span className={`px-2 py-1 text-xs rounded-full ${activeTab === item.value
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-gray-100 text-gray-600'
                      }`}>
                      {item.count}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </nav>

          {/* Footer - Fixed */}
          <div className="p-4 border-t mt-auto">
            <button
              onClick={handleLogout}
              className="flex items-center text-red-600 hover:text-red-700 w-full"
            >
              <LogOut className="h-5 w-5 mr-2" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 ml-64  lg:pt-8">
        <div className="p-4">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}
