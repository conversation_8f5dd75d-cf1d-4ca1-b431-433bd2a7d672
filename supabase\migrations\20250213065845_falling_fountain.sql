-- Add resolution_comment column to support_tickets
ALTER TABLE support_tickets 
ADD COLUMN resolution_comment text,
ADD COLUMN resolved_by uuid REFERENCES profiles(id),
ADD COLUMN resolved_at timestamptz;

-- Create function to resolve ticket
CREATE OR REPLACE FUNCTION resolve_support_ticket(
  ticket_id uuid,
  resolution text,
  resolver_id uuid
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE support_tickets
  SET 
    status = 'resolved',
    resolution_comment = resolution,
    resolved_by = resolver_id,
    resolved_at = now(),
    updated_at = now()
  WHERE id = ticket_id;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION resolve_support_ticket TO authenticated;