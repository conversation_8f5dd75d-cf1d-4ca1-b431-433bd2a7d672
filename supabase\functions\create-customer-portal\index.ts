import 'jsr:@supabase/functions-js/edge-runtime.d.ts';
import Stripe from 'npm:stripe@17.7.0';
import { createClient } from 'npm:@supabase/supabase-js@2.49.1';
// import { headers } from '../_shared/cors.ts'; // Assuming you have a shared CORS setup

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': '*',
  'Access-Control-Allow-Headers': '*',
  'Content-Type': 'application/json'
};
// Stripe Setup
const stripeSecret = Deno.env.get('STRIPE_SECRET_KEY');
if (!stripeSecret) {
  throw new Error('STRIPE_SECRET_KEY environment variable not set');
}
const stripe = new Stripe(stripeSecret, {
  apiVersion: '2024-06-20', // Use the latest API version
  appInfo: {
    name: 'IRS Integration - Customer Portal',
    version: '1.0.0'
  }
});

// Supabase Setup (Service Role Key needed to fetch profile)
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Supabase URL or Service Role Key environment variable not set');
}
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);

// --- Get Site URL from environment variable ---
const siteUrl = Deno.env.get('SITE_URL');
// const siteUrl = 'http://localhost:5173';
if (!siteUrl) {
  throw new Error('SITE_URL environment variable not set');
}
// --- End Site URL ---


Deno.serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: headers });
  }

  try {
    // Ensure POST request
    if (req.method !== 'POST') {
      return new Response('Method Not Allowed', { status: 405, headers: headers });
    }

    // --- User Authentication ---
    // Get the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { ...headers, 'Content-Type': 'application/json' },
      });
    }
    // Create a Supabase client with the user's token
    const supabaseUserClient = createClient(supabaseUrl, Deno.env.get('SUPABASE_ANON_KEY')!, {
        global: { headers: { Authorization: authHeader } },
    });
    // Get the user from the token
    const { data: { user }, error: userError } = await supabaseUserClient.auth.getUser();
    if (userError || !user) {
        console.error('User auth error:', userError);
        return new Response(JSON.stringify({ error: 'Authentication failed' }), {
            status: 401,
            headers: { ...headers, 'Content-Type': 'application/json' },
        });
    }
    // --- End User Authentication ---


    // Fetch the user's profile to get the Stripe Customer ID
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Profile fetch error:', profileError);
      throw new Error('Could not retrieve user profile.');
    }

    if (!profile?.stripe_customer_id) {
      console.error(`User ${user.id} does not have a stripe_customer_id.`);
      return new Response(JSON.stringify({ error: 'Stripe customer ID not found for this user.' }), {
        status: 400,
        headers: { ...headers, 'Content-Type': 'application/json' },
      });
    }

    const stripeCustomerId = profile.stripe_customer_id;

    // --- Use SITE_URL for the return URL ---
    // Define the return URL (where users go after managing billing)
    const returnUrl = `${siteUrl}/profile`; // Use the configured SITE_URL
    // --- End return URL update ---


    // Create a Stripe Billing Portal session
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: stripeCustomerId,
      return_url: returnUrl,
    });

    // Return the portal session URL
    return new Response(JSON.stringify({ url: portalSession.url }), {
      headers: { ...headers, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Error creating customer portal session:', error);
    return new Response(JSON.stringify({ error: error.message || 'Internal Server Error' }), {
      headers: { ...headers, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});