DROP TABLE IF EXISTS subscriptions CASCADE;
DROP TABLE IF EXISTS subscription_items CASCADE;
DROP TABLE IF EXISTS products CASCADE;

-- Add monthly and yearly discount fields to products table
-- ALTER TABLE products 
-- ADD COLUMN monthly_discount numeric DEFAULT 0 CHECK (monthly_discount >= 0 AND monthly_discount <= 100),
-- ADD COLUMN yearly_discount numeric DEFAULT 0 CHECK (yearly_discount >= 0 AND yearly_discount <= 100);

CREATE TABLE products (
  id UUID NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text NOT NULL,
  price numeric NOT NULL,
  image_url text NULL,
  category text NOT NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  pages jsonb NULL DEFAULT '[]'::jsonb,
  specifications jsonb NULL DEFAULT '[]'::jsonb,
  theme_color text NULL DEFAULT 'blue'::text,
  stripe_monthly_price_id text NULL,
  stripe_yearly_price_id text NULL,
  stripe_product_id text NULL,
  monthly_discount numeric DEFAULT 0 CHECK (monthly_discount >= 0 AND monthly_discount <= 100),
  yearly_discount numeric DEFAULT 0 CHECK (yearly_discount >= 0 AND yearly_discount <= 100),
  CONSTRAINT products_pkey PRIMARY KEY (id),
  CONSTRAINT products_price_check CHECK ((price >= (0)::numeric)),
  CONSTRAINT products_theme_color_check CHECK (
    (theme_color = ANY (
      ARRAY[
        'blue'::text,
        'orange'::text,
        'purple'::text,
        'yellow'::text
      ]
    ))
  )
) TABLESPACE pg_default;

ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Add these grants after RLS enablement
GRANT SELECT ON products TO public;
GRANT ALL PRIVILEGES ON products TO authenticated;
GRANT ALL PRIVILEGES ON products TO service_role;

-- Existing policies remain unchanged
CREATE POLICY "Products are viewable by everyone" 
  ON products FOR SELECT 
  TO public 
  USING (true);

CREATE POLICY "Products are insertable by authenticated users" 
  ON products FOR INSERT 
  TO authenticated 
  WITH CHECK (true);

CREATE POLICY "Products are updatable by authenticated users" 
  ON products FOR UPDATE 
  TO authenticated 
  USING (true);

CREATE POLICY "Public read access for products"
ON products FOR SELECT
USING (true);


CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ref text,
  user_id UUID REFERENCES profiles(id) NOT NULL,
  product_id UUID REFERENCES products(id),
  status TEXT NOT NULL CHECK (status IN ('active', 'trialing', 'canceled', 'pending cancellation', 'past due')),
  period text NOT NULL,
  current_period_start TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  current_period_end TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  cancel_at_period_end BOOLEAN NOT NULL DEFAULT false,  -- Already exists
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  deleted BOOLEAN NOT NULL DEFAULT false,
  deleted_at TIMESTAMPTZ,
  quantity INTEGER NOT NULL DEFAULT 1,
  stripe_subscription_id TEXT
);
ALTER TABLE public.subscriptions
ADD CONSTRAINT subscriptions_stripe_subscription_id_key UNIQUE (stripe_subscription_id);

CREATE TABLE public.subscription_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id uuid NOT NULL REFERENCES public.subscriptions(id) ON DELETE CASCADE, -- Link to the subscription
    product_id uuid NOT NULL REFERENCES public.products(id), -- Link to the specific product
    amount numeric NOT NULL,
    stripe_subscription_item_id text,
    status text NOT NULL CHECK (status IN ('active', 'inactive', 'scheduled')), -- Status of the item (active or inactive)
    stripe_price_id text NOT NULL, -- Store the Stripe Price ID (e.g., price_xxxxxxxx)
    quantity integer NOT NULL DEFAULT 1,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Add indexes for faster lookups
CREATE INDEX idx_subscription_items_subscription_id ON public.subscription_items(subscription_id);
CREATE INDEX idx_subscription_items_product_id ON public.subscription_items(product_id);

-- Enable RLS (adjust policy as needed)
ALTER TABLE public.subscription_items ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions to service_role for backend operations (like webhooks)
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.subscription_items TO service_role;

-- Grant permissions for authenticated users if they need direct access (adjust as needed)
GRANT SELECT ON TABLE public.subscription_items TO authenticated;
GRANT INSERT ON TABLE public.subscription_items TO authenticated; -- If users can insert directly
GRANT UPDATE ON TABLE public.subscription_items TO authenticated; -- If users can update directly
GRANT DELETE ON TABLE public.subscription_items TO authenticated; -- If users can delete directly


ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Update policies to reference products instead of products
CREATE POLICY "Users can read own subscriptions"
  ON subscriptions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);
 
CREATE POLICY "Admins can manage subscriptions"
  ON subscriptions
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );
 
CREATE POLICY "Admins can manage subscription_items"
  ON subscription_items
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );
 
GRANT ALL ON subscriptions TO service_role;
GRANT SELECT ON subscriptions TO authenticated;
GRANT INSERT, UPDATE, DELETE ON subscriptions TO authenticated;

-- Allow users to update the status or cancel_at_period_end flag of their own subscriptions
-- This is crucial for the handleCancelSubscription function
CREATE POLICY "Allow users to update own subscription status/cancellation"
  ON public.subscriptions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id) -- Check condition: User owns the subscription
  WITH CHECK (auth.uid() = user_id); -- Check condition for the new data

-- Allow users to update the 'deleted' flag of their own subscriptions
-- This is crucial for the handleDeleteSubscription function
CREATE POLICY "Allow users to mark own subscriptions as deleted"
  ON public.subscriptions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id) -- Check condition: User owns the subscription
  WITH CHECK (
    auth.uid() = user_id AND -- User must own the subscription
    status = 'canceled' -- Optional: Only allow hiding if already canceled? Adjust if needed.
  );

-- Re-grant permissions just in case (optional but safe)
-- GRANT UPDATE (status, cancel_at_period_end, deleted, deleted_at) ON public.subscriptions TO authenticated;

GRANT ALL ON subscription_items TO service_role;
GRANT SELECT ON subscription_items TO authenticated;
GRANT INSERT, UPDATE, DELETE ON subscription_items TO authenticated;

ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;

ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS subscription_ids TEXT[];

CREATE OR REPLACE FUNCTION append_subscription(
  user_id UUID,
  subscription_id TEXT
) RETURNS void AS $$
BEGIN
  UPDATE profiles
  SET subscription_ids = array_append(subscription_ids, subscription_id)
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql;

-- TRUNCATE TABLE auth.users CASCADE;
TRUNCATE TABLE products CASCADE;


INSERT INTO "public"."products" ("id", "name", "description", "price", "image_url", "category", "created_at", "updated_at", "pages", "specifications", "theme_color", "stripe_monthly_price_id", "stripe_yearly_price_id", "stripe_product_id", "monthly_discount", "yearly_discount") VALUES ('3ae69956-369a-4d3a-9c9a-234c62dcf802', 'SOAR Basic', 'Ideal for growing organizations. Includes advanced features, priority support, and custom workflows.', '499.99', null, 'Software', '2025-04-11 15:58:00.761488+00', '2025-04-11 15:58:00.761488+00', '["Products", "SOAR"]', '["Dedicated account manager", "Custom integration", "24/7 support", "Enterprise features"]', 'orange', 'price_1RFGphCef6Q1C2PEhDXEmREI', 'price_1RFGphCef6Q1C2PEhDXEmREI', 'prod_S9Zzc1G6vdoSIo', '14', '34'),
('5f23c777-a4dd-461a-bd9e-02716402e88f', 'GrantReady™ Basic', 'Perfect for small organizations. Includes basic grant management features and email support.', '599.99', 'https://images.unsplash.com/photo-*************-afdab827c52f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80', 'Software', '2025-04-09 11:29:29.826125+00', '2025-04-09 14:40:32.387723+00', '["GrantReady", "Products"]', '["Up to 5 users", "Basic grant management", "Email support"]', 'purple', 'price_1RE6BLCef6Q1C2PErRkUF9am', 'price_1RFfLkCef6Q1C2PEpL6yk9pG', '**********', '10', '16'),
('841af1f1-053e-490e-8156-5b5c30ce22b7', 'ELENOR Pro', 'Ideal for growing organizations. Includes advanced features, priority support, and custom workflows.', '599', null, 'Software', '2025-04-11 16:00:15.00727+00', '2025-04-11 16:00:15.00727+00', '["Products", "Elenor"]', '["Dedicated account manager", "Custom integration", "24/7 support", "Enterprise features"]', 'yellow', 'price_1RFGoiCef6Q1C2PEiBqt2Mwx', 'price_1RFGoiCef6Q1C2PEiBqt2Mwx', 'prod_S9ZyYk1A0zYIHB', '17', '21'),
('8c7fbff5-6fd2-4f48-8e39-dfc21be792c9', 'SOAR Pro', 'Ideal for growing organizations. Includes advanced features, priority support, and custom workflows.', '499.99', null, 'Software', '2025-04-11 15:59:10.230965+00', '2025-04-11 15:59:10.230965+00', '["Products", "SOAR"]', '["Dedicated account manager", "Custom integration", "24/7 support", "Enterprise features"]', 'orange', 'price_1RFGjTCef6Q1C2PEEaQIu1vW', 'price_1RFGjTCef6Q1C2PEEaQIu1vW', 'prod_S9ZthjB29bFeQA', '8', '16'),
('8e9a1616-3028-43bb-b03e-e2f0d8718caa', 'GrantReady™ Pro', 'Ideal for growing organizations. Includes advanced features, priority support, and custom workflows.', '199.99', 'https://images.unsplash.com/photo-*************-f8e8b4b6d7e3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80', 'Software', '2025-04-09 11:29:29.826125+00', '2025-04-09 13:06:09.416455+00', '["Products", "GrantReady"]', '["Unlimited users", "Enterprise features", "24/7 support", "Custom integration", "Dedicated account manager"]', 'purple', 'price_1RE6CFCef6Q1C2PE7gl1efJC', 'price_1RE6CFCef6Q1C2PE7gl1efJC', 'prod_123456545', '11', '13'),
('9abcd7d2-9077-4f4a-86c4-87a8e166a524', 'ELENOR BASIC', 'Ideal for growing organizations. Includes advanced features, priority support, and custom workflows.', '400', null, 'Software', '2025-04-11 16:01:13.01425+00', '2025-04-11 16:01:13.01425+00', '["Products", "Elenor"]', '["Dedicated account manager", "Custom integration", "24/7 support", "Enterprise features"]', 'yellow', 'price_1RFGqoCef6Q1C2PEPltNrPxF', 'price_1RFGqoCef6Q1C2PEPltNrPxF', 'prod_S9a0g5q5DVBcOF', '7', '19'),
('a7b4d23c-3019-4ac7-8b60-fc919d3519f3', 'ELENOR ULTIMATE', 'Ideal for growing organizations. Includes advanced features, priority support, and custom workflows.', '599.99', null, 'Software', '2025-04-11 16:03:13.099064+00', '2025-04-11 16:03:13.099064+00', '["Products", "Elenor"]', '["Dedicated account manager", "Custom integration", "24/7 support", "Enterprise features"]', 'yellow', 'price_3202156458', 'price_3202156458', 'prod_354687', '12', '30'),
('b572f628-2132-481c-b370-1a9ecd5c9105', 'GrantReady™ Enterprise', 'For large organizations. Includes unlimited users, 24/7 support, and custom integrations.', '193.99', 'https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80', 'Software', '2025-04-09 11:29:29.826125+00', '2025-04-09 14:40:55.253283+00', '["GrantReady", "Products"]', '["Up to 20 users", "Advanced grant management", "Priority support", "Custom workflows"]', 'purple', 'price_1RE6CYCef6Q1C2PE8Z4SmaS8', 'price_1RE6CYCef6Q1C2PE8Z4SmaS8', '************', '11', '19'),('e8b73688-ee16-40ea-ac93-0ca5b0379fba', 'SOAR Enterprise', 'Ideal for growing organizations. Includes advanced features, priority support, and custom workflows.', '699.99', null, 'Software', '2025-04-11 15:56:21.745497+00', '2025-04-11 15:56:21.745497+00', '["Products", "SOAR"]', '["Dedicated account manager", "Custom integration", "24/7 support", "Enterprise features"]', 'orange', 'price_1RFGkWCef6Q1C2PEs6xIGqbN', 'price_1RFGkWCef6Q1C2PEs6xIGqbN', 'prod_S9Zu2aQEpL5pyD', '10', '15');


-- Add function to update expired subscriptions
CREATE OR REPLACE FUNCTION public.update_expired_subscriptions_status()
RETURNS void AS $$
BEGIN
  UPDATE public.subscriptions
  SET status = 'past due'
  WHERE
    status IN ('active', 'trialing') -- Only update active or trialing subscriptions
    AND current_period_end < NOW()   -- Check if the period end date is in the past
    AND NOT deleted;                 -- Ignore subscriptions marked as deleted
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; -- Use SECURITY DEFINER if RLS might interfere

-- Grant execute permission to the authenticated role (or postgres role if needed)
-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.update_expired_subscriptions_status() TO postgres;
-- GRANT EXECUTE ON FUNCTION public.update_expired_subscriptions_status() TO service_role;
-- GRANT EXECUTE ON FUNCTION public.update_expired_subscriptions_status() TO supabase_admin;

-- Grant USAGE permission on the cron schema AND INSERT on the job table
-- GRANT USAGE ON SCHEMA cron TO supabase_admin; -- Add this grant
-- GRANT INSERT ON TABLE cron.job TO postgres;
-- GRANT INSERT ON TABLE cron.job TO supabase_admin;

-- Schedule the function to run daily using pg_cron
-- Ensure pg_cron is enabled in your Supabase project (Dashboard -> Database -> Extensions)
-- This schedules the job to run once daily at midnight UTC. Adjust '0 0 * * *' as needed.
-- Check if the job already exists before inserting to avoid errors on re-runs.
-- DO $$
-- BEGIN
--   IF NOT EXISTS (
--     SELECT 1 FROM cron.job WHERE command = 'SELECT public.update_expired_subscriptions_status()'
--   ) THEN
--     INSERT INTO cron.job (schedule, command, nodename, nodeport, database, username)
--     VALUES ('0 0 * * *', 'SELECT public.update_expired_subscriptions_status()', 'localhost', 5432, 'postgres', 'postgres');
--   END IF;
-- END;
-- $$;

-- Example: To unschedule (run manually in SQL editor if needed)
-- DELETE FROM cron.job WHERE command = 'SELECT public.update_expired_subscriptions_status()';




-- Reda s migrations 
-- First grant necessary permissions to the current user
DO $$
BEGIN
  IF EXISTS (SELECT FROM pg_roles WHERE rolname = 'postgres') THEN
    EXECUTE 'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres';
  END IF;
END $$;
 
-- Drop existing policies if they exist (modified to check table existence)
DO $$
BEGIN
  -- Temporarily disable RLS for all tables
  ALTER TABLE IF EXISTS campaign_logs DISABLE ROW LEVEL SECURITY;
  ALTER TABLE IF EXISTS subscribers DISABLE ROW LEVEL SECURITY;
  ALTER TABLE IF EXISTS email_campaigns DISABLE ROW LEVEL SECURITY;
 
  -- Drop policies
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'campaign_logs') THEN
    EXECUTE 'DROP POLICY IF EXISTS "Admins can view campaign logs" ON campaign_logs';
  END IF;
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'subscribers') THEN
    EXECUTE 'DROP POLICY IF EXISTS "Admins can manage subscribers" ON subscribers';
    EXECUTE 'DROP POLICY IF EXISTS "Users can manage own subscriptions" ON subscribers';
    EXECUTE 'DROP POLICY IF EXISTS "Public read access to subscribers" ON subscribers';
  END IF;
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'email_campaigns') THEN
    EXECUTE 'DROP POLICY IF EXISTS "Admins can manage campaigns" ON email_campaigns';
  END IF;
END $$;
 
-- Drop existing function if it exists
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
 
-- Drop existing tables if they exist (cascade to remove dependencies)
DROP TABLE IF EXISTS campaign_logs CASCADE;
DROP TABLE IF EXISTS subscribers CASCADE;
DROP TABLE IF EXISTS email_campaigns CASCADE;
 
-- Create email_campaigns table
CREATE TABLE email_campaigns (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  subject text NOT NULL,
  content text NOT NULL,
  status text NOT NULL CHECK (status IN ('draft', 'scheduled', 'sent')),
  scheduled_for timestamptz,
  sent boolean DEFAULT false,
  sent_at timestamptz,
  opens integer DEFAULT 0,
  clicks integer DEFAULT 0,
  recipients integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
 
-- Create subscribers table
CREATE TABLE subscribers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  full_name text,
  status text NOT NULL CHECK (status IN ('active', 'unsubscribed')) DEFAULT 'active',
  subscribed_at timestamptz DEFAULT now(),
  last_email_sent timestamptz,
  CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);
 
-- Create campaign_logs table for tracking email events
CREATE TABLE campaign_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid REFERENCES email_campaigns(id) ON DELETE CASCADE,
  subscriber_id uuid REFERENCES subscribers(id) ON DELETE CASCADE,
  event_type text NOT NULL CHECK (event_type IN ('sent', 'opened', 'clicked', 'bounced', 'complained', 'unsubscribed')),
  created_at timestamptz DEFAULT now()
);
 
-- Enable RLS
ALTER TABLE email_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_logs ENABLE ROW LEVEL SECURITY;
 
-- Create policies for email_campaigns
CREATE POLICY "Public read access to campaigns" ON email_campaigns
  FOR SELECT
  TO public
  USING (true);
 
CREATE POLICY "Public insert access to campaigns" ON email_campaigns
  FOR INSERT
  TO public
  WITH CHECK (true);
 
CREATE POLICY "Public update access to campaigns" ON email_campaigns
  FOR UPDATE
  TO public
  USING (true)
  WITH CHECK (true);
 
CREATE POLICY "Public delete access to campaigns" ON email_campaigns
  FOR DELETE
  TO public
  USING (true);
 
-- Create policies for subscribers
CREATE POLICY "Public read access" ON subscribers
  FOR SELECT
  TO public
  USING (true);
 
CREATE POLICY "Public insert access" ON subscribers
  FOR INSERT
  TO public
  WITH CHECK (true);
 
CREATE POLICY "Public update own" ON subscribers
  FOR UPDATE
  TO public
  USING (email = current_setting('request.jwt.claims', true)::json->>'email')
  WITH CHECK (email = current_setting('request.jwt.claims', true)::json->>'email');
 
CREATE POLICY "Public delete own" ON subscribers
  FOR DELETE
  TO public
  USING (email = current_setting('request.jwt.claims', true)::json->>'email');
 
CREATE POLICY "Authenticated full access" ON subscribers
  FOR ALL
  TO authenticated
  USING (email = auth.jwt() ->> 'email')
  WITH CHECK (email = auth.jwt() ->> 'email');
 
CREATE POLICY "Admin full access" ON subscribers
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  ));
 
-- Create policies for campaign_logs
CREATE POLICY "Admins can view campaign logs"
  ON campaign_logs
  FOR SELECT
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));
 
-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ language 'plpgsql';
 
-- Create trigger for updated_at
CREATE TRIGGER update_email_campaigns_updated_at
  BEFORE UPDATE ON email_campaigns
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
 
-- Insert sample data
INSERT INTO subscribers (email, full_name, status) VALUES
  ('<EMAIL>', 'John Doe', 'active'),
  ('<EMAIL>', 'Jane Smith', 'active'),
  ('<EMAIL>', 'Bob Wilson', 'unsubscribed');
 
INSERT INTO email_campaigns (name, subject, content, status, opens, clicks, recipients) VALUES
  (
    'March Newsletter',
    'Latest Updates from IRS',
    'Here are the latest updates from International Responder Systems...',
    'sent',
    245,
    89,
    1000
  ),
  (
    'Product Launch',
    'Introducing New Features',
    'We''re excited to announce new features in our products...',
    'draft',
    0,
    0,
    0
  );
 
-- Grant necessary permissions
-- Update grants for subscribers table
GRANT SELECT, INSERT, UPDATE, DELETE ON subscribers TO public;
GRANT SELECT, INSERT, UPDATE, DELETE ON subscribers TO anon;
GRANT ALL ON subscribers TO authenticated;
GRANT ALL ON subscribers TO service_role;
GRANT ALL ON email_campaigns TO public;
GRANT ALL ON email_campaigns TO anon;
GRANT ALL ON campaign_logs TO authenticated;
 
-- Add these to your migration file
CREATE OR REPLACE FUNCTION get_active_subscriber_count()
RETURNS integer AS $$
BEGIN
  RETURN (SELECT COUNT(*) FROM subscribers WHERE status = 'active');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
 
CREATE OR REPLACE FUNCTION subscribe_to_newsletter(
  p_email text,
  p_full_name text
) RETURNS void AS $$
BEGIN
  -- Check if already subscribed
  IF EXISTS (SELECT 1 FROM subscribers WHERE email = p_email AND status = 'active') THEN
    RAISE EXCEPTION 'This email is already subscribed';
  END IF;
  -- Insert new subscriber
  INSERT INTO subscribers (email, full_name, status, subscribed_at)
  VALUES (p_email, p_full_name, 'active', NOW());
  -- You can add email sending logic here if needed
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;






-- Create site_settings table
DROP TABLE IF EXISTS site_settings;
CREATE TABLE site_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  settings jsonb NOT NULL DEFAULT '{
    "general": {
      "siteName": "International Responder Systems",
      "siteDescription": "Healthcare Emergency Response Solutions",
      "contactEmail": "<EMAIL>",
      "phone": "",
      "address": "157 E Main Street, Elkton, MD 21921-5977",
      "socialLinks": {
        "linkedin": "https://www.linkedin.com/company/international-responder-systems",
        "twitter": "https://twitter.com/intrespondersys",
        "facebook": "https://www.facebook.com/InternationalResponderSystems"
      }
    },
    "seo": {
      "defaultTitle": "International Responder Systems - Healthcare Emergency Response Solutions",
      "defaultDescription": "Leading provider of healthcare emergency response and grant management solutions.",
      "defaultKeywords": "healthcare, emergency response, grant management, SOAR, GrantReady™",
      "googleAnalyticsId": "",
      "googleSiteVerification": "",
      "bingVerification": "",
      "robotsTxt": "User-agent: *\nAllow: /",
      "sitemapEnabled": true
    },
    "appearance": {
      "logo": "",
      "favicon": "",
      "primaryColor": "#2563eb",
      "secondaryColor": "#1e40af",
      "fontFamily": "Inter"
    },
    "features": {
      "blogEnabled": true,
      "commentsEnabled": true,
      "userRegistrationEnabled": true,
      "maintenanceMode": false
    }
  }'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
 
-- Enable RLS
ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;
 
-- Create policies
CREATE POLICY "Anyone can read site settings"
  ON site_settings
  FOR SELECT
  USING (true);
 
CREATE POLICY "Admins can manage site settings"
  ON site_settings
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));
 
-- Create trigger for updated_at
CREATE TRIGGER update_site_settings_updated_at
  BEFORE UPDATE ON site_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
 
-- Insert default settings
INSERT INTO site_settings (settings)
VALUES (
  '{
    "general": {
      "siteName": "International Responder Systems",
      "siteDescription": "Healthcare Emergency Response Solutions",
      "contactEmail": "<EMAIL>",
      "phone": "",
      "address": "157 E Main Street, Elkton, MD 21921-5977",
      "socialLinks": {
        "linkedin": "https://www.linkedin.com/company/international-responder-systems",
        "twitter": "https://twitter.com/intrespondersys",
        "facebook": "https://www.facebook.com/InternationalResponderSystems"
      }
    },
    "seo": {
      "defaultTitle": "International Responder Systems - Healthcare Emergency Response Solutions",
      "defaultDescription": "Leading provider of healthcare emergency response and grant management solutions.",
      "defaultKeywords": "healthcare, emergency response, grant management, SOAR, GrantReady™",
      "googleAnalyticsId": "",
      "googleSiteVerification": "",
      "bingVerification": "",
      "robotsTxt": "User-agent: *\nAllow: /",
      "sitemapEnabled": true
    },
    "appearance": {
      "logo": "",
      "favicon": "",
      "primaryColor": "#2563eb",
      "secondaryColor": "#1e40af",
      "fontFamily": "Inter"
    },
    "features": {
      "blogEnabled": true,
      "commentsEnabled": true,
      "userRegistrationEnabled": true,
      "maintenanceMode": false
    }
  }'::jsonb
);
 
-- Grant necessary permissions
GRANT ALL ON site_settings TO authenticated;
GRANT ALL ON site_settings TO anon;
GRANT ALL ON site_settings TO service_role;