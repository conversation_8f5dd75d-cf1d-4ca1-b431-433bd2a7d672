-- Migration: Add theme_color column to products table
-- Purpose: Allow admins to select a theme color for each product

-- Check if the column already exists, if not add it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'products'
        AND column_name = 'theme_color'
    ) THEN
        ALTER TABLE products
        ADD COLUMN theme_color text DEFAULT 'blue' CHECK (theme_color IN ('blue', 'orange', 'purple', 'yellow'));
    END IF;
END $$;

-- Comment on the theme_color column
COMMENT ON COLUMN products.theme_color IS 'Theme color for the product card (blue, orange, purple, yellow)';

-- Refresh the schema cache by altering the column to the same type
ALTER TABLE products ALTER COLUMN theme_color TYPE text;

-- Grant permissions on the column
GRANT SELECT, UPDATE(theme_color) ON products TO authenticated;
GRANT SELECT(theme_color) ON products TO anon;

-- Notify the system that the schema has changed
NOTIFY pgrst, 'reload schema';
