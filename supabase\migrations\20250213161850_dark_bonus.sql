-- Drop existing table if it exists
DROP TABLE IF EXISTS pages CASCADE;

-- Create pages table
CREATE TABLE pages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  slug text UNIQUE NOT NULL,
  title text NOT NULL,
  content text NOT NULL,
  meta_description text,
  is_published boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read published pages"
  ON pages
  FOR SELECT
  USING (is_published = true);

CREATE POLICY "<PERSON><PERSON> can manage all pages"
  ON pages
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));

-- <PERSON>reate function to update timestamps
CREATE OR REPLACE FUNCTION update_pages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_pages_updated_at
  BEFORE UPDATE ON pages
  FOR EACH ROW
  EXECUTE FUNCTION update_pages_updated_at();

-- Create function to validate slug format
CREATE OR REPLACE FUNCTION validate_slug()
RETURNS TRIGGER AS $$
DECLARE
  segment text;
BEGIN
  -- Allow empty slug for home page
  IF NEW.slug = '' THEN
    RETURN NEW;
  END IF;

  -- Check each segment of the slug
  FOR segment IN 
    SELECT unnest(string_to_array(NEW.slug, '/'))
  LOOP
    -- Skip empty segments
    IF segment = '' THEN
      CONTINUE;
    END IF;

    -- Validate segment format
    IF segment !~ '^[a-z0-9][a-z0-9-]*[a-z0-9]$' THEN
      RAISE EXCEPTION 'Invalid slug segment: %. Each segment must contain only lowercase letters, numbers, and hyphens, and cannot start or end with a hyphen', segment;
    END IF;
  END LOOP;

  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for slug validation
CREATE TRIGGER validate_page_slug
  BEFORE INSERT OR UPDATE ON pages
  FOR EACH ROW
  EXECUTE FUNCTION validate_slug();

-- Grant necessary permissions
GRANT ALL ON pages TO authenticated;
GRANT ALL ON pages TO anon;
GRANT ALL ON pages TO service_role;

-- Insert sample pages
INSERT INTO pages (slug, title, content, meta_description, is_published) VALUES
  (
    'about-us',
    'About Us',
    '# About International Responder Systems

International Responder Systems was established in 2015 to support international and domestic governments as well as private sector training organizations. With over 50 years of combined experience in public health and emergency response, we are uniquely prepared to support these organizations.

## Our Mission

To revolutionize healthcare emergency response through innovative solutions.

## Our Vision

To be the global leader in healthcare emergency response systems and grant management solutions.

## Our Values

- **Excellence** in everything we do
- **Innovation** in our solutions
- **Integrity** in our relationships
- **Commitment** to our clients
',
    'Learn about International Responder Systems and our mission to revolutionize healthcare emergency response.',
    true
  ),
  (
    'privacy-policy',
    'Privacy Policy',
    '# Privacy Policy

Last updated: March 2024

## Introduction

International Responder Systems ("we," "our," or "us") respects your privacy and is committed to protecting your personal information.

## Information We Collect

- Name and contact information
- Professional and employment information
- Technical data and usage information
- Communications and correspondence

## How We Use Your Information

We use your information to:
- Provide our services
- Improve our offerings
- Communicate with you
- Comply with legal obligations

## Data Security

We implement appropriate security measures to protect your information.
',
    'Our commitment to protecting your privacy and personal information.',
    true
  );