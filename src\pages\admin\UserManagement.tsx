import React, { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../../lib/supabase';
import {
  Trash2, Search, X, UserRound, Mail, RefreshCw, UserPlus,
  AlertCircle, Edit, AlertTriangle, Filter
} from 'lucide-react';

interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  avatar_url: string | null;
  role: string;
  created_at: string;
  email_confirmed_at?: string | null;
}

interface CartItem {
  product_id: string;
  quantity: number;
  billing_period?: string;
}

// Add these interfaces for type safety
interface RelatedData {
  subscriptions: Array<{
    id: string;
    status: string;
    plan_id?: string;
    current_period_end?: string;
  }>;
  userCart: {
    items: CartItem[];
  } | null;
  blogPosts: Array<{
    id: string;
    title: string;
    status: string;
  }>;
  blogComments: Array<{
    id: string;
    post_id: string;
    content: string;
    status: string;
  }>;
  loading: boolean;
}

interface CleanupResult {
  success: boolean;
  table: string;
  message?: string;
  error?: string;
}

export default function UserManagement() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [roleFilter, setRoleFilter] = useState<'all' | 'user' | 'admin'>('all');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    full_name: '',
    auto_confirm: true
  });
  const [creatingUser, setCreatingUser] = useState(false);
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null);
  const [editingUser, setEditingUser] = useState<UserProfile | null>(null);
  const [updatingUser, setUpdatingUser] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<{id: string, name: string, email: string} | null>(null);
  const [editForm, setEditForm] = useState({
    email: '',
    full_name: '',
    password: '',
    role: 'user'
  });

  // Advanced filter states
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [dateFilter, setDateFilter] = useState<{
    startDate: string | null;
    endDate: string | null;
  }>({ startDate: null, endDate: null });
  const [verificationFilter, setVerificationFilter] = useState<'all' | 'verified' | 'unverified'>('all');
  const [verificationLoading, setVerificationLoading] = useState(false);
  const filterMenuRef = useRef<HTMLDivElement>(null);

  // Close filter menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (filterMenuRef.current && !filterMenuRef.current.contains(event.target as Node)) {
        setShowFilterMenu(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Define fetchUsers with useCallback to avoid dependency issues
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      setVerificationLoading(true);

      // Get the current session for the auth token
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('You must be logged in to perform this action');
      }

      // Call the unified Edge Function to get all user data
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/get-users`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify({
            page: currentPage,
            pageSize,
            roleFilter,
            dateFilter,
            searchTerm: debouncedSearchTerm,
            verificationFilter
          })
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch users');
      }

      // Update state with the data from the edge function
      setUsers(result.data);
      setTotalCount(result.count);
      setTotalPages(result.totalPages);

    } catch (err: Error | unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      console.error("Error fetching users:", err);
    } finally {
      setLoading(false);
      setVerificationLoading(false);
    }
  }, [currentPage, pageSize, roleFilter, debouncedSearchTerm, dateFilter, verificationFilter]);

  // Fetch users when component mounts or dependencies change
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [roleFilter, debouncedSearchTerm, dateFilter.startDate, dateFilter.endDate, verificationFilter]);

  // Debounce search term to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const handleRefresh = async () => {
    setRefreshing(true);
    setCurrentPage(1); // Reset to first page
    try {
      await fetchUsers();
    } finally {
      setRefreshing(false);
    }
  };

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    setCreatingUser(true);

    try {
      // Validate form
      if (!newUser.email || !newUser.password || !newUser.full_name) {
        setError('All fields are required');
        setCreatingUser(false);
        return;
      }

      if (newUser.password.length < 8) {
        setError('Password must be at least 8 characters long');
        setCreatingUser(false);
        return;
      }

      // Get the current session for the auth token
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('You must be logged in to perform this action');
      }

      // Call the Edge Function to create the user with auto-confirmation
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-user`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify({
            email: newUser.email,
            password: newUser.password,
            full_name: newUser.full_name,
            auto_confirm: newUser.auto_confirm
          })
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create user');
      }

      setSuccess(newUser.auto_confirm
        ? 'User created successfully (email auto-confirmed)'
        : 'User created successfully');
      setNewUser({
        email: '',
        password: '',
        full_name: '',
        auto_confirm: true
      });

      // Hide the form after successful creation
      setShowCreateModal(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      // Refresh the user list
      fetchUsers();
    } catch (err: Error | unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      // Clear error message after 5 seconds
      setTimeout(() => {
        setError(null);
      }, 5000);
    } finally {
      setCreatingUser(false);
    }
  };

  const handleEditUser = (user: UserProfile) => {
    setEditingUser(user);
    setEditForm({
      email: user.email,
      full_name: user.full_name,
      password: '',
      role: user.role
    });
    setError(null);
    setSuccess(null);
  };

  const handleCancelEdit = () => {
    setEditingUser(null);
    setEditForm({
      email: '',
      full_name: '',
      password: '',
      role: 'user'
    });
    setError(null);
    setSuccess(null);
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingUser) return;

    setError(null);
    setSuccess(null);
    setUpdatingUser(true);

    try {
      // Validate form
      if (!editForm.email || !editForm.full_name) {
        setError('Email and full name are required');
        setUpdatingUser(false);
        return;
      }

      if (editForm.password && editForm.password.length < 8 && editForm.password.length > 0) {
        setError('Password must be at least 8 characters long');
        setUpdatingUser(false);
        return;
      }

      // Get the current session for the auth token
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('You must be logged in to perform this action');
      }

      // Call the Edge Function to update the user
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/update-user`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify({
            userId: editingUser.id,
            email: editForm.email,
            full_name: editForm.full_name,
            password: editForm.password || undefined,
            role: editForm.role
          })
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update user');
      }

      setSuccess('User updated successfully');

      // Reset form and exit edit mode
      setEditingUser(null);
      setEditForm({
        email: '',
        full_name: '',
        password: '',
        role: 'user'
      });

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      // Refresh the user list
      fetchUsers();
    } catch (err: Error | unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      // Clear error message after 5 seconds
      setTimeout(() => {
        setError(null);
      }, 5000);
    } finally {
      setUpdatingUser(false);
    }
  };

  const openDeleteModal = async (user: UserProfile) => {
    setUserToDelete({
      id: user.id,
      name: user.full_name || 'this user',
      email: user.email
    });
    setShowDeleteModal(true);
    await fetchRelatedData(user.id);
  };

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
    setUserToDelete(null);
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    const id = userToDelete.id;
    closeDeleteModal();

    try {
      setError(null);
      setSuccess(null);
      setDeletingUserId(id);

      // Get the current session for the auth token
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('You must be logged in to perform this action');
      }

      // Call the Edge Function to delete the user
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/delete-user`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify({ userId: id })
        }
      );

      const result = await response.json();

      if (!response.ok) {
        // Enhanced error handling
        const errorMessage = result.cleanupResults?.length > 0
          ? `Failed to delete user. Some operations failed:\n${(result.cleanupResults as CleanupResult[])
              .filter((r: CleanupResult) => !r.success)
              .map((r: CleanupResult) => `- ${r.table}: ${r.error}`)
              .join('\n')}`
          : result.error || 'Failed to delete user';

        throw new Error(errorMessage);
      }

      // Enhanced success message
      const successMessage = result.cleanupResults?.length > 0
        ? `User deleted successfully. Cleaned up: ${(result.cleanupResults as CleanupResult[])
            .filter((r: CleanupResult) => r.success)
            .map((r: CleanupResult) => r.table)
            .join(', ')}`
        : 'User deleted successfully';

      setSuccess(successMessage);

      // Clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);

      fetchUsers();
    } catch (err: Error | unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      // Clear error message after 8 seconds (longer for errors to be readable)
      setTimeout(() => {
        setError(null);
      }, 8000);
    } finally {
      setDeletingUserId(null);
    }
  };

  // Loading state is now handled within the table section

  // Add the related data state
  const [relatedData, setRelatedData] = useState<RelatedData>({
    subscriptions: [],
    userCart: null,
    blogPosts: [],
    blogComments: [],
    loading: false,
  });

  // Add the fetch related data function
  const fetchRelatedData = async (userId: string) => {
    setRelatedData(prev => ({ ...prev, loading: true }));
    try {
      const [subsResponse, cartResponse, postsResponse, commentsResponse] = await Promise.all([
        // Get active/trialing/pending subscriptions with related items and products
        supabase
          .from('subscriptions')
          .select(`
            id,
            status,
            period,
            current_period_end,
            subscription_items (
              id,
              quantity,
              status,
              amount,
              products (
                id,
                name,
                price
              )
            )
          `)
          .eq('user_id', userId)
          .in('status', ['active', 'trialing', 'pending cancellation']),
        
        // Get user's cart
        supabase
          .from('user_carts')
          .select('*')
          .eq('user_id', userId)
          .single(),
        
        // Get user's blog posts
        supabase
          .from('blog_posts')
          .select('id, title, status')
          .eq('author_id', userId),
        
        // Get user's blog comments
        supabase
          .from('blog_comments')
          .select('id, post_id, content, status')
          .eq('user_id', userId)
      ]);

      setRelatedData({
        subscriptions: subsResponse.data || [],
        userCart: cartResponse.data,
        blogPosts: postsResponse.data || [],
        blogComments: commentsResponse.data || [],
        loading: false
      });
    } catch (error) {
      console.error('Error fetching related data:', error);
      setRelatedData(prev => ({ ...prev, loading: false }));
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-900">User & Admin Management</h2>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleRefresh}
            className="flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-md p-1.5 text-gray-600 transition-colors"
            disabled={refreshing}
            title="Refresh users"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin text-blue-600' : ''}`} />
          </button>

          <div className="flex items-center space-x-2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search users..."
                className="pl-7 pr-7 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm !== debouncedSearchTerm && (
                <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
                </div>
              )}
              {searchTerm && (
                <button
                  className="absolute inset-y-0 right-0 pr-2 flex items-center"
                  onClick={() => setSearchTerm('')}
                >
                  <X className="h-4 w-4 text-gray-400" />
                </button>
              )}
            </div>

            {/* Advanced Filter Button */}
            <div className="relative" ref={filterMenuRef}>
              <button
                onClick={() => setShowFilterMenu(!showFilterMenu)}
                className={`flex items-center justify-center p-1.5 rounded-md transition-colors ${
                  showFilterMenu
                    ? 'bg-blue-100 text-blue-600 ring-2 ring-blue-200'
                    : dateFilter.startDate || dateFilter.endDate || verificationFilter !== 'all' || roleFilter !== 'all'
                      ? 'bg-blue-500 text-white hover:bg-blue-600'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}
                title={
                  dateFilter.startDate || dateFilter.endDate || verificationFilter !== 'all' || roleFilter !== 'all'
                    ? 'Filters active - click to modify'
                    : 'Advanced filters'
                }
              >
                <Filter className="h-4 w-4" />
                {(dateFilter.startDate || dateFilter.endDate || verificationFilter !== 'all' || roleFilter !== 'all') && (
                  <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center shadow-sm border border-white">
                    {(dateFilter.startDate ? 1 : 0) + (dateFilter.endDate ? 1 : 0) + (verificationFilter !== 'all' ? 1 : 0) + (roleFilter !== 'all' ? 1 : 0)}
                  </span>
                )}
              </button>

              {/* Filter Dropdown Menu */}
              {showFilterMenu && (
                <div className="absolute right-0 mt-1 w-64 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                  <div className="p-3">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Advanced Filters</h3>

                    {/* Date Range Filter */}
                    <div className="mb-3">
                      <label className="block text-xs font-medium text-gray-500 mb-1">Registration Date</label>
                      <div className="flex space-x-2">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">From</label>
                          <input
                            type="date"
                            value={dateFilter.startDate || ''}
                            onChange={(e) => setDateFilter({...dateFilter, startDate: e.target.value || null})}
                            className="w-full text-xs p-1.5 border border-gray-300 rounded"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">To</label>
                          <input
                            type="date"
                            value={dateFilter.endDate || ''}
                            onChange={(e) => setDateFilter({...dateFilter, endDate: e.target.value || null})}
                            className="w-full text-xs p-1.5 border border-gray-300 rounded"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Verification Status Filter */}
                    <div className="mb-3">
                      <label className="block text-xs font-medium text-gray-500 mb-1">
                        Verification Status
                        {verificationLoading && (
                          <span className="ml-2 inline-block">
                            <RefreshCw className="h-3 w-3 text-blue-500 animate-spin" />
                          </span>
                        )}
                      </label>
                      <select
                        value={verificationFilter}
                        onChange={(e) => setVerificationFilter(e.target.value as 'all' | 'verified' | 'unverified')}
                        className="w-full text-sm p-1.5 border border-gray-300 rounded"
                        disabled={verificationLoading}
                      >
                        <option value="all">All Users</option>
                        <option value="verified">Verified Only</option>
                        <option value="unverified">Unverified Only</option>
                      </select>
                    </div>

                    {/* Role Filter */}
                    <div className="mb-3">
                      <label className="block text-xs font-medium text-gray-500 mb-1">
                        User Role
                      </label>
                      <select
                        value={roleFilter}
                        onChange={(e) => setRoleFilter(e.target.value as 'all' | 'user' | 'admin')}
                        className="w-full text-sm p-1.5 border border-gray-300 rounded"
                      >
                        <option value="all">All Users</option>
                        <option value="user">Regular Users</option>
                        <option value="admin">Admins Only</option>
                      </select>
                    </div>

                    {/* Filter Actions */}
                    <div className="flex justify-between pt-3 mt-1 border-t border-gray-200">
                      <button
                        onClick={() => {
                          // Only show animation if there are active filters
                          const hasActiveFilters =
                            dateFilter.startDate !== null ||
                            dateFilter.endDate !== null ||
                            verificationFilter !== 'all' ||
                            roleFilter !== 'all';

                          // Clear all filters
                          setDateFilter({ startDate: null, endDate: null });
                          setVerificationFilter('all');
                          setRoleFilter('all');

                          // Close the filter menu if filters were cleared
                          if (hasActiveFilters) {
                            setTimeout(() => setShowFilterMenu(false), 300);
                          }
                        }}
                        className={`flex items-center text-xs px-2.5 py-1.5 rounded transition-colors font-medium ${
                          dateFilter.startDate || dateFilter.endDate || verificationFilter !== 'all' || roleFilter !== 'all'
                            ? 'bg-red-50 text-red-600 hover:bg-red-100 border border-red-200'
                            : 'bg-gray-100 text-gray-500 hover:bg-gray-200 border border-gray-200'
                        }`}
                        type="button"
                      >
                        <X className={`h-3 w-3 mr-1 ${
                          dateFilter.startDate || dateFilter.endDate || verificationFilter !== 'all' || roleFilter !== 'all'
                            ? 'text-red-500'
                            : 'text-gray-400'
                        }`} />
                        Clear All Filters
                      </button>
                      <button
                        onClick={() => setShowFilterMenu(false)}
                        className={`text-xs px-3 py-1.5 rounded font-medium ${
                          dateFilter.startDate || dateFilter.endDate || verificationFilter !== 'all' || roleFilter !== 'all'
                            ? 'bg-green-500 text-white hover:bg-green-600'
                            : 'bg-blue-500 text-white hover:bg-blue-600'
                        }`}
                        type="button"
                      >
                        {dateFilter.startDate || dateFilter.endDate || verificationFilter !== 'all' || roleFilter !== 'all'
                          ? 'Apply Filters'
                          : 'Close'
                        }
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          <button
            onClick={() => {
              setShowCreateModal(!showCreateModal);
              setError(null);
              setSuccess(null);
              if (showCreateModal) {
                // Reset form when closing
                setNewUser({
                  email: '',
                  password: '',
                  full_name: '',
                  auto_confirm: true
                });
              }
            }}
            className="flex items-center bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 text-sm disabled:bg-blue-400 disabled:cursor-not-allowed"
            disabled={editingUser !== null}
          >
            <UserPlus className="h-4 w-4 mr-1" />
            {showCreateModal ? 'Cancel' : 'Create User'}
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 text-sm rounded-md flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-3 py-2 text-sm rounded-md">
          {success}
        </div>
      )}

      {/* Create User Modal */}
      {showCreateModal && !editingUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Create New User</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          <form onSubmit={handleCreateUser} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Full Name
              </label>
              <input
                type="text"
                required
                value={newUser.full_name}
                onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2"
                placeholder="Enter full name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                type="email"
                required
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2"
                placeholder="Enter email address"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                type="password"
                required
                minLength={8}
                value={newUser.password}
                onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2"
                placeholder="Enter password (min. 8 characters)"
              />
              <p className="mt-1 text-sm text-gray-500">Must be at least 8 characters long</p>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="auto-confirm"
                checked={newUser.auto_confirm}
                onChange={(e) => setNewUser({ ...newUser, auto_confirm: e.target.checked })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="auto-confirm" className="ml-2 block text-sm text-gray-700">
                Auto-confirm email (user can login immediately)
              </label>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={creatingUser}
              >
                {creatingUser ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating...
                  </>
                ) : (
                  'Create User'
                )}
              </button>
            </div>
          </form>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {editingUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Edit User</h3>
              <button
                onClick={handleCancelEdit}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          <form onSubmit={handleUpdateUser} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Full Name
              </label>
              <input
                type="text"
                required
                value={editForm.full_name}
                onChange={(e) => setEditForm({ ...editForm, full_name: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2"
                placeholder="Enter full name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                type="email"
                required
                value={editForm.email}
                onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2"
                placeholder="Enter email address"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                type="password"
                value={editForm.password}
                onChange={(e) => setEditForm({ ...editForm, password: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2"
                placeholder="Enter new password (optional)"
              />
              <p className="mt-1 text-sm text-gray-500">Leave blank to keep current password</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Role
              </label>
              <div className="relative w-40">
                <select
                  value={editForm.role}
                  onChange={(e) => setEditForm({ ...editForm, role: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2 appearance-none"
                >
                  <option value="user">User</option>
                  <option value="admin">Admin</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2 text-gray-700 mt-1">
                  <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancelEdit}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={updatingUser}
              >
                {updatingUser ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Updating...
                  </>
                ) : (
                  'Update User'
                )}
              </button>
            </div>
          </form>
          </div>
        </div>
      )}

      <div className="bg-white rounded-md shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Verified
                </th>
                <th className="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Registered
                </th>
                <th className="px-4 py-2 bg-gray-50"></th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                      <span className="text-gray-500">{debouncedSearchTerm ? 'Searching...' : 'Loading users...'}</span>
                    </div>
                  </td>
                </tr>
              ) : users.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-gray-500 text-sm">
                    {debouncedSearchTerm || roleFilter !== 'all' ? 'No users match your criteria.' : 'No users found.'}
                  </td>
                </tr>
              ) : (
                users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-4 py-2 whitespace-nowrap">
                      <div className="flex items-center">
                        {user.avatar_url ? (
                          <img
                            src={user.avatar_url}
                            alt={user.full_name || user.email}
                            className="h-7 w-7 rounded-full object-cover"
                          />
                        ) : (
                          <UserRound className="h-7 w-7 text-gray-400" />
                        )}
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            {user.full_name || 'No Name'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500">
                        <Mail className="h-3 w-3 mr-1 text-gray-400" />
                        {user.email}
                      </div>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        user.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                      }`}>
                        {user.role}
                      </span>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      {user.email_confirmed_at ? (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Verified
                        </span>
                      ) : (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Pending
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {new Date(user.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleEditUser(user)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Edit User"
                          disabled={editingUser !== null || deletingUserId === user.id}
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => openDeleteModal(user)}
                          className="text-red-600 hover:text-red-900 disabled:text-red-300 disabled:cursor-not-allowed"
                          title="Delete User"
                          disabled={deletingUserId === user.id || editingUser !== null}
                        >
                          {deletingUserId === user.id ? (
                            <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>

          {/* Simple and Reliable Pagination */}
          <div className="border-t border-gray-200 bg-white px-4 py-3 mt-4">
            {/* Results info and page size selector */}
            <div className="flex flex-wrap items-center justify-between mb-4">
              <div className="text-sm text-gray-700 mb-2 sm:mb-0 flex items-center">
                Showing <span className="font-medium mx-1">{users.length > 0 && !loading ? (currentPage - 1) * pageSize + 1 : 0}</span> to{' '}
                <span className="font-medium mx-1">{!loading ? Math.min(currentPage * pageSize, totalCount) : 0}</span> of{' '}
                <span className="font-medium mx-1">{totalCount}</span> results
                {debouncedSearchTerm && <span className="ml-2 text-blue-600">for "{debouncedSearchTerm}"</span>}
              </div>

              <div className="flex items-center">
                <span className="mr-2 text-sm text-gray-700">Rows per page:</span>
                <div className="relative">
                  <select
                    value={pageSize}
                    onChange={(e) => {
                      const newSize = Number(e.target.value);
                      setPageSize(newSize);
                      // Adjust current page to maintain position in data as much as possible
                      const firstItemIndex = (currentPage - 1) * pageSize;
                      const newPage = Math.floor(firstItemIndex / newSize) + 1;
                      setCurrentPage(Math.min(newPage, Math.ceil(totalCount / newSize) || 1));
                    }}
                    className="py-1 text-sm bg-white border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 pl-2 pr-8 appearance-none"
                  >
                    <option value="5">5</option>
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-1 text-gray-700">
                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Simplified Pagination controls */}
            <div className="flex items-center justify-center">
              <nav className="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {/* Previous page button */}
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage <= 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </button>

                {/* Page number display */}
                <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-700">
                  Page <span className="font-bold mx-1 text-blue-600">{currentPage}</span> of <span className="font-bold mx-1">{totalPages || 1}</span>
                </span>

                {/* Next page button */}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages || 1))}
                  disabled={currentPage >= (totalPages || 1)}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && userToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all">
            <div className="flex items-center mb-4">
              <div className="bg-red-100 rounded-full p-2 mr-3">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">Confirm Deletion</h3>
            </div>
            <div className="mt-3 text-gray-600">
              <p>Are you sure you want to delete this user?</p>
              <div className="mt-2 bg-gray-50 p-3 rounded-md">
                <p className="font-medium text-gray-800">{userToDelete.name}</p>
                <p className="text-gray-500 text-sm">{userToDelete.email}</p>
              </div>

              {/* Summary of related data */}
              <div className="mt-4 space-y-2">
                {relatedData.loading ? (
                  <div className="flex items-center justify-center py-3">
                    <RefreshCw className="h-5 w-5 text-blue-500 animate-spin mr-2" />
                    <span className="text-sm text-gray-500">Loading related data...</span>
                  </div>
                ) : (
                  <div className="bg-red-50 p-3 rounded-md">
                    {/* Only show the section if there's any related data */}
                    {(relatedData.subscriptions.length > 0 || 
                      (relatedData.userCart?.items?.length ?? 0) > 0 || 
                      relatedData.blogPosts.length > 0 || 
                      relatedData.blogComments.length > 0) ? (
                      <>
                        <h4 className="text-sm font-semibold text-red-700 mb-2">This will also delete:</h4>
                        <ul className="space-y-1.5">
                          {relatedData.subscriptions.length > 0 && (
                            <li className="text-sm flex items-center text-red-600">
                              <span className="w-5 h-5 flex items-center justify-center bg-red-100 rounded-full mr-2 text-xs font-medium">
                                {relatedData.subscriptions.length}
                              </span>
                              Active subscription{relatedData.subscriptions.length !== 1 ? 's' : ''}
                            </li>
                          )}
                          {relatedData.userCart?.items?.length > 0 && (
                            <li className="text-sm flex items-center text-red-600">
                              <span className="w-5 h-5 flex items-center justify-center bg-red-100 rounded-full mr-2 text-xs font-medium">
                                {relatedData.userCart.items.length}
                              </span>
                              Cart item{relatedData.userCart.items.length !== 1 ? 's' : ''}
                            </li>
                          )}
                          {relatedData.blogPosts.length > 0 && (
                            <li className="text-sm flex items-center text-red-600">
                              <span className="w-5 h-5 flex items-center justify-center bg-red-100 rounded-full mr-2 text-xs font-medium">
                                {relatedData.blogPosts.length}
                              </span>
                              Blog post{relatedData.blogPosts.length !== 1 ? 's' : ''}
                            </li>
                          )}
                          {relatedData.blogComments.length > 0 && (
                            <li className="text-sm flex items-center text-red-600">
                              <span className="w-5 h-5 flex items-center justify-center bg-red-100 rounded-full mr-2 text-xs font-medium">
                                {relatedData.blogComments.length}
                              </span>
                              Blog comment{relatedData.blogComments.length !== 1 ? 's' : ''}
                            </li>
                          )}
                        </ul>
                        <p className="text-xs text-red-500 mt-2 font-medium">
                          All related data will be permanently deleted.
                        </p>
                      </>
                    ) : (
                      <p className="text-sm text-gray-600">
                        This user has no related data (subscriptions, cart items, blog posts, or comments).
                      </p>
                    )}
                  </div>
                )}
              </div>

              <p className="mt-3 text-sm text-gray-500">This action cannot be undone.</p>
            </div>
            <div className="mt-5 flex justify-end space-x-3">
              <button
                onClick={closeDeleteModal}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteUser}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 flex items-center"
              >
                <Trash2 className="h-4 w-4 mr-1.5" />
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
