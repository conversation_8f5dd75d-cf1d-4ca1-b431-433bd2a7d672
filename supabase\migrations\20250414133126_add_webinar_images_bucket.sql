-- Create storage bucket for webinar images if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('webinar-images', 'webinar-images', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for webinar images bucket
CREATE POLICY "Public can view webinar images"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'webinar-images');

CREATE POLICY "Ad<PERSON> can manage webinar images"
ON storage.objects FOR ALL 
TO authenticated
USING (
  bucket_id = 'webinar-images' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);

-- Add image format validation
CREATE POLICY "Validate webinar image uploads"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'webinar-images' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ) AND
  storage.extension(name) IN ('jpg', 'jpeg', 'png', 'gif', 'webp')
);
