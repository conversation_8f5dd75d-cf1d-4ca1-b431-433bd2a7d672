-- Function to sync frontend content
CREATE OR REPLACE FUNCTION sync_frontend_content()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- GrantReady page
  INSERT INTO pages (slug, title, content, html_content, custom_css, meta_description, is_published)
  VALUES (
    'solutions/grantready',
    'GrantReady™',
    '# GrantReady™ - Effortlessly Manage Grants',
    '<div class="">
      <section class="relative bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 text-white py-32 overflow-hidden">
        <div class="absolute inset-0 overflow-hidden">
          <div class="absolute -top-1/2 -left-1/2 w-full h-full rounded-full bg-gradient-to-br from-purple-400/30 to-transparent blur-3xl animate-[spin_30s_linear_infinite]"></div>
          <div class="absolute -bottom-1/2 -right-1/2 w-full h-full rounded-full bg-gradient-to-br from-purple-500/30 to-transparent blur-3xl animate-[spin_25s_linear_infinite_reverse]"></div>
          <div class="absolute inset-0 bg-[url(''data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiIGlkPSJhIj48c3RvcCBzdG9wLWNvbG9yPSIjZmZmIiBzdG9wLW9wYWNpdHk9Ii4yIiBvZmZzZXQ9IjAlIi8+PHN0b3Agc3RvcC1jb2xvcj0iI2ZmZiIgc3RvcC1vcGFjaXR5PSIwIiBvZmZzZXQ9IjEwMCUiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cGF0aCBkPSJNMCAwaDYwdjYwSDB6IiBmaWxsPSJ1cmwoI2EpIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4='')]"></div>
          <div class="absolute inset-0 bg-gradient-to-br from-purple-600/80 via-purple-700/80 to-purple-800/80 backdrop-blur-[2px]"></div>
        </div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid md:grid-cols-2 gap-12 items-center relative">
            <div class="relative z-10 animate-[fadeIn_1s_ease-out]">
              <h1 class="text-5xl font-bold mb-6">Effortlessly<br/>Manage<br/>Grants</h1>
              <p class="text-xl mb-8 text-purple-100">Execute Your Grants Quickly and Effectively</p>
              <div class="space-x-4">
                <a href="/solutions/grantready/whitepaper" class="bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5">Read Whitepaper</a>
                <a href="https://grantready.internationalrespondersystems.net/login/?redirect-path=%2F" target="_blank" rel="noopener noreferrer" class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white/10 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5">Try GrantReady™</a>
              </div>
            </div>
            <div class="relative z-10 animate-[slideIn_1s_ease-out]">
              <div class="bg-white/10 backdrop-blur-md p-6 rounded-xl transform hover:scale-[1.02] transition-all duration-300 hover:shadow-2xl">
                <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" alt="GrantReady Preview" class="w-full aspect-video rounded-lg object-cover">
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>',
    '.feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      padding: 2rem;
    }
    
    .feature-card {
      background: white;
      padding: 2rem;
      border-radius: 1rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
    }',
    'GrantReady™ - Streamline your grant management process with our comprehensive solution',
    true
  )
  ON CONFLICT (slug) DO UPDATE
  SET 
    title = EXCLUDED.title,
    content = EXCLUDED.content,
    html_content = EXCLUDED.html_content,
    custom_css = EXCLUDED.custom_css,
    meta_description = EXCLUDED.meta_description;

  -- SOAR page
  INSERT INTO pages (slug, title, content, html_content, custom_css, meta_description, is_published)
  VALUES (
    'solutions/soar',
    'SOAR Platform',
    '# SOAR - Predict Tomorrow, Act Today',
    '<div class="">
      <section class="relative bg-gradient-to-br from-orange-500 via-orange-600 to-orange-700 text-white py-32 overflow-hidden">
        <div class="absolute inset-0 overflow-hidden">
          <div class="absolute -top-1/2 -left-1/2 w-full h-full rounded-full bg-gradient-to-br from-orange-300/30 to-transparent blur-3xl animate-[spin_30s_linear_infinite]"></div>
          <div class="absolute -bottom-1/2 -right-1/2 w-full h-full rounded-full bg-gradient-to-br from-orange-400/30 to-transparent blur-3xl animate-[spin_25s_linear_infinite_reverse]"></div>
          <div class="absolute inset-0 bg-gradient-to-br from-orange-500/80 via-orange-600/80 to-orange-700/80 backdrop-blur-[2px]"></div>
        </div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid md:grid-cols-2 gap-12 items-center relative">
            <div class="relative z-10 animate-[fadeIn_1s_ease-out]">
              <h1 class="text-5xl font-bold mb-6">Predict Tomorrow<br/>Act Today</h1>
              <p class="text-xl mb-8 text-orange-100">Advanced epidemic forecasting and outbreak prediction powered by cutting-edge AI</p>
              <div class="space-x-4">
                <a href="https://metabase.chriszhu.me/public/dashboard/62499bf1-b6b2-4723-aee6-470f023e934d" target="_blank" rel="noopener noreferrer" class="bg-white text-orange-600 px-6 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5">Get Started</a>
              </div>
            </div>
            <div class="relative z-10 animate-[slideIn_1s_ease-out]">
              <img src="https://images.unsplash.com/photo-1584036561566-baf8f5f1b144?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" alt="SOAR Platform" class="rounded-lg shadow-2xl">
            </div>
          </div>
        </div>
      </section>
    </div>',
    '.feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      padding: 2rem;
    }
    
    .stat-card {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 1.5rem;
      border-radius: 1rem;
      animation: fadeIn 0.5s ease-out forwards;
      opacity: 0;
    }
    
    @keyframes fadeIn {
      to {
        opacity: 1;
      }
    }',
    'SOAR Platform - Advanced epidemic forecasting and outbreak prediction powered by cutting-edge AI',
    true
  )
  ON CONFLICT (slug) DO UPDATE
  SET 
    title = EXCLUDED.title,
    content = EXCLUDED.content,
    html_content = EXCLUDED.html_content,
    custom_css = EXCLUDED.custom_css,
    meta_description = EXCLUDED.meta_description;

  -- ELENOR page
  INSERT INTO pages (slug, title, content, html_content, custom_css, meta_description, is_published)
  VALUES (
    'solutions/elenor',
    'ELENOR',
    '# ELENOR - Situational Awareness at your Fingertips',
    '<div class="">
      <section class="relative bg-gradient-to-r from-yellow-500 to-yellow-600 text-white py-32 overflow-hidden">
        <div class="absolute inset-0 overflow-hidden">
          <div class="absolute inset-0 bg-[url(''data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0MCIgaGVpZ2h0PSI1MDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IHgxPSIxMDAlIiB5MT0iMjEuMTgyJSIgeDI9IjUwJSIgeTI9IjEwMCUiIGlkPSJhIj48c3RvcCBzdG9wLWNvbG9yPSIjRkZGIiBvZmZzZXQ9IjAlIi8+PHN0b3Agc3RvcC1jb2xvcj0iI0ZGRiIgc3RvcC1vcGFjaXR5PSIwIiBvZmZzZXQ9IjEwMCUiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cGF0aCBkPSJNLTM1MS41IDUwMS41bDE4OTgtMTA5NiIgc3Ryb2tlPSJ1cmwoI2EpIiBzdHJva2Utd2lkdGg9IjEuNSIgZmlsbD0ibm9uZSIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJNLTM4MS41IDUwMS41bDE4OTgtMTA5NiIgc3Ryb2tlPSJ1cmwoI2EpIiBzdHJva2Utd2lkdGg9IjEuNSIgZmlsbD0ibm9uZSIgb3BhY2l0eT0iLjQiLz48L3N2Zz4=''))] animate-[gradient_15s_ease_infinite]"></div>
          <div class="absolute inset-0 bg-gradient-to-r from-yellow-500/90 to-yellow-600/90"></div>
        </div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">Situational Awareness at your Fingertips</h1>
          <p class="text-xl md:text-2xl mb-12 text-blue-100">We are working hard to build this new site experience for you!</p>
          <a href="/contact" class="inline-flex items-center bg-white text-yellow-600 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-50 transition duration-300">Contact Us</a>
        </div>
      </section>
    </div>',
    '.feature-card {
      background: white;
      padding: 2rem;
      border-radius: 1rem;
      transition: all 0.3s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }',
    'ELENOR - Advanced emergency response and coordination platform',
    true
  )
  ON CONFLICT (slug) DO UPDATE
  SET 
    title = EXCLUDED.title,
    content = EXCLUDED.content,
    html_content = EXCLUDED.html_content,
    custom_css = EXCLUDED.custom_css,
    meta_description = EXCLUDED.meta_description;

END;
$$;

-- Run the sync function
SELECT sync_frontend_content();

-- Drop the sync function as it's no longer needed
DROP FUNCTION sync_frontend_content();