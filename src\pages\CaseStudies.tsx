import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>, Building2, ArrowRight, Search, Filter, LogIn, X, Image } from 'lucide-react';
import { Link, redirect } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';
import defaultImage from '../assets/images/default-image.jpg';


interface CaseStudy {
  id: string;
  title: string;
  organization: string;
  impact: string;
  category: string;
  image_url: string;
  content: string;
  is_public: boolean;
}

export default function CaseStudies() {
  const user = useAuthStore((state) => state.user);

  const [caseStudies, setCaseStudies] = useState<CaseStudy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterTerm, setFilterTerm] = useState('');
  const [showLoginModal, setShowLoginModal] = useState(false);

  useEffect(() => {
    const fetchCaseStudies = async () => {
      try {
        let query = supabase
          .from('case_studies')
          .select('*')
          .eq('is_public', true)
          .order('created_at', { ascending: false });

        const { data, error } = await query;

        if (error) throw error;
        setCaseStudies(data || []);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchCaseStudies();
  }, []);

  const filteredStudies = caseStudies.filter(study => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch =
      study.title.toLowerCase().includes(searchLower) ||
      study.content.toLowerCase().includes(searchLower) ||
      study.organization.toLowerCase().includes(searchLower) ||
      study.impact.toLowerCase().includes(searchLower) ||
      study.category.toLowerCase().includes(searchLower);

    const matchesFilter = filterTerm === '' || study.organization === filterTerm;
    return matchesSearch && matchesFilter;
  });

  const featuredStudy = caseStudies[0];
  const truncatedContent = (text: string, maxLength = 150) => {
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  const getFirstParagraph = (text: string) => {
    // Remove markdown headings (lines starting with #)
    const withoutHeadings = text.replace(/^#+\s.*$/gm, '');
    // Get first paragraph (text until double newline)
    const firstParagraph = withoutHeadings.split('\n\n')[0];
    // Trim and truncate if needed
    return firstParagraph.trim().length > 150
      ? `${firstParagraph.trim().substring(0, 150)}...`
      : firstParagraph.trim();
  };

  // Only show featured study when not searching
  const shouldShowFeatured = searchTerm === '' && filterTerm === '';

  // if (loading) return <div className=" flex justify-center py-20">Loading...</div>;
  // if (error) return <div className="flex justify-center py-20 text-red-500">Error: {error}</div>;

  return (
    <div className="">
      {showLoginModal ? (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity">
          <div className="bg-white rounded-2xl shadow-2xl p-0 max-w-md w-full text-center relative animate-fade-in">
            <div className="flex flex-col items-stretch">
              <div className="flex items-center justify-between px-6 pt-6 pb-2">
                <h2 className="text-lg font-bold text-gray-900">Register for Webinar</h2>
                <button
                  onClick={() => setShowLoginModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                  aria-label="Close"
                >
                  <X className='w-8 h-8' />
                </button>
              </div>
              <div className="bg-blue-50 rounded-xl mx-6 mb-6 px-6 py-8 flex flex-col items-center">
                <LogIn className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                <div className="font-semibold text-lg text-gray-900 mb-1">Login Required</div>
                <p className="text-gray-600 text-sm mb-5">
                  Please log in to view case studies details.
                </p>
                <Link
                  to="/login"
                  className="inline-flex items-center justify-center bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition text-base mb-2"
                >
                  <LogIn className="h-6 w-6 mr-2" />
                  Log In
                </Link>
                <div className="text-gray-500 text-sm">
                  Don&apos;t have an account?{' '}
                  <Link to="/signup" className="text-blue-600 hover:underline font-medium">
                    Sign up
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : null}

      {/* Hero Section */}
      <section className="text-white py-24 relative rounded-lg" style={{background: 'radial-gradient(50% 55% at 0% 0%, #06B6D4 0%, #0891B2 100%)'}}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Medium circles */}
          <div className="absolute top-1/2 -left-20 w-32 h-32 bg-white/4 rounded-full"></div>
          <div className="absolute bottom-10 left-1/4 w-24 h-24 bg-white/3 rounded-full"></div>

          {/* Small floating elements */}
          <div className="absolute top-16 left-1/3 w-4 h-4 bg-white/20 rounded-full animate-pulse"></div>
          <div className="absolute bottom-32 right-1/4 w-3 h-3 bg-white/30 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute top-1/3 right-1/3 w-2 h-2 bg-white/25 rounded-full animate-pulse delay-500"></div>
        </div>

        {/* Creative illustration on the right - Made bigger */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block overflow-hidden">
          {/* Main illustration container */}
          <div className="relative h-full flex items-center justify-center">
            {/* Digital case studies visualization - Scaled up */}
            <div className="relative scale-150">
              {/* Floating case study cards - Made bigger */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float border border-white/10">
                <div className="p-3">
                  <div className="w-full h-2.5 bg-blue-300/60 rounded mb-1.5"></div>
                  <div className="w-3/4 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-1/2 h-1.5 bg-blue-200/50 rounded"></div>
                  {/* Mini chart icon */}
                  <div className="absolute top-1 right-1 w-3 h-3 bg-blue-400/40 rounded flex items-center justify-center">
                    <div className="w-1.5 h-1.5 bg-white/60 rounded-sm"></div>
                  </div>
                </div>
              </div>

              <div className="absolute -top-6 -left-8 w-18 h-22 bg-white/8 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed border border-white/10">
                <div className="p-2.5">
                  <div className="w-full h-2 bg-blue-300/60 rounded mb-1"></div>
                  <div className="w-2/3 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-3/4 h-1.5 bg-blue-200/50 rounded"></div>
                  {/* Mini building icon */}
                  <div className="absolute top-1 right-1 w-3 h-3 bg-blue-400/40 rounded flex items-center justify-center">
                    <Building2 className="w-2 h-2 text-white/60" />
                  </div>
                </div>
              </div>

              {/* Central case study icon - Enhanced */}
              <div className="w-32 h-40 bg-white/15 rounded-xl backdrop-blur-sm border-2 border-white/30 flex flex-col items-center justify-center transform hover:scale-105 transition-all duration-300 hover:bg-white/20 hover:border-white/40">
                <div className="relative">
                  <FileChart className="h-16 w-16 text-white/90 drop-shadow-lg" />
                  {/* Glowing effect */}
                  <div className="absolute inset-0 h-16 w-16 bg-blue-400/20 rounded-full blur-xl"></div>
                </div>
                {/* <div className="mt-2 text-xs text-white/70 font-medium">Case Studies</div> */}
              </div>

              {/* More floating elements - Enhanced */}
              <div className="absolute -bottom-8 -right-4 w-16 h-20 bg-white/12 rounded-lg backdrop-blur-sm transform rotate-6 animate-float border border-white/10">
                <div className="p-2">
                  <div className="w-full h-2 bg-blue-300/60 rounded mb-1"></div>
                  <div className="w-3/4 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded"></div>
                  {/* Mini arrow icon */}
                  <div className="absolute bottom-1 right-1 w-3 h-3 bg-blue-400/40 rounded flex items-center justify-center">
                    <ArrowRight className="w-2 h-2 text-white/60" />
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-4 -left-6 w-14 h-18 bg-white/10 rounded-lg backdrop-blur-sm transform -rotate-12 animate-float-delayed border border-white/10">
                <div className="p-2">
                  <div className="w-full h-1.5 bg-blue-300/60 rounded mb-1"></div>
                  <div className="w-2/3 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded"></div>
                  {/* Mini image icon */}
                  <div className="absolute bottom-1 right-1 w-3 h-3 bg-blue-400/40 rounded flex items-center justify-center">
                    <Image className="w-2 h-2 text-white/60" />
                  </div>
                </div>
              </div>

              {/* Connecting lines/paths - Made more visible */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-24 h-0.5 bg-white/20 transform rotate-45 absolute"></div>
                <div className="w-20 h-0.5 bg-white/15 transform -rotate-45 absolute top-4"></div>
                <div className="w-16 h-0.5 bg-white/20 transform rotate-12 absolute -top-4 left-2"></div>
              </div>

              {/* Orbiting elements - Made bigger */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-48 h-48 border border-white/10 rounded-full animate-spin-slow">
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white/30 rounded-full"></div>
                  <div className="absolute top-1/2 -right-2 transform -translate-y-1/2 w-3 h-3 bg-white/25 rounded-full"></div>
                  <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white/20 rounded-full"></div>
                  <div className="absolute top-1/2 -left-2 transform -translate-y-1/2 w-3 h-3 bg-white/25 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content pushed to the left */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Case Studies
              <span className="block text-3xl font-normal mt-2 text-blue-200">
                Success Stories Hub
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Discover how organizations are transforming their operations and achieving remarkable results with our solutions
            </p>

            {/* Search functionality integrated into banner */}
            <div className="relative w-full max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search case studies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3.5 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Case Study - only show when not searching */}
      {shouldShowFeatured && featuredStudy && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-lg shadow-xl overflow-hidden">
              <div className="md:flex">
                <div className="max-h-96 md:w-1/2">
                  {featuredStudy.image_url ? (
                    <img
                      src={featuredStudy.image_url}
                      alt={featuredStudy.title}
                      className="h-full w-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.onerror = null;
                        target.src = defaultImage;
                      }}
                    />
                  ) : (
                    <img
                      src={defaultImage}
                      alt="Default Case Study"
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
                <div className="md:w-1/2 p-8">
                  <span className="inline-block px-4 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mb-4">
                    Featured Case Study
                  </span>
                  <h2 className="text-3xl font-bold mb-4">{featuredStudy.title}</h2>
                  <p className="text-gray-600 mb-6">
                    {getFirstParagraph(featuredStudy.content)}
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center">
                      <Building2 className="h-5 w-5 text-gray-400 mr-2" />
                      <span className="text-gray-600">{featuredStudy.organization}</span>
                    </div>
                    <div className="flex items-center">
                      <FileChart className="h-5 w-5 text-gray-400 mr-2" />
                      <span className="text-gray-600">{featuredStudy.impact}</span>
                    </div>
                  </div>
                  {user ? (
                    <Link
                      to={`/case-studies/${featuredStudy.id}`}
                      className="flex items-center text-blue-600 font-medium hover:text-blue-700"
                    >
                      Read Full Case Study
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </Link>
                  ) : (
                    <button
                      onClick={() => setShowLoginModal(true)}
                      className="flex items-center text-blue-600 font-medium hover:text-blue-700"
                    >
                      Read Full Case Study
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Case Studies Grid */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">All Case Studies</h2>
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <select
                className="pl-10 pr-4 py-2 rounded-lg border border-gray-300 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filterTerm}
                onChange={(e) => setFilterTerm(e.target.value)}
              >
                <option value="">All Organizations</option>
                {Array.from(new Set(caseStudies.map(study => study.organization))).map(org => (
                  <option key={org} value={org}>{org}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {filteredStudies.map((study) => (
              <div key={study.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div className="w-full h-48 relative">
                  {study.image_url ? (
                    <img
                      src={study.image_url}
                      alt={study.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.onerror = null;
                        target.src = defaultImage; // Path to your default image
                      }}
                    />
                  ) : (
                    <img
                      src={defaultImage}
                      alt="Default Case Study"
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
                <div className="p-6">
                  <span className="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                    {study.category}
                  </span>
                  <h3 className="text-xl font-bold mt-4 mb-2">{study.title}</h3>
                  <div className="space-y-3 mb-4">
                    <div className="flex items-center text-gray-600">
                      <Building2 className="h-5 w-5 mr-2" />
                      <span>{study.organization}</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <FileChart className="h-5 w-5 mr-2" />
                      <span>{study.impact}</span>
                    </div>
                  </div>
                  {user ? (
                    <Link
                      to={`/case-studies/${study.id}`}
                      className="text-blue-600 font-medium hover:text-blue-700"
                    >
                      Read Case Study →
                    </Link>
                  ) : (
                    <button
                      onClick={() => setShowLoginModal(true)}
                      className="text-blue-600 font-medium hover:text-blue-700"
                    >
                      Read Case Study →
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );


}