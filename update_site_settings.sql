-- SQL commands to update site_settings table with new resource features
-- Run these commands in your database to add the new resource feature flags

-- First, let's see what we currently have
SELECT settings FROM site_settings ORDER BY created_at DESC LIMIT 1;

-- Update existing site_settings records to include the new resource features
-- This adds all the new resource feature flags to existing records
UPDATE site_settings 
SET settings = jsonb_set(
  jsonb_set(
    jsonb_set(
      jsonb_set(
        jsonb_set(
          jsonb_set(
            settings,
            '{features,resourcesLibraryEnabled}',
            'true'::jsonb
          ),
          '{features,webinarsEnabled}',
          'true'::jsonb
        ),
        '{features,whitepapersEnabled}',
        'true'::jsonb
      ),
      '{features,guidesEnabled}',
      'true'::jsonb
    ),
    '{features,caseStudiesEnabled}',
    'true'::jsonb
  ),
  '{features,eventsEnabled}',
  'true'::jsonb
)
WHERE settings IS NOT NULL;

-- If no site_settings exist, insert default settings with all features
-- This ensures we have a complete settings record
INSERT INTO site_settings (settings)
SELECT '{
  "general": {
    "siteName": "International Responder Systems",
    "siteDescription": "Healthcare Emergency Response Solutions",
    "contactEmail": "<EMAIL>",
    "phone": "",
    "address": "157 E Main Street, Elkton, MD 21921-5977",
    "socialLinks": {
      "linkedin": "https://www.linkedin.com/company/international-responder-systems",
      "twitter": "https://twitter.com/intrespondersys",
      "facebook": "https://www.facebook.com/InternationalResponderSystems"
    }
  },
  "seo": {
    "defaultTitle": "International Responder Systems - Healthcare Emergency Response Solutions",
    "defaultDescription": "Leading provider of healthcare emergency response and grant management solutions.",
    "defaultKeywords": "healthcare, emergency response, grant management, SOAR, GrantReady™",
    "googleAnalyticsId": "",
    "googleTagManagerId": "",
    "googleSiteVerification": "",
    "bingVerification": "",
    "robotsTxt": "User-agent: *\nAllow: /",
    "sitemapEnabled": true
  },
  "appearance": {
    "logo": "",
    "favicon": "",
    "primaryColor": "#2563eb",
    "secondaryColor": "#1e40af",
    "fontFamily": "Inter"
  },
  "features": {
    "blogEnabled": true,
    "commentsEnabled": true,
    "userRegistrationEnabled": true,
    "maintenanceMode": false,
    "resourcesLibraryEnabled": true,
    "webinarsEnabled": true,
    "whitepapersEnabled": true,
    "guidesEnabled": true,
    "caseStudiesEnabled": true,
    "eventsEnabled": true
  }
}'::jsonb
WHERE NOT EXISTS (SELECT 1 FROM site_settings);

-- Verify the update worked
SELECT 
  settings->'features'->>'resourcesLibraryEnabled' as resources_library_enabled,
  settings->'features'->>'blogEnabled' as blog_enabled,
  settings->'features'->>'webinarsEnabled' as webinars_enabled,
  settings->'features'->>'whitepapersEnabled' as whitepapers_enabled,
  settings->'features'->>'guidesEnabled' as guides_enabled,
  settings->'features'->>'caseStudiesEnabled' as case_studies_enabled,
  settings->'features'->>'eventsEnabled' as events_enabled
FROM site_settings 
ORDER BY created_at DESC 
LIMIT 1;

-- Optional: If you want to see the complete settings structure
-- SELECT jsonb_pretty(settings) FROM site_settings ORDER BY created_at DESC LIMIT 1;
