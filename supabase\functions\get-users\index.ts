import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4'

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Handle OPTIONS request for CORS
export const corsHandler = (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
}

Deno.serve(async (req) => {
  // Handle CORS
  const corsResponse = corsHandler(req)
  if (corsResponse) return corsResponse

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { ...corsHeaders }
      });
    }

    // Get filter parameters from request
    const { 
      page = 1, 
      pageSize = 10, 
      roleFilter = 'all', 
      dateFilter = { startDate: null, endDate: null }, 
      searchTerm = '',
      verificationFilter = 'all'
    } = await req.json();

    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get the current user's session
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing Authorization header' }), {
        status: 401,
        headers: { ...corsHeaders }
      });
    }

    // Verify the user is an admin
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token)
    
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders }
      });
    }

    // Get the user's profile to check if they're an admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || !profile || profile.role !== 'admin') {
      return new Response(JSON.stringify({ error: 'Unauthorized - Admin privileges required' }), {
        status: 403,
        headers: { ...corsHeaders }
      });
    }

    // Calculate pagination values
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    // Build the query with count option to get total
    let query = supabaseAdmin
      .from('profiles')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Apply role filter if provided
    if (roleFilter !== 'all') {
      query = query.eq('role', roleFilter);
    }

    // Apply date range filter if provided
    if (dateFilter.startDate) {
      query = query.gte('created_at', dateFilter.startDate);
    }

    if (dateFilter.endDate) {
      // Add one day to include the end date fully
      const endDateObj = new Date(dateFilter.endDate);
      endDateObj.setDate(endDateObj.getDate() + 1);
      const adjustedEndDate = endDateObj.toISOString().split('T')[0];
      query = query.lt('created_at', adjustedEndDate);
    }

    // Apply search filter if provided
    if (searchTerm.trim() !== '') {
      query = query.or(`email.ilike.%${searchTerm}%,full_name.ilike.%${searchTerm}%,role.ilike.%${searchTerm}%`);
    }

    // Apply pagination
    query = query.range(from, to);

    // Execute the query
    const { data: profiles, error, count } = await query;

    if (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders }
      });
    }

    // Get auth users to add email confirmation status
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.listUsers();

    if (authError) {
      return new Response(JSON.stringify({ error: authError.message }), {
        status: 500,
        headers: { ...corsHeaders }
      });
    }

    // Combine the data with auth information
    let combinedData = profiles.map(profile => {
      const authUser = authData?.users?.find(u => u.id === profile.id);
      return {
        ...profile,
        email_confirmed_at: authUser?.email_confirmed_at || null,
        last_sign_in_at: authUser?.last_sign_in_at || null
      };
    });

    // Apply verification filter if needed
    if (verificationFilter !== 'all') {
      combinedData = combinedData.filter(user => {
        if (verificationFilter === 'verified') {
          return user.email_confirmed_at !== null;
        } else { // unverified
          return user.email_confirmed_at === null;
        }
      });
    }

    // Return the combined data with pagination info
    return new Response(JSON.stringify({
      success: true,
      data: combinedData,
      count: count || 0,
      totalPages: Math.ceil((count || 0) / pageSize)
    }), {
      status: 200,
      headers: { ...corsHeaders }
    });
  } catch (err) {
    return new Response(JSON.stringify({ error: err.message }), {
      status: 500,
      headers: { ...corsHeaders }
    });
  }
});
