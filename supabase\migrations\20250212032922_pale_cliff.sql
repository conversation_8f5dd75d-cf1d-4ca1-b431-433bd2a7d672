-- First drop the trigger that automatically creates profiles
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Clear existing data
TRUNCATE auth.users CASCADE;

-- Create new admin user
DO $$
DECLARE
  admin_id uuid := gen_random_uuid();
BEGIN
  -- Insert admin user with explicit ID
  INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    role,
    aud,
    confirmation_token
  )
  VALUES (
    admin_id,
    '00000000-0000-0000-0000-000000000000'::uuid,
    '<EMAIL>',
    crypt('11111111', gen_salt('bf')),
    now(),
    now(),
    now(),
    '{"provider":"email","providers":["email"]}'::jsonb,
    '{"name":"Admin User"}'::jsonb,
    false,
    'authenticated',
    'authenticated',
    'confirmed'
  );

  -- Add admin to admin_users table
  INSERT INTO admin_users (id, email, role)
  VALUES (
    admin_id,
    '<EMAIL>',
    'admin'
  );

  -- Add admin to profiles table
  INSERT INTO profiles (id, email, full_name)
  VALUES (
    admin_id,
    '<EMAIL>',
    'Admin User'
  );
END $$;

-- Recreate the trigger for future user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();