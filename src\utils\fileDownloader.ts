/**
 * Utility function to securely download files
 * This prevents the browser from opening PDFs directly and forces a download
 */

export const downloadFile = async (url: string, filename?: string, callbacks?: {
  onStart?: () => void;
  onComplete?: () => void;
  onError?: (error: unknown) => void;
}) => {
  try {
    // Call the start callback if provided
    if (callbacks?.onStart) callbacks.onStart();

    // Fetch the file
    const response = await fetch(url);
    const blob = await response.blob();

    // Create a blob URL
    const blobUrl = window.URL.createObjectURL(blob);

    // Create a temporary link element
    const link = document.createElement('a');
    link.href = blobUrl;

    // Set the download attribute with filename (use original filename or extract from URL)
    link.download = filename || url.substring(url.lastIndexOf('/') + 1);

    // Append to the document
    document.body.appendChild(link);

    // Trigger the download
    link.click();

    // Clean up
    document.body.removeChild(link);
    window.URL.revokeObjectURL(blobUrl);

    // Call the complete callback if provided
    if (callbacks?.onComplete) callbacks.onComplete();

    return true;
  } catch (error) {
    console.error('Download failed:', error);

    // Call the error callback if provided
    if (callbacks?.onError) callbacks.onError(error);

    return false;
  }
};
