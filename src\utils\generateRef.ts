/**
 * Generates a random reference string of specified length
 * @param length Length of the reference string (default: 8)
 * @returns Random alphanumeric reference string
 */
export function generateRef(length: number = 8): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters.charAt(randomIndex);
  }
  
  return result;
}
