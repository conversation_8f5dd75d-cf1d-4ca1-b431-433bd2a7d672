/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  safelist: [
    // Dynamic color classes that should never be purged
    'text-[#F77455]',
    'text-[#46A899]',
    'text-[#797EEC]',
    'text-[#3B82F6]',
    'text-[#EF4444]',
    'text-[#10B981]',
    'text-[#F59E0B]',
    'text-[#8B5CF6]',
    // Add any other dynamic colors you might use
    {
      pattern: /text-\[#[0-9A-Fa-f]{6}\]/,
      variants: ['hover', 'focus', 'active'],
    },
  ],
  theme: {
    extend: {
      colors: {
        'custom-gray': '#F9FAFB',
      },
      borderRadius: {
        '8': '8px',
      },
      fontFamily: {
        sans: [
          "Inter",
          "ui-sans-serif",
          "system-ui",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Helvetica Neue",
          "Arial",
          "Noto Sans",
          "sans-serif",
        ],
        inter: ["Inter", "sans-serif"],
        platform: ["Platform", "Inter", "sans-serif"],
      },
      keyframes: {
        "fade-in-down": {
          "0%": {
            opacity: "0",
            transform: "translateY(-10px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "fade-in": {
          "0%": {
            opacity: "0",
          },
          "100%": {
            opacity: "1",
          },
        },
        "scale-in": {
          "0%": {
            opacity: "0",
            transform: "scale(0.95)",
          },
          "100%": {
            opacity: "1",
            transform: "scale(1)",
          },
        },
      },
      animation: {
        "fade-in-down": "fade-in-down 0.2s ease-out",
        "fade-in": "fade-in 0.3s ease-out forwards",
        "scale-in": "scale-in 0.2s ease-out forwards",
      },
    },
  },
  plugins: [],
};
