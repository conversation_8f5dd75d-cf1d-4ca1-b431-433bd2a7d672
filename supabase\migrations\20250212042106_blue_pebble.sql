-- Add default values to blog_posts table
ALTER TABLE blog_posts
  ALTER COLUMN title SET DEFAULT '',
  ALTER COLUMN content SET DEFAULT '',
  ALTER COLUMN status SET DEFAULT 'draft',
  ALTER COLUMN excerpt SET DEFAULT NULL,
  ALTER COLUMN featured_image SET DEFAULT NULL;

-- Add trigger to ensure title and content are not empty strings
CREATE OR REPLACE FUNCTION validate_blog_post()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Validate title
  IF length(trim(NEW.title)) = 0 THEN
    RAISE EXCEPTION 'Title cannot be empty';
  END IF;

  -- Validate content
  IF length(trim(NEW.content)) = 0 THEN
    RAISE EXCEPTION 'Content cannot be empty';
  END IF;

  -- Validate status
  IF NEW.status NOT IN ('draft', 'published', 'archived') THEN
    RAISE EXCEPTION 'Invalid status value';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for validation
DROP TRIGGER IF EXISTS validate_blog_post_trigger ON blog_posts;
CREATE TRIGGER validate_blog_post_trigger
  BEFORE INSERT OR UPDATE ON blog_posts
  FOR EACH ROW
  EXECUTE FUNCTION validate_blog_post();