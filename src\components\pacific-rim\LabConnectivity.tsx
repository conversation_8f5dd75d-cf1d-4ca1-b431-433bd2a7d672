import { Activity, Globe, Database } from 'lucide-react';

interface LabConnectivityProps {
  stats: Array<{
    number: string;
    label: string;
  }>;
  partners: string[];
}

export const LabConnectivity = ({ stats, partners }: LabConnectivityProps) => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl font-bold mb-6">Laboratory Connectivity & Logistics</h2>
            <p className="text-gray-600 mb-6">
              Since 2016, iConnect Consulting has been providing its cloud-based ETOR (Electronic Test Orders and Results) LWP platform to public health laboratories nationwide, offering an intuitive, real-time interface for test ordering, progress tracking, and result viewing.
            </p>
            <div className="grid grid-cols-3 gap-6 mb-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600 mb-1">{stat.number}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
            <div className="space-y-4">
              <h3 className="text-xl font-semibold">Current Pacific Rim Partners</h3>
              <div className="flex flex-wrap gap-3">
                {partners.map((partner, index) => (
                  <span key={index} className="px-4 py-2 bg-purple-50 text-purple-600 rounded-full font-medium">
                    {partner}
                  </span>
                ))}
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-br from-orange-50 to-purple-50 p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-semibold mb-6">Integration Features</h3>
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-white rounded-lg shadow-md">
                  <Activity className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h4 className="font-semibold">Predictive Dashboards</h4>
                  <p className="text-gray-600">Real-time analytics and forecasting visualization</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="p-3 bg-white rounded-lg shadow-md">
                  <Globe className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h4 className="font-semibold">Geospatial Data</h4>
                  <p className="text-gray-600">Advanced geographical data analysis and mapping</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="p-3 bg-white rounded-lg shadow-md">
                  <Database className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h4 className="font-semibold">SOAR Integration</h4>
                  <p className="text-gray-600">Seamless connection with SOAR platform for comprehensive analysis</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
