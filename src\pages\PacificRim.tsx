import { Hero, Overview, SwabSeqPlatform, LabConnectivity, Timeline, Contact } from '../components/pacific-rim';

export default function PacificRim() {
  const timeline = [
    { days: 30, title: "Initial Setup", items: [
      "Memorandum of Understanding (MOU) reviewed and signed",
      "Process flow developed",
      "Data Use Agreement (DUA) verified",
      "Public Health Laboratories Identified"
    ]},
    { days: 60, title: "Implementation", items: [
      "Labs briefed and trained",
      "Proof of Concept begins",
      "DUA confirmed",
      "Model finalized",
      "Evaluation structure set"
    ]},
    { days: 90, title: "Evaluation", items: [
      "Evaluation begins",
      "Feedback monitored",
      "Scaling discussed",
      "Revisions made",
      "Go/No Go determined",
      "SOAR API completed"
    ]}
  ];

  const partnerships = {
    institutions: ['UCLA', 'Lumina Labs', 'iConnect Consulting'],
    regions: ['Pacific Rim Consortium', 'California Public Health Labs'],
    networkSize: '100+ academic and private entities, including STLT health departments'
  };

  const swabseqFeatures = [
    {
      title: 'Detection Capabilities',
      items: [
        'SARS-CoV-2',
        'Human Coronaviruses',
        'Human Metapneumovirus',
        'Parainfluenza Virus 1-4',
        'Rhinovirus/Enterovirus',
        'RSV',
        'Adenovirus',
        'Influenza A/B',
        'Influenza C',
        'HSV',
        'Coccidioides'
      ]
    },
    {
      title: 'Technical Features',
      items: [
        'Meta-genomics based untargeted NGS diagnostic',
        'Nucleic acid extraction',
        'NGS library preparation',
        'Bioinformatics pipeline',
        'Quality control checks',
        'Internal controls validation',
        'Pathogen matching'
      ]
    },
    {
      title: 'Platform Benefits',
      items: [
        'Large scale automation',
        'High throughput sequencing',
        'Scalable low-cost approach',
        'Multiple sample type support',
        'Electronic result delivery',
        'Validated performance metrics'
      ]
    }
  ];

  const labConnectivity = {
    stats: [
      { number: '25', label: 'State PHLs' },
      { number: '7.5M', label: 'Orders Processed' },
      { number: '7,800+', label: 'Submitting Facilities' }
    ],
    partners: ['CDPH', 'HI DOH', 'WA DOH', 'NV DOH']
  };

  const investigator = {
    name: 'Holli Dowless Kozar',
    email: '<EMAIL>'
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-orange-50">
      <Hero />
      <Overview partnerships={partnerships} />
      <SwabSeqPlatform features={swabseqFeatures} />
      <LabConnectivity {...labConnectivity} />
      <Timeline phases={timeline} />
      <Contact investigator={investigator} />
    </div>
  );
}