import React, { useState, useEffect } from 'react';
import { MessageSquare, Search, Filter, Clock, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';
import { Link } from 'react-router-dom';

interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  user_id: string;
  contact_name: string;
  contact_phone: string;
  resolution_comment?: string;
  resolved_by?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  user: {
    email: string;
    full_name: string;
    phone: string;
  };
}

export default function Support() {
  const user = useAuthStore((state) => state.user);
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'technical',
    priority: 'medium' as const,
    contact_name: '',
    contact_phone: ''
  });

  useEffect(() => {
    if (user) {
      fetchUserProfile();
      fetchTickets();
    }
  }, [user]);

  const fetchUserProfile = async () => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('full_name, phone')
        .eq('id', user?.id)
        .single();

      if (error) throw error;

      if (profile) {
        setFormData(prev => ({
          ...prev,
          contact_name: profile.full_name || '',
          contact_phone: profile.phone || ''
        }));
      }
    } catch (err) {
      console.error('Error fetching user profile:', err);
    }
  };

  const fetchTickets = async () => {
    try {
      const { data, error } = await supabase
        .from('support_tickets')
        .select(`
          *,
          user:profiles!support_tickets_user_id_fkey(
            email,
            full_name,
            phone
          )
        `)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTickets(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.contact_name.trim()) {
      setError('Please provide your name');
      return;
    }

    if (!formData.contact_phone.trim()) {
      setError('Please provide your phone number');
      return;
    }

    try {
      const { error } = await supabase
        .from('support_tickets')
        .insert([{
          ...formData,
          user_id: user?.id,
          status: 'open'
        }]);

      if (error) throw error;

      // Prepare email data
      const emailData = {
        name: formData.contact_name,
        email: user?.email,
        phone: formData.contact_phone,
        company: '', // Add company if available in your formData
        message: `
          <strong>Title:</strong> ${formData.title}<br/>
          <strong>Description:</strong> ${formData.description}<br/>
          <strong>Category:</strong> ${formData.category}<br/>
          <strong>Priority:</strong> ${formData.priority}
        `
      };

      // Send email to admin
      await supabase.functions.invoke('send-contact-email', {
        body: {
          to: '<EMAIL>',
          subject: `New Support Ticket from ${emailData.name}`,
          html: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #2c3e50; max-width: 600px; margin: auto;">
  <h2 style="color: #2c3e50; border-bottom: 2px solid #ccc; padding-bottom: 5px;">New Support Ticket - IRS Website</h2>

  <table cellpadding="10" cellspacing="0" border="0" style="width: 100%; border-collapse: collapse;">
    <tbody>
      <tr>
        <td style="font-weight: bold; width: 120px;">Name:</td>
        <td>${emailData.name}</td>
      </tr>
      <tr>
        <td style="font-weight: bold;">Email:</td>
        <td><a href="mailto:${emailData.email}" style="color: #3498db;">${emailData.email}</a></td>
      </tr>
      ${emailData.phone ? `
      <tr>
        <td style="font-weight: bold;">Phone:</td>
        <td><a href="tel:${emailData.phone}" style="color: #3498db;">${emailData.phone}</a></td>
      </tr>` : ''}
      ${emailData.company ? `
      <tr>
        <td style="font-weight: bold;">Company:</td>
        <td>${emailData.company}</td>
      </tr>` : ''}
    </tbody>
  </table>

  <p style="margin-top: 20px; font-weight: bold;">Message:</p>
  <div style="background-color: #f4f6f8; padding: 15px; border-left: 4px solid #2980b9; white-space: pre-wrap;">
    ${emailData.message}
  </div>
</div>

          `
        }
      });

      // Send confirmation email to user
      await supabase.functions.invoke('send-contact-email', {
        body: {
          to: emailData.email,
          subject: 'Your Support Ticket has been received',
          html: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
              <h2 style="color: #2c3e50;">Thank you for contacting IRS Support</h2>
              <p>Dear ${emailData.name},</p>
              <p>We have received your support ticket and our team will get back to you as soon as possible.</p>
              <p><strong>Ticket Details:</strong></p>
              <ul>
                <li><strong>Title:</strong> ${formData.title}</li>
                <li><strong>Description:</strong> ${formData.description}</li>
                <li><strong>Category:</strong> ${formData.category}</li>
                <li><strong>Priority:</strong> ${formData.priority}</li>
              </ul>
              <p>Thank you,<br/>IRS Support Team</p>
            </div>
          `
        }
      });

      setFormData({
        title: '',
        description: '',
        category: 'technical',
        priority: 'medium',
        contact_name: formData.contact_name,
        contact_phone: formData.contact_phone
      });
      setShowForm(false);
      fetchTickets();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'resolved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'closed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
        <div className="max-w-md w-full">
          {/* Main card */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 text-center relative overflow-hidden">
            {/* Subtle decorative element */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-gray-50 rounded-full -translate-y-12 translate-x-12"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 bg-gray-100 rounded-full translate-y-8 -translate-x-8"></div>
            
            {/* Icon */}
            <div className="relative mb-8">
              <div className="w-16 h-16 bg-gray-100 rounded-xl mx-auto flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
            </div>

            {/* Content */}
            <div className="relative z-10 space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-3">
                  Authentication Required
                </h2>
                <div className="w-12 h-0.5 bg-gray-300 mx-auto mb-4"></div>
                <p className="text-gray-600 leading-relaxed">
                  Please sign in to access your support tickets and create new requests
                </p>
              </div>

              {/* Features */}
              <div className="bg-gray-50 rounded-xl p-6 text-left">
                <div className="space-y-3">
                  <div className="flex items-center text-sm">
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-3 flex-shrink-0"></div>
                    <span className="text-gray-700">Track ticket status</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-3 flex-shrink-0"></div>
                    <span className="text-gray-700">Priority support queue</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-3 flex-shrink-0"></div>
                    <span className="text-gray-700">Direct team communication</span>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="space-y-4">
                <Link
                  to="/login"
                  className="group w-full inline-flex items-center justify-center bg-gray-900 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:bg-gray-800"
                >
                  <svg className="w-4 h-4 mr-2 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                  Sign In
                </Link>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-xs">
                    <span className="px-3 bg-white text-gray-500">or</span>
                  </div>
                </div>

                <Link
                  to="/signup"
                  className="w-full inline-flex items-center justify-center border border-gray-300 text-gray-700 px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:border-gray-400 hover:bg-gray-50"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                  Create Account
                </Link>
              </div>

              {/* Help text */}
              <div className="pt-4 border-t border-gray-100">
                <p className="text-xs text-gray-500">
                  Need immediate help?{' '}
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-gray-700 hover:text-gray-900 font-medium underline decoration-1 underline-offset-2"
                  >
                    Email us directly
                  </a>
                </p>
              </div>
            </div>
          </div>

          {/* Minimal footer */}
          <div className="mt-8 text-center">
            <div className="flex justify-center space-x-1">
              <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-700 via-blue-500 to-indigo-900 text-white py-24 relative rounded-lg">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Small decorative dots */}
          <div className="absolute top-32 right-20 w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-32 w-3 h-3 bg-blue-200 rounded-full opacity-60 animate-pulse delay-75"></div>
          <div className="absolute bottom-32 right-24 w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-150"></div>
          <div className="absolute bottom-20 right-40 w-1 h-1 bg-blue-100 rounded-full animate-pulse delay-300"></div>

          {/* Geometric shapes */}
          <div className="absolute top-16 right-16 w-12 h-12 border border-white/20 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-24 right-64 w-8 h-8 border border-white/10 rotate-12 animate-bounce"></div>

          {/* Additional decorative lines */}
          <div className="absolute top-1/3 right-0 w-px h-20 bg-gradient-to-b from-transparent via-white/20 to-transparent"></div>
          <div className="absolute top-2/3 right-12 w-px h-16 bg-gradient-to-b from-transparent via-white/15 to-transparent"></div>
        </div>

        {/* Creative illustration on the right - Support themed */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block overflow-hidden">
          <div className="relative h-full flex items-center justify-center">
            <div className="relative scale-150">
              {/* Floating support icons */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float">
                <div className="p-3 flex items-center justify-center h-full">
                  <MessageSquare className="h-8 w-8 text-white" />
                </div>
              </div>

              <div className="absolute -top-6 right-10 w-18 h-22 bg-white/15 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed">
                <div className="p-2.5 flex items-center justify-center h-full">
                  <Search className="h-7 w-7 text-white" />
                </div>
              </div>

              {/* Central support hub */}
              <div className="w-40 h-40 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center relative animate-pulse-slow">
                <div className="w-28 h-28 bg-white/30 rounded-full flex items-center justify-center">
                  <div className="w-16 h-16 bg-white/40 rounded-full flex items-center justify-center">
                    <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold text-xl">?</span>
                    </div>
                  </div>
                </div>

                {/* Orbiting elements */}
                <div className="absolute inset-0 animate-spin-slow">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-300 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -right-3 transform -translate-y-1/2 w-4 h-4 bg-blue-200 rounded-full opacity-60"></div>
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-400 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -left-3 transform -translate-y-1/2 w-4 h-4 bg-blue-100 rounded-full opacity-60"></div>
                </div>
              </div>

              {/* Floating ticket status icons */}
              <div className="absolute -bottom-8 -left-8 w-16 h-16 bg-white/15 rounded-lg backdrop-blur-sm flex items-center justify-center transform -rotate-12 animate-bounce-slow">
                <CheckCircle className="h-8 w-8 text-green-300" />
              </div>

              <div className="absolute bottom-3 right-6 w-14 h-14 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center animate-float">
                <Clock className="h-7 w-7 text-yellow-300" />
              </div>

              <div className="absolute top-10 -left-10 w-18 h-18 bg-white/10 rounded-xl backdrop-blur-sm flex items-center justify-center transform rotate-45 animate-pulse">
                <AlertTriangle className="h-8 w-8 text-orange-300 transform -rotate-45" />
              </div>

              {/* Support chat bubbles */}
              <div className="absolute -bottom-12 right-2 w-12 h-8 bg-white/15 rounded-lg backdrop-blur-sm flex items-center justify-center animate-bounce delay-500">
                <div className="w-6 h-1 bg-white/60 rounded"></div>
              </div>
              
              <div className="absolute top-8 left-4 w-10 h-6 bg-white/20 rounded-lg backdrop-blur-sm animate-pulse delay-1000"></div>
            </div>

            {/* Support connection lines */}
            <div className="absolute inset-0 pointer-events-none scale-125">
              <svg className="w-full h-full opacity-20" viewBox="0 0 400 400">
                <defs>
                  <linearGradient id="supportGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="rgba(255,255,255,0)" />
                    <stop offset="50%" stopColor="rgba(255,255,255,0.3)" />
                    <stop offset="100%" stopColor="rgba(255,255,255,0)" />
                  </linearGradient>
                </defs>
                <path
                  d="M50,200 Q200,100 350,200 Q200,300 50,200"
                  fill="none"
                  stroke="url(#supportGradient1)"
                  strokeWidth="3"
                  className="animate-pulse"
                />
                <path
                  d="M100,150 Q200,50 300,150"
                  fill="none"
                  stroke="url(#supportGradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-500"
                />
                <path
                  d="M100,250 Q200,350 300,250"
                  fill="none"
                  stroke="url(#supportGradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-1000"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Support Center
              <span className="block text-3xl font-normal mt-2 text-blue-200">
                We're Here to Help
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Get help with your questions and issues. Our support team is here to assist you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={() => setShowForm(true)}
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-all duration-300 hover:transform hover:scale-105 shadow-lg"
              >
                Create New Ticket
              </button>
              <a
                href="mailto:<EMAIL>"
                className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white/10 transition-all duration-300 hover:transform hover:scale-105 text-center"
              >
                Email Support
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Add custom animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(12deg); }
          50% { transform: translateY(-10px) rotate(12deg); }
        }
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0px) rotate(-6deg); }
          50% { transform: translateY(-8px) rotate(-6deg); }
        }
        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        @keyframes bounce-slow {
          0%, 100% { transform: translateY(0px) rotate(-12deg); }
          50% { transform: translateY(-5px) rotate(-12deg); }
        }
        @keyframes pulse-slow {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }
        .animate-float { animation: float 3s ease-in-out infinite; }
        .animate-float-delayed { animation: float-delayed 3s ease-in-out infinite 1s; }
        .animate-spin-slow { animation: spin-slow 20s linear infinite; }
        .animate-bounce-slow { animation: bounce-slow 2s ease-in-out infinite; }
        .animate-pulse-slow { animation: pulse-slow 4s ease-in-out infinite; }
      `}</style>

      {/* Content Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {error && (
            <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-8">
              {error}
            </div>
          )}

          {showForm && (
            <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
              <h2 className="text-2xl font-bold mb-6">Create Support Ticket</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Your Name
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.contact_name}
                      onChange={(e) => setFormData({ ...formData, contact_name: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Full Name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      required
                      value={formData.contact_phone}
                      onChange={(e) => setFormData({ ...formData, contact_phone: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Phone Number"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Brief description of your issue"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    required
                    rows={4}
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Detailed description of your issue"
                  />
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="technical">Technical Issue</option>
                      <option value="billing">Billing</option>
                      <option value="account">Account</option>
                      <option value="feature">Feature Request</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Priority
                    </label>
                    <select
                      value={formData.priority}
                      onChange={(e) => setFormData({ ...formData, priority: e.target.value as 'low' | 'medium' | 'high' | 'urgent' })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>
                </div>

                <div className="flex justify-end space-x-4">
                  <button
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="text-gray-600 hover:text-gray-900"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700"
                  >
                    Submit Ticket
                  </button>
                </div>
              </form>
            </div>
          )}

          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="p-6 border-b">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Your Support Tickets</h2>
                {/* <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search tickets..."
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <button className="flex items-center text-gray-600 hover:text-gray-900">
                    <Filter className="h-5 w-5 mr-2" />
                    Filter
                  </button>
                </div> */}
              </div>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            ) : tickets.length > 0 ? (
              <div className="divide-y">
                {tickets.map((ticket) => (
                  <div key={ticket.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        {getStatusIcon(ticket.status)}
                        <div>
                          <h3 className="font-semibold text-lg mb-1">{ticket.title}</h3>
                          <p className="text-gray-600 mb-2">{ticket.description}</p>
                          <div className="flex items-center space-x-4 text-sm">
                            <span className={`px-2 py-1 rounded-full ${getPriorityColor(ticket.priority)}`}>
                              {ticket.priority}
                            </span>
                            <span className="text-gray-500">
                              {new Date(ticket.created_at).toLocaleDateString()}
                            </span>
                            <span className="text-gray-500">
                              Contact: {ticket.contact_name} ({ticket.contact_phone})
                            </span>
                            <span className="text-gray-500">
                              #{ticket.id.split('-')[0]}
                            </span>
                          </div>
                          {ticket.resolution_comment && (
                            <div className="mt-4 bg-gray-50 p-4 rounded-lg">
                              <p className="text-sm font-medium text-gray-700">Resolution:</p>
                              <p className="text-gray-600">{ticket.resolution_comment}</p>
                            </div>
                          )}
                        </div>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${ticket.status === 'resolved'
                          ? 'bg-green-100 text-green-800'
                          : ticket.status === 'closed'
                            ? 'bg-red-100 text-red-800'
                            : ticket.status === 'in_progress'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-blue-100 text-blue-800'
                        }`}>
                        {ticket.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-8 text-center">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No support tickets</h3>
                <p className="text-gray-600">
                  You haven't created any support tickets yet.
                </p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
}