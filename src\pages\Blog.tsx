import React, { useState, useEffect } from 'react';
import { Calendar, User, Clock, ArrowRight, Search, Timer, ArrowUpRight, FileText, Edit3, BookOpen } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { supabase } from '../lib/supabase';
import { Link } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import defaultImage from '../assets/images/default-image.jpg';

interface BlogPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  status: string;
  featured_image: string;
  created_at: string;
  comments: BlogComment[];
}

interface BlogComment {
  id: string;
  content: string;
  user_id: string;
  created_at: string;
  status: string;  // Added status field
  user: {
    email: string;
    full_name: string;
  };
}

export default function Blog() {
  const user = useAuthStore((state) => state.user);
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newComment, setNewComment] = useState('');
  const [selectedPost, setSelectedPost] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const fetchPosts = async () => {
    try {
      const { data: posts, error: postsError } = await supabase
        .from('blog_posts')
        .select(`*`)
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      if (postsError) throw postsError;
      return posts || [];
    } catch (err: any) {
      setError(err.message);
      return [];
    }
  };

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        const posts = await fetchPosts();
        if (isMounted) {
          setPosts(posts);
          setFilteredPosts(posts);
        }
      } catch (err: any) {
        if (isMounted) setError(err.message);
      } finally {
        if (isMounted) setLoading(false);
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, []);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredPosts(posts);
    } else {
      const filtered = posts.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredPosts(filtered);
    }
  }, [searchQuery, posts]);

  // if (loading) return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  // if (error) return <div className="min-h-screen flex items-center justify-center text-red-500">Error: {error}</div>;

  return (
    <div className="">
      {/* Hero Section */}
      <section className="text-white py-24 relative rounded-lg" style={{ background: 'radial-gradient(50% 55% at 0% 0%, #10B981 0%, #059669 100%)' }}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Medium circles */}
          <div className="absolute top-1/2 -left-20 w-32 h-32 bg-white/4 rounded-full"></div>
          <div className="absolute bottom-10 left-1/4 w-24 h-24 bg-white/3 rounded-full"></div>

          {/* Small floating elements */}
          <div className="absolute top-16 left-1/3 w-4 h-4 bg-white/20 rounded-full animate-pulse"></div>
          <div className="absolute bottom-32 right-1/4 w-3 h-3 bg-white/30 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute top-1/3 right-1/3 w-2 h-2 bg-white/25 rounded-full animate-pulse delay-500"></div>
        </div>

        {/* Creative illustration on the right - Made bigger */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block overflow-hidden">
          {/* Main illustration container */}
          <div className="relative h-full flex items-center justify-center">
            {/* Digital blog visualization - Scaled up */}
            <div className="relative scale-150">
              {/* Floating blog articles - Made bigger */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float border border-white/10">
                <div className="p-3">
                  <div className="w-full h-2.5 bg-blue-300/60 rounded mb-1.5"></div>
                  <div className="w-3/4 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-1/2 h-1.5 bg-blue-200/50 rounded"></div>
                  {/* Mini edit icon */}
                  <div className="absolute top-1 right-1 w-3 h-3 bg-blue-400/40 rounded flex items-center justify-center">
                    <Edit3 className="w-2 h-2 text-white/60" />
                  </div>
                </div>
              </div>

              <div className="absolute -top-6 -left-8 w-18 h-22 bg-white/8 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed border border-white/10">
                <div className="p-2.5">
                  <div className="w-full h-2 bg-blue-300/60 rounded mb-1"></div>
                  <div className="w-2/3 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-3/4 h-1.5 bg-blue-200/50 rounded"></div>
                  {/* Mini calendar icon */}
                  <div className="absolute top-1 right-1 w-3 h-3 bg-blue-400/40 rounded flex items-center justify-center">
                    <Calendar className="w-2 h-2 text-white/60" />
                  </div>
                </div>
              </div>

              {/* Central blog icon - Enhanced */}
              <div className="w-32 h-40 bg-white/15 rounded-xl backdrop-blur-sm border-2 border-white/30 flex flex-col items-center justify-center transform hover:scale-105 transition-all duration-300 hover:bg-white/20 hover:border-white/40">
                <div className="relative">
                  <BookOpen className="h-16 w-16 text-white/90 drop-shadow-lg" />
                  {/* Glowing effect */}
                  <div className="absolute inset-0 h-16 w-16 bg-blue-400/20 rounded-full blur-xl"></div>
                </div>

              </div>

              {/* More floating elements - Enhanced */}
              <div className="absolute -bottom-8 -right-4 w-16 h-20 bg-white/12 rounded-lg backdrop-blur-sm transform rotate-6 animate-float border border-white/10">
                <div className="p-2">
                  <div className="w-full h-2 bg-blue-300/60 rounded mb-1"></div>
                  <div className="w-3/4 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded"></div>
                  {/* Mini user icon */}
                  <div className="absolute bottom-1 right-1 w-3 h-3 bg-blue-400/40 rounded flex items-center justify-center">
                    <User className="w-2 h-2 text-white/60" />
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-4 -left-6 w-14 h-18 bg-white/10 rounded-lg backdrop-blur-sm transform -rotate-12 animate-float-delayed border border-white/10">
                <div className="p-2">
                  <div className="w-full h-1.5 bg-blue-300/60 rounded mb-1"></div>
                  <div className="w-2/3 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded"></div>
                  {/* Mini clock icon */}
                  <div className="absolute bottom-1 right-1 w-3 h-3 bg-blue-400/40 rounded flex items-center justify-center">
                    <Clock className="w-2 h-2 text-white/60" />
                  </div>
                </div>
              </div>

              {/* Connecting lines/paths - Made more visible */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-24 h-0.5 bg-white/20 transform rotate-45 absolute"></div>
                <div className="w-20 h-0.5 bg-white/15 transform -rotate-45 absolute top-4"></div>
                <div className="w-16 h-0.5 bg-white/20 transform rotate-12 absolute -top-4 left-2"></div>
              </div>

              {/* Orbiting elements - Made bigger */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-48 h-48 border border-white/10 rounded-full animate-spin-slow">
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white/30 rounded-full"></div>
                  <div className="absolute top-1/2 -right-2 transform -translate-y-1/2 w-3 h-3 bg-white/25 rounded-full"></div>
                  <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white/20 rounded-full"></div>
                  <div className="absolute top-1/2 -left-2 transform -translate-y-1/2 w-3 h-3 bg-white/25 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content pushed to the left */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Blog
              <span className="block text-3xl font-normal mt-2 text-blue-200">
                Insights & Expertise Hub
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Insights and updates from public health emergency response experts to keep you informed and prepared
            </p>

            {/* Search functionality integrated into banner */}
            <div className="relative w-full max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3.5 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Post - Only show when not searching */}
      {filteredPosts.length > 0 && searchQuery.trim() === '' && (
        <section className="py-12 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl font-bold mb-8">Featured Article</h2>
            <div className="bg-white rounded-xl shadow-lg overflow-hidden flex flex-col md:flex-row">
              {filteredPosts[0].featured_image ? (
                <div className="md:w-1/2 h-96 md:h-auto">
                  <img
                    src={filteredPosts[0].featured_image}
                    alt={filteredPosts[0].title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.src = defaultImage;
                    }}
                  />
                </div>
              ) : (
                <img
                  src={defaultImage}
                  alt="Default Case Study"
                  className="w-full h-full object-cover"
                />
              )}
              <div className="p-8 md:w-1/2 flex flex-col justify-center">
                <div className="flex items-center mb-4">
                  <span className="flex items-center gap-2 text-sm bg-blue-500 font-semibold px-2 py-1 rounded-full text-white">
                    <Timer className='w-5 h-5' />
                    {new Date(filteredPosts[0].created_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
                <h3 className="text-3xl font-bold mb-4">{filteredPosts[0].title}</h3>
                <p className="text-gray-600 text-lg mb-6">
                  {filteredPosts[0].excerpt || filteredPosts[0].content.substring(0, 200) + '...'}
                </p>
                <div className="mt-auto">
                  <button
                    onClick={() => navigate(`/blog/${filteredPosts[0].id}`)}
                    className="flex items-center gap-1 bg-blue-400 hover:bg-blue-300 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
                  >
                    Read Full Article
                    <ArrowUpRight className="h-5 w-5 ml-2" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Recent Posts - Show all filtered posts when searching */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold mb-8">
            {searchQuery.trim() ? 'Search Results' : 'Recent Articles'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.length > 0 ? (
              filteredPosts.map((post) => (
                <div key={post.id} className="relative bg-white rounded-lg shadow-md overflow-hidden flex flex-col h-full">
                  {post.featured_image ? (
                    <img
                      src={post.featured_image}
                      alt={post.title}
                      className="w-full h-48 object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  ) : (
                    <img
                      src={defaultImage}
                      alt="Default Case Study"
                      className="w-full h-48 object-cover"
                    />
                  )}
                  <span className="w-auto flex items-center gap-2 text-sm bg-blue-500 font-semibold px-2 py-1 rounded-full text-white absolute top-1 right-1">
                    <Timer className='w-5 h-5' />
                    {new Date(filteredPosts[0].created_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                  <div className="p-4 flex-grow">
                    <h3 className="text-xl font-bold mb-2 line-clamp-2">{post.title}</h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {post.excerpt || post.content.substring(0, 150) + '...'}
                    </p>
                  </div>
                  <div className="p-4 border-t">
                    <button
                      onClick={() => navigate(`/blog/${post.id}`)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center justify-center transition-colors duration-200"
                    >
                      Read Full Article
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-500 text-lg">
                  {searchQuery ? 'No articles match your search.' : 'No articles found.'}
                </p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
}