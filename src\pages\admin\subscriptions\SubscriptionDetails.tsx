import React, { useState } from 'react';
// Add CreditCard icon
import { RefreshCw, AlertCircle, ExternalLink, CreditCard } from 'lucide-react';

// --- Ensure interfaces match SubscriptionsManagement.tsx ---
interface Subscription {
    id: string;
    user_id: string;
    product_id: string;
    status: string;
    period: 'monthly' | 'yearly';
    current_period_start: string;
    current_period_end: string;
    cancel_at_period_end: boolean;
    created_at: string;
    quantity: number;
    deleted: boolean;
    deleted_at?: string | null;
    ref?: string | null;
    stripe_subscription_id?: string | null;
    // User details (fetched via join)
    user_email?: string;
    user_full_name?: string | null; // Add this field
}

interface SubscriptionItem {
    id: string; // Your DB item ID
    product_id: string; // Your DB product ID
    stripe_price_id: string; // Stripe Price ID from your DB
    quantity: number;
    status: string;
    product_name?: string;
    stripe_subscription_item_id?: string; // Stripe Item ID from your DB (if stored)
    created_at?: string; // If you fetch this from your DB
}

interface StripeDetails {
    // Existing
    payment_method_details?: string;
    recent_invoices?: Array<{
        id: string;
        created: number;
        total: number;
        status: string;
        invoice_pdf?: string;
        due_date?: number | null;
    }>;
    // New fields
    status?: string;
    current_period_start?: number;
    current_period_end?: number;
    cancel_at_period_end?: boolean;
    canceled_at?: number | null;
    created?: number;
    customer_email?: string | null;
    schedule?: any | null;
    pending_update?: any | null;
    stripe_items?: Array<{
        id: string; // Stripe Item ID
        stripe_price_id: string;
        product_name: string;
        product_id: string | null; // Stripe Product ID
        quantity: number;
    }>;
    latest_invoice_id?: string | null;
}


interface SubscriptionDetailsProps {
    subscription: Subscription | null;
    items: SubscriptionItem[]; // Items from your DB
    stripeDetails: StripeDetails | null; // Enhanced details from Stripe via Edge Function
    isLoading: boolean;
    error: string | null;
    onClose: () => void;
}

const SubscriptionDetails: React.FC<SubscriptionDetailsProps> = ({
    subscription,
    items, // These are from your DB query
    stripeDetails, // These are from the Edge Function
    isLoading,
    error,
    onClose
}) => {
    if (!subscription) {
        return null;
    }

    // Helper to format currency (example)
    const formatCurrency = (amount: number) => {
        // Assumes amount is in cents
        return (amount / 100).toLocaleString('en-US', { style: 'currency', currency: 'USD' });
    }

    // Helper to format timestamp
    const formatDate = (timestamp: number | string | null | undefined) => {
        if (!timestamp) return 'N/A'; // This line causes "N/A" if timestamp is null/undefined
        // Check if timestamp is in seconds (from Stripe) or needs parsing (from DB)
        const date = typeof timestamp === 'number' ? new Date(timestamp * 1000) : new Date(timestamp);
        return date.toLocaleDateString();
    }
    const formatDateTime = (timestamp: number | string | null | undefined) => {
        if (!timestamp) return 'N/A';
        const date = typeof timestamp === 'number' ? new Date(timestamp * 1000) : new Date(timestamp);
        return date.toLocaleString(); // Includes time
    }


    // Helper function to parse payment method details and format output
    const formatPaymentMethod = (details: string | null | undefined) => {
        if (!details) return 'N/A';

        const parts = details.toLowerCase().split(' ending in ');
        if (parts.length !== 2) return details; // Return original if format is unexpected

        const brand = parts[0].trim();
        const last4 = parts[1].trim();

        // Basic icon mapping (extend as needed)
        let BrandIcon = CreditCard; // Default icon

        return (
            <span className="flex items-center">
                <BrandIcon className="h-5 w-5 mr-2 text-gray-600" />
                <span className="font-mono tracking-wider">
                    <span className="text-gray-400">**** **** ****</span> {last4}
                </span>
                <span className="ml-2 capitalize text-gray-700">({brand})</span>
            </span>
        );
    };

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex justify-center items-center z-50">
            <div className="relative p-8 border w-full max-w-4xl shadow-lg rounded-md bg-white max-h-[90vh] overflow-y-auto"> {/* Increased max-width */}
                {/* Header */}
                <div className="flex justify-between items-center mb-6 border-b pb-4">
                    <h3 className="text-2xl font-semibold text-gray-800">Subscription Details</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center"
                    >
                        {/* Close Icon SVG */}
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>
                    </button>
                </div>

                {/* Loading State */}
                {isLoading && (
                    <div className="flex justify-center items-center py-10">
                        <RefreshCw className="h-6 w-6 text-blue-600 animate-spin mr-2" />
                        <span>Loading details...</span>
                    </div>
                )}

                {/* Error State */}
                {error && !isLoading && (
                    <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-lg flex items-center">
                        <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
                        <span>{error}</span>
                    </div>
                )}

                {/* Main Content Area (only show if not loading initial subscription) */}
                {!isLoading && subscription && (
                    <div className="space-y-8">
                        <section>
                            <h4 className="text-lg font-semibold text-gray-700 mb-3">Core Information</h4>
                            {/* Adjusted label/value styling */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4 text-sm">
                                {subscription.ref && <div><span className="font-medium text-gray-500">Reference (DB): </span>
                                    <span className="text-gray-900 font-medium uppercase">{subscription.ref}</span>
                                </div>}
                                <div>
                                    <span className="font-medium text-gray-500">User: </span>
                                    <span className="text-gray-900 font-medium ml-1">
                                        {subscription.user_full_name || 'N/A'}
                                        {subscription.user_full_name && subscription.user_email ? ' (' : ''}
                                        {subscription.user_email || (!subscription.user_full_name ? stripeDetails?.customer_email || 'N/A' : '')}
                                        {subscription.user_full_name && subscription.user_email ? ')' : ''}
                                    </span>
                                </div>
                                {/* Use Stripe status if available, fallback to DB status */}
                                <div>
                                    <span className="font-medium text-gray-500">Status: </span>
                                    <span className={`font-bold capitalize ${(stripeDetails?.status === 'active' || (!stripeDetails && subscription.status === 'active')) ? 'text-green-600' :
                                        (stripeDetails?.status === 'canceled' || (!stripeDetails && subscription.status === 'canceled')) ? 'text-red-600' :
                                            'text-yellow-600' // Covers trialing, past_due, incomplete etc.
                                        }`}>{stripeDetails?.status || subscription.status}</span>
                                </div>
                                <div>
                                    <span className="font-medium text-gray-500">Period (DB): </span>
                                    <span className="text-gray-900 font-medium">{subscription.period}</span>
                                </div>
                                <div>
                                    <span className="font-medium text-gray-500">Quantity (DB): </span>
                                    <span className="text-gray-900 font-medium">
                                        {subscription.quantity}
                                    </span>
                                </div>
                                {/* Use Stripe dates if available */}
                                <div>
                                    <span className="font-medium text-gray-500">Current Period Start: </span>
                                    <span className="text-gray-900 font-medium">{formatDate(stripeDetails?.current_period_start) || formatDate(subscription.current_period_start)}</span>
                                </div>
                                <div>
                                    <span className="font-medium text-gray-500">Current Period End: </span>
                                    <span className="text-gray-900 font-medium">{formatDate(stripeDetails?.current_period_end) || formatDate(subscription.current_period_end)}</span>
                                </div>
                                <div>
                                    <span className="font-medium text-gray-500">Created At: </span>
                                    <span className="text-gray-900 font-medium">{formatDateTime(stripeDetails?.created) || formatDateTime(subscription.created_at)}</span>
                                </div>
                                <div>
                                    <span className="font-medium text-gray-500">Cancel at Period End: </span>
                                    <span className="text-gray-900 font-medium">
                                        {(stripeDetails?.cancel_at_period_end ?? subscription.cancel_at_period_end) ? 'Yes' : 'No'}
                                    </span>
                                </div>
                                {stripeDetails?.canceled_at && <div>
                                    <span className="font-medium text-gray-500">Canceled At: </span>
                                    <span className="text-gray-900 font-medium">{formatDateTime(stripeDetails.canceled_at)}</span>
                                </div>}
                                {subscription.stripe_subscription_id && <div>
                                    <span className="font-medium text-gray-500">Stripe Sub ID: </span>
                                    <span className="text-gray-900 font-medium break-all">{subscription.stripe_subscription_id}</span>
                                </div>}
                            </div>
                        </section>

                        {/* --- Subscription Items (From DB) --- */}
                        <section>
                            <h4 className="text-lg font-semibold text-gray-700 mb-3">Subscription Items History (From Database)</h4>
                            {items && items.length > 0 ? (
                                <ul className="divide-y divide-gray-200 border rounded-md">
                                    {items.map(item => (
                                        // Added hover effect and adjusted label/value styling
                                        <li key={item.id} className="px-4 py-3 grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-2 text-sm hover:bg-gray-50 transition-colors duration-150">
                                            <div>
                                                <span className="font-medium text-gray-500">Product: </span>
                                                <span className="text-gray-900 font-semibold">{item.product_name || item.product_id}</span>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-500">Quantity: </span>
                                                <span className="text-gray-900 font-medium">{item.quantity}</span>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-500">Status: </span>
                                                {/* Status color logic remains */}
                                                <span className={`capitalize font-semibold ${(item?.status === 'active') ? 'text-green-600' :
                                                    (item?.status === 'canceled') ? 'text-red-600' :
                                                        (item?.status === 'inactive') ? 'text-gray-900' :
                                                            'text-blue-600'
                                                    }`}>
                                                    {item.status}
                                                </span>
                                            </div>
                                            {item.created_at && <div>
                                                <span className="font-medium text-gray-500">Created At: </span>
                                                <span className="text-gray-900 font-medium">{formatDateTime(item.created_at)}</span>
                                            </div>}
                                        </li>
                                    ))}
                                </ul>
                            ) : (
                                <p className="text-sm text-gray-500 italic">No subscription items found in database.</p>
                            )}
                        </section>

                        {/* --- Stripe Items (From Edge Function) --- */}
                        {stripeDetails?.stripe_items && stripeDetails.stripe_items.length > 0 && (
                            <section>
                                <div className="">
                                    <div className="flex items-center justify-between mb-3">
                                        <h4 className="text-lg font-semibold text-gray-700">Subscription Items (From Stripe)</h4>
                                        <a
                                            href={`https://dashboard.stripe.com/subscriptions/${subscription.stripe_subscription_id}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-[#9333ea] text-sm font-semibold hover:text-blue-800 hover:underline inline-flex items-center gap-1.5"
                                        >
                                            View in Stripe Dashboard
                                            <ExternalLink className="h-4 w-4" />
                                        </a>
                                    </div>
                                </div>
                                <ul className="divide-y divide-gray-200 border rounded-md">
                                    {stripeDetails.stripe_items.map(item => (
                                        <li key={item.id} className="px-4 py-3 grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                            <div>
                                                <span className="font-medium text-gray-600">Product: </span>
                                                <span className="text-gray-800 font-semibold">{item.product_name} (x{item.quantity})</span>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-600">Subscription Item ID: </span>
                                                <span className="text-gray-800 break-all font-semibold">{item.id}</span>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-600">Price ID: </span>
                                                <span className="text-gray-800 break-all font-semibold">{item.stripe_price_id}</span>
                                            </div>

                                            {item.product_id && <div>
                                                <span className="font-medium text-gray-600">Product ID: </span>
                                                <span className="text-gray-800 break-all font-semibold">{item.product_id}</span></div>}
                                        </li>
                                    ))}
                                </ul>
                            </section>
                        )}

                        {subscription.stripe_subscription_id && stripeDetails && (
                            <section>
                                {/* <h4 className="text-lg font-semibold text-gray-700 mb-3">Stripe Information</h4> */}
                                <div className="space-y-4 text-sm">

                                    {/* Schedule */}
                                    {stripeDetails.schedule && (
                                        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                                            <h5 className="font-medium text-blue-800 mb-2">Subscription Schedule</h5>
                                            <p className="text-blue-700 mb-2">
                                                <span className="font-semibold">Status: </span>
                                                {stripeDetails.schedule.status}
                                            </p>
                                            {/* Iterate through phases and display details */}
                                            {stripeDetails.schedule.phases && stripeDetails.schedule.phases.length > 0 ? (
                                                <div className="space-y-3">
                                                    {stripeDetails.schedule.phases.map((phase: any, index: number) => {
                                                        // Check if this is the last phase and the schedule releases the subscription
                                                        const isLastPhase = index === stripeDetails.schedule.phases.length - 1;
                                                        const isReleaseBehavior = stripeDetails.schedule.end_behavior === 'release';

                                                        return (
                                                            <div key={index} className="p-2 border border-blue-100 rounded bg-white">
                                                                <p className="text-sm font-semibold text-blue-700 mb-1">Phase {index + 1}</p>
                                                                <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-gray-700">
                                                                    {/* Use formatDateTime for start date */}
                                                                    <div><span className="font-medium">Start Date:</span> {formatDateTime(phase.start_date)}</div>
                                                                    {/* Use formatDateTime for end date, with special handling for the last phase if behavior is 'release' */}
                                                                    <div>
                                                                        <span className="font-medium">End Date:</span>
                                                                        {isLastPhase && isReleaseBehavior
                                                                            ? <span className="italic text-gray-600"> Transitions to Ongoing</span> // Display custom text for the final transition phase
                                                                            : <span className="ml-1">{phase.end_date ? formatDateTime(phase.end_date) : 'Current'}</span> // Otherwise, show the actual end date or 'Current'
                                                                        }
                                                                    </div>
                                                                    {/* Display items within the phase */}
                                                                    {phase.items && phase.items.length > 0 && (
                                                                        <div className="col-span-2 mt-1">
                                                                            <p className="font-medium mb-0.5">Items:</p>
                                                                            <ul className="list-disc list-inside pl-2 space-y-0.5">
                                                                                {phase.items.map((item: any, itemIndex: number) => (
                                                                                    <li key={itemIndex}>
                                                                                        Qty {item.quantity} of Price ID: <span className="font-mono text-blue-600">{item.price || item.plan || 'N/A'}</span>
                                                                                    </li>
                                                                                ))}
                                                                            </ul>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            ) : (
                                                <p className="text-xs text-blue-600 italic">No schedule phases found.</p>
                                            )}
                                            {/* Optionally keep the raw JSON for debugging, commented out by default */}
                                            {/*
                                            <pre className="text-xs mt-2 bg-gray-100 p-2 rounded overflow-x-auto">
                                                <code>{JSON.stringify(stripeDetails.schedule, null, 2)}</code>
                                            </pre>
                                            */}
                                        </div>
                                    )}

                                    {stripeDetails.payment_method_details && (
                                        <div>
                                            <h5 className="font-medium text-gray-600 my-4">Payment Method</h5>
                                            {/* Ensure formatPaymentMethod is called here */}
                                            <div className="text-gray-800">
                                                {formatPaymentMethod(stripeDetails.payment_method_details)}
                                            </div>
                                        </div>
                                    )}

                                    {/* Recent Invoices */}
                                    <div>
                                        <h5 className="font-medium text-gray-600 mb-2">Recent Invoices</h5>
                                        {/* Check if recent_invoices exists and has items */}
                                        {stripeDetails.recent_invoices && stripeDetails.recent_invoices.length > 0 ? (
                                            <ul className="divide-y divide-gray-200 border rounded-md">
                                                {stripeDetails.recent_invoices.map(invoice => (
                                                    <li key={invoice.id} className="px-4 py-3 grid grid-cols-5 gap-4 items-center"> {/* Adjust grid as needed */}
                                                        {/* Invoice Date */}
                                                        <div><span className="font-medium text-gray-600">Date:</span> <span className="text-gray-800">{formatDate(invoice.created)}</span></div>
                                                        {/* Due Date */}
                                                        <div>
                                                            <span className="font-medium text-gray-600">Due Date:</span>
                                                            <span className="text-gray-800 ml-1">
                                                                {invoice.due_date ? formatDate(invoice.due_date) : (invoice.status === 'paid' ? 'Paid' : 'N/A')}
                                                            </span>
                                                        </div>
                                                        {/* Total */}
                                                        <div><span className="font-medium text-gray-600">Total:</span> <span className="text-gray-800">{formatCurrency(invoice.total)}</span></div>
                                                        {/* Status */}
                                                        <div><span className="font-medium text-gray-600">Status:</span> <span className="text-gray-800 capitalize">{invoice.status}</span></div>
                                                        {/* View PDF Link - Ensure this part exists */}
                                                        <div className="text-right">
                                                            {invoice.invoice_pdf ? (
                                                                <a
                                                                    href={invoice.invoice_pdf}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className="text-blue-600 hover:text-blue-800 hover:underline inline-flex items-center text-xs"
                                                                >
                                                                    View PDF <ExternalLink className="h-3 w-3 ml-1" />
                                                                </a>
                                                            ) : (
                                                                <span className="text-gray-400 text-xs italic">No PDF</span>
                                                            )}
                                                        </div>
                                                    </li>
                                                ))}
                                            </ul>
                                        ) : (
                                            <p className="text-sm text-gray-500 italic">No recent invoices found via Stripe.</p>
                                        )}
                                    </div>
                                </div>
                            </section>
                        )}
                        {/* Message if Stripe details couldn't be loaded but ID exists */}
                        {subscription.stripe_subscription_id && !isLoading && !stripeDetails && !error && (
                            <p className="text-sm text-gray-500 italic">Could not load Stripe details.</p>
                        )}
                    </div>
                )}

                {/* Footer Actions */}
                <div className="mt-6 pt-4 border-t flex justify-end space-x-2">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm"
                    >
                        Close
                    </button>
                    {/* Add other action buttons here if needed */}
                </div>
            </div>
        </div>
    );
}

export default SubscriptionDetails;
