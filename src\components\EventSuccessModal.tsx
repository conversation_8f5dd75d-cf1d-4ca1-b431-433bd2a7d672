import { X, CheckCircle, Calendar, Clock, MapPin } from 'lucide-react';

interface EventSuccessModalProps {
  eventTitle: string;
  eventDate: string;
  eventTime: string;
  eventLocation: string;
  onClose: () => void;
}

// Helper function to convert 24-hour time to 12-hour time with AM/PM
const formatTimeWithAMPM = (time: string): string => {
  if (!time) return '';

  // Parse the time string (expected format: HH:MM)
  const [hours, minutes] = time.split(':').map(Number);

  if (isNaN(hours) || isNaN(minutes)) return time;

  // Convert to 12-hour format
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
};

export default function EventSuccessModal({
  eventTitle,
  eventDate,
  eventTime,
  eventLocation,
  onClose
}: EventSuccessModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-bold">Registration Successful</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="flex flex-col items-center justify-center mb-6">
            <div className="bg-green-100 p-3 rounded-full mb-4">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <h4 className="text-lg font-semibold text-center mb-2">
              You're registered for the event!
            </h4>
            <p className="text-gray-600 text-center">
              Thank you for registering for "{eventTitle}". We've sent a confirmation email with all the details.
            </p>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <div className="flex items-center mb-2">
              <Calendar className="h-5 w-5 text-blue-600 mr-2" />
              <span className="text-blue-800 font-medium">{eventDate}</span>
            </div>
            <div className="flex items-center mb-2">
              <Clock className="h-5 w-5 text-blue-600 mr-2" />
              <span className="text-blue-800 font-medium">{formatTimeWithAMPM(eventTime)}</span>
            </div>
            <div className="flex items-center">
              <MapPin className="h-5 w-5 text-blue-600 mr-2" />
              <span className="text-blue-800 font-medium">{eventLocation}</span>
            </div>
          </div>

          <div className="text-center">
            <p className="text-gray-600 mb-4">
              We look forward to seeing you at the event. If you have any questions, please contact us.
            </p>
            <button
              onClick={onClose}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
