import { Calendar, ChevronRight, Circle, CheckCircle2 } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import styles from './Timeline.module.css';

interface TimelinePhase {
  days: number;
  title: string;
  items: string[];
}

interface TimelineProps {
  phases: TimelinePhase[];
}

export const Timeline: React.FC<TimelineProps> = ({ phases }) => {
  const [activePhase, setActivePhase] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const timelineRefs = useRef<(HTMLDivElement | null)[]>([]);
  const sectionRef = useRef<HTMLElement>(null);

  // Handle mouse move for parallax effect
  const handleMouseMove = (e: MouseEvent) => {
    if (sectionRef.current) {
      const rect = sectionRef.current.getBoundingClientRect();
      const x = (e.clientX - rect.left) / rect.width - 0.5;
      const y = (e.clientY - rect.top) / rect.height - 0.5;
      setMousePosition({ x, y });
    }
  };

  useEffect(() => {
    const section = sectionRef.current;
    if (section) {
      section.addEventListener('mousemove', handleMouseMove);
    }
    return () => {
      if (section) {
        section.removeEventListener('mousemove', handleMouseMove);
      }
    };
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const target = entry.target as HTMLDivElement;
            target.classList.add(styles.visible);
            
            const index = timelineRefs.current.findIndex((ref) => ref === target);
            if (index !== -1) {
              setActivePhase(index);
              
              // Add visible class to all items in this phase with delay
              const items = target.querySelectorAll('.timeline-item');
              items.forEach((item, i) => {
                setTimeout(() => {
                  item.classList.add(styles.visible);
                }, i * 200); // Slightly longer delay for smoother staggering
              });
            }
          }
        });
      },
      {
        threshold: 0.4, // Increased threshold for smoother transitions
        rootMargin: '-10% 0px -10% 0px'
      }
    );

    timelineRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, []);

  return (
    <section 
      ref={sectionRef}
      className="py-12 sm:py-20 bg-gradient-to-br from-white to-orange-50/30 relative overflow-hidden"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className={`text-3xl sm:text-4xl font-bold mb-3 sm:mb-4 ${styles['gradient-text']}`}>
            Project Timeline
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base px-4">
            A structured approach to implementing and scaling our initiative across the Pacific Rim region.
          </p>
        </div>
        
        <div className="relative">
          {/* Mobile timeline line */}
          <div className="absolute left-4 sm:left-1/2 top-0 bottom-0 w-1 transform sm:-translate-x-1/2 bg-gradient-to-b from-orange-100 to-purple-100 backdrop-blur-sm"></div>
          
          {/* Animated progress line */}
          <div 
            className={`${styles['progress-line']} absolute left-4 sm:left-1/2 transform sm:-translate-x-1/2 w-1 bg-gradient-to-b from-orange-500 to-purple-500`}
            style={{ 
              height: `${((activePhase + 1) / phases.length) * 100}%`,
              opacity: 0.8
            }}
          />

          <div className="space-y-12 sm:space-y-24">
            {phases.map((phase, index) => (
              <div
                key={index}
                ref={(el) => (timelineRefs.current[index] = el)}
                className={`${styles['timeline-item']} relative flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-8 ${
                  index % 2 === 0 ? 'sm:flex-row' : 'sm:flex-row-reverse'
                }`}
              >
                {/* Timeline connector - Hidden on mobile */}
                <div className="hidden sm:block w-1/2 relative">
                  <div 
                    className={`${styles['timeline-connector']} absolute top-1/2 ${
                      index % 2 === 0 ? 'right-0 bg-gradient-to-r' : 'left-0 bg-gradient-to-l'
                    } transform -translate-y-1/2 w-16 h-[2px] from-orange-200 to-orange-500 opacity-0`}
                    style={{
                      opacity: index <= activePhase ? 0.8 : 0,
                      width: index <= activePhase ? '4rem' : '2rem',
                      transform: `translateY(-50%) translateX(${mousePosition.x * 10}px)`
                    }}
                  />
                </div>

                {/* Timeline node */}
                <div 
                  className={`${styles['timeline-node']} absolute left-4 sm:left-1/2 transform sm:-translate-x-1/2 flex items-center justify-center w-8 sm:w-12 h-8 sm:h-12 rounded-full ${
                    index <= activePhase ? 'active' : ''
                  }`}
                  style={{
                    transform: `translate(${-50}%, 0) scale(${index <= activePhase ? 1.2 : 1}) 
                               translate3d(${mousePosition.x * 15}px, ${mousePosition.y * 15}px, 0)`
                  }}
                >
                  <div className={`absolute w-full h-full rounded-full animate-ping ${
                    index <= activePhase ? 'bg-orange-200' : 'bg-gray-200'
                  }`} style={{ animationDuration: '3s' }} />
                  <div className={`absolute w-full h-full rounded-full ${
                    index <= activePhase ? 'bg-orange-100' : 'bg-gray-100'
                  }`} />
                  {index <= activePhase ? (
                    <CheckCircle2 className="w-6 h-6 sm:w-8 sm:h-8 text-orange-600 relative z-10" />
                  ) : (
                    <Circle className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400 relative z-10" />
                  )}
                </div>

                {/* Content card - Full width on mobile */}
                <div className="ml-12 sm:ml-0 sm:w-1/2 w-[calc(100%-3rem)]">
                  <div 
                    className={`${styles['timeline-card']} bg-white p-4 sm:p-8 rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl ${
                      index <= activePhase ? 'active border-l-4 border-orange-500' : 'border-l-4 border-transparent'
                    }`}
                    style={{
                      animation: index <= activePhase ? 'borderPulse 2s ease-in-out infinite' : 'none',
                      transform: `translate3d(${mousePosition.x * 20}px, ${mousePosition.y * 20}px, 0) 
                                 ${index <= activePhase ? 'translateY(-8px)' : 'translateY(0)'}`
                    }}
                  >
                    <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                      <div className={`p-2 sm:p-3 rounded-lg transform transition-transform hover:scale-110 ${
                        index <= activePhase ? 'bg-orange-100' : 'bg-gray-100'
                      }`}>
                        <Calendar className={`h-5 w-5 sm:h-6 sm:w-6 ${
                          index <= activePhase ? 'text-orange-600' : 'text-gray-400'
                        }`} />
                      </div>
                      <div>
                        <span className={`text-xs sm:text-sm font-medium ${
                          index <= activePhase ? 'text-orange-600' : 'text-gray-400'
                        }`}>
                          Phase {index + 1}
                        </span>
                        <h3 className="text-base sm:text-xl font-semibold">{phase.days} Days - {phase.title}</h3>
                      </div>
                    </div>
                    <ul className="space-y-2 sm:space-y-3">
                      {phase.items.map((item, i) => (
                        <li 
                          key={i} 
                          className={`${styles['timeline-item']} timeline-item flex items-start gap-2 sm:gap-3 hover:translate-x-1 transition-transform`}
                          style={{ 
                            transitionDelay: `${i * 200}ms`,
                            opacity: index <= activePhase ? 1 : 0.6
                          }}
                        >
                          <ChevronRight 
                            className={`w-4 h-4 sm:w-5 sm:h-5 mt-0.5 flex-shrink-0 transition-colors ${
                              index <= activePhase ? 'text-orange-500' : 'text-gray-400'
                            }`}
                          />
                          <span className={`${styles['timeline-content']} text-sm sm:text-base`}>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
