-- Migration: Grant Basic Permissions for Products Table
-- Purpose: 
-- 1. Grant SELECT permission to anonymous users (anon role)
-- 2. <PERSON> full permissions to authenticated users
-- 3. This is needed in addition to RLS policies

-- Grant SELECT permission to anonymous users
GRANT SELECT ON TABLE products TO anon, authenticated;

-- Grant full permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE products TO authenticated;

-- Ensure RLS is enabled
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies (optional, uncomment if needed)
-- DROP POLICY IF EXISTS "Allow anonymous read access to products" ON products;
-- DROP POLICY IF EXISTS "Allow authenticated read access to products" ON products;
-- DROP POLICY IF EXISTS "Allow authenticated insert to products" ON products;
-- DROP POLICY IF EXISTS "Allow authenticated update to products" ON products;
-- DROP POLICY IF EXISTS "Allow authenticated delete from products" ON products;
-- DROP POLICY IF EXISTS "Allow admin insert to products" ON products;
-- DROP POLICY IF EXISTS "Allow admin update to products" ON products;
-- DROP POLICY IF EXISTS "Allow admin delete from products" ON products;

-- Create a simple policy for public read access
CREATE POLICY "Public read access for products"
ON products FOR SELECT
USING (true);

-- Create a policy for admin users to insert/update/delete
CREATE POLICY "Admin full access for products"
ON products FOR ALL
TO authenticated
USING (
  -- Check if user is in profiles table with role='admin'
  (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin'
)
WITH CHECK (
  -- Check if user is in profiles table with role='admin'
  (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin'
);
