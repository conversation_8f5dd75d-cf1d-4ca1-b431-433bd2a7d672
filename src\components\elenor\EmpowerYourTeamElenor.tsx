import React from 'react';
// Import the TM image
import tmImage from '../../assets/images/grantready/TM.png';

export interface CardData {
    id: number;
    title: string;
    description: string;
    icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
    gradientFrom: string;
    gradientTo: string;
}

interface EmpowerYourTeamProps {
    cardsData: CardData[];
    gridCols?: string; // Optional grid configuration, defaults to 3-column
    title?: string; // Optional custom title
    description?: string; // Optional custom description
    showTrademark?: boolean; // Optional to show/hide GrantReady trademark
}

const EmpowerYourTeamElenor: React.FC<EmpowerYourTeamProps> = ({
    cardsData,
    gridCols = "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    title,
    description,
    showTrademark = true
}) => {

    return (
      <div className="py-16 px-4 sm:px-6 lg:px-8 bg-[#EDEFFE]">
        {/* Container */}
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="text-left mb-16">
            {title ? (
              // Custom title (for ELENOR)
              <>
                <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl text-center">
                  {title}
                </h2>
                {description && (
                  <p className="text-2xl font-medium text-black text-center mx-auto leading-relaxed font-inter max-w-4xl">
                    {description}
                  </p>
                )}
              </>
            ) : (
              // Default GrantReady title
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-normal text-gray-900 mb-6 max-w-6xl">
                Empower your team with{" "}
                {showTrademark && (
                  <span
                    className="relative bg-clip-text text-transparent inline-flex items-center gap-1"
                    style={{
                      background:
                        "linear-gradient(266.64deg, #797EEC 13.28%, #22C55E 58.62%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                    }}
                  >
                    GrantReady™
                    <img
                      src={tmImage}
                      alt="TM"
                      className="w-40 h-20  ml-1 absolute -top-12 -right-20"
                    />
                  </span>
                )}
                <br />
                the all-in-one platform built to simplify oversight, boost
                compliance, and accelerate outcomes.
              </h2>
            )}
          </div>

          {/* Cards Grid */}
          <div className={`grid ${gridCols} gap-4 lg:gap-6`}>
            {cardsData.map((card) => (
              <FeatureCard key={card.id} card={card} />
            ))}
          </div>
        </div>
      </div>
    );
};

interface FeatureCardProps {
    card: CardData;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ card }) => {
    const IconComponent = card.icon;

    return (
        <div
            className="group relative h-64 rounded-xl overflow-hidden cursor-pointer transition-all duration-500 hover:shadow-2xl"
            style={{ transform: 'scale(0.92)' }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.02)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(0.92)'}
        >
            {/* Background Image with Opacity */}
            <div
                className="absolute inset-0 opacity-30 rounded-xl"
                style={{
                    backgroundImage: 'url("data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3Cpattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"%3E%3Ccircle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100" height="100" fill="url(%23grain)"/%3E%3C/svg%3E")',
                    backgroundSize: '100px 100px'
                }}
            />

            {/* Main Card Content - Visible by default */}
            <div
                className="absolute inset-0 p-4 text-white transition-transform duration-700 ease-in-out group-hover:-translate-y-full rounded-xl overflow-hidden"
                style={{
                    backgroundColor: '#09183d'
                }}
            >
                {/* Neon shadow in bottom-right corner - Balanced */}
                <div
                    className="absolute bottom-0 right-0 w-32 h-32"
                    style={{
                        background: 'radial-gradient(circle at center, rgba(92, 240, 254, 0.12) 0%, rgba(92, 240, 254, 0.06) 40%, transparent 70%)',
                        filter: 'blur(18px)',
                        borderRadius: '0 0 0.75rem 0'
                    }}
                ></div>
                {/* Title at top left */}
                <h3
                    className="text-left mb-4"
                    style={{
                        fontFamily: 'Zenith Trial',
                        fontWeight: 400,
                        fontStyle: 'italic',
                        fontSize: '28px',
                        lineHeight: '26px',
                        letterSpacing: '0%'
                    }}
                >
                    {card.title}
                </h3>

                {/* Icon Container - Centered */}
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-16 h-16 rounded-full flex items-center justify-center bg-white/10 backdrop-blur-sm border border-white/20">
                        <IconComponent className="w-8 h-8 text-white" />
                    </div>
                </div>
            </div>

            {/* Hidden Content - Revealed on hover */}
            <div className="absolute inset-0 bg-white p-4 transform translate-y-full transition-transform duration-700 ease-in-out group-hover:translate-y-0 rounded-xl">
                 {/* Title - Same styling as before hover */}
                <h3
                    className="text-left mb-4"
                    style={{
                        fontFamily: 'Zenith Trial',
                        fontWeight: 400,
                        fontStyle: 'italic',
                        fontSize: '22px',
                        lineHeight: '26px',
                        letterSpacing: '0%',
                        color: '#1f2937'
                    }}
                >
                    {card.title}
                </h3>

                {/* Description */}
                <p className="text-gray-600 leading-relaxed mb-4 text-sm">
                    {card.description}
                </p>

                {/* Icon in bottom-right corner */}
                <div className="absolute bottom-4 right-4">
                    <div
                        className="w-10 h-10 rounded-full flex items-center justify-center shadow-lg"
                        style={{ backgroundColor: '#6490cc' }}
                    >
                        <IconComponent className="w-5 h-5 text-white" />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default EmpowerYourTeamElenor;
