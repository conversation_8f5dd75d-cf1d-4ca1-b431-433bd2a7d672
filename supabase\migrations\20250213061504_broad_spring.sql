CREATE TABLE site_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  settings jsonb NOT NULL DEFAULT '{
    "general": {
      "siteName": "International Responder Systems",
      "siteDescription": "Healthcare Emergency Response Solutions",
      "contactEmail": "<EMAIL>",
      "phone": "",
      "address": "157 E Main Street, Elkton, MD 21921-5977",
      "socialLinks": {
        "linkedin": "https://www.linkedin.com/company/international-responder-systems",
        "twitter": "https://twitter.com/intrespondersys",
        "facebook": "https://www.facebook.com/InternationalResponderSystems"
      }
    },
    "seo": {
      "defaultTitle": "International Responder Systems - Healthcare Emergency Response Solutions",
      "defaultDescription": "Leading provider of healthcare emergency response and grant management solutions.",
      "defaultKeywords": "healthcare, emergency response, grant management, SOAR, GrantReady™",
      "googleAnalyticsId": "",
      "googleTagManagerId": "",
      "googleSiteVerification": "",
      "bingVerification": "",
      "robotsTxt": "User-agent: *\nAllow: /",
      "sitemapEnabled": true
    },
    "appearance": {
      "logo": "",
      "favicon": "",
      "primaryColor": "#2563eb",
      "secondaryColor": "#1e40af",
      "fontFamily": "Inter"
    },
    "features": {
      "blogEnabled": true,
      "commentsEnabled": true,
      "userRegistrationEnabled": true,
      "maintenanceMode": false
    }
  }'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
 
-- Enable RLS
ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;
 
-- Create policies
CREATE POLICY "Anyone can read site settings"
  ON site_settings
  FOR SELECT
  USING (true);
 
CREATE POLICY "Admins can manage site settings"
  ON site_settings
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));
 
-- Create trigger for updated_at
CREATE TRIGGER update_site_settings_updated_at
  BEFORE UPDATE ON site_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
 
-- Insert default settings
INSERT INTO site_settings (settings)
VALUES (
  '{
    "general": {
      "siteName": "International Responder Systems",
      "siteDescription": "Healthcare Emergency Response Solutions",
      "contactEmail": "<EMAIL>",
      "phone": "",
      "address": "157 E Main Street, Elkton, MD 21921-5977",
      "socialLinks": {
        "linkedin": "https://www.linkedin.com/company/international-responder-systems",
        "twitter": "https://twitter.com/intrespondersys",
        "facebook": "https://www.facebook.com/InternationalResponderSystems"
      }
    },
    "seo": {
      "defaultTitle": "International Responder Systems - Healthcare Emergency Response Solutions",
      "defaultDescription": "Leading provider of healthcare emergency response and grant management solutions.",
      "defaultKeywords": "healthcare, emergency response, grant management, SOAR, GrantReady™",
      "googleAnalyticsId": "",
      "googleTagManagerId": "",
      "googleSiteVerification": "",
      "bingVerification": "",
      "robotsTxt": "User-agent: *\nAllow: /",
      "sitemapEnabled": true
    },
    "appearance": {
      "logo": "",
      "favicon": "",
      "primaryColor": "#2563eb",
      "secondaryColor": "#1e40af",
      "fontFamily": "Inter"
    },
    "features": {
      "blogEnabled": true,
      "commentsEnabled": true,
      "userRegistrationEnabled": true,
      "maintenanceMode": false
    }
  }'::jsonb
);
 
-- Grant necessary permissions
GRANT ALL ON site_settings TO authenticated;
GRANT ALL ON site_settings TO anon;
GRANT ALL ON site_settings TO service_role;