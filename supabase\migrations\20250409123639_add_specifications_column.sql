-- Migration: Add specifications column to products table
-- Purpose: Ensure the specifications column exists and is properly recognized in the schema cache

-- Check if the column already exists, if not add it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'products'
        AND column_name = 'specifications'
    ) THEN
        ALTER TABLE products
        ADD COLUMN specifications JSONB DEFAULT '[]'::jsonb;
    END IF;
END $$;

-- Comment on the specifications column
COMMENT ON COLUMN products.specifications IS 'Array of product specifications in JSON format';

-- Refresh the schema cache by altering the column to the same type
-- This is a safer approach than updating system tables directly
ALTER TABLE products ALTER COLUMN specifications TYPE JSONB;

-- Grant permissions on the column
GRANT SELECT, UPDATE(specifications) ON products TO authenticated;
GRANT SELECT(specifications) ON products TO anon;

-- Notify the system that the schema has changed
NOTIFY pgrst, 'reload schema';


-- Add monthly and yearly discount fields to products table
ALTER TABLE products 
ADD COLUMN monthly_discount numeric DEFAULT 0 CHECK (monthly_discount >= 0 AND monthly_discount <= 100),
ADD COLUMN yearly_discount numeric DEFAULT 0 CHECK (yearly_discount >= 0 AND yearly_discount <= 100);
