import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Building2, User, Mail, Phone, Eye, Trash2, CheckCircle, XCircle } from 'lucide-react';
import { supabase } from '../../lib/supabase';

interface DemoRequest {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  company: string;
  job_title: string;
  team_size: string;
  use_case: string;
  preferred_date: string;
  preferred_time: string;
  additional_info?: string;
  status: 'pending' | 'scheduled' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

const DemoRequestsManagement: React.FC = () => {
  const [demoRequests, setDemoRequests] = useState<DemoRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<DemoRequest | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [filter, setFilter] = useState<string>('all');

  useEffect(() => {
    fetchDemoRequests();
  }, []);

  const fetchDemoRequests = async () => {
    try {
      const { data, error } = await supabase
        .from('demo_requests')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      setDemoRequests(data || []);

      if (!data || data.length === 0) {
        console.log('No demo requests found');
      } else {
        console.log(`Found ${data.length} demo requests`);
      }
    } catch (error) {
      console.error('Error fetching demo requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async (id: string, status: DemoRequest['status']) => {
    try {
      const { error } = await supabase
        .from('demo_requests')
        .update({ status })
        .eq('id', id);

      if (error) throw error;

      setDemoRequests(prev =>
        prev.map(request =>
          request.id === id ? { ...request, status } : request
        )
      );
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const deleteRequest = async (id: string) => {
    if (!confirm('Are you sure you want to delete this demo request?')) return;

    try {
      const { error } = await supabase
        .from('demo_requests')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setDemoRequests(prev => prev.filter(request => request.id !== id));
    } catch (error) {
      console.error('Error deleting demo request:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredRequests = demoRequests.filter(request =>
    filter === 'all' || request.status === filter
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // const testConnection = async () => {
  //   try {
  //     // Test basic connection
  //     const { data: testData, error: testError } = await supabase
  //       .from('demo_requests')
  //       .select('count');

  //     console.log('Connection test:', { testData, testError });

  //     // Try to insert a test record
  //     const { data: insertData, error: insertError } = await supabase
  //       .from('demo_requests')
  //       .insert({
  //         first_name: 'Test',
  //         last_name: 'User',
  //         email: '<EMAIL>',
  //         company: 'Test Company',
  //         job_title: 'Test Job',
  //         team_size: '1-10 employees',
  //         use_case: 'Testing',
  //         preferred_date: new Date().toISOString().split('T')[0],
  //         preferred_time: '10:00 AM'
  //       })
  //       .select();

  //     console.log('Insert test:', { insertData, insertError });

  //     // Refresh the list
  //     fetchDemoRequests();
  //   } catch (error) {
  //     console.error('Test failed:', error);
  //   }
  // };

  return (
    <div className="p-6">
      {/* <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Demo Requests Management</h1>
        <p className="text-gray-600">Manage and track demo requests from potential customers.</p>

        <div className="mt-4">
          <button
            onClick={testConnection}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Test Connection & Add Sample Data
          </button>
          <span className="ml-4 text-sm text-gray-500">
            Current count: {demoRequests.length} requests
          </span>
        </div>
      </div> */}



      {/* Filter Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'all', label: 'All Requests', count: demoRequests.length },
              { key: 'pending', label: 'Pending', count: demoRequests.filter(r => r.status === 'pending').length },
              { key: 'scheduled', label: 'Scheduled', count: demoRequests.filter(r => r.status === 'scheduled').length },
              { key: 'completed', label: 'Completed', count: demoRequests.filter(r => r.status === 'completed').length },
              { key: 'cancelled', label: 'Cancelled', count: demoRequests.filter(r => r.status === 'cancelled').length }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${filter === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Demo Requests Table */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Company
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Use Case
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Preferred Date/Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRequests.map((request) => (
                <tr key={request.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <User className="h-5 w-5 text-blue-600" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {request.first_name} {request.last_name}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          {request.email}
                        </div>
                        {request.phone && (
                          <div className="text-sm text-gray-500 flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            {request.phone}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{request.company}</div>
                    <div className="text-sm text-gray-500">{request.job_title}</div>
                    <div className="text-sm text-gray-500">{request.team_size}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{request.use_case}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {new Date(request.preferred_date).toLocaleDateString()}
                    </div>
                    <div className="text-sm text-gray-500 flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {request.preferred_time}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(request.status)}`}>
                      {request.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedRequest(request);
                          setShowModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>

                      {request.status === 'pending' && (
                        <button
                          onClick={() => updateStatus(request.id, 'scheduled')}
                          className="text-green-600 hover:text-green-900"
                          title="Mark as Scheduled"
                        >
                          <CheckCircle className="h-4 w-4" />
                        </button>
                      )}

                      {request.status === 'scheduled' && (
                        <button
                          onClick={() => updateStatus(request.id, 'completed')}
                          className="text-green-600 hover:text-green-900"
                          title="Mark as Completed"
                        >
                          <CheckCircle className="h-4 w-4" />
                        </button>
                      )}

                      <button
                        onClick={() => updateStatus(request.id, 'cancelled')}
                        className="text-red-600 hover:text-red-900"
                        title="Cancel"
                      >
                        <XCircle className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => deleteRequest(request.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredRequests.length === 0 && (
          <div className="text-center py-12">
            <Building2 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No demo requests</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter === 'all' ? 'No demo requests have been submitted yet.' : `No ${filter} demo requests found.`}
            </p>
          </div>
        )}
      </div>

      {/* Modal for viewing details */}
      {showModal && selectedRequest && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Demo Request Details</h3>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircle className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Name</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.first_name} {selectedRequest.last_name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Company</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.company}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Job Title</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.job_title}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Team Size</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.team_size}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Use Case</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.use_case}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Preferred Date & Time</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(selectedRequest.preferred_date).toLocaleDateString()} at {selectedRequest.preferred_time}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedRequest.status)}`}>
                      {selectedRequest.status}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Submitted</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(selectedRequest.created_at).toLocaleString()}
                    </p>
                  </div>
                </div>

                {selectedRequest.additional_info && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Additional Information</label>
                    <p className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                      {selectedRequest.additional_info}
                    </p>
                  </div>
                )}
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DemoRequestsManagement;
