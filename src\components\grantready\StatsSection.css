/* Icon Glow Effects for StatsSection */

/* Keyframes for glowing animation */
@keyframes iconGlow {
    0% {
        filter: drop-shadow(0 0 5px #5CF0FE) drop-shadow(0 0 10px #5CF0FE) drop-shadow(0 0 15px #5CF0FE);
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 10px #5CF0FE) drop-shadow(0 0 20px #5CF0FE) drop-shadow(0 0 30px #5CF0FE);
        transform: scale(1.05);
    }
    100% {
        filter: drop-shadow(0 0 5px #5CF0FE) drop-shadow(0 0 10px #5CF0FE) drop-shadow(0 0 15px #5CF0FE);
        transform: scale(1);
    }
}

@keyframes containerGlow {
    0% {
        box-shadow: 
            0 0 10px rgba(92, 240, 254, 0.3),
            0 0 20px rgba(92, 240, 254, 0.2),
            0 0 30px rgba(92, 240, 254, 0.1),
            inset 0 0 10px rgba(92, 240, 254, 0.1);
    }
    50% {
        box-shadow: 
            0 0 20px rgba(92, 240, 254, 0.5),
            0 0 40px rgba(92, 240, 254, 0.3),
            0 0 60px rgba(92, 240, 254, 0.2),
            inset 0 0 20px rgba(92, 240, 254, 0.2);
    }
    100% {
        box-shadow: 
            0 0 10px rgba(92, 240, 254, 0.3),
            0 0 20px rgba(92, 240, 254, 0.2),
            0 0 30px rgba(92, 240, 254, 0.1),
            inset 0 0 10px rgba(92, 240, 254, 0.1);
    }
}

/* Icon glow effect */
.icon-glow {
    animation: iconGlow 3s ease-in-out infinite;
    transition: all 0.3s ease;
}

/* Container glow effect */
.icon-glow-container {
    animation: containerGlow 3s ease-in-out infinite;
    transition: all 0.3s ease;
    position: relative;
    overflow: visible;
}

/* Hover effects */
.icon-glow-container:hover {
    animation-duration: 1.5s;
}

.icon-glow-container:hover .icon-glow {
    animation-duration: 1.5s;
    filter: drop-shadow(0 0 15px #5CF0FE) drop-shadow(0 0 25px #5CF0FE) drop-shadow(0 0 35px #5CF0FE);
}

/* Additional glow for the entire card on hover */
.icon-glow-container:hover {
    background: radial-gradient(circle at center, rgba(92, 240, 254, 0.3), rgba(92, 240, 254, 0.1));
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .icon-glow {
        animation-duration: 4s;
    }
    
    .icon-glow-container {
        animation-duration: 4s;
    }
}

/* Staggered animation delays for each card */
.icon-glow-container:nth-child(1) {
    animation-delay: 0s;
}

.icon-glow-container:nth-child(1) .icon-glow {
    animation-delay: 0s;
}

/* Second card delay */
.icon-glow-container:nth-child(2) {
    animation-delay: 1s;
}

.icon-glow-container:nth-child(2) .icon-glow {
    animation-delay: 1s;
}

/* Third card delay */
.icon-glow-container:nth-child(3) {
    animation-delay: 2s;
}

.icon-glow-container:nth-child(3) .icon-glow {
    animation-delay: 2s;
}

/* Pulse effect for enhanced visual appeal */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

.icon-glow-container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #5CF0FE, transparent, #5CF0FE);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.3;
    animation: pulse 2s ease-in-out infinite;
}
