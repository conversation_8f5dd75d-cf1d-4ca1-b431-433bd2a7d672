-- Create function to handle ticket resolution with email notification
CREATE OR REPLACE FUNCTION resolve_ticket_with_notification(
  ticket_id uuid,
  resolution text,
  resolver_id uuid
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  ticket_info record;
BEGIN
  -- Update ticket status and add resolution
  UPDATE support_tickets
  SET 
    status = 'resolved',
    resolution_comment = resolution,
    resolved_by = resolver_id,
    resolved_at = now(),
    updated_at = now()
  WHERE id = ticket_id
  RETURNING * INTO ticket_info;

  -- Note: Email notification would be handled by Edge Functions or external service
  -- This function just handles the database updates
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION resolve_ticket_with_notification TO authenticated;