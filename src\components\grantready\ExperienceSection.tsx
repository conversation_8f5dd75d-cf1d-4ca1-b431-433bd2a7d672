import React from 'react';

// Import the video file
import experienceVideo from '../../assets/images/grantReady-assets/50-years.mp4';

const ExperienceSection: React.FC = () => {
  const [screenSize, setScreenSize] = React.useState({
    isMobile: false,
    isTablet: false,
    isSmallMobile: false
  });

  // Track screen size for responsive design
  React.useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setScreenSize({
        isSmallMobile: width <= 480,
        isMobile: width <= 768,
        isTablet: width <= 1024
      });
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const { isMobile, isTablet, isSmallMobile } = screenSize;

  return (
    <div style={{
      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',      backgroundColor: '#FFFFFF',
      padding: isSmallMobile ? '24px 15px' : isMobile ? '24px 15px' : '44px 15px',
      textAlign: 'center',
    }}>
      {/* Video Ball Section */}
      <div style={{
        position: 'relative',
        display: 'inline-block',
        marginBottom: '0px',
      }}>
        {/* Circular Video Background */}
        <div style={{
          position: 'relative',
          width: isSmallMobile ? 'min(85vw, 250px)' : isMobile ? 'min(90vw, 320px)' : isTablet ? '450px' : '550px',
          height: isSmallMobile ? 'min(85vw, 250px)' : isMobile ? 'min(90vw, 320px)' : isTablet ? '450px' : '550px',
          borderRadius: '50%',
          overflow: 'hidden',
          margin: '0 auto',
        }}>
          <video
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              minWidth: isSmallMobile ? '220%' : isMobile ? '210%' : '200%',
              minHeight: isSmallMobile ? '220%' : isMobile ? '210%' : '200%',
              width: 'auto',
              height: 'auto',
              transform: 'translate(-50%, -50%)',
              filter: 'blur(15px)',
              opacity: 0.15,
            }}
            autoPlay
            loop
            muted
            playsInline
          >
            <source src={experienceVideo} type="video/mp4" />
          </video>

          {/* Content Overlay on the Ball */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 2,
            width: '100%',
            padding: isSmallMobile ? '15px' : isMobile ? '20px' : '20px',
          }}>
            {/* Large Number */}
            <h1 style={{
              fontSize: isSmallMobile ? '3rem' : isMobile ? '4rem' : isTablet ? '6rem' : '8rem',
              fontWeight: 900,
              color: '#4A90E2',
              margin: isSmallMobile ? '0 0 5px 0' : '0 0 10px 0',
              lineHeight: 1,
              letterSpacing: '-0.02em',
            }}>
              50
            </h1>

            {/* Years of experience text */}
            <h2 style={{
              fontSize: isSmallMobile ? '1rem' : isMobile ? '1.2rem' : isTablet ? '1.8rem' : '2.2rem',
              fontWeight: 800,
              color: '#4A90E2',
              margin: isSmallMobile ? '0 0 5px 0' : '0 0 8px 0',
              lineHeight: 1.2,
            }}>
              Years of experience
            </h2>

            {/* Description text */}
            <p style={{
              fontSize: isSmallMobile ? '0.75rem' : isMobile ? '0.9rem' : isTablet ? '1.2rem' : '1.6rem',
              fontWeight: 700,
              color: '#2C3E50',
              margin: 0,
              lineHeight: 1.3,
              maxWidth: isSmallMobile ? '95%' : isMobile ? '90%' : '400px',
              marginLeft: 'auto',
              marginRight: 'auto',
              padding: isSmallMobile ? '0 5px' : isMobile ? '0 10px' : '0',
            }}>
              in public health emergency<br />
              preparedness and response
            </p>
          </div>
        </div>
      </div>


    </div>
  );
};

export default ExperienceSection;
