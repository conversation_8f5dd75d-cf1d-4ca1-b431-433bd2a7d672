-- Create teams table
CREATE TABLE teams (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  image_url TEXT NOT NULL,
  bio TEXT NOT NULL,
  linkedin_url TEXT,
  email TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grant permissions
GRANT ALL PRIVILEGES ON TABLE teams TO postgres;
GRANT ALL PRIVILEGES ON TABLE teams TO authenticated;
GRANT SELECT ON TABLE teams TO anon;

-- Enable RLS
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

-- Public read policy
CREATE POLICY "Public teams are visible to everyone" 
ON teams FOR SELECT 
USING (TRUE);

-- Public update policy
CREATE POLICY "Public can update teams"
ON teams FOR UPDATE
TO public
USING (TRUE)
WITH CHECK (TRUE);

-- Public delete policy
CREATE POLICY "Public can delete teams"
ON teams FOR DELETE
TO public
USING (TRUE);

-- Admin full access policy
CREATE POLICY "<PERSON><PERSON> can manage all teams"
ON teams FOR ALL
TO authenticated
USING (auth.role() = 'admin')
WITH CHECK (auth.role() = 'admin');

-- Remove or comment out these policies as they conflict with admin access
-- CREATE POLICY "Allow updates for authenticated users"
-- ON teams FOR UPDATE
-- TO authenticated
-- USING (auth.uid() = id::text)
-- WITH CHECK (auth.uid() = id::text);

-- CREATE POLICY "Allow deletes for authenticated users"
-- ON teams FOR DELETE
-- TO authenticated
-- USING (auth.uid() = id::text);

-- Update the insert policy to allow public inserts
CREATE POLICY "Allow public inserts to teams"
ON teams FOR INSERT
TO public
WITH CHECK (true);

-- Set up policies for team images
CREATE POLICY "Public read access for team images" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'teams');

CREATE POLICY "Authenticated can upload team images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'teams');

CREATE POLICY "Admin full access to team images"
ON storage.objects FOR ALL
TO authenticated
USING (bucket_id = 'teams' AND auth.role() = 'admin');

-- Create the teams bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public) 
VALUES ('teams', 'teams', true)
ON CONFLICT (id) DO NOTHING;