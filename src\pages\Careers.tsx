import React, { useState, useEffect } from 'react';
import { Briefcase, Users, Target, Heart, Zap, Star, Award, Mail, Building2, CheckCircle, Coffee, Brain, ArrowRight, Workflow } from 'lucide-react';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';

interface JobListing {
  id: string;
  title: string;
  department: string;
  location: string;
  type: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  salary_range: {
    min: number;
    max: number;
    currency: string;
  } | null;
}

export default function Careers() {
  const [jobs, setJobs] = useState<JobListing[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchJobs();
  }, []);

  const fetchJobs = async () => {
    try {
      const { data } = await supabase
        .from('job_listings')
        .select('*')
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      setJobs(data || []);
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  const values = [
    { icon: Target, title: 'Mission', description: 'Revolutionize health & emergency response' },
    { icon: Heart, title: 'Vision', description: 'Redefining public health standards' },
    { icon: Zap, title: 'Impact', description: 'Making a difference in public health' }
  ];

  const beliefs = [
    { icon: CheckCircle, title: 'Pursue Perfection', description: 'Encourage questions, feedback and discourse to fuel individual growth' },
    { icon: Heart, title: 'Express Gratitude', description: 'Go the extra mile. Show appreciation to employees and clients' },
    { icon: Coffee, title: 'Employee Satisfaction', description: 'Happy employees make happier customers' },
    { icon: Star, title: 'Cultivate Success', description: 'Push the boundaries on what is possible' },
    { icon: Brain, title: 'Value Expertise', description: 'Knowledgeable people give us our competitive edge' },
    { icon: Target, title: 'Push boundaries', description: 'Set high standards and continually raise the bar' }
  ];

  const benefits = [
    { icon: Building2, title: '401k', description: 'Comprehensive retirement plans' },
    { icon: Heart, title: 'Medical', description: 'Full health coverage' },
    { icon: Coffee, title: 'PTO', description: 'Flexible time off' },
    { icon: Brain, title: 'Growth', description: 'Career development opportunities' }
  ];

  return (
    <div className="">
      {/* Hero Section */}
      <section className="relative text-white py-24 rounded-lg" style={{ background: 'radial-gradient(50% 55% at 0% 0%, #06B6D4 0%, #0891B2 100%)' }}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Small decorative dots */}
          <div className="absolute top-32 right-20 w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-32 w-3 h-3 bg-blue-200 rounded-full opacity-60 animate-pulse delay-75"></div>
          <div className="absolute bottom-32 right-24 w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-150"></div>
          <div className="absolute bottom-20 right-40 w-1 h-1 bg-blue-100 rounded-full animate-pulse delay-300"></div>

          {/* Geometric shapes */}
          <div className="absolute top-16 right-16 w-12 h-12 border border-white/20 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-24 right-64 w-8 h-8 border border-white/10 rotate-12 animate-bounce"></div>

          {/* Additional decorative lines */}
          <div className="absolute top-1/3 right-0 w-px h-20 bg-gradient-to-b from-transparent via-white/20 to-transparent"></div>
          <div className="absolute top-2/3 right-12 w-px h-16 bg-gradient-to-b from-transparent via-white/15 to-transparent"></div>
        </div>

        {/* Creative illustration on the right - Career themed */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block overflow-hidden">
          <div className="relative h-full flex items-center justify-center">
            <div className="relative scale-150">
              {/* Floating career icons */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float">
                <div className="p-3 flex items-center justify-center h-full">
                  <Briefcase className="h-8 w-8 text-white" />
                </div>
              </div>

              <div className="absolute -top-6 right-10 w-18 h-22 bg-white/15 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed">
                <div className="p-2.5 flex items-center justify-center h-full">
                  <Users className="h-7 w-7 text-white" />
                </div>
              </div>

              {/* Central career hub */}
              <div className="w-40 h-40 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center relative animate-pulse-slow">
                <div className="w-28 h-28 bg-white/30 rounded-full flex items-center justify-center">
                  <div className="w-16 h-16 bg-white/40 rounded-full flex items-center justify-center">
                    <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold text-xl">
                        <Workflow className="h-6 w-6 text-blue-600" />
                      </span>
                    </div>
                  </div>
                </div>

                {/* Orbiting career elements */}
                <div className="absolute inset-0 animate-spin-slow">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-300 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -right-3 transform -translate-y-1/2 w-4 h-4 bg-blue-200 rounded-full opacity-60"></div>
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-400 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -left-3 transform -translate-y-1/2 w-4 h-4 bg-blue-100 rounded-full opacity-60"></div>
                </div>
              </div>

              {/* Floating skill icons */}
              <div className="absolute -bottom-8 -left-8 w-16 h-16 bg-white/15 rounded-lg backdrop-blur-sm flex items-center justify-center transform -rotate-12 animate-bounce-slow">
                <Brain className="h-8 w-8 text-blue-300" />
              </div>

              <div className="absolute bottom-3 right-6 w-14 h-14 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center animate-float">
                <Target className="h-7 w-7 text-yellow-300" />
              </div>

              <div className="absolute top-10 -left-10 w-18 h-18 bg-white/10 rounded-xl backdrop-blur-sm flex items-center justify-center transform rotate-45 animate-pulse">
                <Star className="h-8 w-8 text-green-300 transform -rotate-45" />
              </div>

              {/* Growth pathway elements */}
              <div className="absolute -bottom-12 right-2 w-12 h-8 bg-white/15 rounded-lg backdrop-blur-sm flex items-center justify-center animate-bounce delay-500">
                <ArrowRight className="h-6 w-6 text-white/80" />
              </div>

              <div className="absolute top-8 left-4 w-10 h-6 bg-white/20 rounded-lg backdrop-blur-sm animate-pulse delay-1000"></div>

              {/* Professional ladder visualization */}
              <div className="absolute -left-6 top-1/2 transform -translate-y-1/2">
                <div className="w-1 h-20 bg-white/20 rounded-full relative">
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-green-300 rounded-full"></div>
                  <div className="absolute top-1/3 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-yellow-300 rounded-full"></div>
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-300 rounded-full"></div>
                </div>
              </div>
            </div>

            {/* Career connection lines */}
            <div className="absolute inset-0 pointer-events-none scale-125">
              <svg className="w-full h-full opacity-20" viewBox="0 0 400 400">
                <defs>
                  <linearGradient id="careerGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="rgba(255,255,255,0)" />
                    <stop offset="50%" stopColor="rgba(255,255,255,0.3)" />
                    <stop offset="100%" stopColor="rgba(255,255,255,0)" />
                  </linearGradient>
                </defs>
                <path
                  d="M50,200 Q200,100 350,200 Q200,300 50,200"
                  fill="none"
                  stroke="url(#careerGradient1)"
                  strokeWidth="3"
                  className="animate-pulse"
                />
                <path
                  d="M100,150 Q200,50 300,150"
                  fill="none"
                  stroke="url(#careerGradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-500"
                />
                <path
                  d="M100,250 Q200,350 300,250"
                  fill="none"
                  stroke="url(#careerGradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-1000"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Join Our Mission
              <span className="block text-3xl font-normal mt-2 text-blue-200">
                Shape the Future
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Be part of a team that's revolutionizing public health emergency response. We're looking for passionate individuals who want to make a real difference.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href="#openings"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-all duration-300 hover:transform hover:scale-105 shadow-lg text-center"
              >
                View Open Positions
              </a>
              <a
                href="mailto:<EMAIL>"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-all duration-300 hover:transform hover:scale-105 text-center"
              >
                Send Your Resume
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Add custom animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(12deg); }
          50% { transform: translateY(-10px) rotate(12deg); }
        }
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0px) rotate(-6deg); }
          50% { transform: translateY(-8px) rotate(-6deg); }
        }
        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        @keyframes bounce-slow {
          0%, 100% { transform: translateY(0px) rotate(-12deg); }
          50% { transform: translateY(-5px) rotate(-12deg); }
        }
        @keyframes pulse-slow {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }
        .animate-float { animation: float 3s ease-in-out infinite; }
        .animate-float-delayed { animation: float-delayed 3s ease-in-out infinite 1s; }
        .animate-spin-slow { animation: spin-slow 20s linear infinite; }
        .animate-bounce-slow { animation: bounce-slow 2s ease-in-out infinite; }
        .animate-pulse-slow { animation: pulse-slow 4s ease-in-out infinite; }
      `}</style>


      {/* Current Openings Section */}
      <section id="openings" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Current Openings</h2>
            <p className="text-xl text-gray-600">Join our growing team</p>
          </div>

          {loading ? (
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : jobs.length > 0 ? (
            <div className="grid md:grid-cols-2 gap-8">
              {jobs.map((job) => (
                <div key={job.id} className="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 hover:border-blue-500 transition-colors">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        <Briefcase className="h-6 w-6 text-blue-600 mr-3" />
                        <h3 className="text-xl font-bold">{job.title}</h3>
                      </div>
                      <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                        {job.type}
                      </span>
                    </div>

                    <div className="space-y-4 mb-6">
                      <div className="flex items-center text-gray-600">
                        <Building2 className="h-5 w-5 mr-2" />
                        <span>{job.department}</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Users className="h-5 w-5 mr-2" />
                        <span>{job.location}</span>
                      </div>
                      {job.salary_range && (
                        <div className="flex items-center text-gray-600">
                          <Award className="h-5 w-5 mr-2" />
                          <span>
                            {job.salary_range.currency} {job.salary_range.min.toLocaleString()} - {job.salary_range.max.toLocaleString()} / year
                          </span>
                        </div>
                      )}
                    </div>

                    <p className="text-gray-600 mb-6">
                      {job.description.length > 200
                        ? `${job.description.substring(0, 200)}...`
                        : job.description}
                    </p>

                    <div className="flex gap-3">
                      <Link
                        to={`/careers/job/${job.id}`}
                        className="inline-flex items-center bg-gray-100 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300"
                      >
                        View Details
                      </Link>
                      <Link
                        to={`/careers/apply/${job.id}`}
                        className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
                      >
                        Apply Now
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <Briefcase className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600 mb-6">
                We're always looking for talented individuals to join our team.
                Check back soon for new opportunities or send us your resume.
              </p>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
              >
                <Mail className="mr-2 h-5 w-5" />
                Send Your Resume
              </a>
            </div>
          )}
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Our Values</h2>
            <p className="text-xl text-gray-600">What drives us forward</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white p-8 rounded-lg text-center shadow-lg">
                <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-6">
                  <value.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-4">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Core Beliefs Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Core Beliefs</h2>
            <p className="text-xl text-gray-600">
              Our core beliefs drive everything we do
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {beliefs.map((belief, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-lg shadow-lg">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <belief.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold">{belief.title}</h3>
                </div>
                <p className="text-gray-600">{belief.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Benefits</h2>
            <p className="text-xl text-gray-600">What we offer to our team</p>
          </div>
          <div className="grid md:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="bg-blue-100 p-4 rounded-full w-fit mx-auto mb-6">
                  <benefit.icon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stay Connected Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-8">Stay Connected</h2>
          <div className="flex justify-center space-x-4">
            <a
              href="https://www.linkedin.com/company/international-responder-systems"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300"
            >
              Follow us on LinkedIn
            </a>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white/10 transition duration-300"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}