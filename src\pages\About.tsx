import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {  ArrowRight } from 'lucide-react';
import Value1 from '../assets/images/value1.avif'
import Value2 from '../assets/images/value2.avif'
import Value3 from '../assets/images/value3.avif'
import AboutUsBannerVideo from '../assets/video/about-us-banner.mp4';

// About Us Section Images
import AboutUsSec1 from '../assets/images/about-us/about-us-sec-1.png';
import AboutUsSec2 from '../assets/images/about-us/about-us-sec-2.png';
import AboutUsSec3 from '../assets/images/about-us/about-us-sec-3.png';
import AboutUsSec4 from '../assets/images/about-us/about-us-sec-4.png';



export default function About() {
  const navigate = useNavigate();

  const values = [
    { image: Value1, title: 'Working With Us', description: 'Prepare your team to be : Take proactive steps, Think critically, React to crucial situations' },
    { image: Value2, title: 'Prepare for the Worst ', description: 'Our team of subject matter experts with proven track records of success ready to train and serve you for anything that can confront you.' },
    { image: Value3, title: 'Exceeding Expectations', description: 'We are eager to go the extra mile to show our gratitude to employees and clients.' }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="text-white py-4 sm:py-6 md:py-8 lg:py-10 xl:py-12 relative rounded-lg overflow-hidden">
        {/* Background Video */}
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
        >
          <source src={AboutUsBannerVideo} type="video/mp4" />
        </video>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            {/* Small title */}
            <p className="text-sm sm:text-base md:text-lg font-medium mb-2 sm:mb-4 text-purple-200 tracking-wide">
              Public Health Responder Systems
            </p>

            {/* Main heading */}
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 sm:mb-6 md:mb-8 leading-tight px-2 sm:px-0">
              International Responder Systems
            </h1>

            {/* Description */}
            <p className="text-sm sm:text-base md:text-lg lg:text-xl mb-6 sm:mb-8 md:mb-10 opacity-90 leading-relaxed max-w-sm sm:max-w-2xl md:max-w-3xl lg:max-w-4xl mx-auto px-2 sm:px-0">
              Established in 2015 to support international and domestic
              governments as <br /> well as private sector training
              organizations. <br /> International Responder is uniquely prepared
              to support these organizations.
            </p>
          </div>
        </div>
      </section>

      {/* Accomplishments Section */}
      <section className="py-16 bg-white">
        <div className="max-w-[1470px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div
              className="flex flex-col opacity-100"
              style={{
                width: "666px",
                height: "521px",
                gap: "20px",
                maxWidth: "100%", // Ensure responsiveness
              }}
            >
              <h2
                className="text-gray-900"
                style={{
                  fontFamily: "Inter, sans-serif",
                  fontWeight: 700,
                  fontSize: "28px",
                  lineHeight: "38.4px",
                  letterSpacing: "0%",
                }}
              >
                Accomplishments
              </h2>

              <div className="flex flex-col" style={{ gap: "20px" }}>
                <p
                  className="text-gray-900"
                  style={{
                    fontFamily: "Inter, sans-serif",
                    fontWeight: 400,
                    fontSize: "18px",
                    lineHeight: "110%",
                    letterSpacing: "0%",
                  }}
                >
                  Contracted by DC Government to instruct HSEEP Compliant Full
                  Scale and Table Top Exercises, Weaponized Anthrax Full Scale
                  Exercise, After Action Reporting, Data Analytics, Mass Trauma
                  Planning and National Special Security Events Strategic
                  Support
                </p>

                <p
                  className="text-gray-700"
                  style={{
                    fontFamily: "Inter, sans-serif",
                    fontWeight: 400,
                    fontSize: "18px",
                    lineHeight: "140%",
                    letterSpacing: "0%",
                  }}
                >
                  Contracted by FEMA to program executive leadership education
                  as well as training academy logistics and student services
                </p>

                <p
                  className="text-gray-700"
                  style={{
                    fontFamily: "Inter, sans-serif",
                    fontWeight: 400,
                    fontSize: "18px",
                    lineHeight: "140%",
                    letterSpacing: "0%",
                  }}
                >
                  Developed Virtual Instructor led E-Learning Training Modules
                  for Leidos, synchronous and asynchronous. Provided subject
                  matter experts and training instructors for response
                  necessities including but not limited to CBRNE Training,
                  Public Health Emergency Response, law enforcement, hazardous
                  materials and live agents. Infrastructure support, developing
                  audio/visual studios
                </p>

                <p
                  className="text-gray-700"
                  style={{
                    fontFamily: "Inter, sans-serif",
                    fontWeight: 400,
                    fontSize: "18px",
                    lineHeight: "140%",
                    letterSpacing: "0%",
                  }}
                >
                  Contracted by eHealth Africa for NIMS and ICS Training.
                  Logistics Coordination for Ebola response as well as
                  inter-agency coordination for disaster support
                </p>

                <p
                  className="text-gray-700"
                  style={{
                    fontFamily: "Inter, sans-serif",
                    fontWeight: 400,
                    fontSize: "18px",
                    lineHeight: "140%",
                    letterSpacing: "0%",
                  }}
                >
                  Provided Public Health System Disaster Response Support for
                  Puerto Rico Department of Health.
                </p>
              </div>
            </div>

            {/* Right Image */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                <img
                  src={AboutUsSec1}
                  alt="Modern office building representing our accomplishments"
                  className="shadow-lg object-cover transform transition-transform hover:scale-[1.02]"
                  style={{
                    width: "662px",
                    height: "479px",
                    borderRadius: "14px",
                    opacity: 1,
                    maxWidth: "100%", // Ensure responsiveness
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Philosophy Section */}
      <section
        className="py-16 rounded-lg"
        style={{ backgroundColor: "#EDEFFE" }}
      >
        <div className="max-w-[1470px] mx-auto px-4 sm:px-6 lg:px-8 ">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Image */}
            <div className="flex justify-center lg:justify-start">
              <div className="relative">
                <img
                  src={AboutUsSec2}
                  alt="Team collaboration representing our philosophy"
                  className="shadow-lg object-cover transform transition-transform hover:scale-[1.02]"
                  style={{
                    width: "662px",
                    height: "479px",
                    borderRadius: "14px",
                    opacity: 1,
                    maxWidth: "100%", // Ensure responsiveness
                  }}
                />
              </div>
            </div>

            {/* Right Content */}
            <div
              className="flex flex-col opacity-100"
              style={{
                width: "666px",
                height: "479px",
                justifyContent: "space-between",
                maxWidth: "100%", // Ensure responsiveness
              }}
            >
              <h2
                style={{
                  fontFamily: "Inter, sans-serif",
                  fontWeight: 400,
                  fontSize: "22px",
                  lineHeight: "100%",
                  letterSpacing: "0%",
                  textAlign: "left",
                  color: "#6E6E6E",
                }}
              >
                Our Philosophy
              </h2>

              <div className="flex flex-col" style={{ gap: "20px" }}>
                <p
                  className="text-gray-900"
                  style={{
                    fontFamily: "Inter, sans-serif",
                    fontWeight: 700,
                    fontSize: "20px",
                    lineHeight: "100%",
                    letterSpacing: "0%",
                  }}
                >
                  Established in 2015 by Mr. James Mullikin (CEO) to support
                  international and domestic governments as well as private
                  sector training organizations.
                </p>

                <p
                  className="text-gray-700"
                  style={{
                    fontFamily: "Inter, sans-serif",
                    fontWeight: 400,
                    fontSize: "18px",
                    lineHeight: "110%",
                    letterSpacing: "0%",
                  }}
                >
                  in the power of teamwork. With a supportive and encouraging
                  nexus, each project set for success. Our employees rely on us
                  and we trust them. With the company on the cutting edge, we
                  have a passion and desire to ensure successes and time
                  efficient outcomes for our partners and stakeholders. We
                  inspire straightforward and personable service.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Team Section */}
      <section className="py-16 bg-white">
        <div className="max-w-[1470px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div
              className="flex flex-col opacity-100"
              style={{
                width: "666px",
                height: "479px",
                justifyContent: "space-between",
                maxWidth: "100%", // Ensure responsiveness
              }}
            >
              <h2
                style={{
                  fontFamily: "Inter",
                  fontWeight: 400,
                  fontSize: "22px",
                  lineHeight: "100%",
                  letterSpacing: "0%",
                  textAlign: "left",
                  color: "#6E6E6E",
                }}
              >
                Our Team
              </h2>

              <div className="flex flex-col" style={{ gap: "20px" }}>
                <h3
                  className="text-gray-900"
                  style={{
                    fontFamily: "Platform",
                    fontWeight: 500,
                    fontSize: "34px",
                    lineHeight: "38.4px",
                    letterSpacing: "0%",
                  }}
                >
                  Accomplished Experts
                </h3>

                <p
                  className="text-gray-700"
                  style={{
                    fontFamily: "Inter",
                    fontWeight: 400,
                    fontSize: "28px",
                    lineHeight: "38.4px",
                    letterSpacing: "0%",
                  }}
                >
                  International Responder is uniquely prepared to support any
                  organization for any situation. We are subject matter experts
                  (SMEs) for emergency response and public health with over 50
                  years of combined experience.
                </p>

                <Link
                  to="https://www.cdc.gov/insight-net/php/about/index.html?CDC_AA_refVal=https%3A%2F%2Fwww.cdc.gov%2Fforecast-outbreak-analytics%2Fabout%2Foadm-network.html"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center bg-black hover:bg-gray-800 transition-colors w-fit cursor-pointer"
                  style={{
                    fontFamily: "Inter",
                    fontWeight: 400,
                    fontSize: "16px",
                    color: "white",
                    padding: "5px 4px 5px 12px",
                    borderRadius: "8px",
                    border: "none",
                    gap: "8px",
                  }}
                >
                  Read About Our Recent Trip To DC
                  <div
                    className="flex items-center justify-center p-2 rounded-8"
                    style={{
                      backgroundColor: "white",
                    }}
                  >
                    <ArrowRight className="h-6 w-6 text-black" />
                  </div>
                </Link>
              </div>
            </div>

            {/* Right Image */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                <img
                  src={AboutUsSec3}
                  alt="Team collaboration and data analysis representing our accomplished experts"
                  className="shadow-lg object-cover transform transition-transform hover:scale-[1.02]"
                  style={{
                    width: "662px",
                    height: "479px",
                    borderRadius: "14px",
                    opacity: 1,
                    maxWidth: "100%", // Ensure responsiveness
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Work Section */}
      <section
        className="py-16 rounded-lg"
        style={{ backgroundColor: "#EDEFFE" }}
      >
        <div className="max-w-[1470px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Image */}
            <div className="flex justify-center lg:justify-start">
              <div className="relative">
                <img
                  src={AboutUsSec4}
                  alt="Team meeting representing our diverse portfolio"
                  className="shadow-lg object-cover transform transition-transform hover:scale-[1.02]"
                  style={{
                    width: "662px",
                    height: "479px",
                    borderRadius: "14px",
                    opacity: 1,
                    maxWidth: "100%", // Ensure responsiveness
                  }}
                />
              </div>
            </div>

            {/* Right Content */}
            <div
              className="flex flex-col opacity-100"
              style={{
                width: "666px",
                height: "479px",
                justifyContent: "space-between",
                maxWidth: "100%", // Ensure responsiveness
              }}
            >
              <h2
                style={{
                  fontFamily: "Inter",
                  fontWeight: 400,
                  fontSize: "22px",
                  lineHeight: "100%",
                  letterSpacing: "0%",
                  textAlign: "left",
                  color: "#6E6E6E",
                }}
              >
                Our Work
              </h2>

              <div className="flex flex-col" style={{ gap: "20px" }}>
                <h3
                  className="text-gray-900"
                  style={{
                    fontFamily: "Platform",
                    fontWeight: 500,
                    fontSize: "34px",
                    lineHeight: "38.4px",
                    letterSpacing: "0%",
                  }}
                >
                  Diverse Portfolio
                </h3>

                <p
                  className="text-gray-700"
                  style={{
                    fontFamily: "Inter",
                    fontWeight: 400,
                    fontSize: "28px",
                    lineHeight: "38.4px",
                    letterSpacing: "0%",
                  }}
                >
                  We separate ourselves from other firms with the diversity of
                  our clients, projects, and professional expertise. Our
                  successful growth is a result of our ability to offer our
                  services at roughly half the cost of our competitors.
                </p>

                <button
                  className="inline-flex items-center bg-black hover:bg-gray-800 transition-colors w-fit"
                  style={{
                    fontFamily: "Inter",
                    fontWeight: 400,
                    fontSize: "16px",
                    color: "white",
                    padding: "5px 4px 5px 12px",
                    borderRadius: "8px",
                    border: "none",
                    gap: "8px",
                  }}
                  onClick={() => navigate("/solutions")}
                >
                  Explore Our Services
                  <div
                    className="flex items-center justify-center"
                    style={{
                      backgroundColor: "white",
                      borderRadius: "3px",
                      width: "40px",
                      height: "40px",
                    }}
                  >
                    <ArrowRight className="h-6 w-6 text-black" />
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="pt-16 flex justify-center">
        <div className="max-w-[1480px] mx-0 px-0 sm:px-0 lg:px-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Years of Experience */}
            <div
              className="flex items-center justify-between bg-white rounded-8"
              style={{
                width: "488.67px",
                height: "143px",
                gap: "14px",
                opacity: 1,
                padding: "24px",
                boxShadow: "0px 0px 12px 0px #0000001F",
                maxWidth: "100%", // Ensure responsiveness
              }}
            >
              <div className="flex flex-col">
                <h3
                  className="text-gray-900 w-full mb-3 text-xl md:text-[44px]"
                  style={{
                    fontFamily: "Zenith Trial",
                    fontWeight: 400,
                    fontStyle: "italic",
                    letterSpacing: "0%",
                    verticalAlign: "middle",
                  }}
                >
                  10
                  <span
                    className="text-xl md:text-[38px]"
                    style={{
                      fontFamily: "Zenith Trial",
                      fontWeight: 400,
                      fontStyle: "italic",
                    }}
                  >
                    +
                  </span>
                </h3>
                <p
                  className="text-gray-900 text-base sm:text-lg md:text-xl"
                  style={{
                    fontFamily: "Inter, system-ui, sans-serif",
                    fontWeight: 700,
                    lineHeight: "1.21em",
                    letterSpacing: "-1%",
                  }}
                >
                  Years of Experience
                </p>
              </div>

              <div className="flex-shrink-0">
                <svg
                  width="68"
                  height="67"
                  viewBox="0 0 68 67"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_1292_15193)">
                    <path
                      d="M56.5481 2.77989C56.5481 1.24474 57.7925 0.000244141 59.3277 0.000244141C60.8629 0.000244141 62.1074 1.24474 62.1074 2.77989V5.55955H64.887C66.4222 5.55955 67.6667 6.80405 67.6667 8.3392C67.6667 9.87434 66.4222 11.1188 64.887 11.1188H62.1074V13.8985C62.1074 15.4336 60.8629 16.6781 59.3277 16.6781C57.7925 16.6781 56.5481 15.4336 56.5481 13.8985V11.1188H53.7684C52.2332 11.1188 50.9888 9.87434 50.9888 8.3392C50.9888 6.80405 52.2332 5.55955 53.7684 5.55955H56.5481V2.77989Z"
                      fill="url(#paint0_linear_1292_15193)"
                    />
                    <path
                      d="M59.8658 22.4448C61.2779 21.842 62.9329 22.4914 63.4051 23.9521C65.29 29.7835 65.3851 36.0706 63.6425 41.9907C61.637 48.803 57.323 54.7048 51.4407 58.6833C45.5584 62.6615 38.475 64.4686 31.4058 63.7937C24.3365 63.119 17.7226 60.0047 12.6991 54.9855C7.67568 49.9663 4.55597 43.3549 3.8754 36.2863C3.19485 29.2176 4.99593 22.1325 8.9695 16.247C12.943 10.3614 18.8413 6.04252 25.6519 4.03147C31.5706 2.28382 37.8577 2.37385 43.6907 4.25401C45.1517 4.72497 45.8024 6.37947 45.2009 7.79181C44.5991 9.20418 42.9655 9.85389 41.4951 9.41268C36.8639 8.02294 31.9073 8.00081 27.2316 9.38141C21.6635 11.0255 16.8414 14.5565 13.5927 19.3683C10.3441 24.1801 8.87162 29.9726 9.42803 35.7517C9.9844 41.5306 12.535 46.9359 16.642 51.0395C20.749 55.1431 26.1562 57.689 31.9356 58.2408C37.7153 58.7922 43.5065 57.3151 48.3155 54.0624C53.1246 50.8099 56.6517 45.9847 58.2911 40.4154C59.6679 35.7387 59.6418 30.782 58.2481 26.1518C57.8058 24.6818 58.454 23.0476 59.8658 22.4448Z"
                      fill="url(#paint1_linear_1292_15193)"
                    />
                    <path
                      d="M29.7928 23.238V45.0002H25.4215V27.6408L23.3774 28.3956V24.2758L25.736 23.238H29.7928ZM42.1753 30.0309C42.1753 29.1084 41.9447 28.3641 41.4835 27.798C41.0432 27.232 40.3828 26.9489 39.5022 26.9489C37.7202 26.9489 36.8291 27.9762 36.8291 30.0309V38.2074C36.8291 40.2411 37.7202 41.2579 39.5022 41.2579C40.3828 41.2579 41.0432 40.9853 41.4835 40.4402C41.9447 39.8742 42.1753 39.1299 42.1753 38.2074V30.0309ZM46.5467 30.0309V38.1445C46.5467 40.3669 45.9177 42.1385 44.6598 43.4593C43.4228 44.7591 41.6827 45.4091 39.4393 45.4091C37.0912 45.4091 35.3406 44.7696 34.1875 43.4907C33.0344 42.2118 32.4578 40.4298 32.4578 38.1445V30.0309C32.4578 27.7876 33.0449 26.0264 34.2189 24.7476C35.414 23.4687 37.1751 22.8292 39.5022 22.8292C41.7455 22.8292 43.4752 23.4791 44.6912 24.779C45.9282 26.0789 46.5467 27.8295 46.5467 30.0309Z"
                      fill="#6D5CF9"
                    />
                  </g>
                  <defs>
                    <linearGradient
                      id="paint0_linear_1292_15193"
                      x1="59.3277"
                      y1="0.000244141"
                      x2="59.3277"
                      y2="16.6781"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#A079FB" />
                      <stop offset="1" stopColor="#6D5CF9" />
                    </linearGradient>
                    <linearGradient
                      id="paint1_linear_1292_15193"
                      x1="34.3108"
                      y1="2.77979"
                      x2="34.3108"
                      y2="63.9321"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#A079FB" />
                      <stop offset="1" stopColor="#6D5CF9" />
                    </linearGradient>
                    <clipPath id="clip0_1292_15193">
                      <rect
                        width="66.7116"
                        height="66.7116"
                        fill="white"
                        transform="translate(0.955078 0.000244141)"
                      />
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </div>

            {/* Clients Served */}
            <div
              className="flex items-center justify-between bg-white rounded-8"
              style={{
                width: "488.67px",
                height: "143px",
                gap: "14px",
                opacity: 1,
                padding: "24px",
                boxShadow: "0px 0px 12px 0px #0000001F",
                maxWidth: "100%", // Ensure responsiveness
              }}
            >
              <div className="flex flex-col">
                <h3
                  className="text-gray-900 w-full mb-3 text-xl md:text-[44px]"
                  style={{
                    fontFamily: "Zenith Trial",
                    fontWeight: 400,
                    fontStyle: "italic",
                    letterSpacing: "0%",
                    verticalAlign: "middle",
                  }}
                >
                  1000
                  <span
                    className="text-xl md:text-[38px]"
                    style={{
                      fontFamily: "Zenith Trial",
                      fontWeight: 400,
                      fontStyle: "italic",
                    }}
                  >
                    +
                  </span>
                </h3>
                <p
                  className="text-gray-900 text-base sm:text-lg md:text-xl"
                  style={{
                    fontFamily: "Inter, system-ui, sans-serif",
                    fontWeight: 700,
                    lineHeight: "1.21em",
                    letterSpacing: "-1%",
                  }}
                >
                  Clients Served
                </p>
              </div>

              <div className="flex-shrink-0">
                <svg
                  width="69"
                  height="69"
                  viewBox="0 0 69 69"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    opacity="0.4"
                    d="M51.3282 20.6427C51.1582 20.6144 50.9598 20.6144 50.7898 20.6427C46.8798 20.501 43.7632 17.2994 43.7632 13.3327C43.7632 9.28104 47.0215 6.02271 51.0732 6.02271C55.1249 6.02271 58.3832 9.30937 58.3832 13.3327C58.3549 17.2994 55.2382 20.501 51.3282 20.6427Z"
                    stroke="url(#paint0_linear_1292_15201)"
                    strokeWidth="4.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    opacity="0.4"
                    d="M48.4087 41.2697C52.2904 41.9213 56.5687 41.2413 59.5721 39.2297C63.5671 36.5663 63.5671 32.203 59.5721 29.5397C56.5404 27.5279 52.2054 26.8479 48.3237 27.5279"
                    stroke="url(#paint1_linear_1292_15201)"
                    strokeWidth="4.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    opacity="0.4"
                    d="M17.2381 20.6425C17.4081 20.6141 17.6064 20.6141 17.7764 20.6425C21.6864 20.5008 24.8031 17.2991 24.8031 13.3325C24.8031 9.28079 21.5448 6.02246 17.4931 6.02246C13.4414 6.02246 10.1831 9.30913 10.1831 13.3325C10.2114 17.2991 13.3281 20.5008 17.2381 20.6425Z"
                    stroke="url(#paint2_linear_1292_15201)"
                    strokeWidth="4.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    opacity="0.4"
                    d="M20.1591 41.2697C16.2774 41.9213 11.9991 41.2413 8.99576 39.2297C5.00076 36.5663 5.00076 32.203 8.99576 29.5397C12.0274 27.5279 16.3624 26.8479 20.2441 27.5279"
                    stroke="url(#paint3_linear_1292_15201)"
                    strokeWidth="4.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M34.3341 41.808C34.1641 41.7796 33.9658 41.7796 33.7958 41.808C29.8858 41.6663 26.769 38.4646 26.769 34.498C26.769 30.4463 30.0274 27.188 34.0791 27.188C38.1308 27.188 41.3891 30.4746 41.3891 34.498C41.3608 38.4646 38.2441 41.6946 34.3341 41.808Z"
                    stroke="url(#paint4_linear_1292_15201)"
                    strokeWidth="4.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M26.0915 50.7335C22.0965 53.3968 22.0965 57.7598 26.0915 60.4232C30.6248 63.4548 38.0481 63.4548 42.5815 60.4232C46.5765 57.7598 46.5765 53.3968 42.5815 50.7335C38.0765 47.7301 30.6248 47.7301 26.0915 50.7335Z"
                    stroke="url(#paint5_linear_1292_15201)"
                    strokeWidth="4.25"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <defs>
                    <linearGradient
                      id="paint0_linear_1292_15201"
                      x1="51.0732"
                      y1="6.02271"
                      x2="51.0732"
                      y2="20.6427"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#9F78FB" />
                      <stop offset="1" stopColor="#6D5CF9" />
                    </linearGradient>
                    <linearGradient
                      id="paint1_linear_1292_15201"
                      x1="55.446"
                      y1="27.2725"
                      x2="55.446"
                      y2="41.5089"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#9F78FB" />
                      <stop offset="1" stopColor="#6D5CF9" />
                    </linearGradient>
                    <linearGradient
                      id="paint2_linear_1292_15201"
                      x1="17.4931"
                      y1="6.02246"
                      x2="17.4931"
                      y2="20.6425"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#9F78FB" />
                      <stop offset="1" stopColor="#6D5CF9" />
                    </linearGradient>
                    <linearGradient
                      id="paint3_linear_1292_15201"
                      x1="13.1218"
                      y1="27.2725"
                      x2="13.1218"
                      y2="41.5089"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#9F78FB" />
                      <stop offset="1" stopColor="#6D5CF9" />
                    </linearGradient>
                    <linearGradient
                      id="paint4_linear_1292_15201"
                      x1="34.0791"
                      y1="27.188"
                      x2="34.0791"
                      y2="41.808"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#9F78FB" />
                      <stop offset="1" stopColor="#6D5CF9" />
                    </linearGradient>
                    <linearGradient
                      id="paint5_linear_1292_15201"
                      x1="34.3365"
                      y1="48.481"
                      x2="34.3365"
                      y2="62.6969"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#9F78FB" />
                      <stop offset="1" stopColor="#6D5CF9" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>

            {/* Team Members */}
            <div
              className="flex items-center justify-between bg-white rounded-8"
              style={{
                width: "488.67px",
                height: "143px",
                gap: "14px",
                opacity: 1,
                padding: "24px",
                boxShadow: "0px 0px 12px 0px #0000001F",
                maxWidth: "100%", // Ensure responsiveness
              }}
            >
              <div className="flex flex-col">
                <h3
                  className="text-gray-900 w-full mb-3 text-xl md:text-[44px]"
                  style={{
                    fontFamily: "Zenith Trial",
                    fontWeight: 400,
                    fontStyle: "italic",
                    letterSpacing: "0%",
                    verticalAlign: "middle",
                  }}
                >
                  100
                  <span
                    className="text-xl md:text-[38px]"
                    style={{
                      fontFamily: "Zenith Trial",
                      fontWeight: 400,
                      fontStyle: "italic",
                    }}
                  >
                    +
                  </span>
                </h3>
                <p
                  className="text-gray-900 text-base sm:text-lg md:text-xl"
                  style={{
                    fontFamily: "Inter, system-ui, sans-serif",
                    fontWeight: 700,
                    lineHeight: "1.21em",
                    letterSpacing: "-1%",
                  }}
                >
                  Team Members
                </p>
              </div>

              <div className="flex-shrink-0">
                <svg
                  width="60"
                  height="61"
                  viewBox="0 0 60 61"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_1292_15212)">
                    <path
                      d="M8.75 30.356C7.09301 30.354 5.50445 29.6949 4.33278 28.5232C3.1611 27.3515 2.50198 25.763 2.5 24.106C2.50846 23.5419 2.59425 22.9817 2.755 22.441C3.1653 22.7545 3.6638 22.9312 4.18 22.946C4.4659 22.9386 4.74839 22.882 5.015 22.7785C5.015 22.806 5 22.831 5 22.856V25.356C5 26.019 5.26339 26.6549 5.73223 27.1237C6.20107 27.5926 6.83696 27.856 7.5 27.856H10C10.663 27.856 11.2989 27.5926 11.7678 27.1237C12.2366 26.6549 12.5 26.019 12.5 25.356C13.084 25.3512 13.6473 25.1392 14.0896 24.7578C14.5319 24.3764 14.8244 23.8504 14.915 23.2735C14.9622 23.5487 14.9906 23.8268 15 24.106C15 24.141 14.99 24.176 14.99 24.211L25.555 26.561C25.9086 25.7784 26.4197 25.0771 27.0562 24.5007C27.6928 23.9244 28.4413 23.4853 29.255 23.211L28.265 15.306C26.9791 15.2074 25.7562 14.7091 24.7675 13.881C24.9177 13.56 24.997 13.2104 25 12.856V10.356C25 11.019 25.2634 11.6549 25.7322 12.1237C26.2011 12.5926 26.837 12.856 27.5 12.856H30C30.663 12.856 31.2989 12.5926 31.7678 12.1237C32.2366 11.6549 32.5 11.019 32.5 10.356V7.85596C32.5 7.19292 32.2366 6.55703 31.7678 6.08819C31.2989 5.61935 30.663 5.35596 30 5.35596H27.5C26.837 5.35596 26.2011 5.61935 25.7322 6.08819C25.2634 6.55703 25 7.19292 25 7.85596V10.356C24.9981 9.71725 24.7503 9.10381 24.308 8.64296C23.8658 8.1821 23.2631 7.90923 22.625 7.88096C22.786 7.07096 23.106 6.30093 23.5665 5.61544C24.027 4.92994 24.619 4.3426 25.308 3.8874C25.9971 3.43221 26.7696 3.1182 27.5808 2.96356C28.392 2.80891 29.2259 2.8167 30.0341 2.98647C30.8423 3.15624 31.6088 3.48463 32.2892 3.95262C32.9696 4.4206 33.5505 5.0189 33.9981 5.71288C34.4458 6.40685 34.7514 7.18273 34.8972 7.99559C35.0429 8.80845 35.0261 9.64216 34.8475 10.4485L46.175 15.481C46.7982 14.6142 47.6336 13.922 48.6011 13.4706C49.5685 13.0193 50.6356 12.8239 51.7003 12.9032C52.7649 12.9825 53.7913 13.3338 54.6812 13.9235C55.5711 14.5132 56.2947 15.3215 56.7827 16.271C57.2707 17.2205 57.5067 18.2794 57.4681 19.3463C57.4295 20.4131 57.1177 21.4522 56.5624 22.364C56.0071 23.2758 55.227 24.0297 54.2969 24.5536C53.3667 25.0775 52.3176 25.3538 51.25 25.356C51.1975 25.356 51.1475 25.341 51.095 25.341L46.3425 45.9385C47.3544 46.3988 48.225 47.1211 48.8642 48.0307C49.5034 48.9402 49.8879 50.0041 49.9781 51.1121C50.0683 52.2201 49.8609 53.3321 49.3772 54.3331C48.8936 55.3341 48.1513 56.1877 47.2272 56.8056C46.303 57.4236 45.2306 57.7834 44.1207 57.8479C43.0109 57.9124 41.904 57.6792 40.9145 57.1725C39.925 56.6658 39.0888 55.9039 38.4925 54.9657C37.8961 54.0275 37.5613 52.947 37.5225 51.836L19.5675 48.8435C19.2005 49.8022 18.6027 50.6556 17.827 51.328C17.0513 52.0005 16.1217 52.4711 15.1205 52.6983C14.1194 52.9254 13.0777 52.9021 12.0877 52.6303C11.0978 52.3585 10.1902 51.8466 9.44536 51.1401C8.70056 50.4336 8.14157 49.5543 7.81795 48.58C7.49433 47.6058 7.41606 46.5668 7.5901 45.5551C7.76414 44.5433 8.1851 43.5902 8.81567 42.7801C9.44624 41.97 10.2669 41.328 11.205 40.911L8.855 30.346C8.82 30.346 8.7875 30.356 8.75 30.356ZM17.5 47.856V45.356C17.5 44.6929 17.2366 44.057 16.7678 43.5882C16.2989 43.1193 15.663 42.856 15 42.856H12.5C11.837 42.856 11.2011 43.1193 10.7322 43.5882C10.2634 44.057 10 44.6929 10 45.356V47.856C10 48.519 10.2634 49.1549 10.7322 49.6237C11.2011 50.0926 11.837 50.356 12.5 50.356H15C15.663 50.356 16.2989 50.0926 16.7678 49.6237C17.2366 49.1549 17.5 48.519 17.5 47.856ZM18.96 43.1635C19.5889 44.12 19.941 45.2318 19.9775 46.376L37.9325 49.3685C38.3065 48.4078 38.913 47.5548 39.6975 46.886L33.115 35.041C32.2345 35.3386 31.297 35.428 30.376 35.3022C29.4551 35.1764 28.5759 34.8388 27.8075 34.316L18.96 43.1635ZM45 47.856H42.5C41.837 47.856 41.2011 48.1193 40.7322 48.5882C40.2634 49.057 40 49.6929 40 50.356V52.856C40 53.519 40.2634 54.1549 40.7322 54.6237C41.2011 55.0926 41.837 55.356 42.5 55.356H45C45.663 55.356 46.2989 55.0926 46.7678 54.6237C47.2366 54.1549 47.5 53.519 47.5 52.856V50.356C47.5 49.6929 47.2366 49.057 46.7678 48.5882C46.2989 48.1193 45.663 47.856 45 47.856ZM48.6575 24.776C47.7569 24.3587 46.9685 23.7332 46.3575 22.951L37.2625 27.496C37.5896 28.6272 37.5782 29.8294 37.2299 30.9543C36.8816 32.0792 36.2116 33.0775 35.3025 33.826L41.885 45.671C42.4868 45.4706 43.1158 45.3644 43.75 45.356C43.8025 45.356 43.85 45.371 43.905 45.371L48.6575 24.776ZM47.5 17.856V20.356C47.5 21.019 47.7634 21.6549 48.2322 22.1237C48.7011 22.5926 49.337 22.856 50 22.856H52.5C53.163 22.856 53.7989 22.5926 54.2678 22.1237C54.7366 21.6549 55 21.019 55 20.356V17.856C55 17.1929 54.7366 16.557 54.2678 16.0882C53.7989 15.6193 53.163 15.356 52.5 15.356H50C49.337 15.356 48.7011 15.6193 48.2322 16.0882C47.7634 16.557 47.5 17.1929 47.5 17.856ZM30.745 14.9985L31.735 22.906C32.5929 22.972 33.4278 23.2156 34.1865 23.6215C34.9453 24.0273 35.6114 24.5865 36.1425 25.2635L45.2375 20.7135C44.9532 19.7542 44.9239 18.7375 45.1525 17.7635L33.825 12.731C33.0622 13.7902 31.9869 14.584 30.75 15.001L30.745 14.9985ZM27.5 27.856V30.356C27.5 31.019 27.7634 31.6549 28.2322 32.1237C28.7011 32.5926 29.337 32.856 30 32.856H32.5C33.163 32.856 33.7989 32.5926 34.2678 32.1237C34.7366 31.6549 35 31.019 35 30.356V27.856C35 27.1929 34.7366 26.557 34.2678 26.0882C33.7989 25.6194 33.163 25.356 32.5 25.356H30C29.337 25.356 28.7011 25.6194 28.2322 26.0882C27.7634 26.557 27.5 27.1929 27.5 27.856ZM11.295 29.801L13.645 40.366C13.68 40.366 13.7125 40.356 13.75 40.356C14.975 40.3554 16.1728 40.7172 17.1925 41.396L26.0425 32.546C25.3635 31.5271 25.0008 30.3303 25 29.106C25 29.0685 25.01 29.036 25.01 29.001L14.445 26.651C13.8158 28.051 12.6951 29.1717 11.295 29.801ZM4.18 20.446L2.41 18.676L6.04 15.051C5.59639 14.3851 5.28569 13.6398 5.125 12.856H0V10.356H5.125C5.286 9.57303 5.59669 8.82856 6.04 8.16346L2.41 4.53596L4.18 2.76596L7.805 6.39596C8.47086 5.95235 9.2162 5.64165 10 5.48096V0.355957H12.5V5.48096C13.2829 5.64195 14.0274 5.95264 14.6925 6.39596L18.32 2.76596L20.09 4.53596L16.46 8.16096C16.9036 8.82682 17.2143 9.57216 17.375 10.356H22.5V12.856H17.375C17.214 13.6389 16.9033 14.3833 16.46 15.0485L20.09 18.676L18.32 20.446L14.695 16.816C14.0292 17.2596 13.2838 17.5703 12.5 17.731V22.856H10V17.731C9.21705 17.57 8.47258 17.2593 7.8075 16.816L4.18 20.446ZM7.5 11.606C7.5 12.3476 7.71993 13.0727 8.13199 13.6893C8.54404 14.306 9.12971 14.7867 9.81494 15.0705C10.5002 15.3543 11.2542 15.4286 11.9816 15.2839C12.709 15.1392 13.3772 14.7821 13.9017 14.2576C14.4261 13.7332 14.7833 13.065 14.9279 12.3375C15.0726 11.6101 14.9984 10.8561 14.7145 10.1709C14.4307 9.48567 13.9501 8.9 13.3334 8.48795C12.7167 8.07589 11.9917 7.85596 11.25 7.85596C10.2558 7.85728 9.30279 8.25279 8.59981 8.95577C7.89684 9.65874 7.50132 10.6118 7.5 11.606Z"
                      fill="url(#paint0_linear_1292_15212)"
                    />
                  </g>
                  <defs>
                    <linearGradient
                      id="paint0_linear_1292_15212"
                      x1="28.7361"
                      y1="0.355957"
                      x2="28.7361"
                      y2="57.8584"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#6583FF" />
                      <stop offset="1" stopColor="#6CC2D5" />
                    </linearGradient>
                    <clipPath id="clip0_1292_15212">
                      <rect
                        width="60"
                        height="60"
                        fill="white"
                        transform="translate(0 0.355957)"
                      />
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Envision Section */}
      {/* <section id="envision" className="py-20 bg-gray-50">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row-reverse items-center gap-10">
            <div className="flex-shrink-0 w-full md:w-[45%]">
              <img
                src={Envision}
                alt="Vision"
                className="rounded-xl shadow-lg w-full object-cover transform transition-transform hover:scale-[1.02]"
              />
            </div>
            <div className="w-full md:w-[55%]">
              <div className='mb-8'>
                <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Future</span>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">We Envision</h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  innovative sciences and technologies effectively integrating into the design, construction, operation, and maintenance of our clients projects; promote stewardship and sustainability in all phases of our work product and employment environment. We envision a mutually beneficial connection with our clients and employees based on trust, ethics, and value driven service.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Experts Section */}
      {/* <section id="experts" className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center gap-10">
            <div className="flex-shrink-0 w-full md:w-[45%]">
              <img
                src={Accompolishments}
                alt="Experts"
                className="rounded-xl shadow-lg w-full object-cover transform transition-transform hover:scale-[1.02]"
              />
            </div>
            <div className="w-full md:w-[55%]">
              <div className='mb-8'>
                <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Team</span>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Accomplished Experts</h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  International Responder is uniquely prepared to support any organization for any situation. We are subject matter experts (SMEs) for emergency response and public health with over 50 years of combined experience.
                </p>
                <Link
                  to="https://www.linkedin.com/posts/james-mullikin-42a30211_we-had-a-wonderful-kickoff-with-the-insight-activity-7129517543630761987-_31L/?trk=public_profile_share_view"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-4 inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                >
                  Read about our recent trip to DC
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Portfolio Section */}
      {/* <section id="portfolio" className="py-20 bg-gray-50">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row-reverse items-center gap-10">
            <div className="flex-shrink-0 w-full md:w-[45%]">
              <img
                src={Portfolio}
                alt="Portfolio"
                className="rounded-xl shadow-lg w-full object-cover transform transition-transform hover:scale-[1.02]"
              />
            </div>
            <div className="w-full md:w-[55%]">
              <div className='mb-8'>
                <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Work</span>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Diverse Portfolio</h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  We separate ourselves from other firms with the diversity of our clients, projects, and professional expertise. Our successful growth is a result of our ability to offer our services at roughly half the cost of our competitors.
                </p>
              </div>
              <Link
                to={'/solutions'}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors group"
              >
                Explore Our Services
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </section> */}

      {/* Clients Section */}
      {/* <section className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center gap-10">
            <div className="flex-shrink-0 w-full md:w-[45%]">
              <img
                src={Clients}
                alt="Clients"
                className="rounded-xl shadow-lg w-full object-cover transform transition-transform hover:scale-[1.02]"
              />
            </div>
            <div className="w-full md:w-[55%]">
              <div className='mb-8'>
                <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Clients</span>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Organizations</h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  Trained & Prepared by IRS. Training and Exercises prepare you for a wide variety of emergencies and give you the tools to respond proactively. Each one of these clients are ready for whatever disaster strikes next.
                </p>
              </div>
              <Link
                to={'/contact'}
                className="inline-flex items-center px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors group"
              >
                Work With us
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </section> */}

      {/* Values Section */}
      {/* <section id="values" className="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Foundation</span>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">Driving innovation in public health emergency response through our unwavering commitment to excellence</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-gray-100"
              >
                <div className="w-[100%] mb-6">
                  <img className='w-[100%] h-60' src={value.image} alt={value.title} />
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-900">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section> */}

      {/* Story Section */}
      {/* <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-24 items-center">
            <div>
              <h2 className="text-3xl sm:text-4xl font-bold mb-6 text-gray-900">Accomplishments</h2>
              <p className="text-gray-600 mb-6 text-lg">
                Contracted by DC Government to instruct HSEEP Compliant Full Scale and Table Top Exercises, Weaponized Anthrax Full Scale Exercise, After Action Reporting, Data Analytics, Mass Trauma Planning and National Special Security Events Strategic Support
              </p>
              <p className="text-gray-600 mb-6 text-lg">
                Contracted by FEMA  to program executive leadership education as well as training academy logistics and student services
              </p>
              <p className="text-gray-600 mb-6 text-lg" >Developed Virtual Instructor led E-Learning Training Modules for Leidos, synchronous and asynchronous. Provided subject matter experts and training instructors for response necessities including but not limited to CBRNE Training, Public Health Emergency Response, law enforcement, hazardous materials and live agents. Infrastructure support, developing audio/visual studios</p>
              <p className="text-gray-600 mb-6 text-lg" >Contracted by eHealth Africa for NIMS and ICS Training. Logistics Coordination for Ebola response as well as inter-agency coordination for disaster support</p>
              <p className="text-gray-600 mb-6 text-lg" >Provided Public Health System Disaster Response Support for Puerto Rico Department of Health.</p>
            </div>
            <div className="flex flex-col space-y-6 justify-center">
              <img
                src={Acc1}
                alt="Office meeting"
                className="rounded-xl shadow-2xl w-full max-w-md transform transition-transform hover:scale-[1.02]"
              />
              <img
                src={Acc2}
                alt="Office meeting"
                className="rounded-xl shadow-2xl w-full max-w-md transform transition-transform hover:scale-[1.02]"
              />
            </div>
          </div>
        </div>
      </section> */}
      {/* <section className='py-20 bg-white' >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 text-center mb-8">Alliances</h2>
          <div className='flex flex-wrap md:flex-row  justify-around items-center'>
            <img src={All1} alt="Alliance1" />
            <img src={All2} alt="Alliance2" />
            <img src={All3} alt="Alliance3" />
            <img src={All4} alt="Alliance4" />
            <img src={All5} alt="Alliance5" />
          </div>
        </div>
      </section> */}

      {/* <section className='py-20 bg-[#F9FAFB] mb-10'>
        <div className=" max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center px-4 sm:px-6 lg:px-8">
          <div>
            <h2 className='text-3xl sm:text-4xl font-bold text-gray-900 text-center mb-8'>Additional Info</h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <span className="font-semibold text-gray-700">EIN</span>
                <p className="text-gray-900">47-4010088</p>
              </div>
              <div className="space-y-1">
                <span className="font-semibold text-gray-700">DUNS</span>
                <p className="text-gray-900">079943388</p>
              </div>
              <div className="space-y-1">
                <span className="font-semibold text-gray-700">CAGE</span>
                <p className="text-gray-900">7YTM4</p>
              </div>
              <div className="space-y-1">
                <span className="font-semibold text-gray-700">Licensed to Operate In</span>
                <p className="text-gray-900">
                  <span className="block">Alabama</span>
                  <span className="block">Washington D.C</span>
                  <span className="block">Maryland</span>
                  <span className="block">West Virginia</span>
                </p>
              </div>
            </div>
          </div>
          <div>
            <h2 className='text-3xl sm:text-4xl font-bold text-gray-900 text-center mb-8'>Certifications</h2>
            <img src={Certif1} alt="" />
          </div>
        </div>
      </section> */}

      {/* CTA Section */}
      {/* <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-6">Ready to Transform Your Emergency Response?</h2>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Partner with International Responder Systems and experience the difference of working with true experts in public health and emergency response.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/contact"
              className="px-8 py-3 bg-white text-blue-600 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              Get in Touch
            </Link>
            <Link
              to="/solutions"
              className="px-8 py-3 border border-white text-white rounded-lg font-medium hover:bg-white/10 transition-colors"
            >
              Explore Solutions
            </Link>
          </div>
        </div>
      </section> */}
    </div>
  );
}