{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hcaptcha/react-hcaptcha": "^1.12.0", "@headlessui/react": "^2.2.1", "@hookform/resolvers": "^5.0.1", "@react-pdf/renderer": "^3.1.12", "@supabase/supabase-js": "^2.39.7", "@tailwindcss/line-clamp": "^0.4.4", "@types/yup": "^0.29.14", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lucide-react": "^0.344.0", "prism-react-renderer": "^2.4.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.55.0", "react-markdown": "^9.0.3", "react-markdown-editor-lite": "^1.3.4", "react-router-dom": "^6.22.2", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-underline": "^1.0.5", "swiper": "^11.2.8", "vite-react-typescript-starter": "file:", "yup": "^1.6.1", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/lodash": "^4.17.16", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-google-recaptcha": "^2.1.9", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "supabase": "^2.20.12", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}