import { serve } from 'https://deno.land/std/http/server.ts';
import nodemailer from 'npm:nodemailer';
serve(async (req)=>{
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': '*',
    'Access-Control-Allow-Headers': '*',
    'Content-Type': 'application/json'
  };
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers
    });
  }
  try {
    const body = await req.json();
    if (!body) {
      throw new Error('Request body is empty');
    }
    // Validate required fields
    if (!body.subject || !body.html) {
      throw new Error('Missing required fields (to, subject, html)');
    }
    const { to, subject, html } = body;
    const transporter = nodemailer.createTransport({
      host: 'smtp.office365.com',
      port: 587,
      secure: false,
      auth: {
        user: Deno.env.get('SMTP_USERNAME'),
        pass: Deno.env.get('SMTP_PASSWORD')
      }
    });
    // Verify SMTP connection
    const isValid = await transporter.verify();
    console.log('SMTP verified:', isValid);
    await transporter.sendMail({
      from: Deno.env.get('SMTP_USERNAME'),
      to: to.trim()===''? Deno.env.get('CONTACT_FORM_ADMIN'): to,
      subject,
      html
    });
    return new Response(JSON.stringify({
      success: true
    }), {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({
      error: 'Failed to process request',
      details: error.message
    }), {
      status: 400,
      headers
    });
  }
});
