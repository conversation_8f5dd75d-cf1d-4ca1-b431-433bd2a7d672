-- Create webinars table
CREATE TABLE webinars (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text NOT NULL,
  date text NOT NULL, -- Store as ISO string
  time text, -- For upcoming webinars
  duration text, -- For recorded webinars
  speaker text NOT NULL,
  image_url text NOT NULL,
  recording_url text, -- NULL for upcoming webinars
  status text NOT NULL CHECK (status IN ('upcoming', 'recorded')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create webinar_registrations table
CREATE TABLE webinar_registrations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id uuid NOT NULL REFERENCES webinars(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE SET NULL, -- Optional, for logged-in users
  full_name text NOT NULL,
  email text NOT NULL,
  phone text DEFAULT NULL,
  company text DEFAULT NULL,
  captcha_token text,
  registered_at timestamptz DEFAULT now(),
  attended boolean DEFAULT false,
  CONSTRAINT webinar_registrations_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Add RLS policies
ALTER TABLE webinars ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_registrations ENABLE ROW LEVEL SECURITY;

-- Webinars policies
CREATE POLICY "Anyone can view webinars"
ON webinars FOR SELECT
TO public
USING (true);

CREATE POLICY "Only admins can insert webinars"
ON webinars FOR INSERT
TO authenticated
WITH CHECK (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

CREATE POLICY "Only admins can update webinars"
ON webinars FOR UPDATE
TO authenticated
USING (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

CREATE POLICY "Only admins can delete webinars"
ON webinars FOR DELETE
TO authenticated
USING (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

-- Webinar registrations policies
CREATE POLICY "Anyone can register for webinars"
ON webinar_registrations FOR INSERT
TO public
WITH CHECK (true);

CREATE POLICY "Users can view their own registrations"
ON webinar_registrations FOR SELECT
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Admins can view all registrations"
ON webinar_registrations FOR SELECT
TO authenticated
USING (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

CREATE POLICY "Admins can update registrations"
ON webinar_registrations FOR UPDATE
TO authenticated
USING (EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id = auth.uid()
  AND profiles.role = 'admin'
));

-- Create function to update webinar timestamps
CREATE OR REPLACE FUNCTION update_webinar_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_webinar_timestamp
  BEFORE UPDATE ON webinars
  FOR EACH ROW
  EXECUTE FUNCTION update_webinar_timestamp();

-- Grant permissions to anon and authenticated roles
GRANT SELECT ON TABLE webinars TO anon, authenticated;
GRANT SELECT, INSERT ON TABLE webinar_registrations TO anon, authenticated;
GRANT ALL ON TABLE webinars TO authenticated;
GRANT ALL ON TABLE webinar_registrations TO authenticated;
