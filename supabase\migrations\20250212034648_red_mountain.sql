BEGIN;

-- First drop all objects safely
DO $$
BEGIN
    -- Drop triggers only if their tables exist
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'blog_posts') THEN
        DROP TRIGGER IF EXISTS update_blog_posts_updated_at ON blog_posts;
    END IF;
    
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'site_settings') THEN
        DROP TRIGGER IF EXISTS update_site_settings_updated_at ON site_settings;
    END IF;
    
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'blog_comments') THEN
        DROP TRIGGER IF EXISTS update_blog_comments_updated_at ON blog_comments;
    END IF;
    
    -- Drop policies only if their tables exist
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'blog_posts') THEN
        DROP POLICY IF EXISTS "Anyone can read published blog posts" ON blog_posts;
        DROP POLICY IF EXISTS "Admins can manage blog posts" ON blog_posts;
    END IF;
    
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'blog_categories') THEN
        DROP POLICY IF EXISTS "Anyone can read blog categories" ON blog_categories;
        DROP POLICY IF EXISTS "Admins can manage blog categories" ON blog_categories;
    END IF;
    
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'blog_tags') THEN
        DROP POLICY IF EXISTS "Anyone can read blog tags" ON blog_tags;
        DROP POLICY IF EXISTS "Admins can manage blog tags" ON blog_tags;
    END IF;
    
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'blog_posts_categories') THEN
        DROP POLICY IF EXISTS "Anyone can read blog posts categories" ON blog_posts_categories;
        DROP POLICY IF EXISTS "Admins can manage blog posts categories" ON blog_posts_categories;
    END IF;
    
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'blog_posts_tags') THEN
        DROP POLICY IF EXISTS "Anyone can read blog posts tags" ON blog_posts_tags;
        DROP POLICY IF EXISTS "Admins can manage blog posts tags" ON blog_posts_tags;
    END IF;
    
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'site_settings') THEN
        DROP POLICY IF EXISTS "Anyone can read site settings" ON site_settings;
        DROP POLICY IF EXISTS "Admins can manage site settings" ON site_settings;
    END IF;
    
    -- Then drop tables in reverse dependency order
    DROP TABLE IF EXISTS site_settings CASCADE;
    DROP TABLE IF EXISTS blog_posts_tags CASCADE;
    DROP TABLE IF EXISTS blog_tags CASCADE;
    DROP TABLE IF EXISTS blog_posts_categories CASCADE;
    DROP TABLE IF EXISTS blog_categories CASCADE;
    DROP TABLE IF EXISTS blog_comments CASCADE;  -- Add this line
    DROP TABLE IF EXISTS blog_posts CASCADE;
    
    -- Finally drop the function
    DROP FUNCTION IF EXISTS update_updated_at_column CASCADE;
END
$$;

-- Now recreate everything in proper order

-- 1. First create tables (simplified version)
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email TEXT,
  role TEXT NOT NULL DEFAULT 'user'
);

CREATE TABLE blog_posts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  content text NOT NULL,
  excerpt text,
  status text NOT NULL CHECK (status IN ('draft', 'published', 'archived')),
  author_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
  featured_image text,
  slug text UNIQUE,
  meta_description text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 2. Simplify admin policies using auth.role()
-- Add this policy if it doesn't exist
CREATE POLICY "Enable read access for all users" 
ON blog_posts FOR SELECT
USING (status = 'published');

-- And ensure admins have full access
-- After creating the blog_posts table, add these:
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;

-- Grant basic permissions
GRANT SELECT ON TABLE blog_posts TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON TABLE blog_posts TO authenticated;

-- Add policies for public read access
-- Replace the existing blog_posts policies with these updated ones:

-- Public can read published posts
CREATE POLICY "Public can read published posts" 
ON blog_posts FOR SELECT
TO anon, authenticated
USING (status = 'published');

-- Users can create posts (modified WITH CHECK clause)
CREATE POLICY "Users can create posts"
ON blog_posts FOR INSERT
TO authenticated
WITH CHECK (true);  -- Allow any authenticated user to create posts

-- Users can manage their own posts (for updates/deletes)
CREATE POLICY "Users can manage their own posts"
ON blog_posts FOR ALL
TO authenticated
USING (author_id = auth.uid())
WITH CHECK (author_id = auth.uid());

-- Admins can manage all posts
CREATE POLICY "Admins can manage all blog posts"
ON blog_posts FOR ALL
TO authenticated
USING (auth.role() = 'admin')
WITH CHECK (auth.role() = 'admin');

-- 3. Remove duplicate function definition (keep only one)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- 4. Remove duplicate policies section (commented out in original)
/* Remove this:
CREATE POLICY "Anyone can read site settings"
  ON site_settings
  FOR SELECT
  USING (true);
*/

CREATE TABLE blog_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  slug text UNIQUE,
  description text,
  created_at timestamptz DEFAULT now()
);

CREATE TABLE blog_posts_categories (
  post_id uuid REFERENCES blog_posts(id) ON DELETE CASCADE,
  category_id uuid REFERENCES blog_categories(id) ON DELETE CASCADE,
  PRIMARY KEY (post_id, category_id)
);

CREATE TABLE blog_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  slug text UNIQUE,
  created_at timestamptz DEFAULT now()
);

CREATE TABLE blog_posts_tags (
  post_id uuid REFERENCES blog_posts(id) ON DELETE CASCADE,
  tag_id uuid REFERENCES blog_tags(id) ON DELETE CASCADE,
  PRIMARY KEY (post_id, tag_id)
);

CREATE TABLE site_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  key text NOT NULL UNIQUE,
  value jsonb NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

CREATE TABLE blog_comments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id uuid REFERENCES blog_posts(id) ON DELETE CASCADE,
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  content text NOT NULL,
  status text NOT NULL CHECK (status IN ('pending', 'approved', 'rejected')) DEFAULT 'pending',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Finally insert data
INSERT INTO site_settings (key, value) VALUES
  ('general', '{
    "siteName": "International Responder Systems",
    "siteDescription": "Healthcare Emergency Response Solutions",
    "contactEmail": "<EMAIL>",
    "socialLinks": {
      "linkedin": "https://www.linkedin.com/company/international-responder-systems",
      "twitter": "https://twitter.com/intrespondersys",
      "facebook": "https://www.facebook.com/InternationalResponderSystems"
    }
  }'::jsonb);

INSERT INTO blog_categories (name, slug, description) VALUES
  ('Emergency Response', 'emergency-response', 'Articles about emergency response and preparedness'),
  ('Healthcare', 'healthcare', 'Healthcare industry news and insights'),
  ('Technology', 'technology', 'Technology trends and innovations'),
  ('Grant Management', 'grant-management', 'Grant management tips and best practices');

-- Enable RLS for comments
ALTER TABLE blog_comments ENABLE ROW LEVEL SECURITY;

-- Grant permissions to both roles
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE blog_comments TO anon, authenticated;

-- Add policies for comments
CREATE POLICY "Public can read approved comments"
  ON blog_comments
  FOR SELECT
  USING (status = 'approved');

CREATE POLICY "Users can manage their own comments"
  ON blog_comments
  FOR ALL
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Admin can change comment status"
  ON blog_comments
  FOR UPDATE
  TO authenticated
  USING (
    (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin'
  )
  WITH CHECK (
    (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin'
  );

  CREATE POLICY "Enable delete for admin users" 
ON blog_posts FOR DELETE
TO authenticated
USING (auth.role() = 'admin');

CREATE POLICY "Enable delete for authors" 
ON blog_posts FOR DELETE
USING (auth.uid() = author_id);

-- Add this policy if it doesn't exist
CREATE POLICY "Enable read access for all users" 
ON storage.objects FOR SELECT
USING (bucket_id = 'blog-images');

CREATE POLICY "Admins can manage all comments"
  ON blog_comments
  FOR ALL
  TO authenticated
  USING (auth.role() = 'admin');

-- And ensure admins have full access
CREATE POLICY "Admins can manage blog images"
ON storage.objects FOR ALL
TO authenticated
USING (auth.role() = 'admin')
WITH CHECK (bucket_id = 'blog-images');

CREATE POLICY "Admins can read all comments"
ON blog_comments
FOR SELECT
USING (
  (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin'
);

-- Remove this duplicate section completely:
/*
-- Create policies for site_settings
CREATE POLICY "Anyone can read site settings"
  ON site_settings
  FOR SELECT
  USING (true);

CREATE POLICY "Admins can manage site settings"
  ON site_settings
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));
*/

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ language 'plpgsql';

COMMIT;

