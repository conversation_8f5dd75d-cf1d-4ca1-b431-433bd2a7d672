import {
  <PERSON><PERSON><PERSON>,
  Dollar<PERSON>ign,
  <PERSON><PERSON>s,
  TrendingUp,
  BarChart,
  ArrowRight,
  Sparkles,
  Zap,
  Target,
  CheckCircle,
} from "lucide-react";
import { useState, useEffect } from "react";
import Grantready1 from "../../assets/home/<USER>";

function Works() {
  const [activeStep, setActiveStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % 5);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const steps = [
    {
      id: 1,
      title: "Planning Phase",
      description:
        "Set up capability planning guides and create detailed work plans to align with grant objectives",
      icon: PieChart,
      color: "bg-gradient-to-br from-blue-400 to-blue-600",
      iconColor: "text-white",
      glowColor: "shadow-blue-500/50",
      bgPattern: "bg-blue-50",
      accentColor: "border-blue-200",
      stats: "95% Accuracy",
    },
    {
      id: 2,
      title: "Budget Allocation",
      description:
        "Develop detailed spend plans and budgets that reflect program priorities and compliance requirements",
      icon: DollarSign,
      color: "bg-gradient-to-br from-purple-400 to-purple-600",
      iconColor: "text-white",
      glowColor: "shadow-purple-500/50",
      bgPattern: "bg-purple-50",
      accentColor: "border-purple-200",
      stats: "$2M+ Managed",
    },
    {
      id: 3,
      title: "Implementation",
      description:
        "Execute work plans and track progress with easy-to-use dashboards and monitoring tools",
      icon: Settings,
      color: "bg-gradient-to-br from-indigo-400 to-indigo-600",
      iconColor: "text-white",
      glowColor: "shadow-indigo-500/50",
      bgPattern: "bg-indigo-50",
      accentColor: "border-indigo-200",
      stats: "Real-time Updates",
    },
    {
      id: 4,
      title: "Financial Tracking",
      description:
        "Manage invoices, track expenditures, and ensure proper allocation of funds across all programs",
      icon: TrendingUp,
      color: "bg-gradient-to-br from-emerald-400 to-emerald-600",
      iconColor: "text-white",
      glowColor: "shadow-emerald-500/50",
      bgPattern: "bg-emerald-50",
      accentColor: "border-emerald-200",
      stats: "100% Compliance",
    },
    {
      id: 5,
      title: "Reporting & Analysis",
      description:
        "Generate comprehensive reports and analytics to demonstrate impact and ensure grant compliance",
      icon: BarChart,
      color: "bg-gradient-to-br from-orange-400 to-orange-600",
      iconColor: "text-white",
      glowColor: "shadow-orange-500/50",
      bgPattern: "bg-orange-50",
      accentColor: "border-orange-200",
      stats: "Instant Reports",
    },
  ];

  return (
    <section className="py-8 sm:py-12 md:py-16 lg:py-20 relative overflow-hidden">
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header with Enhanced Animation */}
        <div
          className={`text-center mb-8 sm:mb-12 md:mb-16 transition-all duration-1000 ${
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
          }`}
        >
          {/* <div className="inline-flex items-center gap-1 sm:gap-2 mb-3 sm:mb-4">
              <div className="w-6 sm:w-8 md:w-12 h-0.5 bg-gradient-to-r from-transparent to-teal-500"></div>
              <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-teal-500 animate-pulse" />
              <div className="w-6 sm:w-8 md:w-12 h-0.5 bg-gradient-to-l from-transparent to-teal-500"></div>
            </div> */}
          <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">
            How{" "}
            <span className="bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent">
              GrantReady™
            </span>{" "}
            Works
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-black max-w-4xl mx-auto leading-relaxed px-4">
            Our platform supports the entire grant lifecycle from initial
            planning to final reporting ensuring compliance and efficiency
          </p>

          {/* Progress Indicator */}
          <div className="hidden justify-center mt-6 sm:mt-8">
            <div className="flex space-x-1.5 sm:space-x-2">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full transition-all duration-500 ${
                    index === activeStep
                      ? "bg-teal-500 w-6 sm:w-8"
                      : "bg-gray-300"
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Process Flow */}
        <div className="relative">
          {/* Connecting Line for Desktop */}
          <div className="hidden lg:block absolute top-8 md:top-10 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>

          {/* Desktop Layout */}
          <div className="hidden lg:flex items-start justify-between relative">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center group">
                {/* Step Container */}
                <div
                  className={`flex flex-col items-center transition-all duration-700 ${
                    index === activeStep
                      ? "transform scale-105 lg:scale-110"
                      : ""
                  }`}
                >
                  {/* Animated Circle */}
                  <div className="relative mb-4 md:mb-6">
                    <div
                      className={`w-16 h-16 md:w-20 md:h-20 ${
                        step.color
                      } rounded-full flex items-center justify-center shadow-xl ${
                        step.glowColor
                      } transition-all duration-500 group-hover:scale-110 ${
                        index === activeStep ? "animate-pulse shadow-2xl" : ""
                      }`}
                    >
                      <step.icon
                        className={`w-8 h-8 md:w-10 md:h-10 ${step.iconColor} transition-transform duration-300 group-hover:scale-110`}
                      />
                    </div>

                    {/* Floating Stats Badge */}
                    <div
                      className={`absolute -top-1 -right-1 md:-top-2 md:-right-2 px-1.5 py-0.5 md:px-2 md:py-1 ${
                        step.bgPattern
                      } ${
                        step.accentColor
                      } border rounded-full text-xs font-semibold transition-all duration-500 ${
                        index === activeStep
                          ? "opacity-100 scale-100"
                          : "opacity-0 scale-75"
                      }`}
                    >
                      {step.stats}
                    </div>

                    {/* Ripple Effect */}
                    {index === activeStep && (
                      <div className="absolute inset-0 rounded-full border-2 border-teal-400 animate-ping opacity-30"></div>
                    )}
                  </div>

                  {/* Content */}
                  <div
                    className={`text-center max-w-40 md:max-w-48 transition-all duration-500 ${
                      index === activeStep
                        ? "transform translate-y-0"
                        : "transform translate-y-2"
                    }`}
                  >
                    <h3 className="text-base md:text-lg font-bold text-gray-900 mb-2 md:mb-3 group-hover:text-teal-600 transition-colors">
                      {step.title}
                    </h3>
                    <p className="text-xs md:text-sm text-gray-600 leading-relaxed group-hover:text-gray-800 transition-colors">
                      {step.description}
                    </p>

                    {/* Interactive Indicator */}
                    <div
                      className={`mt-2 md:mt-3 flex justify-center transition-all duration-300 ${
                        index === activeStep ? "opacity-100" : "opacity-0"
                      }`}
                    >
                      <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-teal-500" />
                    </div>
                  </div>
                </div>

                {/* Enhanced Arrow (except for last item) */}
                {index < steps.length - 1 && (
                  <div className="mx-4 md:mx-6 lg:mx-8 flex flex-col items-center">
                    <ArrowRight
                      className={`w-6 h-6 md:w-8 md:h-8 text-teal-500 transition-all duration-500 ${
                        index === activeStep ? "animate-pulse scale-110" : ""
                      }`}
                    />
                    <div className="w-12 md:w-16 h-0.5 bg-gradient-to-r from-teal-200 to-blue-200 mt-1 md:mt-2 rounded-full"></div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Mobile/Tablet Layout */}
          <div className="lg:hidden space-y-6 sm:space-y-8">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`transition-all duration-500 ${
                  isVisible
                    ? "opacity-100 translate-x-0"
                    : "opacity-0 translate-x-10"
                }`}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <div className="relative">
                  {/* Connecting Line */}
                  {index < steps.length - 1 && (
                    <div className="absolute left-6 sm:left-8 top-14 sm:top-16 w-0.5 h-12 sm:h-16 bg-gradient-to-b from-teal-300 to-transparent"></div>
                  )}

                  <div className="flex items-start space-x-4 sm:space-x-6 group">
                    {/* Enhanced Mobile Circle */}
                    <div className="relative">
                      <div
                        className={`w-12 h-12 sm:w-16 sm:h-16 ${step.color} rounded-full flex items-center justify-center flex-shrink-0 shadow-xl ${step.glowColor} transition-all duration-300 group-hover:scale-110`}
                      >
                        <step.icon
                          className={`w-6 h-6 sm:w-8 sm:h-8 ${step.iconColor}`}
                        />
                      </div>

                      {/* Mobile Stats Badge */}
                      <div
                        className={`absolute -top-1 -right-1 px-1 py-0.5 sm:px-1.5 sm:py-0.5 ${step.bgPattern} ${step.accentColor} border rounded-full text-xs font-semibold`}
                      >
                        {step.id}
                      </div>
                    </div>

                    {/* Enhanced Content */}
                    <div className="flex-1 pt-1 sm:pt-2">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-2">
                        <h3 className="text-lg sm:text-xl font-bold text-gray-900 group-hover:text-teal-600 transition-colors">
                          {step.title}
                        </h3>
                        <div
                          className={`px-2 py-1 ${step.bgPattern} ${step.accentColor} border rounded-full text-xs font-medium self-start sm:self-auto`}
                        >
                          {step.stats}
                        </div>
                      </div>
                      <p className="text-sm sm:text-base text-gray-600 leading-relaxed group-hover:text-gray-800 transition-colors">
                        {step.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Interactive Controls */}
          <div className="mt-4 sm:mt-8 flex justify-center">
            <div className="flex space-x-2 sm:space-x-3 md:space-x-4">
              {steps.map((step, index) => (
                <button
                  key={step.id}
                  onClick={() => setActiveStep(index)}
                  className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                    index === activeStep
                      ? `${step.color} shadow-lg scale-110`
                      : "bg-gray-200 hover:bg-gray-300"
                  }`}
                >
                  <step.icon
                    className={`w-5 h-5 sm:w-6 sm:h-6 ${
                      index === activeStep ? step.iconColor : "text-gray-600"
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Image Section */}
        <div className="mx-auto w-full max-w-7xl mt-0 px-4 sm:px-6">
          <div className="shadow-lg rounded-lg overflow-hidden">
            <img
              className="w-full h-auto object-cover"
              src={Grantready1}
              alt="GrantReady platform overview"
            />
          </div>
        </div>
        <div className="border max-w-6xl mx-auto hidden items-center justify-center md:mb-6 md:mt-2 p-4 rounded-8">
          <p className="text-sm text-center uppercase sm:text-base md:text-lg text-black font-medium mx-auto leading-relaxed px-2 sm:px-0">
            {/* @augment , this place must have a variable that contain the content of this p elements.
            so we we enable the the admin to insert and edit this content of this elemt from the admin panel. */}
            # under dev
          </p>
        </div>
      </div>
    </section>
  );
}

export default Works;
