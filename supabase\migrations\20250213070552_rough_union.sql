-- Drop existing foreign key constraints if they exist
DO $$ 
BEGIN
  IF EXISTS (
    SELECT 1 
    FROM information_schema.table_constraints 
    WHERE constraint_name = 'support_tickets_user_id_fkey'
  ) THEN
    ALTER TABLE support_tickets DROP CONSTRAINT support_tickets_user_id_fkey;
  END IF;
END $$;

-- Add proper foreign key constraints
ALTER TABLE support_tickets
ADD CONSTRAINT support_tickets_user_id_fkey 
FOREIGN KEY (user_id) 
REFERENCES profiles(id) 
ON DELETE CASCADE;

-- Create function to get ticket with user info
CREATE OR REPLACE FUNCTION get_ticket_with_user_info(ticket_row support_tickets)
RETURNS json
LANGUAGE sql
STABLE
AS $$
  SELECT json_build_object(
    'id', ticket_row.id,
    'title', ticket_row.title,
    'description', ticket_row.description,
    'status', ticket_row.status,
    'priority', ticket_row.priority,
    'category', ticket_row.category,
    'contact_name', ticket_row.contact_name,
    'contact_phone', ticket_row.contact_phone,
    'resolution_comment', ticket_row.resolution_comment,
    'resolved_by', ticket_row.resolved_by,
    'resolved_at', ticket_row.resolved_at,
    'created_at', ticket_row.created_at,
    'updated_at', ticket_row.updated_at,
    'user', (
      SELECT json_build_object(
        'email', p.email,
        'full_name', p.full_name,
        'phone', p.phone
      )
      FROM profiles p
      WHERE p.id = ticket_row.user_id
    )
  );
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_ticket_with_user_info TO authenticated;