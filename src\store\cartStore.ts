import { create } from 'zustand';
import { supabase } from '../lib/supabase';

interface CartItem {
  product_id: string;
  quantity: number;
  billing_period?: 'monthly' | 'yearly';
}

interface ProductDetails {
  id: string;
  name: string;
  price: number;
  stripe_monthly_price_id?: string;
  stripe_yearly_price_id?: string;
  description?: string;
  image_url?: string;
  category?: string;
  monthly_discount?: number;
  yearly_discount?: number;
}

interface CartItemWithDetails extends CartItem {
  product?: ProductDetails;
}

interface CartState {
  items: CartItem[];
  itemsWithDetails: CartItemWithDetails[];
  loading: boolean;
  error: string | null;
  addItem: (productId: string, billingPeriod?: 'monthly' | 'yearly') => Promise<void>;
  removeItem: (productId: string, billingPeriod?: 'monthly' | 'yearly') => Promise<void>;
  updateQuantity: (productId: string, quantity: number, billingPeriod?: 'monthly' | 'yearly') => Promise<void>;
  clearCart: () => Promise<void>;
  fetchCart: (userId: string) => Promise<void>;
  syncCart: () => Promise<void>;
  getCartWithProductDetails: () => Promise<CartItemWithDetails[]>;
}

export const useCartStore = create<CartState>((set, get) => ({
  items: [],
  itemsWithDetails: [],
  loading: false,
  error: null,

  fetchCart: async (userId) => {
    set({ loading: true, error: null });
    try {
      const { data, error } = await supabase
        .from('user_carts')
        .select('items')
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      set({ items: data?.items || [] });

      // Also fetch product details for the cart items
      get().getCartWithProductDetails();
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : 'An unknown error occurred' });
    } finally {
      set({ loading: false });
    }
  },

  // Get cart with product details for display purposes
  getCartWithProductDetails: async () => {
    const state = get();
    const items = state.items;

    if (items.length === 0) {
      return [];
    }

    try {
      // Get all product IDs from cart
      const productIds = items.map(item => item.product_id);

      // Fetch product details for all items in one query
      const { data: products, error } = await supabase
        .from('products')
        .select('*')
        .in('id', productIds);

      if (error) throw error;

      // Map cart items with their product details
      const itemsWithDetails = items.map(item => {
        const product = products?.find(p => p.id === item.product_id);
        return {
          ...item,
          product
        };
      });

      set({ itemsWithDetails });
      return itemsWithDetails;
    } catch (error) {
      console.error('Error fetching product details:', error);
      return items.map(item => ({ ...item, product: undefined }));
    }
  },

  addItem: async (productId, billingPeriod = 'monthly') => {
    console.log('Adding item with product ID:', productId, 'billing period:', billingPeriod);
    set({ loading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');
      console.log('User authenticated:', user.id);

      // Get product details to verify it exists
      console.log('Checking if product exists with ID:', productId);
      const { data: product, error: productError } = await supabase
        .from('products')
        .select('id, name')
        .eq('id', productId)
        .single();

      if (productError) {
        console.error('Product not found error:', productError);
        throw new Error('Product not found');
      }

      console.log('Product found:', product);

      const { data: cartData } = await supabase
        .from('user_carts')
        .select('items')
        .eq('user_id', user.id)
        .single();

      const existingItems = cartData?.items || [];
      // Find item with same product ID and billing period
      const exactMatchIndex = existingItems.findIndex((i: CartItem) =>
        i.product_id === productId && i.billing_period === billingPeriod
      );

      // Find item with same product ID but different billing period
      const sameProductDifferentPeriodIndex = existingItems.findIndex((i: CartItem) =>
        i.product_id === productId && i.billing_period !== billingPeriod
      );

      let updatedItems;

      if (exactMatchIndex >= 0) {
        // If exact match exists, increment quantity
        updatedItems = existingItems.map((i: CartItem, index: number) =>
          index === exactMatchIndex
            ? { ...i, quantity: i.quantity + 1 }
            : i
        );
      } else if (sameProductDifferentPeriodIndex >= 0) {
        // If same product with different billing period exists, replace it, keep the quantity and increase by 1
        const existingItem = existingItems[sameProductDifferentPeriodIndex];
        const newQuantity = existingItem.quantity + 1;
        console.log(`Replacing product ${productId} with billing period ${existingItem.billing_period} to ${billingPeriod}, updating quantity: ${existingItem.quantity} → ${newQuantity}`);
        updatedItems = existingItems.map((i: CartItem, index: number) =>
          index === sameProductDifferentPeriodIndex
            ? { product_id: productId, quantity: newQuantity, billing_period: billingPeriod }
            : i
        );
      } else {
        // If product doesn't exist in cart at all, add it
        updatedItems = [...existingItems, {
          product_id: productId,
          quantity: 1,
          billing_period: billingPeriod
        }];
      }

      const { error } = await supabase
        .from('user_carts')
        .upsert({
          user_id: user.id,
          items: updatedItems,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });

      if (error) {
        console.error('Error updating cart:', error);
        throw error;
      }

      console.log('Cart updated successfully with items:', updatedItems);
      set({ items: updatedItems });

      // Update items with details
      get().getCartWithProductDetails();
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : 'An unknown error occurred' });
    } finally {
      set({ loading: false });
    }
  },

  removeItem: async (productId, billingPeriod) => {
    set({ loading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: cartData } = await supabase
        .from('user_carts')
        .select('items')
        .eq('user_id', user.id)
        .single();

      // If billingPeriod is provided, only remove items with matching product_id AND billing_period
      const updatedItems = (cartData?.items || []).filter((item: CartItem) => {
        if (billingPeriod) {
          // Remove only if both product_id and billing_period match
          return !(item.product_id === productId && item.billing_period === billingPeriod);
        } else {
          // Legacy behavior: remove all items with matching product_id
          return item.product_id !== productId;
        }
      });

      const { error } = await supabase
        .from('user_carts')
        .upsert({
          user_id: user.id,
          items: updatedItems,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });

      if (error) throw error;
      set({ items: updatedItems });

      // Update items with details
      get().getCartWithProductDetails();
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : 'An unknown error occurred' });
    } finally {
      set({ loading: false });
    }
  },

  updateQuantity: async (productId, quantity, billingPeriod) => {
    set({ loading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: cartData } = await supabase
        .from('user_carts')
        .select('items')
        .eq('user_id', user.id)
        .single();

      const updatedItems = (cartData?.items || []).map((item: CartItem) => {
        if (billingPeriod) {
          // Update quantity only if both product_id and billing_period match
          return (item.product_id === productId && item.billing_period === billingPeriod)
            ? { ...item, quantity }
            : item;
        } else {
          // Legacy behavior: update all items with matching product_id
          return item.product_id === productId ? { ...item, quantity } : item;
        }
      });

      const { error } = await supabase
        .from('user_carts')
        .upsert({
          user_id: user.id,
          items: updatedItems,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });

      if (error) throw error;
      set({ items: updatedItems });

      // Update items with details
      get().getCartWithProductDetails();
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : 'An unknown error occurred' });
    } finally {
      set({ loading: false });
    }
  },

  clearCart: async () => {
    set({ loading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('user_carts')
        .upsert({
          user_id: user.id,
          items: [],
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });

      if (error) throw error;
      set({ items: [] });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : 'An unknown error occurred' });
    } finally {
      set({ loading: false });
    }
  },

  // Sync cart with server - useful when user might have made changes on another device
  syncCart: async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return; // Silently return if no user

      // Fetch the latest cart data from the server
      const { data, error } = await supabase
        .from('user_carts')
        .select('items')
        .eq('user_id', user.id)
        .single();

      if (error) throw error;

      // Update the local state with the server data
      set({ items: data?.items || [] });
    } catch (err: unknown) {
      // Just log the error but don't update the error state to avoid UI disruptions
      console.error('Error syncing cart:', err instanceof Error ? err.message : 'An unknown error occurred');
    }
  }
}));