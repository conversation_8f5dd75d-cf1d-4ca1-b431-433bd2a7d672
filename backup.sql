

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pgsodium";








ALTER SCHEMA "public" OWNER TO "postgres";


CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."append_subscription"("user_id" "uuid", "subscription_id" "text") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  UPDATE profiles
  SET subscription_ids = array_append(subscription_ids, subscription_id)
  WHERE id = user_id;
END;
$$;


ALTER FUNCTION "public"."append_subscription"("user_id" "uuid", "subscription_id" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_active_subscriber_count"() RETURNS integer
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN (SELECT COUNT(*) FROM subscribers WHERE status = 'active');
END;
$$;


ALTER FUNCTION "public"."get_active_subscriber_count"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_recent_tickets"("limit_count" integer DEFAULT 5) RETURNS TABLE("id" "uuid", "title" "text", "description" "text", "status" "text", "priority" "text", "category" "text", "contact_name" "text", "contact_phone" "text", "created_at" timestamp with time zone, "user_id" "uuid", "user_email" "text", "user_full_name" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.title,
    t.description,
    t.status,
    t.priority,
    t.category,
    t.contact_name,
    t.contact_phone,
    t.created_at,
    t.user_id,
    u.email,
    u.full_name
  FROM support_tickets t
  LEFT JOIN profiles u ON t.user_id = u.id
  ORDER BY t.created_at DESC
  LIMIT limit_count;
END;
$$;


ALTER FUNCTION "public"."get_recent_tickets"("limit_count" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_storage_usage"() RETURNS TABLE("total_size" bigint)
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  RETURN QUERY SELECT sum(size) as total_size 
  FROM storage.objects;
END;
$$;


ALTER FUNCTION "public"."get_storage_usage"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."support_tickets" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "description" "text" NOT NULL,
    "status" "text" DEFAULT 'open'::"text" NOT NULL,
    "priority" "text" DEFAULT 'medium'::"text" NOT NULL,
    "category" "text" NOT NULL,
    "user_id" "uuid",
    "assigned_to" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "contact_name" "text",
    "contact_phone" "text",
    "resolution_comment" "text",
    "resolved_by" "uuid",
    "resolved_at" timestamp with time zone,
    CONSTRAINT "support_tickets_category_check" CHECK (("category" = ANY (ARRAY['technical'::"text", 'billing'::"text", 'account'::"text", 'feature'::"text", 'other'::"text"]))),
    CONSTRAINT "support_tickets_priority_check" CHECK (("priority" = ANY (ARRAY['low'::"text", 'medium'::"text", 'high'::"text", 'urgent'::"text"]))),
    CONSTRAINT "support_tickets_status_check" CHECK (("status" = ANY (ARRAY['open'::"text", 'in_progress'::"text", 'resolved'::"text", 'closed'::"text"])))
);


ALTER TABLE "public"."support_tickets" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_ticket_with_user_info"("ticket_row" "public"."support_tickets") RETURNS "json"
    LANGUAGE "sql" STABLE
    AS $$
  SELECT json_build_object(
    'id', ticket_row.id,
    'title', ticket_row.title,
    'description', ticket_row.description,
    'status', ticket_row.status,
    'priority', ticket_row.priority,
    'category', ticket_row.category,
    'contact_name', ticket_row.contact_name,
    'contact_phone', ticket_row.contact_phone,
    'resolution_comment', ticket_row.resolution_comment,
    'resolved_by', ticket_row.resolved_by,
    'resolved_at', ticket_row.resolved_at,
    'created_at', ticket_row.created_at,
    'updated_at', ticket_row.updated_at,
    'user', (
      SELECT json_build_object(
        'email', p.email,
        'full_name', p.full_name,
        'phone', p.phone
      )
      FROM profiles p
      WHERE p.id = ticket_row.user_id
    )
  );
$$;


ALTER FUNCTION "public"."get_ticket_with_user_info"("ticket_row" "public"."support_tickets") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_contact_info"("user_id" "uuid") RETURNS TABLE("full_name" "text", "phone" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.full_name,
    p.phone
  FROM profiles p
  WHERE p.id = user_id;
END;
$$;


ALTER FUNCTION "public"."get_user_contact_info"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
    CASE 
      WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN 'admin'
      ELSE 'user'
    END
  );
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."increment"() RETURNS integer
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  RETURN COALESCE(download_count, 0) + 1;
END;
$$;


ALTER FUNCTION "public"."increment"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_admin"("user_email" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles
    WHERE email = user_email
    AND role = 'admin'
  );
END;
$$;


ALTER FUNCTION "public"."is_admin"("user_email" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."resolve_support_ticket"("ticket_id" "uuid", "resolution" "text", "resolver_id" "uuid") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  UPDATE support_tickets
  SET 
    status = 'resolved',
    resolution_comment = resolution,
    resolved_by = resolver_id,
    resolved_at = now(),
    updated_at = now()
  WHERE id = ticket_id;
END;
$$;


ALTER FUNCTION "public"."resolve_support_ticket"("ticket_id" "uuid", "resolution" "text", "resolver_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."resolve_ticket_with_notification"("ticket_id" "uuid", "resolution" "text", "resolver_id" "uuid") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  ticket_info record;
BEGIN
  -- Update ticket status and add resolution
  UPDATE support_tickets
  SET 
    status = 'resolved',
    resolution_comment = resolution,
    resolved_by = resolver_id,
    resolved_at = now(),
    updated_at = now()
  WHERE id = ticket_id
  RETURNING * INTO ticket_info;

  -- Note: Email notification would be handled by Edge Functions or external service
  -- This function just handles the database updates
END;
$$;


ALTER FUNCTION "public"."resolve_ticket_with_notification"("ticket_id" "uuid", "resolution" "text", "resolver_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."subscribe_to_newsletter"("p_email" "text", "p_full_name" "text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if already subscribed
  IF EXISTS (SELECT 1 FROM subscribers WHERE email = p_email AND status = 'active') THEN
    RAISE EXCEPTION 'This email is already subscribed';
  END IF;
  
  -- Insert new subscriber
  INSERT INTO subscribers (email, full_name, status, subscribed_at)
  VALUES (p_email, p_full_name, 'active', NOW());
  
  -- You can add email sending logic here if needed
END;
$$;


ALTER FUNCTION "public"."subscribe_to_newsletter"("p_email" "text", "p_full_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_event_timestamp"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_event_timestamp"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_job_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_job_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_last_login"("user_email" "text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
BEGIN
  UPDATE profiles
  SET updated_at = now()
  WHERE email = user_email;
END;
$$;


ALTER FUNCTION "public"."update_last_login"("user_email" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_pages_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_pages_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_support_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_support_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_webinar_timestamp"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_webinar_timestamp"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."validate_slug"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $_$
DECLARE
  segment text;
BEGIN
  -- Allow empty slug for home page
  IF NEW.slug = '' THEN
    RETURN NEW;
  END IF;

  -- Check each segment of the slug
  FOR segment IN 
    SELECT unnest(string_to_array(NEW.slug, '/'))
  LOOP
    -- Skip empty segments
    IF segment = '' THEN
      CONTINUE;
    END IF;

    -- Validate segment format
    IF segment !~ '^[a-z0-9][a-z0-9-]*[a-z0-9]$' THEN
      RAISE EXCEPTION 'Invalid slug segment: %. Each segment must contain only lowercase letters, numbers, and hyphens, and cannot start or end with a hyphen', segment;
    END IF;
  END LOOP;

  RETURN NEW;
END;
$_$;


ALTER FUNCTION "public"."validate_slug"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."verify_captcha"("token" "text") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  IF token IS NULL OR token = '' THEN
    RAISE EXCEPTION 'Invalid captcha token';
  END IF;
  
  RETURN true;
END;
$$;


ALTER FUNCTION "public"."verify_captcha"("token" "text") OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."blog_categories" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "slug" "text",
    "description" "text",
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."blog_categories" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."blog_comments" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "post_id" "uuid",
    "user_id" "uuid",
    "content" "text" NOT NULL,
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "blog_comments_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'approved'::"text", 'rejected'::"text"])))
);


ALTER TABLE "public"."blog_comments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."blog_posts" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "excerpt" "text",
    "status" "text" NOT NULL,
    "author_id" "uuid",
    "featured_image" "text",
    "slug" "text",
    "meta_description" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "type" "text" DEFAULT 'blog'::"text",
    CONSTRAINT "blog_posts_status_check" CHECK (("status" = ANY (ARRAY['draft'::"text", 'published'::"text", 'archived'::"text"])))
);


ALTER TABLE "public"."blog_posts" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."blog_posts_categories" (
    "post_id" "uuid" NOT NULL,
    "category_id" "uuid" NOT NULL
);


ALTER TABLE "public"."blog_posts_categories" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."blog_posts_tags" (
    "post_id" "uuid" NOT NULL,
    "tag_id" "uuid" NOT NULL
);


ALTER TABLE "public"."blog_posts_tags" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."blog_tags" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "slug" "text",
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."blog_tags" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."campaign_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "campaign_id" "uuid",
    "subscriber_id" "uuid",
    "event_type" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "campaign_logs_event_type_check" CHECK (("event_type" = ANY (ARRAY['sent'::"text", 'opened'::"text", 'clicked'::"text", 'bounced'::"text", 'complained'::"text", 'unsubscribed'::"text"])))
);


ALTER TABLE "public"."campaign_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."case_studies" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "organization" "text" NOT NULL,
    "impact" "text" NOT NULL,
    "category" "text" NOT NULL,
    "image_url" "text" NOT NULL,
    "content" "text" NOT NULL,
    "is_public" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."case_studies" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."contact_submissions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "email" "text" NOT NULL,
    "phone" "text",
    "company" "text",
    "message" "text" NOT NULL,
    "captcha_token" "text" NOT NULL,
    "submitted_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."contact_submissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."email_campaigns" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "subject" "text" NOT NULL,
    "content" "text" NOT NULL,
    "status" "text" NOT NULL,
    "scheduled_for" timestamp with time zone,
    "sent_at" timestamp with time zone,
    "opens" integer DEFAULT 0,
    "clicks" integer DEFAULT 0,
    "recipients" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "email_campaigns_status_check" CHECK (("status" = ANY (ARRAY['draft'::"text", 'scheduled'::"text", 'sent'::"text"])))
);


ALTER TABLE "public"."email_campaigns" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."event_registrations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "event_id" "uuid" NOT NULL,
    "user_id" "uuid",
    "full_name" "text" NOT NULL,
    "email" "text" NOT NULL,
    "phone" "text",
    "company" "text",
    "registered_at" timestamp with time zone DEFAULT "now"(),
    "status" "text" DEFAULT 'registered'::"text" NOT NULL,
    "captcha_token" "text",
    CONSTRAINT "event_registrations_email_check" CHECK (("email" ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::"text")),
    CONSTRAINT "event_registrations_status_check" CHECK (("status" = ANY (ARRAY['registered'::"text", 'cancelled'::"text", 'attended'::"text"])))
);


ALTER TABLE "public"."event_registrations" OWNER TO "postgres";


COMMENT ON TABLE "public"."event_registrations" IS 'Stores registrations for events';



COMMENT ON COLUMN "public"."event_registrations"."captcha_token" IS 'Stores the captcha token for verification purposes';



CREATE TABLE IF NOT EXISTS "public"."events" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "description" "text" NOT NULL,
    "date" "text" NOT NULL,
    "time" "text" NOT NULL,
    "end_time" "text",
    "location" "text" NOT NULL,
    "address" "text",
    "type" "text" NOT NULL,
    "capacity" integer,
    "image_url" "text" NOT NULL,
    "is_featured" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."events" OWNER TO "postgres";


COMMENT ON TABLE "public"."events" IS 'Stores information about upcoming and past events';



CREATE TABLE IF NOT EXISTS "public"."guides" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "title" "text" NOT NULL,
    "category" "text" NOT NULL,
    "description" "text" NOT NULL,
    "download_count" integer DEFAULT 0,
    "image_url" "text",
    "pdf_url" "text" NOT NULL,
    "is_public" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."guides" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."job_applications" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "job_id" "uuid",
    "full_name" "text" NOT NULL,
    "email" "text" NOT NULL,
    "phone" "text" NOT NULL,
    "resume_url" "text" NOT NULL,
    "cover_letter" "text",
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "job_applications_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'reviewed'::"text", 'interviewing'::"text", 'accepted'::"text", 'rejected'::"text"])))
);


ALTER TABLE "public"."job_applications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."job_listings" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "department" "text" NOT NULL,
    "location" "text" NOT NULL,
    "type" "text" NOT NULL,
    "description" "text" NOT NULL,
    "requirements" "text"[] NOT NULL,
    "responsibilities" "text"[] NOT NULL,
    "benefits" "text"[] NOT NULL,
    "salary_range" "jsonb",
    "status" "text" DEFAULT 'draft'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "job_listings_status_check" CHECK (("status" = ANY (ARRAY['draft'::"text", 'published'::"text", 'archived'::"text"]))),
    CONSTRAINT "job_listings_type_check" CHECK (("type" = ANY (ARRAY['full-time'::"text", 'part-time'::"text", 'contract'::"text", 'internship'::"text"])))
);


ALTER TABLE "public"."job_listings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."media_items" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "description" "text" NOT NULL,
    "type" "text" NOT NULL,
    "url" "text" NOT NULL,
    "thumbnail_url" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "file_url" "text",
    CONSTRAINT "media_items_type_check" CHECK (("type" = ANY (ARRAY['guide'::"text", 'whitepaper'::"text", 'tutorial'::"text", 'template'::"text"])))
);


ALTER TABLE "public"."media_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "slug" "text" NOT NULL,
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "meta_description" "text",
    "is_published" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "html_content" "text",
    "custom_css" "text",
    "custom_js" "text"
);


ALTER TABLE "public"."pages" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."page_management" AS
 SELECT "pages"."id",
    "pages"."slug",
    "pages"."title",
    "pages"."content",
    "pages"."html_content",
    "pages"."custom_css",
    "pages"."custom_js",
    "pages"."meta_description",
    "pages"."is_published",
    "pages"."created_at",
    "pages"."updated_at"
   FROM "public"."pages";


ALTER TABLE "public"."page_management" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."plans" (
    "id" "text" NOT NULL,
    "name" "text" NOT NULL,
    "description" "text" NOT NULL,
    "price" numeric NOT NULL,
    "features" "text"[] NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."plans" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."products" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text" NOT NULL,
    "price" numeric NOT NULL,
    "image_url" "text",
    "category" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "pages" "jsonb" DEFAULT '[]'::"jsonb",
    "specifications" "jsonb" DEFAULT '[]'::"jsonb",
    "theme_color" "text" DEFAULT 'blue'::"text",
    "price_id" "text",
    CONSTRAINT "products_price_check" CHECK (("price" >= (0)::numeric)),
    CONSTRAINT "products_theme_color_check" CHECK (("theme_color" = ANY (ARRAY['blue'::"text", 'orange'::"text", 'purple'::"text", 'yellow'::"text"])))
);


ALTER TABLE "public"."products" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "uuid" NOT NULL,
    "email" "text" NOT NULL,
    "full_name" "text",
    "avatar_url" "text",
    "phone" "text",
    "company" "text",
    "title" "text",
    "bio" "text",
    "address" "text",
    "city" "text",
    "state" "text",
    "zip_code" "text",
    "role" "text" DEFAULT 'user'::"text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "stripe_customer_id" "text",
    "subscription_ids" "text"[],
    CONSTRAINT "profiles_role_check" CHECK (("role" = ANY (ARRAY['admin'::"text", 'user'::"text"])))
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."site_settings" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "key" "text" NOT NULL,
    "value" "jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."site_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."stripe_customers" (
    "user_id" "uuid" NOT NULL,
    "customer_id" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "deleted_at" timestamp with time zone
);


ALTER TABLE "public"."stripe_customers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."subscribers" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" "text" NOT NULL,
    "full_name" "text",
    "status" "text" DEFAULT 'active'::"text" NOT NULL,
    "subscribed_at" timestamp with time zone DEFAULT "now"(),
    "last_email_sent" timestamp with time zone,
    CONSTRAINT "subscribers_status_check" CHECK (("status" = ANY (ARRAY['active'::"text", 'unsubscribed'::"text"]))),
    CONSTRAINT "valid_email" CHECK (("email" ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::"text"))
);


ALTER TABLE "public"."subscribers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."subscriptions" (
    "id" "text" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "product_id" "uuid" NOT NULL,
    "status" "text" NOT NULL,
    "current_period_start" timestamp with time zone NOT NULL,
    "current_period_end" timestamp with time zone NOT NULL,
    "cancel_at_period_end" boolean DEFAULT false NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "subscriptions_status_check" CHECK (("status" = ANY (ARRAY['active'::"text", 'trialing'::"text", 'canceled'::"text", 'past due'::"text"])))
);


ALTER TABLE "public"."subscriptions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."support_attachments" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "ticket_id" "uuid",
    "file_name" "text" NOT NULL,
    "file_url" "text" NOT NULL,
    "content_type" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."support_attachments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."support_comments" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "ticket_id" "uuid",
    "user_id" "uuid",
    "content" "text" NOT NULL,
    "is_internal" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."support_comments" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."support_tickets_with_users" AS
 SELECT "t"."id",
    "t"."title",
    "t"."description",
    "t"."status",
    "t"."priority",
    "t"."category",
    "t"."user_id",
    "t"."assigned_to",
    "t"."created_at",
    "t"."updated_at",
    "t"."contact_name",
    "t"."contact_phone",
    "t"."resolution_comment",
    "t"."resolved_by",
    "t"."resolved_at",
    "u"."email" AS "user_email",
    "u"."full_name" AS "user_full_name",
    "a"."email" AS "assignee_email",
    "a"."full_name" AS "assignee_full_name",
    "r"."email" AS "resolver_email",
    "r"."full_name" AS "resolver_full_name"
   FROM ((("public"."support_tickets" "t"
     LEFT JOIN "public"."profiles" "u" ON (("t"."user_id" = "u"."id")))
     LEFT JOIN "public"."profiles" "a" ON (("t"."assigned_to" = "a"."id")))
     LEFT JOIN "public"."profiles" "r" ON (("t"."resolved_by" = "r"."id")));


ALTER TABLE "public"."support_tickets_with_users" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."teams" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "role" "text" NOT NULL,
    "image_url" "text" NOT NULL,
    "bio" "text" NOT NULL,
    "linkedin_url" "text",
    "email" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."teams" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_carts" (
    "user_id" "uuid" NOT NULL,
    "items" "jsonb" DEFAULT '[]'::"jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."user_carts" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."webinar_registrations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "webinar_id" "uuid" NOT NULL,
    "user_id" "uuid",
    "full_name" "text" NOT NULL,
    "email" "text" NOT NULL,
    "phone" "text",
    "company" "text",
    "captcha_token" "text",
    "registered_at" timestamp with time zone DEFAULT "now"(),
    "status" "text" DEFAULT 'registered'::"text",
    CONSTRAINT "webinar_registrations_email_check" CHECK (("email" ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::"text")),
    CONSTRAINT "webinar_registrations_status_check" CHECK (("status" = ANY (ARRAY['registered'::"text", 'cancelled'::"text", 'attended'::"text", 'no_show'::"text"])))
);


ALTER TABLE "public"."webinar_registrations" OWNER TO "postgres";


COMMENT ON COLUMN "public"."webinar_registrations"."status" IS 'Registration status: registered, cancelled, attended, no_show';



CREATE TABLE IF NOT EXISTS "public"."webinars" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "description" "text" NOT NULL,
    "date" "text" NOT NULL,
    "time" "text",
    "duration" "text",
    "speaker" "text" NOT NULL,
    "image_url" "text" NOT NULL,
    "recording_url" "text",
    "status" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "webinar_link" "text",
    CONSTRAINT "webinars_status_check" CHECK (("status" = ANY (ARRAY['upcoming'::"text", 'recorded'::"text", 'live'::"text"])))
);


ALTER TABLE "public"."webinars" OWNER TO "postgres";


COMMENT ON COLUMN "public"."webinars"."status" IS 'Webinar status: upcoming, live, recorded';



COMMENT ON COLUMN "public"."webinars"."webinar_link" IS 'Link to join the webinar, visible to registered users';



ALTER TABLE ONLY "public"."blog_categories"
    ADD CONSTRAINT "blog_categories_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."blog_categories"
    ADD CONSTRAINT "blog_categories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."blog_categories"
    ADD CONSTRAINT "blog_categories_slug_key" UNIQUE ("slug");



ALTER TABLE ONLY "public"."blog_comments"
    ADD CONSTRAINT "blog_comments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."blog_posts_categories"
    ADD CONSTRAINT "blog_posts_categories_pkey" PRIMARY KEY ("post_id", "category_id");



ALTER TABLE ONLY "public"."blog_posts"
    ADD CONSTRAINT "blog_posts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."blog_posts"
    ADD CONSTRAINT "blog_posts_slug_key" UNIQUE ("slug");



ALTER TABLE ONLY "public"."blog_posts_tags"
    ADD CONSTRAINT "blog_posts_tags_pkey" PRIMARY KEY ("post_id", "tag_id");



ALTER TABLE ONLY "public"."blog_tags"
    ADD CONSTRAINT "blog_tags_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."blog_tags"
    ADD CONSTRAINT "blog_tags_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."blog_tags"
    ADD CONSTRAINT "blog_tags_slug_key" UNIQUE ("slug");



ALTER TABLE ONLY "public"."campaign_logs"
    ADD CONSTRAINT "campaign_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."case_studies"
    ADD CONSTRAINT "case_studies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."contact_submissions"
    ADD CONSTRAINT "contact_submissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."email_campaigns"
    ADD CONSTRAINT "email_campaigns_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."event_registrations"
    ADD CONSTRAINT "event_registrations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."events"
    ADD CONSTRAINT "events_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."guides"
    ADD CONSTRAINT "guides_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_applications"
    ADD CONSTRAINT "job_applications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_listings"
    ADD CONSTRAINT "job_listings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."media_items"
    ADD CONSTRAINT "media_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages"
    ADD CONSTRAINT "pages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages"
    ADD CONSTRAINT "pages_slug_key" UNIQUE ("slug");



ALTER TABLE ONLY "public"."plans"
    ADD CONSTRAINT "plans_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."site_settings"
    ADD CONSTRAINT "site_settings_key_key" UNIQUE ("key");



ALTER TABLE ONLY "public"."site_settings"
    ADD CONSTRAINT "site_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."stripe_customers"
    ADD CONSTRAINT "stripe_customers_customer_id_key" UNIQUE ("customer_id");



ALTER TABLE ONLY "public"."stripe_customers"
    ADD CONSTRAINT "stripe_customers_pkey" PRIMARY KEY ("user_id", "customer_id");



ALTER TABLE ONLY "public"."subscribers"
    ADD CONSTRAINT "subscribers_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."subscribers"
    ADD CONSTRAINT "subscribers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."support_attachments"
    ADD CONSTRAINT "support_attachments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."support_comments"
    ADD CONSTRAINT "support_comments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."support_tickets"
    ADD CONSTRAINT "support_tickets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_carts"
    ADD CONSTRAINT "user_carts_pkey" PRIMARY KEY ("user_id");



ALTER TABLE ONLY "public"."webinar_registrations"
    ADD CONSTRAINT "webinar_registrations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."webinars"
    ADD CONSTRAINT "webinars_pkey" PRIMARY KEY ("id");



CREATE INDEX "blog_posts_type_idx" ON "public"."blog_posts" USING "btree" ("type");



CREATE INDEX "idx_contact_submissions_email" ON "public"."contact_submissions" USING "btree" ("email");



CREATE INDEX "idx_contact_submissions_submitted_at" ON "public"."contact_submissions" USING "btree" ("submitted_at");



CREATE INDEX "pages_is_published_idx" ON "public"."pages" USING "btree" ("is_published");



CREATE INDEX "pages_slug_idx" ON "public"."pages" USING "btree" ("slug");



CREATE INDEX "user_carts_user_id_idx" ON "public"."user_carts" USING "btree" ("user_id");



CREATE OR REPLACE TRIGGER "update_email_campaigns_updated_at" BEFORE UPDATE ON "public"."email_campaigns" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_event_timestamp" BEFORE UPDATE ON "public"."events" FOR EACH ROW EXECUTE FUNCTION "public"."update_event_timestamp"();



CREATE OR REPLACE TRIGGER "update_job_applications_updated_at" BEFORE UPDATE ON "public"."job_applications" FOR EACH ROW EXECUTE FUNCTION "public"."update_job_updated_at"();



CREATE OR REPLACE TRIGGER "update_job_listings_updated_at" BEFORE UPDATE ON "public"."job_listings" FOR EACH ROW EXECUTE FUNCTION "public"."update_job_updated_at"();



CREATE OR REPLACE TRIGGER "update_pages_updated_at" BEFORE UPDATE ON "public"."pages" FOR EACH ROW EXECUTE FUNCTION "public"."update_pages_updated_at"();



CREATE OR REPLACE TRIGGER "update_support_comments_updated_at" BEFORE UPDATE ON "public"."support_comments" FOR EACH ROW EXECUTE FUNCTION "public"."update_support_updated_at"();



CREATE OR REPLACE TRIGGER "update_support_tickets_updated_at" BEFORE UPDATE ON "public"."support_tickets" FOR EACH ROW EXECUTE FUNCTION "public"."update_support_updated_at"();



CREATE OR REPLACE TRIGGER "update_webinar_timestamp" BEFORE UPDATE ON "public"."webinars" FOR EACH ROW EXECUTE FUNCTION "public"."update_webinar_timestamp"();



CREATE OR REPLACE TRIGGER "validate_page_slug" BEFORE INSERT OR UPDATE ON "public"."pages" FOR EACH ROW EXECUTE FUNCTION "public"."validate_slug"();



ALTER TABLE ONLY "public"."blog_comments"
    ADD CONSTRAINT "blog_comments_post_id_fkey" FOREIGN KEY ("post_id") REFERENCES "public"."blog_posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."blog_comments"
    ADD CONSTRAINT "blog_comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."blog_posts"
    ADD CONSTRAINT "blog_posts_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "public"."profiles"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."blog_posts_categories"
    ADD CONSTRAINT "blog_posts_categories_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."blog_categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."blog_posts_categories"
    ADD CONSTRAINT "blog_posts_categories_post_id_fkey" FOREIGN KEY ("post_id") REFERENCES "public"."blog_posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."blog_posts_tags"
    ADD CONSTRAINT "blog_posts_tags_post_id_fkey" FOREIGN KEY ("post_id") REFERENCES "public"."blog_posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."blog_posts_tags"
    ADD CONSTRAINT "blog_posts_tags_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "public"."blog_tags"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."campaign_logs"
    ADD CONSTRAINT "campaign_logs_campaign_id_fkey" FOREIGN KEY ("campaign_id") REFERENCES "public"."email_campaigns"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."campaign_logs"
    ADD CONSTRAINT "campaign_logs_subscriber_id_fkey" FOREIGN KEY ("subscriber_id") REFERENCES "public"."subscribers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."event_registrations"
    ADD CONSTRAINT "event_registrations_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."event_registrations"
    ADD CONSTRAINT "event_registrations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."job_applications"
    ADD CONSTRAINT "job_applications_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "public"."job_listings"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."stripe_customers"
    ADD CONSTRAINT "stripe_customers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id");



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."support_attachments"
    ADD CONSTRAINT "support_attachments_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "public"."support_tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."support_comments"
    ADD CONSTRAINT "support_comments_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "public"."support_tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."support_comments"
    ADD CONSTRAINT "support_comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."support_tickets"
    ADD CONSTRAINT "support_tickets_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "public"."profiles"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."support_tickets"
    ADD CONSTRAINT "support_tickets_resolved_by_fkey" FOREIGN KEY ("resolved_by") REFERENCES "public"."profiles"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."support_tickets"
    ADD CONSTRAINT "support_tickets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_carts"
    ADD CONSTRAINT "user_carts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."webinar_registrations"
    ADD CONSTRAINT "webinar_registrations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."webinar_registrations"
    ADD CONSTRAINT "webinar_registrations_webinar_id_fkey" FOREIGN KEY ("webinar_id") REFERENCES "public"."webinars"("id") ON DELETE CASCADE;



CREATE POLICY "Admin full access" ON "public"."contact_submissions" TO "authenticated" USING (("auth"."role"() = 'admin'::"text"));



CREATE POLICY "Admin full access" ON "public"."subscribers" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage all blog posts" ON "public"."blog_posts" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text"))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage all comments" ON "public"."blog_comments" TO "authenticated" USING (("auth"."role"() = 'admin'::"text"));



CREATE POLICY "Admins can manage all guides" ON "public"."guides" TO "authenticated" USING (("auth"."role"() = 'admin'::"text"));



CREATE POLICY "Admins can manage all pages" ON "public"."pages" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage all teams" ON "public"."teams" TO "authenticated" USING (("auth"."role"() = 'admin'::"text")) WITH CHECK (("auth"."role"() = 'admin'::"text"));



CREATE POLICY "Admins can manage applications" ON "public"."job_applications" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage campaigns" ON "public"."email_campaigns" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage job listings" ON "public"."job_listings" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage media items" ON "public"."media_items" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage plans" ON "public"."plans" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage subscriptions" ON "public"."subscriptions" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can update all registrations" ON "public"."event_registrations" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can update registrations" ON "public"."webinar_registrations" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view all applications" ON "public"."job_applications" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view all registrations" ON "public"."event_registrations" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view all registrations" ON "public"."webinar_registrations" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view campaign logs" ON "public"."campaign_logs" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "All guides are visible to all users" ON "public"."guides" FOR SELECT TO "authenticated", "anon" USING (true);



CREATE POLICY "Allow authenticated inserts" ON "public"."guides" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Allow authenticated updates" ON "public"."guides" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow download count updates" ON "public"."guides" FOR UPDATE TO "authenticated" USING (true) WITH CHECK ((( SELECT "count"(*) AS "count"
   FROM "jsonb_each"("to_jsonb"("guides".*)) "jsonb_each"("key", "value")
  WHERE ("jsonb_each"."key" <> 'download_count'::"text")) = 0));



CREATE POLICY "Allow public inserts to teams" ON "public"."teams" FOR INSERT WITH CHECK (true);



CREATE POLICY "Anyone can read plans" ON "public"."plans" FOR SELECT USING (true);



CREATE POLICY "Anyone can read profiles" ON "public"."profiles" FOR SELECT USING (true);



CREATE POLICY "Anyone can read published pages" ON "public"."pages" FOR SELECT USING (("is_published" = true));



CREATE POLICY "Anyone can register for events" ON "public"."event_registrations" FOR INSERT WITH CHECK (true);



CREATE POLICY "Anyone can register for webinars" ON "public"."webinar_registrations" FOR INSERT WITH CHECK (true);



CREATE POLICY "Anyone can submit applications" ON "public"."job_applications" FOR INSERT WITH CHECK (true);



CREATE POLICY "Anyone can view events" ON "public"."events" FOR SELECT USING (true);



CREATE POLICY "Anyone can view media items" ON "public"."media_items" FOR SELECT USING (true);



CREATE POLICY "Anyone can view published jobs" ON "public"."job_listings" FOR SELECT USING (("status" = 'published'::"text"));



CREATE POLICY "Anyone can view webinars" ON "public"."webinars" FOR SELECT USING (true);



CREATE POLICY "Authenticated full access" ON "public"."subscribers" TO "authenticated" USING (("email" = ("auth"."jwt"() ->> 'email'::"text"))) WITH CHECK (("email" = ("auth"."jwt"() ->> 'email'::"text")));



CREATE POLICY "Enable all access for admin" ON "public"."case_studies" USING ((EXISTS ( SELECT 1
   FROM "auth"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND (("users"."raw_user_meta_data" ->> 'role'::"text") = 'admin'::"text")))));



CREATE POLICY "Enable delete for admin users" ON "public"."blog_posts" FOR DELETE TO "authenticated" USING (("auth"."role"() = 'admin'::"text"));



CREATE POLICY "Enable delete for authenticated users" ON "public"."case_studies" FOR DELETE USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Enable delete for authors" ON "public"."blog_posts" FOR DELETE USING (("auth"."uid"() = "author_id"));



CREATE POLICY "Enable delete for own cart" ON "public"."user_carts" FOR DELETE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Enable insert for authenticated users" ON "public"."case_studies" FOR INSERT WITH CHECK (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Enable insert for authenticated users" ON "public"."user_carts" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Enable read access for all users" ON "public"."blog_posts" FOR SELECT USING (("status" = 'published'::"text"));



CREATE POLICY "Enable read access for all users" ON "public"."case_studies" FOR SELECT USING (("is_public" = true));



CREATE POLICY "Enable read access for own cart" ON "public"."user_carts" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Enable update for authenticated users" ON "public"."case_studies" FOR UPDATE USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Enable update for own cart" ON "public"."user_carts" FOR UPDATE USING (("auth"."uid"() = "user_id")) WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Only admins can delete events" ON "public"."events" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Only admins can delete webinars" ON "public"."webinars" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Only admins can insert events" ON "public"."events" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Only admins can insert webinars" ON "public"."webinars" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Only admins can update events" ON "public"."events" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Only admins can update webinars" ON "public"."webinars" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Products are insertable by authenticated users" ON "public"."products" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Products are updatable by authenticated users" ON "public"."products" FOR UPDATE TO "authenticated" USING (true);



CREATE POLICY "Products are viewable by everyone" ON "public"."products" FOR SELECT USING (true);



CREATE POLICY "Public can delete guides" ON "public"."guides" FOR DELETE TO "authenticated", "anon" USING (true);



CREATE POLICY "Public can delete teams" ON "public"."teams" FOR DELETE USING (true);



CREATE POLICY "Public can read approved comments" ON "public"."blog_comments" FOR SELECT USING (("status" = 'approved'::"text"));



CREATE POLICY "Public can read published posts" ON "public"."blog_posts" FOR SELECT TO "authenticated", "anon" USING (("status" = 'published'::"text"));



CREATE POLICY "Public can update teams" ON "public"."teams" FOR UPDATE USING (true) WITH CHECK (true);



CREATE POLICY "Public delete access" ON "public"."contact_submissions" FOR DELETE USING (true);



CREATE POLICY "Public delete own" ON "public"."subscribers" FOR DELETE USING (("email" = (("current_setting"('request.jwt.claims'::"text", true))::"json" ->> 'email'::"text")));



CREATE POLICY "Public guides are visible to everyone" ON "public"."guides" FOR SELECT USING (("is_public" = true));



CREATE POLICY "Public insert access" ON "public"."contact_submissions" FOR INSERT WITH CHECK (true);



CREATE POLICY "Public insert access" ON "public"."subscribers" FOR INSERT WITH CHECK (true);



CREATE POLICY "Public read access" ON "public"."subscribers" FOR SELECT USING (true);



CREATE POLICY "Public read access for products" ON "public"."products" FOR SELECT USING (true);



CREATE POLICY "Public teams are visible to everyone" ON "public"."teams" FOR SELECT USING (true);



CREATE POLICY "Public update own" ON "public"."subscribers" FOR UPDATE USING (("email" = (("current_setting"('request.jwt.claims'::"text", true))::"json" ->> 'email'::"text"))) WITH CHECK (("email" = (("current_setting"('request.jwt.claims'::"text", true))::"json" ->> 'email'::"text")));



CREATE POLICY "User can read own submissions" ON "public"."contact_submissions" FOR SELECT TO "authenticated" USING (("email" = ("auth"."jwt"() ->> 'email'::"text")));



CREATE POLICY "Users can create comments" ON "public"."support_comments" FOR INSERT TO "authenticated" WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."support_tickets"
  WHERE (("support_tickets"."id" = "support_comments"."ticket_id") AND ("support_tickets"."user_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text"))))));



CREATE POLICY "Users can create posts" ON "public"."blog_posts" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Users can create tickets" ON "public"."support_tickets" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can manage their own comments" ON "public"."blog_comments" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can manage their own posts" ON "public"."blog_posts" TO "authenticated" USING (("author_id" = "auth"."uid"())) WITH CHECK (("author_id" = "auth"."uid"()));



CREATE POLICY "Users can read own subscriptions" ON "public"."subscriptions" FOR SELECT TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update own profile" ON "public"."profiles" FOR UPDATE TO "authenticated" USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can update own tickets" ON "public"."support_tickets" FOR UPDATE TO "authenticated" USING ((("user_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text"))))));



CREATE POLICY "Users can update their own registrations" ON "public"."event_registrations" FOR UPDATE TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can upload attachments" ON "public"."support_attachments" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."support_tickets"
  WHERE (("support_tickets"."id" = "support_attachments"."ticket_id") AND ("support_tickets"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view own tickets" ON "public"."support_tickets" FOR SELECT TO "authenticated" USING ((("user_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text"))))));



CREATE POLICY "Users can view their own registrations" ON "public"."event_registrations" FOR SELECT TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can view their own registrations" ON "public"."webinar_registrations" FOR SELECT TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can view ticket attachments" ON "public"."support_attachments" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."support_tickets"
  WHERE (("support_tickets"."id" = "support_attachments"."ticket_id") AND ("support_tickets"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view ticket comments" ON "public"."support_comments" FOR SELECT TO "authenticated" USING ((((NOT "is_internal") AND (EXISTS ( SELECT 1
   FROM "public"."support_tickets"
  WHERE (("support_tickets"."id" = "support_comments"."ticket_id") AND ("support_tickets"."user_id" = "auth"."uid"()))))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text"))))));



ALTER TABLE "public"."blog_comments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."blog_posts" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."campaign_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."case_studies" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."contact_submissions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."email_campaigns" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."event_registrations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."events" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."guides" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."job_applications" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."job_listings" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."media_items" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."pages" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."plans" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."products" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."subscribers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."subscriptions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."support_attachments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."support_comments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."support_tickets" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."teams" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_carts" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."webinar_registrations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."webinars" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


REVOKE USAGE ON SCHEMA "public" FROM PUBLIC;
GRANT ALL ON SCHEMA "public" TO "anon";
GRANT ALL ON SCHEMA "public" TO "authenticated";
GRANT ALL ON SCHEMA "public" TO "service_role";




















































































































































































GRANT ALL ON FUNCTION "public"."get_recent_tickets"("limit_count" integer) TO "authenticated";



GRANT ALL ON TABLE "public"."support_tickets" TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_ticket_with_user_info"("ticket_row" "public"."support_tickets") TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_user_contact_info"("user_id" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."is_admin"("user_email" "text") TO "authenticated";



GRANT ALL ON FUNCTION "public"."resolve_support_ticket"("ticket_id" "uuid", "resolution" "text", "resolver_id" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."resolve_ticket_with_notification"("ticket_id" "uuid", "resolution" "text", "resolver_id" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."update_last_login"("user_email" "text") TO "authenticated";



GRANT ALL ON FUNCTION "public"."verify_captcha"("token" "text") TO "anon";


















GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."blog_comments" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."blog_comments" TO "authenticated";



GRANT SELECT ON TABLE "public"."blog_posts" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."blog_posts" TO "authenticated";



GRANT ALL ON TABLE "public"."campaign_logs" TO "authenticated";



GRANT ALL ON TABLE "public"."case_studies" TO "supabase_admin";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."case_studies" TO "authenticated";
GRANT SELECT ON TABLE "public"."case_studies" TO "anon";



GRANT ALL ON TABLE "public"."contact_submissions" TO "authenticated";
GRANT INSERT ON TABLE "public"."contact_submissions" TO "anon";



GRANT ALL ON TABLE "public"."email_campaigns" TO "authenticated";



GRANT SELECT,INSERT ON TABLE "public"."event_registrations" TO "anon";
GRANT ALL ON TABLE "public"."event_registrations" TO "authenticated";



GRANT SELECT ON TABLE "public"."events" TO "anon";
GRANT ALL ON TABLE "public"."events" TO "authenticated";



GRANT ALL ON TABLE "public"."guides" TO "authenticated";
GRANT SELECT ON TABLE "public"."guides" TO "anon";



GRANT ALL ON TABLE "public"."job_applications" TO "authenticated";



GRANT ALL ON TABLE "public"."job_listings" TO "authenticated";



GRANT ALL ON TABLE "public"."media_items" TO "authenticated";



GRANT ALL ON TABLE "public"."pages" TO "authenticated";
GRANT ALL ON TABLE "public"."pages" TO "anon";
GRANT ALL ON TABLE "public"."pages" TO "service_role";



GRANT SELECT ON TABLE "public"."page_management" TO "authenticated";



GRANT ALL ON TABLE "public"."plans" TO "service_role";
GRANT SELECT ON TABLE "public"."plans" TO "authenticated";
GRANT SELECT ON TABLE "public"."plans" TO "anon";



GRANT SELECT ON TABLE "public"."products" TO PUBLIC;
GRANT ALL ON TABLE "public"."products" TO "authenticated";
GRANT ALL ON TABLE "public"."products" TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."subscribers" TO PUBLIC;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."subscribers" TO "anon";
GRANT ALL ON TABLE "public"."subscribers" TO "authenticated";
GRANT ALL ON TABLE "public"."subscribers" TO "service_role";



GRANT ALL ON TABLE "public"."subscriptions" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."subscriptions" TO "authenticated";



GRANT ALL ON TABLE "public"."support_attachments" TO "authenticated";



GRANT ALL ON TABLE "public"."support_comments" TO "authenticated";



GRANT SELECT ON TABLE "public"."support_tickets_with_users" TO "authenticated";



GRANT ALL ON TABLE "public"."teams" TO "authenticated";
GRANT SELECT ON TABLE "public"."teams" TO "anon";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."user_carts" TO "authenticated";



GRANT SELECT,INSERT ON TABLE "public"."webinar_registrations" TO "anon";
GRANT ALL ON TABLE "public"."webinar_registrations" TO "authenticated";



GRANT SELECT ON TABLE "public"."webinars" TO "anon";
GRANT ALL ON TABLE "public"."webinars" TO "authenticated";



























RESET ALL;
