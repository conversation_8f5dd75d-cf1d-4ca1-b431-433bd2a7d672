import React from 'react';
import { pdf } from '@react-pdf/renderer';
import InvoicePDFDocument from './InvoicePDF';
import { supabase } from '../lib/supabase';

interface InvoiceItem {
  product_name: string;
  price: number;
  quantity: number;
  billing_cycle: string;
  amount: number;
}

interface InvoiceData {
  id: string;
  ref?: string;
  stripe_invoice_id?: string;
  customer_name: string | null;
  customer_email: string | null;
  status: string;
  amount_due: number;
  amount_paid: number;
  amount_remaining: number;
  currency: string;
  due_date: string;
  created_at: string;
  line_items?: InvoiceItem[];
  is_manual?: boolean;
  payment_method?: {
    type: string;
    bank_name?: string;
    account_number?: string;
    routing_number?: string;
  };
  notes?: string;
}

/**
 * Generates a PDF from an invoice and uploads it to Supabase storage
 * @param invoice The invoice data to generate a PDF from
 * @returns The URL of the uploaded PDF
 */
export const generateAndUploadInvoicePDF = async (invoice: InvoiceData): Promise<string> => {
  try {
    // Generate the PDF blob
    const blob = await pdf(
      <InvoicePDFDocument invoice={invoice} />
    ).toBlob();
    
    // Create a File object from the blob
    const fileName = `invoice-${invoice.ref || invoice.id.substring(0, 8)}-${Date.now()}.pdf`;
    const file = new File([blob], fileName, { type: 'application/pdf' });
    
    // Upload to Supabase storage
    const filePath = `invoices/${fileName}`;
    
    // Check if the bucket exists, if not, this will fail and we'll need to create it
    const { error: uploadError } = await supabase.storage
      .from('invoice-pdfs')
      .upload(filePath, file, { upsert: true });
    
    if (uploadError) {
      console.error('Error uploading PDF:', uploadError);
      throw new Error(`Failed to upload PDF: ${uploadError.message}`);
    }
    
    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('invoice-pdfs')
      .getPublicUrl(filePath);
    
    return publicUrl;
  } catch (error) {
    console.error('Error generating or uploading PDF:', error);
    throw error;
  }
};
