import React from 'react';
import { X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
}

const LoginModal: React.FC<LoginModalProps> = ({
  isOpen,
  onClose,
  title = 'Register for Webinar',
  message = 'Please log in to register for this webinar. Logging in allows us to save your information for future webinars.'
}) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  // Get the modal type from session storage
  const modalType = typeof window !== 'undefined' ? sessionStorage.getItem('loginModalType') : null;

  // Determine the title and message based on the modal type
  const modalTitle = title === 'Register for Webinar' ? title :
    modalType === 'viewOnline' ? 'View Online Content' : title;

  const modalMessage = modalType === 'viewOnline'
    ? 'Please log in to view this online content. Logging in allows us to save your information for future access.'
    : message;

  const handleLogin = () => {
    navigate('/login');
    onClose();
  };

  const handleSignUp = () => {
    navigate('/signup');
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={onClose}></div>
        </div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
          <div className="flex justify-between items-center px-6 pt-5 pb-2">
            <h3 className="text-xl font-bold text-gray-900">{modalTitle}</h3>
            <button
              type="button"
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              onClick={onClose}
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="px-6 pb-6">
            <div className="bg-blue-50 p-6 rounded-lg">
              <div className="flex justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ transform: 'scaleX(-1)' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-center text-gray-900 mb-2">Login Required</h3>
              <p className="text-gray-600 text-center">{modalMessage}</p>

              <div className="mt-6 flex justify-center">
                <button
                  type="button"
                  onClick={handleLogin}
                  className="inline-flex justify-center items-center px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ transform: 'scaleX(-1)' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                  Log In
                </button>
              </div>

              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  Don't have an account? <button onClick={handleSignUp} className="text-blue-600 hover:underline">Sign up</button>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginModal;
