import React from 'react';
import { Link } from 'react-router-dom';
import LeadershipImage from '../../assets/images/homepage/Leadership_and_Planning.png';
import { ArrowRight } from 'lucide-react';

// Arrow Right Icon Component
const ArrowRightIcon: React.FC<{ color?: string }> = ({ color = '#000000' }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6 12L10 8L6 4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const LeadershipAndPlanning: React.FC = () => {
  const [isMobile, setIsMobile] = React.useState(false);
  const hereColor = '#A892F7'; // Lavender color for "here" and right arrow
  const containerBgColor = '#F8F9FA'; // Very light gray for page background effect
  const textDarkGray = '#3C4043'; // A common dark gray for text
  const textBlack = '#000000';

  // Track screen size for responsive design
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <>
    <div style={{
      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
      backgroundColor: '#FFFFFF', // Changed to white
      padding: isMobile ? '80px 20px 40px 20px' : '80px 20px 50px 20px', // Less bottom padding on mobile
      textAlign: 'center',
      position: 'relative',
      overflowX: 'hidden', // Prevent horizontal scroll from wide arrows if they extend
    }}>
      {/* Content Wrapper for centering and max-width */}
      <div style={{
        maxWidth: '960px',
        margin: '0 auto',
        position: 'relative',
        zIndex: 1,
      }}>
        {/* Main Heading */}
        <h1 
        className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl"style={{
          // fontSize: 'clamp(1.8rem, 4.5vw, 2.8rem)',
          // fontWeight: 700,
          color: textBlack,
          margin: '0 0 25px 0',
          lineHeight: 1.1,
          letterSpacing: '-0.02em',
        }}>
          Leadership and Planning
        </h1>

        {/* Description Text */}
        <p style={{
          fontSize: 'clamp(1.15rem, 2.8vw, 1.45rem)',
          color: textBlack,
          fontWeight: 600,
          maxWidth: '920px',
          margin: '0 auto 5px auto',
          lineHeight: 1.35,
        }}>
          At International Responder Systems, we align our client's objectives
        </p>
        <p style={{
          fontSize: 'clamp(1.15rem, 2.8vw, 1.45rem)',
          color: textBlack,
          fontWeight: 600,
          maxWidth: '720px',
          margin: '0 auto 0px auto',
          lineHeight: 1.65,
        }}>
          with our organizational principles to uphold quality, consistency,
        </p>
        <p style={{
          fontSize: 'clamp(1.15rem, 2.8vw, 1.45rem)',
          color: textBlack,
          fontWeight: 600,
          maxWidth: '960px',
          margin: '0 auto 25px auto',
          lineHeight: 1.7,
        }}>
          integrity, and trust. We prioritize leadership through authentic service.
        </p>

        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '25px',
          flexWrap: 'wrap', // Allow buttons to wrap on smaller screens
          marginBottom: '0px', // Reduced space before image
        }}>
          <Link
            to="/leadership"
            style={{
              backgroundColor: textBlack,
              color: '#FFFFFF',
              border: `2px solid ${textBlack}`,
              padding: '4px 2px 4px 10px', // T,R,B,L - more padding left of text
              borderRadius: '8px',
              fontSize: 'clamp(0.95rem, 2.2vw, 1.05rem)',
              fontWeight: 600,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              transition: 'background-color 0.2s ease, border-color 0.2s ease',
              letterSpacing: '0.5px',
              textDecoration: 'none', // Remove default link underline
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.backgroundColor = '#333333';
              e.currentTarget.style.borderColor = '#333333';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.backgroundColor = textBlack;
              e.currentTarget.style.borderColor = textBlack;
            }}
          >
            Meet our leadership team
            <span style={{
                backgroundColor: '#FFFFFF',
                padding: '7px',
                borderRadius: '6px',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '12px',
                lineHeight: 0,
            }}>
              <ArrowRight color={textBlack} />
            </span>
          </Link>

          {/* <Link
            to="/book-a-demo"
            style={{
              backgroundColor: '#FFFFFF',
              color: textBlack,
              border: `2px solid #9CA3AF`,
              padding: '2px 1px 2px 8px', // T,R,B,L - more padding left of text
              borderRadius: '8px',
              fontSize: 'clamp(0.95rem, 2.2vw, 1.05rem)',
              fontWeight: 600,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              transition: 'background-color 0.2s ease, color 0.2s ease',
              letterSpacing: '0.5px',
              textDecoration: 'none', // Remove default link underline
            }}
            onMouseOver={(e) => { e.currentTarget.style.backgroundColor = '#F0F0F0'; }}
            onMouseOut={(e) => { e.currentTarget.style.backgroundColor = '#FFFFFF'; }}
          >
            See A Demo
            <span style={{
                padding: '7px', // Keep padding consistent for alignment
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '12px',
                lineHeight: 0,
            }}>
              <ArrowRightIcon color={textBlack} />
            </span>
          </Link> */}
        </div>
      </div>

    </div>

    {/* Image and SVG Container */}
    <div style={{
      width: '100%',
      position: 'relative',
      height: isMobile ? '450px' : '650px', // Total container height
      padding: isMobile ? '0 20px' : '0 40px', // Side margins
      display: 'flex',
      justifyContent: 'center',
    }}>
      <div style={{
        position: 'relative',
        width: '100%',
        maxWidth: '1200px', // Maximum width constraint
        height: '100%',
      }}>
      {/* Purple SVG Rectangle - Behind image, just peeking from right side */}
      <div style={{
        position: 'absolute',
        top: isMobile ? '-5px' : '-10px', // Much higher, partially above container
        right: isMobile ? '15px' : '29px', // Moved more to the right
        width: isMobile ? '70%' : '75%', // Smaller width
        height: isMobile ? '180px' : '250px', // Smaller height
        zIndex: 1,
      }}>
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 1014 456"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="none"
          style={{
            width: '100%',
            height: '100%',
          }}
        >
          <path d="M1014 424.748C1014 443.339 997.75 457.749 979.292 455.526L170.817 358.13C160.337 356.868 151.219 350.359 146.621 340.857L3.5387 45.2116C-6.42432 24.6255 8.57227 0.707031 31.4426 0.707031H983C1000.12 0.707031 1014 14.5862 1014 31.707V424.748Z" fill="#797EEC"/>
        </svg>
      </div>

      {/* Leadership Image - Over the SVG, rounded with margins */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: isMobile ? '20px' : '40px', // More margin on right to show SVG
        height: isMobile ? '350px' : '550px',
        backgroundImage: `url(${LeadershipImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        borderRadius: '20px', // Rounded corners
        overflow: 'hidden',
        zIndex: 2, // Above the SVG
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)', // Add shadow for depth
      }}>
        {/* Optional overlay for better text readability if needed */}
        <div style={{
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(0, 0, 0, 0.1)', // Light overlay
          borderRadius: '20px',
        }} />
      </div>
      </div>
    </div>
    </>
  );
};

export default LeadershipAndPlanning;
