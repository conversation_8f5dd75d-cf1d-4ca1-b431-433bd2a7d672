import { useState, useRef } from "react";
import {
  BarChart3,
  DollarSign,
  TrendingUp,
  Calendar,
  Bell,
  FolderOpen,
} from "lucide-react";
import booksIcon from "../assets/images/books-icon.png";
import Banner from "../components/grantready/Banner";
import ReimagineBanner from "../components/grantready/ReimagineBanner";
import StatsSection from "../components/grantready/StatsSection";
import TrustedBy from "../components/grantready/TrustedBy";
import ClientTestimonial from "../components/grantready/ClientTestemonial";
import Accelerate from "../components/elenor/Accelerate";
import Works from "../components/grantready/Works";
import GrantReadyFeature from "../components/grantready/GrantReadyFeature";
import ExperienceSection from "../components/grantready/ExperienceSection";
import PricingSection from "../components/grantready/PricingSection";
import { Colors } from "../constants/Colors";
import EmpowerYourTeam, {
  CardData,
} from "../components/grantready/EmpowerYourTeam";
import Works2 from "../components/grantready/Works2";
import EmergencyManagementBanner from "../components/elenor/EmergencyManagementBanner";
import grantReadyCtaVideoMix from "../assets/video/elenor-cta-bg-mix.mov";
import ChangedText from "../components/ui/ChangedText.tsx";

export default function GrantReady() {
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const closeVideoModal = () => {
    if (videoRef.current) {
      videoRef.current.pause();
    }
    setIsVideoModalOpen(false);
  };

  // GrantReady specific card data
  const grantReadyCardsData: CardData[] = [
    {
      id: 1,
      title: "Comprehensive Planning",
      description:
        "Easily create and manage capability planning guides, work plans, and spend plans all in one platform",
      icon: BarChart3,
      gradientFrom: "#4F63D9",
      gradientTo: "#539CAF",
    },
    {
      id: 2,
      title: "Financial Management",
      description:
        "Streamline invoicing, expenditure reporting, and budget tracking to ensure compliance and efficient fund utilization.",
      icon: DollarSign,
      gradientFrom: "#6358A6",
      gradientTo: "#4673B7",
    },
    {
      id: 3,
      title: "Smart Reports & Dashboards",
      description:
        "Generate comprehensive reports and visualize data through intuitive dashboards for better decision making.",
      icon: TrendingUp,
      gradientFrom: "#5CF0FE",
      gradientTo: "#46A59A",
    },
    {
      id: 4,
      title: "Timeline Management",
      description:
        "Stay on schedule with built-in calendars, timelines, and milestones tracking for the entire 5-year grant lifecycle",
      icon: Calendar,
      gradientFrom: "#47A0A0",
      gradientTo: "#5CF0FE",
    },
    {
      id: 5,
      title: "Smart Notifications",
      description:
        "Never miss an important deadline with automated reminders and timely notifications for all stakeholders.",
      icon: Bell,
      gradientFrom: "#4F69D4",
      gradientTo: "#46A59A",
    },
    {
      id: 6,
      title: "Document Repository",
      description:
        "Centralize all grant-related documents with secure storage, version control, and easy access for team collaboration.",
      icon: FolderOpen,
      gradientFrom: "#6583FF",
      gradientTo: "#46A19D",
    },
  ];

  return (
    <div className="px-0">
      {/* New Banner Section */}
      <Banner />
      <StatsSection />
      <EmpowerYourTeam cardsData={grantReadyCardsData} />
      <Works />
      <ChangedText name={'grant'} />
      <TrustedBy
        title="Trusted by Health Jurisdictions across the states"
        description="Leading health departments and organizations rely on GrantReady for their grant management needs"
      />
      <PricingSection />
      <ClientTestimonial
        color={Colors.GRANTREADY}
        title="See how clients are future proofing their grant."
        description="Health jurisdictions across the country are transforming their grant management with GrantReady"
      />
    </div>
  );
}
