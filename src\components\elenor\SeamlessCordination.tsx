import React from 'react'
import { Users, <PERSON><PERSON>hart3, CheckCircle, ArrowRight } from 'lucide-react'

const SeamlessCordination = () => {
  const steps = [
    {
      id: '01',
      icon: Users,
      title: 'Connect Your Teams',
      description: 'Bring all your responders and partners onto one real-time platform.'
    },
    {
      id: '02',
      icon: BarChart3,
      title: 'Monitor & Manage',
      description: 'Use live dashboards and maps to track incidents and resources effortlessly.'
    },
    {
      id: '03',
      icon: CheckCircle,
      title: 'Respond & Resolve',
      description: 'Coordinate alerts, assign tasks, and document actions until the incident is closed.'
    }
  ]

  return (
    <div className="max-w-6xl mx-auto px-4 py-16">
      {/* Header */}
      <div className="text-center mb-16">
        <h2 className="text-2xl md:text-4xl font-platform mb-4">
          Seamless Coordination in 3 Easy Steps
        </h2>
        <p className="text-lg max-w-3xl mx-auto">
          Our streamlined process gets your team from crisis detection to resolution efficiently.
        </p>
      </div>

      {/* Steps */}
      <div className="grid md:grid-cols-3 gap-8 mb-12">
        {steps.map((step) => {
          const IconComponent = step.icon
          return (
            <div key={step.id} className="border border-blue-400 rounded-2xl p-8 text-center relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-br from-[#2F8AFF] to-[#322072] text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold">
                {step.id}
              </div>
              <div className="mb-6 mt-4">
                <div className="w-24 h-24 rounded-full flex items-center justify-center mx-auto">
                  <IconComponent className="w-20 h-20 text-blue-500" />
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {step.title}
              </h3>
              <p className="text-gray-600">
                {step.description}
              </p>
            </div>
          )
        })}
      </div>

      {/* CTA Button */}
      <div className="text-center">
        <button className="bg-black text-white pl-4 pr-1 py-1 rounded-md font-semibold hover:bg-gray-800 transition-colors duration-200 flex items-center gap-2 mx-auto">
          See It In Action
          <span className='bg-white p-2 rounded-md'>
            <ArrowRight className="w-5 h-5 text-black" />
          </span>
        </button>
      </div>
    </div>
  )
}

export default SeamlessCordination