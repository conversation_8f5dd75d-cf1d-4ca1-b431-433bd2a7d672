-- Secrets table for SMTP settings
DROP TABLE IF EXISTS secrets;
CREATE TABLE IF NOT EXISTS secrets (
    id SERIAL PRIMARY KEY,
    key TEXT NOT NULL UNIQUE,
    value TEXT NOT NULL, -- Encrypted value
    updated_at TIMESTAMP DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id)
);

-- ALTER TABLE secrets ENABLE ROW LEVEL SECURITY;

-- CREATE POLICY admin_only ON secrets
--     FOR ALL
--     TO authenticated
--     USING (
--         (SELECT role FROM public.profiles WHERE id = auth.uid()) = 'admin'
--     );