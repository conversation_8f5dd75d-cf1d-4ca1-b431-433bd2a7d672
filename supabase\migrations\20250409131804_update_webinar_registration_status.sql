-- Migration: Update webinar_registrations table to use status instead of attended
-- Purpose: Replace the boolean 'attended' column with a more flexible 'status' column

-- First, add the new status column
ALTER TABLE webinar_registrations 
ADD COLUMN status TEXT DEFAULT 'registered' CHECK (status IN ('registered', 'cancelled', 'attended', 'no_show'));

-- Update existing records: set status to 'attended' where attended = true
UPDATE webinar_registrations
SET status = 'attended'
WHERE attended = true;

-- Drop the old attended column
ALTER TABLE webinar_registrations
DROP COLUMN attended;

-- Add comment to explain the status column
COMMENT ON COLUMN webinar_registrations.status IS 'Registration status: registered, cancelled, attended, no_show';
