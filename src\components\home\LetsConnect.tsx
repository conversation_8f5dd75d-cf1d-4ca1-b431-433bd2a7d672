import React, { useState, useRef } from "react";
import { MapPin, Mail, Phone, ArrowRight } from "lucide-react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import <PERSON>aptcha from "@hcaptcha/react-hcaptcha";
import { supabase } from "../../lib/supabase";
import Alert from "../ui/Alert";
import letsConnectImg from "../../assets/images/lets-connect.jpg";
import { useSiteSettings } from "../../hooks/useSiteSettings";

import bgImg from "../../assets/images/homepage/letsconnect-bgcolor.png";

const schema = yup.object().shape({
  name: yup.string().required("Full name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  phone: yup
    .string()
    .matches(/^[0-9]+$/, "Phone number must contain only numbers"),
  company: yup.string(),
  message: yup.string().required("Message is required"),
});

const LetsConnect: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState("");
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const captcha = useRef<HCaptcha>(null);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const { settings, loading } = useSiteSettings();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data: any) => {
    if (!captchaToken) {
      setSubmitError("Please complete the hCaptcha verification");
      return;
    }

    setIsSubmitting(true);
    setSubmitError("");

    try {
      // First verify the captcha with your backend (optional additional verification)
      const { error: verifyError } = await supabase.rpc("verify_captcha", {
        token: captchaToken,
      });

      if (verifyError) throw verifyError;

      // Then insert the form data
      const { error: insertError } = await supabase
        .from("contact_submissions")
        .insert([
          {
            ...data,
            captcha_token: captchaToken,
            submitted_at: new Date().toISOString(),
          },
        ]);

      if (insertError) throw insertError;

      // Send email to admin
      const { error: emailError } = await supabase.functions.invoke(
        "send-contact-email",
        {
          body: {
            to: "", // Updated recipient
            subject: `New Contact Submission from ${data.name} - IRS Website`,
            html: `
            <!DOCTYPE html>
            <html lang="en">
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>New Contact Submission</title>
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8fafc;">
              <div style="max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 2px;">
                <div style="background: white; border-radius: 8px; overflow: hidden;">

                  <!-- Header -->
                  <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px 40px; text-align: center;">
                    <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 600; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                      New Contact Submission
                    </h1>
                    <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">
                      International Responder Systems Website
                    </p>
                  </div>

                  <!-- Content -->
                  <div style="padding: 40px;">
                    <div style="background: #f8fafc; border-radius: 12px; padding: 30px; margin-bottom: 30px; border-left: 5px solid #667eea;">
                      <h2 style="color: #2d3748; margin: 0 0 20px 0; font-size: 20px; font-weight: 600;">
                        Contact Information
                      </h2>

                      <table cellpadding="0" cellspacing="0" border="0" style="width: 100%; border-collapse: collapse;">
                        <tr>
                          <td style="padding: 15px; background: white; border-radius: 8px; margin-bottom: 10px; border-left: 4px solid #667eea;">
                            <table cellpadding="0" cellspacing="0" border="0" style="width: 100%;">
                              <tr>
                                <td style="font-weight: 600; color: #2d3748; font-size: 14px; padding-bottom: 5px;">FULL NAME</td>
                              </tr>
                              <tr>
                                <td style="color: #4a5568; font-size: 16px; line-height: 1.4;">${
                                  data.name
                                }</td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                        <tr><td style="height: 10px;"></td></tr>

                        <tr>
                          <td style="padding: 15px; background: white; border-radius: 8px; margin-bottom: 10px; border-left: 4px solid #48bb78;">
                            <table cellpadding="0" cellspacing="0" border="0" style="width: 100%;">
                              <tr>
                                <td style="font-weight: 600; color: #2d3748; font-size: 14px; padding-bottom: 5px;">EMAIL ADDRESS</td>
                              </tr>
                              <tr>
                                <td style="color: #4a5568; font-size: 16px, line-height: 1.4;">
                                  <a href="mailto:${
                                    data.email
                                  }" style="color: #667eea; text-decoration: none; font-weight: 500;">${
              data.email
            }</a>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>

                        ${
                          data.phone
                            ? `
                        <tr><td style="height: 10px;"></td></tr>
                        <tr>
                          <td style="padding: 15px; background: white; border-radius: 8px; margin-bottom: 10px; border-left: 4px solid #ed8936;">
                            <table cellpadding="0" cellspacing="0" border="0" style="width: 100%;">
                              <tr>
                                <td style="font-weight: 600; color: #2d3748; font-size: 14px; padding-bottom: 5px;">PHONE NUMBER</td>
                              </tr>
                              <tr>
                                <td style="color: #4a5568; font-size: 16px; line-height: 1.4;">
                                  <a href="tel:${data.phone}" style="color: #667eea; text-decoration: none; font-weight: 500;">${data.phone}</a>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>`
                            : ""
                        }

                        ${
                          data.company
                            ? `
                        <tr><td style="height: 10px;"></td></tr>
                        <tr>
                          <td style="padding: 15px; background: white; border-radius: 8px; margin-bottom: 10px; border-left: 4px solid #9f7aea;">
                            <table cellpadding="0" cellspacing="0" border="0" style="width: 100%;">
                              <tr>
                                <td style="font-weight: 600; color: #2d3748; font-size: 14px; padding-bottom: 5px;">COMPANY NAME</td>
                              </tr>
                              <tr>
                                <td style="color: #4a5568; font-size: 16px; line-height: 1.4;">${data.company}</td>
                              </tr>
                            </table>
                          </td>
                        </tr>`
                            : ""
                        }
                      </table>
                    </div>

                    <!-- Message Section -->
                    <div style="background: #f8fafc; border-radius: 12px; padding: 30px; border-left: 5px solid #48bb78;">
                      <h2 style="color: #2d3748; margin: 0 0 20px 0; font-size: 20px; font-weight: 600;">
                        Message
                      </h2>
                      <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e2e8f0; line-height: 1.6; white-space: pre-wrap; color: #4a5568; font-size: 16px;">
${data.message}
                      </div>
                    </div>

                    <!-- Timestamp -->
                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
                      <p style="color: #718096; font-size: 14px; margin: 0;">
                        Received on ${new Date().toLocaleDateString("en-US", {
                          weekday: "long",
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </p>
                    </div>
                  </div>

                  <!-- Footer -->
                  <div style="background: #f7fafc; padding: 20px 40px; text-align: center; border-top: 1px solid #e2e8f0;">
                    <p style="color: #718096; font-size: 14px; margin: 0;">
                      This email was sent from the International Responder Systems contact form
                    </p>
                  </div>
                </div>
              </div>
            </body>
            </html>
          `,
          },
        }
      );

      if (emailError) throw emailError;

      // Reset form on success
      reset();
      captcha.current?.resetCaptcha();
      setCaptchaToken(null);
      setSubmitSuccess(true);
    } catch (error: unknown) {
      console.error("Full error details:", error);
      setSubmitError(
        error instanceof Error
          ? error.message
          : "Failed to send message. Please try again later."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="relative overflow-hidden p-0 rounded-8">
      {/* Background Video */}
      <img
        src={bgImg}
        alt="Background"
        className="absolute inset-0 w-full h-full object-cover z-0 pointer-events-none"
      />
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden z-10 pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-600/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-20 max-w-7xl mx-auto px-4 py-8 md:px-[5px] md:py-[50px]">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 lg:items-stretch">
          {/* Left side - Team Image and Contact Info */}
          <div className="flex flex-col md:gap-y-12 gap-y-4 h-full">
            <h2 className="font-platform text-black font-medium mb-0 lg:h-[50px] leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">
              Let's Connect!
            </h2>
            <div className="space-y-3 flex-1 flex flex-col">
              {/* Team Image */}
              <div className="relative group flex-shrink-0">
                <div className="relative overflow-hidden rounded-8 shadow-2xl transform transition-transform duration-300">
                  <img
                    src={letsConnectImg}
                    alt="Team members standing together"
                    className="w-full h-64 sm:h-72 lg:h-80 object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <p className="text-white font-medium text-sm sm:text-base leading-relaxed">
                      Reach out to us and help revolutionize animal feed with
                      innovative, eco-friendly solutions
                    </p>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-white/90 backdrop-blur-sm rounded-8 shadow-xl p-2 lg:p-6 flex-1">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center lg:text-left">
                  Get in Touch
                </h3>

                <div className="space-y-6">
                  {/* Address */}
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                      <MapPin className="h-6 w-6 text-red-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900 mb-1">
                        Address
                      </h4>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {loading ? (
                          <span className="animate-pulse bg-gray-200 h-4 w-full block rounded"></span>
                        ) : (
                          <>
                            {settings?.general?.address || '157 E Main St, Elkton, MD 21921-5977'}
                            <br />
                            United States
                          </>
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Email */}
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <Mail className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900 mb-1">
                        Email
                      </h4>
                      {loading ? (
                        <span className="animate-pulse bg-gray-200 h-4 w-32 block rounded"></span>
                      ) : (
                        <a
                          href={`mailto:${settings?.general?.contactEmail || '<EMAIL>'}`}
                          className="text-blue-600 hover:text-blue-700 text-sm underline decoration-2 underline-offset-2 transition-colors duration-300 break-all"
                        >
                          {settings?.general?.contactEmail || '<EMAIL>'}
                        </a>
                      )}
                    </div>
                  </div>

                  {/* Phone */}
                  {(settings?.general?.phone || loading) && (
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <Phone className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-gray-900 mb-1">
                          Phone
                        </h4>
                        {loading ? (
                          <span className="animate-pulse bg-gray-200 h-4 w-24 block rounded"></span>
                        ) : (
                          <a
                            href={`tel:${settings?.general?.phone}`}
                            className="text-green-600 hover:text-green-700 text-sm underline decoration-2 underline-offset-2 transition-colors duration-300"
                          >
                            {settings?.general?.phone}
                          </a>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Contact Form */}
          <div className="flex flex-col gap-y-12 h-full">
            <p className="text-center md:flex hidden lg:text-left h-[50px] w-full lg:w-full font-inter font-medium text-lg leading-[31.4px] tracking-normal">
              With a passionate and dedicated team, International Responder
              Systems is ready to serve you.
            </p>

            <div className="bg-white/95 backdrop-blur-sm rounded-8 shadow-xl p-6 lg:p-8 flex-1">
              {/* Form Header */}
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2 text-center lg:text-left">
                  Send us a Message
                </h3>
                <p className="text-sm text-gray-600 text-center lg:text-left">
                  We'd love to hear from you. Fill out the form below and we'll
                  get back to you soon.
                </p>
              </div>

              {submitSuccess && (
                <div className="mb-4">
                  <Alert
                    type="success"
                    message="Message sent successfully!"
                    onClose={() => setSubmitSuccess(false)}
                  />
                </div>
              )}

              {submitError && (
                <div className="mb-4">
                  <Alert
                    type="error"
                    message={submitError}
                    onClose={() => setSubmitError("")}
                  />
                </div>
              )}

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
                {/* Name and Email Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      {...register("name")}
                      placeholder="Enter your full name"
                      className="w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 hover:border-gray-400"
                    />
                    {errors.name && (
                      <p className="mt-1 text-xs text-red-600">
                        {errors.name.message}
                      </p>
                    )}
                  </div>
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      {...register("email")}
                      placeholder="Enter your email"
                      className="w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 hover:border-gray-400"
                    />
                    {errors.email && (
                      <p className="mt-1 text-xs text-red-600">
                        {errors.email.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Phone and Company Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="phone"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      {...register("phone")}
                      placeholder="Enter your phone number"
                      className="w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 hover:border-gray-400"
                    />
                    {errors.phone && (
                      <p className="mt-1 text-xs text-red-600">
                        {errors.phone.message}
                      </p>
                    )}
                  </div>
                  <div>
                    <label
                      htmlFor="company"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Company Name
                    </label>
                    <input
                      type="text"
                      id="company"
                      {...register("company")}
                      placeholder="Enter your company name"
                      className="w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 hover:border-gray-400"
                    />
                  </div>
                </div>

                {/* Message */}
                <div>
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Message *
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    {...register("message")}
                    placeholder="Write how can we assist you"
                    className="w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 hover:border-gray-400 resize-vertical"
                  />
                  {errors.message && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.message.message}
                    </p>
                  )}
                </div>

                {/* hCaptcha */}
                <div className="flex justify-center lg:justify-start">
                  <HCaptcha
                    ref={captcha}
                    sitekey={import.meta.env.VITE_HCAPTCHA_SITE_KEY}
                    onVerify={(token) => setCaptchaToken(token)}
                    onExpire={() => setCaptchaToken(null)}
                  />
                </div>

                {/* Submit Button */}
                <div className="flex justify-end rounded-8">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-black text-white p-1 capitalize rounded-8 font-normal text-sm md:text-base hover:bg-gray-800 transition-all duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed "
                  >
                    <span className="flex items-start justify-start p-0 pl-2 pr-6 relative overflow-hidden">
                      <span
                        className={`transition-all duration-500 ${
                          isSubmitting
                            ? "opacity-0 translate-y-2"
                            : "opacity-100 translate-y-0"
                        }`}
                      >
                        Submit
                      </span>
                      <span
                        className={`absolute inset-0 flex items-center justify-center transition-all duration-500 ${
                          isSubmitting
                            ? "opacity-100 translate-y-0"
                            : "opacity-0 -translate-y-2"
                        }`}
                      >
                        Sending...
                      </span>
                    </span>
                    <span className="bg-white text-black h-full py-2 px-2 rounded-md flex items-center justify-center relative overflow-hidden">
                      <span
                        className={`transition-all duration-500 ${
                          isSubmitting
                            ? "opacity-0 rotate-90 scale-0"
                            : "opacity-100 rotate-0 scale-100"
                        }`}
                      >
                        <ArrowRight size={22} />
                      </span>
                      <span
                        className={`absolute inset-0 flex items-center justify-center transition-all duration-500 ${
                          isSubmitting
                            ? "opacity-100 rotate-0 scale-100"
                            : "opacity-0 -rotate-90 scale-0"
                        }`}
                      >
                        <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                      </span>
                    </span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LetsConnect;
