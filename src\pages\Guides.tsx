import React, { useState, useEffect, useCallback } from 'react';
import { FileText, Download, Search } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';
import LoginModal from '../components/LoginModal';

interface Guide {
  id: string;
  title: string;
  description: string;
  image_url?: string;
  pdf_url?: string;
  category: string;
  type: string;
  is_public: boolean;
  download_count: number;
  created_at: string;
}

export default function Guides() {
  const [guides, setGuides] = useState<Guide[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const user = useAuthStore((state) => state.user);

  const categories = [
    "Emergency Response",
    "Grant Management",
    "Public Health Data",
    "Compliance",
    "Best Practices",
    "Implementation"
  ];

  const fetchGuides = useCallback(async () => {
    try {
      let query = supabase
        .from('guides')
        .select('*')
        .eq('type', 'guide') // Only fetch items with type 'guide'
        .order('created_at', { ascending: false });

      if (!user) {
        query = query.eq('is_public', true);
      }

      if (selectedCategory) {
        query = query.eq('category', selectedCategory);
      }

      if (searchQuery) {
        query = query.ilike('title', `%${searchQuery}%`);
      }

      const { data, error } = await query;

      if (error) throw error;
      setGuides(data);
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [user, selectedCategory, searchQuery]);

  useEffect(() => {
    fetchGuides();
  }, [fetchGuides]);

  const handleDownload = useCallback(async (guideId: string, pdfUrl: string) => {
    // Check if user is authenticated
    if (!user) {
      // Show login modal
      setSelectedPdf({ id: guideId, url: pdfUrl });
      setShowLoginModal(true);
      return;
    }

    try {
      // First get current download count
      const { data: guideData, error: fetchError } = await supabase
        .from('guides')
        .select('download_count')
        .eq('id', guideId)
        .single();

      if (fetchError) throw fetchError;

      // Increment download count
      const { error: updateError } = await supabase
        .from('guides')
        .update({ download_count: (guideData.download_count || 0) + 1 })
        .eq('id', guideId);

      if (updateError) throw updateError;

      // Handle download
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = pdfUrl.split('/').pop() || 'guide.pdf';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Refresh guides to show updated count
      fetchGuides();
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || 'Failed to download guide');
    }
  }, [user, fetchGuides]);

  // Update the Featured Guides section render:
  // Update the search input in the Hero section:
  const [expandedDescriptions, setExpandedDescriptions] = useState<Record<string, boolean>>({});
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState<{ id: string, url: string } | null>(null);

  const toggleDescription = (guideId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [guideId]: !prev[guideId]
    }));
  };

  // Handle closing the login modal
  const handleCloseLoginModal = () => {
    setShowLoginModal(false);
    setSelectedPdf(null);
  };

  // Handle continuing with download after login
  const handleContinueDownload = useCallback(() => {
    if (selectedPdf && user) {
      handleDownload(selectedPdf.id, selectedPdf.url);
      setSelectedPdf(null);
    }
  }, [selectedPdf, user, handleDownload]);

  // Check if user changed and we have a pending download
  useEffect(() => {
    if (user && selectedPdf) {
      handleContinueDownload();
    }
  }, [user, selectedPdf, handleContinueDownload]);

  return (
    <div className="">
      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={handleCloseLoginModal}
        title="Download Guide"
        message="Please log in to download this guide. Logging in allows us to save your information for future downloads."
      />

      {/* Error display */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 mx-4 sm:mx-6 lg:mx-8">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  onClick={() => setError(null)}
                  className="inline-flex rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Hero Section */}
      <section className="text-white py-24 relative rounded-lg" style={{ background: 'radial-gradient(50% 55% at 0% 0%, #6366F1 0%, #7C3AED 100%)' }}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Medium circles */}
          <div className="absolute top-1/2 -left-20 w-32 h-32 bg-white/4 rounded-full"></div>
          <div className="absolute bottom-10 left-1/4 w-24 h-24 bg-white/3 rounded-full"></div>

          {/* Small floating elements */}
          <div className="absolute top-16 left-1/3 w-4 h-4 bg-white/20 rounded-full animate-pulse"></div>
          <div className="absolute bottom-32 right-1/4 w-3 h-3 bg-white/30 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute top-1/3 right-1/3 w-2 h-2 bg-white/25 rounded-full animate-pulse delay-500"></div>
        </div>

        {/* Creative illustration on the right - Made bigger */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block overflow-hidden">
          {/* Main illustration container */}
          <div className="relative h-full flex items-center justify-center">
            {/* Digital library visualization - Scaled up */}
            <div className="relative scale-150">
              {/* Floating documents/books - Made bigger */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float">
                <div className="p-3">
                  <div className="w-full h-2.5 bg-indigo-300/50 rounded mb-1.5"></div>
                  <div className="w-3/4 h-1.5 bg-indigo-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-indigo-200/50 rounded mb-1"></div>
                  <div className="w-1/2 h-1.5 bg-indigo-200/50 rounded"></div>
                </div>
              </div>

              <div className="absolute -top-6 -left-8 w-18 h-22 bg-white/8 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed">
                <div className="p-2.5">
                  <div className="w-full h-2 bg-indigo-300/50 rounded mb-1"></div>
                  <div className="w-2/3 h-1.5 bg-indigo-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-indigo-200/50 rounded mb-1"></div>
                  <div className="w-3/4 h-1.5 bg-indigo-200/50 rounded"></div>
                </div>
              </div>

              {/* Central guide book icon - Made bigger */}
              <div className="w-32 h-40 bg-white/15 rounded-xl backdrop-blur-sm border border-white/20 flex items-center justify-center transform hover:scale-105 transition-transform duration-300">
                <FileText className="h-16 w-16 text-white/80" />
              </div>

              {/* More floating elements - Made bigger */}
              <div className="absolute -bottom-8 -right-4 w-16 h-20 bg-white/12 rounded-lg backdrop-blur-sm transform rotate-6 animate-float">
                <div className="p-2">
                  <div className="w-full h-2 bg-indigo-300/50 rounded mb-1"></div>
                  <div className="w-3/4 h-1.5 bg-indigo-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-indigo-200/50 rounded"></div>
                </div>
              </div>

              <div className="absolute -bottom-4 -left-6 w-14 h-18 bg-white/10 rounded-lg backdrop-blur-sm transform -rotate-12 animate-float-delayed">
                <div className="p-2">
                  <div className="w-full h-1.5 bg-indigo-300/50 rounded mb-1"></div>
                  <div className="w-2/3 h-1.5 bg-indigo-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-indigo-200/50 rounded"></div>
                </div>
              </div>

              {/* Connecting lines/paths - Made more visible */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-24 h-0.5 bg-white/20 transform rotate-45 absolute"></div>
                <div className="w-20 h-0.5 bg-white/15 transform -rotate-45 absolute top-4"></div>
                <div className="w-16 h-0.5 bg-white/20 transform rotate-12 absolute -top-4 left-2"></div>
              </div>

              {/* Orbiting elements - Made bigger */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-48 h-48 border border-white/10 rounded-full animate-spin-slow">
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white/30 rounded-full"></div>
                  <div className="absolute top-1/2 -right-2 transform -translate-y-1/2 w-3 h-3 bg-white/25 rounded-full"></div>
                  <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white/20 rounded-full"></div>
                  <div className="absolute top-1/2 -left-2 transform -translate-y-1/2 w-3 h-3 bg-white/25 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content pushed to the left */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Guides Center
              <span className="block text-3xl font-normal mt-2 text-indigo-200">
                Your Implementation Hub
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Access comprehensive guides and step-by-step documentation to enhance your Public Health emergency response capabilities
            </p>

            {/* Search functionality integrated into banner */}
            <div className="relative w-full max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search guides..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3.5 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => setSelectedCategory('')}
              className={`px-6 py-2 rounded-full shadow-sm hover:shadow-md transition-shadow font-medium ${selectedCategory === ''
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 hover:text-blue-600'
                }`}
            >
              All Categories
            </button>
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-2 rounded-full shadow-sm hover:shadow-md transition-shadow font-medium ${selectedCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:text-blue-600'
                  }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Guides */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold mb-8">Featured Guides</h2>

          {loading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : guides.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 px-4 bg-white rounded-lg shadow-sm">
              <FileText className="h-16 w-16 text-blue-200 mb-4" />
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No Guides Available</h3>
              <p className="text-gray-500 text-center max-w-md mb-6">
                {searchQuery && selectedCategory
                  ? `No guides found matching "${searchQuery}" in the "${selectedCategory}" category.`
                  : searchQuery
                    ? `No guides found matching "${searchQuery}".`
                    : selectedCategory
                      ? `No guides available in the "${selectedCategory}" category yet.`
                      : "No guides available yet. Please check back later."}
              </p>
              {(searchQuery || selectedCategory) && (
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('');
                  }}
                  className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                >
                  Clear Filters
                </button>
              )}
            </div>
          ) : (
            <div className="grid md:grid-cols-3 gap-8">
              {guides.map((guide) => (
                <div key={guide.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                  {guide.image_url && (
                    <img
                      src={guide.image_url}
                      alt={guide.title}
                      className="w-full h-48 object-cover"
                    />
                  )}
                  <div className="p-6">
                    <span className="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                      {guide.category}
                    </span>
                    <h3 className="text-xl font-bold mt-4 mb-2">{guide.title}</h3>
                    <div className="mb-4">
                      <p className="text-gray-600">
                        {expandedDescriptions[guide.id]
                          ? guide.description
                          : `${guide.description?.substring(0, 70)}${guide.description?.length > 70 ? '...' : ''}`
                        }
                      </p>
                      {guide.description?.length > 70 && (
                        <button
                          onClick={() => toggleDescription(guide.id)}
                          className="text-blue-600 text-sm mt-1 hover:underline"
                        >
                          {expandedDescriptions[guide.id] ? 'See Less' : 'See More'}
                        </button>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-gray-500">
                        <Download className="h-4 w-4 mr-1" />
                        <span>{guide.download_count} downloads</span>
                      </div>
                      <button
                        onClick={() => guide.pdf_url && handleDownload(guide.id, guide.pdf_url)}
                        className="flex items-center text-blue-600 font-medium hover:text-blue-700"
                        disabled={!guide.pdf_url}
                      >
                        Download PDF
                        <FileText className="h-4 w-4 ml-2" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
}