// src/components/elenor/EmergencyManagementBanner.tsx
import React from 'react';
import { Link } from 'react-router-dom';

interface EmergencyManagementBannerProps {
  videoSrc: string;
  gradientFrom?: string;
  gradientTo?: string;
  gradientOpacity?: string;
}

const EmergencyManagementBanner: React.FC<EmergencyManagementBannerProps> = ({
  videoSrc,
  gradientFrom = 'blue-900',
  gradientTo = 'blue-500',
  gradientOpacity = '75',
}) => {
  return (
    <section className="relative h-[450px] overflow-hidden mx-4 md:px-8 rounded-lg">
      <video
        autoPlay
        loop
        muted
        playsInline // Add playsInline for better mobile support
        className="absolute inset-0 w-full h-full object-cover"
      >
        <source src={videoSrc} type="video/mp4" /> {/* Changed to video/mp4 */}
        <source src={videoSrc} type="video/webm" /> {/* Added WebM as fallback */}
        Your browser does not support the video tag.
      </video>
      {/* Use dynamic gradient classes based on props */}
      <div className={`absolute inset-0 bg-gradient-to-r from-${gradientFrom} to-${gradientTo} opacity-${gradientOpacity}`}></div> {/* Overlay for gradient effect */}
      <div className="container mx-auto px-2 relative z-10 flex items-start h-full"> {/* Center content vertically */}
        <div className="bg-white py-8 px-2 md:p-5 max-w-2xl ml-auto rounded-lg shadow-lg pt-8 mt-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Ready to Elevate Your Emergency Management?
          </h2>
          <p className="text-gray-600 mb-6">
            Experience the power of real-time coordination and incident management. Let's get started.
          </p>
          <div className="flex space-x-4">
            <Link to="/contact" className="flex items-center px-3 py-3 bg-black text-white rounded-md hover:bg-gray-800 transition duration-300">
              Contact Us
              <div className="ml-4 w-8 h-8 bg-white rounded flex items-center justify-center">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.829 9.44331H0.896484V7.48314H12.829L7.34053 1.99467L8.73715 0.622559L16.5778 8.46322L8.73715 16.3039L7.34053 14.9318L12.829 9.44331Z" fill="black"/>
</svg>

              </div>
            </Link>
            <Link to="/book-a-demo" className="flex items-center gap-2 px-3 py-3 border border-gray-300 text-gray-800 rounded-md hover:bg-gray-100 transition duration-300">
              See A Demo
              <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.829 9.44331H0.896484V7.48314H12.829L7.34053 1.99467L8.73715 0.622559L16.5778 8.46322L8.73715 16.3039L7.34053 14.9318L12.829 9.44331Z" fill="black"/>
</svg>

            </Link>
          </div>
        </div>
        {/* Placeholder for the small circular image */}
        {/* You might need to adjust positioning based on the actual layout */}
        {/* Removed absolute positioning from here to keep it with the content block */}
        {/* <div className="absolute bottom-[-30px] right-10 md:right-20 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg">
            {/* Add the image tag here */}
            {/* <img src="..." alt="Avatar" className="w-14 h-14 rounded-full" /> */}
        {/* </div> */}
      </div>
    </section>
  );
};

export default EmergencyManagementBanner;
