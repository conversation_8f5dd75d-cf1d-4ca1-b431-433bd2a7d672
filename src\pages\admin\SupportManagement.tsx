import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { MessageSquare, Search, Filter, Clock, CheckCircle, XCircle, AlertCircle, Mail } from 'lucide-react';

interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  user_id: string;
  contact_name: string;
  contact_phone: string;
  resolution_comment?: string;
  resolved_by?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  user_email: string;
  user_full_name: string;
}

export default function SupportManagement() {
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedPriority, setSelectedPriority] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [resolutionComment, setResolutionComment] = useState('');
  const [resolvingTicketId, setResolvingTicketId] = useState<string | null>(null);

  useEffect(() => {
    fetchTickets();
  }, [selectedStatus, selectedPriority, searchQuery]);

  const fetchTickets = async () => {
    try {
      let query = supabase
        .from('support_tickets_with_users')
        .select('*')
        .order('created_at', { ascending: false });

      if (selectedStatus) {
        query = query.eq('status', selectedStatus);
      }

      if (selectedPriority) {
        query = query.eq('priority', selectedPriority);
      }

      if (searchQuery) {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
      }

      const { data, error: fetchError } = await query;

      if (fetchError) throw fetchError;
      setTickets(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (ticketId: string, newStatus: string) => {
    try {
      if (newStatus === 'resolved' && !resolutionComment) {
        setResolvingTicketId(ticketId);
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('Not authenticated');
      }

      let updatedTicket: SupportTicket | null = null;

      if (newStatus === 'resolved' && resolutionComment) {
        const { error } = await supabase.rpc('resolve_ticket_with_notification', {
          ticket_id: ticketId,
          resolution: resolutionComment,
          resolver_id: user.id
        });

        if (error) throw error;
      } else {
        const { data, error } = await supabase
          .from('support_tickets')
          .update({ status: newStatus })
          .eq('id', ticketId)
          .select();

        if (error) throw error;
        if (data && data.length > 0) {
          updatedTicket = data[0];
        }
      }

      // Find the ticket info (from state or from update)
      const ticket = tickets.find(t => t.id === ticketId) || updatedTicket;
      if (ticket && ticket.user_email) {
        // Compose email content
        let statusText = newStatus.replace('_', ' ').toUpperCase();
        let resolutionText = (newStatus === 'resolved' && resolutionComment)
          ? `<p><strong>Resolution:</strong> ${resolutionComment}</p>`
          : '';
        let emailBody = `
          <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <h2 style="color: #2c3e50;">Your Support Ticket Status Has Changed</h2>
            <p>Dear ${ticket.contact_name},</p>
            <p>Your support ticket <strong>#${ticket.id.split('-')[0]}</strong> ("${ticket.title}") status has been updated to <strong>${statusText}</strong>.</p>
            ${resolutionText}
            <p><strong>Description:</strong> ${ticket.description}</p>
            <p>If you have further questions, please reply to this email.</p>
            <p>Thank you,<br/>IRS Support Team</p>
          </div>
        `;
        // Send email via Supabase Edge Function
        await supabase.functions.invoke('send-contact-email', {
          body: {
            to: ticket.user_email,
            subject: `Support Ticket #${ticket.id.split('-')[0]} Status Updated: ${statusText}`,
            html: emailBody
          }
        });
      }

      setResolvingTicketId(null);
      setResolutionComment('');
      fetchTickets();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleEmailUser = (ticket: SupportTicket) => {
    if (!ticket.user_email) {
      setError('User email not found');
      return;
    }

    const subject = encodeURIComponent(`Re: Support Ticket #${ticket.id.split('-')[0]}`);
    const body = encodeURIComponent(`Dear ${ticket.contact_name},\n\nRegarding your support ticket: ${ticket.title}\n\n`);
    window.location.href = `mailto:${ticket.user_email}?subject=${subject}&body=${body}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'resolved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'closed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Support Tickets</h2>
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search tickets..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={selectedStatus || ''}
            onChange={(e) => setSelectedStatus(e.target.value || null)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Status</option>
            <option value="open">Open</option>
            <option value="in_progress">In Progress</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
          </select>
          <select
            value={selectedPriority || ''}
            onChange={(e) => setSelectedPriority(e.target.value || null)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Priority</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="divide-y">
          {tickets.map((ticket) => (
            <div key={ticket.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  {getStatusIcon(ticket.status)}
                  <div>
                    <h3 className="font-semibold text-lg mb-1">{ticket.title}</h3>
                    <p className="text-gray-600 mb-2">{ticket.description}</p>
                    <div className="flex items-center space-x-4 text-sm">
                      <span className={`px-2 py-1 rounded-full ${getPriorityColor(ticket.priority)}`}>
                        {ticket.priority}
                      </span>
                      <span className="text-gray-500">
                        {new Date(ticket.created_at).toLocaleDateString()}
                      </span>
                      <span className="text-gray-500">
                        {ticket.contact_name} ({ticket.contact_phone})
                      </span>
                      <span className="text-gray-500">
                        #{ticket.id.split('-')[0]}
                      </span>
                    </div>
                    {ticket.resolution_comment && (
                      <div className="mt-4 bg-gray-50 p-4 rounded-lg">
                        <p className="text-sm font-medium text-gray-700">Resolution:</p>
                        <p className="text-gray-600">{ticket.resolution_comment}</p>
                        {ticket.resolved_at && (
                          <p className="text-sm text-gray-500 mt-2">
                            Resolved on: {new Date(ticket.resolved_at).toLocaleString()}
                          </p>
                        )}
                      </div>
                    )}
                    {resolvingTicketId === ticket.id && (
                      <div className="mt-4">
                        <textarea
                          value={resolutionComment}
                          onChange={(e) => setResolutionComment(e.target.value)}
                          placeholder="Enter resolution details..."
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          rows={3}
                        />
                        <div className="mt-2 flex justify-end space-x-4">
                          <button
                            onClick={() => {
                              setResolvingTicketId(null);
                              setResolutionComment('');
                            }}
                            className="text-gray-600 hover:text-gray-900"
                          >
                            Cancel
                          </button>
                          <button
                            onClick={() => handleStatusChange(ticket.id, 'resolved')}
                            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
                          >
                            Resolve Ticket
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => handleEmailUser(ticket)}
                    className="p-2 text-gray-600 hover:text-blue-600 rounded-full"
                    title="Email User"
                    disabled={!ticket.user_email}
                  >
                    <Mail className="h-5 w-5" />
                  </button>
                  <select
                    value={ticket.status}
                    onChange={(e) => handleStatusChange(ticket.id, e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="open">Open</option>
                    <option value="in_progress">In Progress</option>
                    <option value="resolved">Resolved</option>
                    <option value="closed">Closed</option>
                  </select>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}