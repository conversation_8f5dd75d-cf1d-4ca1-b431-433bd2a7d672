import React from 'react';
import { Eye, Mail, ArrowRight, CheckCircle } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function Accessibility() {
  const commitments = [
    {
      title: "WCAG 2.1 Compliance",
      description: "We strive to meet Level A and AA guidelines of the Web Content Accessibility Guidelines (WCAG) 2.1."
    },
    {
      title: "ADA Compliance",
      description: "We work to comply with Americans with Disabilities Act (ADA) effective communication requirements."
    },
    {
      title: "Continuous Improvement",
      description: "We regularly review and enhance our platforms to ensure better accessibility for all users."
    }
  ];

  return (
    <div className="">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-4 mb-6">
            <Eye className="h-10 w-10" />
            <h1 className="text-4xl font-bold">Accessibility Statement</h1>
          </div>
          <p className="text-xl text-blue-100 max-w-3xl">
            We are committed to ensuring digital accessibility for people with disabilities.
          </p>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-xl p-8">
            {/* Main Statement */}
            <div className="mb-12">
              <p className="text-gray-600 leading-relaxed">
                International Responder Systems is committed to digital accessibility, and to conforming to the Web Content 
                Accessibility Guidelines (WCAG) 2.1, Level A and AA and complying with Americans with Disabilities Act (ADA) 
                effective communication requirements, and other applicable regulations.
              </p>
            </div>

            {/* Commitments */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-6">Our Commitments</h2>
              <div className="grid md:grid-cols-3 gap-8">
                {commitments.map((commitment, index) => (
                  <div key={index} className="bg-gray-50 p-6 rounded-lg">
                    <CheckCircle className="h-8 w-8 text-blue-600 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">{commitment.title}</h3>
                    <p className="text-gray-600">{commitment.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Feedback Section */}
            <div className="bg-blue-50 p-8 rounded-lg">
              <h2 className="text-2xl font-bold mb-4">Help Us Improve</h2>
              <p className="text-gray-600 mb-6">
                We will be working to bring accessibility to our platforms. If you encounter any accessibility 
                barriers on any of our platforms, please let us know.
              </p>
              <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
                >
                  <Mail className="mr-2 h-5 w-5" />
                  Report an Accessibility Issue
                </a>
                <Link
                  to="/contact"
                  className="inline-flex items-center text-blue-600 font-medium hover:text-blue-700"
                >
                  Contact Us
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}