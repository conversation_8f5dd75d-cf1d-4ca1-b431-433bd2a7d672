import React from 'react';
import { Link } from 'react-router-dom';
import { FileText, Lock, Zap, ArrowRight } from 'lucide-react';
import elenorVideo from '../../assets/video/elenor-banner-earth-smaller.mp4';
import soarVideo from '../../assets/video/soar-bgbanner-part2.mp4';
import grantreadyVideo from '../../assets/video/grantready-decoration.mp4';

function Solutions() {
  const solutions = [
    {
      id: 'grantready',
      title: 'GrantReady™',
      description: 'Streamline your grant management process with our comprehensive solution.',
      icon: FileText,
      link: '/solutions/grantready',
      buttonText: 'Click here to learn more',
      // GrantReady color schema - black background with video
      bgColor: '#000024',
      iconColor: 'text-white',
      textColor: 'text-white',
      hasVideo: true,
      videoSrc: grantreadyVideo
    },
    {
      id: 'soar',
      title: 'SOAR',
      description: 'Security Orchestration, Automation, and Response tailored for healthcare providers.',
      icon: Lock,
      link: '/solutions/soar',
      buttonText: 'Learn More Here',
      // SOAR color schema - black background with video
      bgColor: '#000024',
      iconColor: 'text-white',
      textColor: 'text-white',
      hasVideo: true,
      videoSrc: soarVideo
    },
    {
      id: 'elenor',
      title: 'ELENOR',
      description: 'Advanced emergency response and coordination platform.',
      icon: Zap,
      link: '/solutions/elenor',
      buttonText: 'Learn About Sustainability',
      // ELENOR color schema - black background with video
      bgColor: '#081629',
      iconColor: 'text-white',
      textColor: 'text-white',
      hasVideo: true,
      videoSrc: elenorVideo
    }
  ];

  return (
    <section className="py-8 sm:py-12 md:py-16 lg:py-20 xl:py-24 bg-white relative overflow-hidden">
      <div className="max-w-7xl mx-auto relative z-10">
        <div className="text-center mb-8 sm:mb-10 md:mb-12 lg:mb-16">
          <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">Our Platforms</h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto px-2 sm:px-4">
            Comprehensive platforms designed for healthcare excellence
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto">
          {solutions.map((solution) => {
            const IconComponent = solution.icon;
            return (
              <div
                key={solution.id}
                className="rounded-2xl overflow-hidden h-full relative w-full shadow-2xl"
                style={{
                  background: solution.bgColor,
                  minHeight: '400px'
                }}
              >
                {/* Video background for ELENOR and SOAR */}
                {solution.hasVideo && (
                  <video
                    autoPlay
                    loop
                    muted
                    playsInline
                    className="absolute top-0 right-0 w-full h-full object-cover"
                    style={{
                      objectPosition: 'center top',
                      transform: solution.id === 'soar'
                        ? 'translateY(-40%) translateX(30%)'
                        : solution.id === 'grantready'
                        ? 'translateY(-30%) translateX(40%) scale(1.1)'
                        : 'translateY(-30%) translateX(20%) scale(1.1)'
                    }}
                  >
                    <source src={solution.videoSrc} type="video/mp4" />
                  </video>
                )}

                {/* Conditional overlay based on card type */}
                {solution.id === 'soar' ? (
                  <div
                    className="absolute inset-0"
                    style={{
                      background: 'radial-gradient(circle at top left, #df7344 0%, rgba(223, 115, 68, 0.4) 25%, rgba(223, 115, 68, 0.1) 35%, transparent 65%)'
                    }}
                  ></div>
                ) : solution.id === 'grantready' ? (
                  <div
                    className="absolute inset-0"
                    style={{
                      background: 'radial-gradient(circle at top left, #42a192 0%, rgba(66, 161, 146, 0.4) 25%, rgba(66, 161, 146, 0.1) 35%, transparent 65%)'
                    }}
                  ></div>
                ) : (
                  <div className="absolute inset-0 bg-black bg-opacity-10"></div>
                )}

                {/* Content */}
                <div className="relative z-10 p-6 sm:p-8 h-full flex flex-col justify-between min-h-[400px]">
                  {/* Header with icon and title */}
                  <div className="flex items-center mb-4">
                    <IconComponent className={`h-8 w-8 mr-3 ${solution.iconColor}`} />
                    <h3 className={`text-2xl sm:text-3xl font-bold ${solution.textColor}`}>{solution.title}</h3>
                  </div>

                  {/* Bottom content */}
                  <div className="mt-auto">
                    {/* Description */}
                    <p className={`${solution.textColor} mb-6 text-base sm:text-lg leading-relaxed`}>
                      {solution.description}
                    </p>

                    {/* Button */}
                    <Link
                      to={solution.link}
                      className="inline-flex items-center bg-white text-gray-800 pl-4 pr-0.5 py-0.5 rounded-8 font-medium text-sm hover:bg-gray-100 transition-colors duration-200"
                    >
                      {solution.buttonText}
                      <span className='bg-black ml-3 flex items-center px-3 py-3 justify-center rounded-8'>
                        <ArrowRight className="text-white w-4 h-4" />
                      </span>
                    </Link>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}

export default Solutions;