import React from 'react';
import <PERSON> from '../components/home/<USER>';
import HomeBannerSlider from '../components/home/<USER>';
import LetsConnect from '../components/home/<USER>';
import { useConsultingServices } from '../hooks/useConsultingServices';

import PassionLedUsHere from '../components/home/<USER>';
import ConsultingServices from '../components/home/<USER>';
import LeadershipAndPlanning from '../components/home/<USER>';
import Solutions from '../components/home/<USER>';
import ServicesOverview from '../components/home/<USER>';
import TrustedBy from '../components/grantready/TrustedBy';

const Home: React.FC = () => {
  const { services: consultingServices, loading: servicesLoading } = useConsultingServices();

  return (
    <div className="">
      <Hero />

      {/* Home Banner Slider */}
      <HomeBannerSlider />

      <PassionLedUsHere />

      <ConsultingServices />

      {/* <OurSolutions /> */}

      <Solutions />

      <ServicesOverview />

      {/* <Capabilities /> */}

      <LeadershipAndPlanning />
      <TrustedBy
        title="Join the ranks of our trusted partners."
        description="We work with leading organizations to deliver innovative solutions."
      />
      <LetsConnect />

      {servicesLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {consultingServices.map((service) => (
            <div key={service.id} className="bg-white p-6 rounded-lg shadow-md">
              {service.image_url && (
                <img
                  src={service.image_url}
                  alt={service.title}
                  className="w-full h-48 object-cover rounded-lg mb-4"
                />
              )}
              <h3 className="text-xl font-semibold mb-3">{service.title}</h3>
              <p className="text-gray-600">{service.description}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Home;
