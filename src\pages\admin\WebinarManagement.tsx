import React, { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import {
  Edit,
  Trash2,
  Plus,
  X,
  AlertCircle,
  Users,
  Eye,
  Download,
  Search,
  Check,
  Upload,
  Image as ImageIcon,
  XCircle,
  Video,
  MonitorPlay
} from 'lucide-react';

// Helper function to convert 24-hour time to 12-hour time with AM/PM
const formatTimeWithAMPM = (time: string): string => {
  if (!time) return '';

  // Parse the time string (expected format: HH:MM)
  const [hours, minutes] = time.split(':').map(Number);

  if (isNaN(hours) || isNaN(minutes)) return time;

  // Convert to 12-hour format
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
};

interface Webinar {
  id: string;
  title: string;
  description: string;
  date: string;
  time?: string;
  duration?: string;
  speaker: string;
  image_url: string;
  recording_url?: string;
  webinar_link?: string;
  status: 'upcoming' | 'recorded' | 'live';
  created_at: string;
  updated_at: string;
}

interface WebinarRegistration {
  id: string;
  webinar_id: string;
  user_id?: string;
  full_name: string;
  email: string;
  phone?: string;
  company?: string;
  registered_at: string;
  status: 'registered' | 'cancelled';
}

export default function WebinarManagement() {
  const [webinars, setWebinars] = useState<Webinar[]>([]);
  const [registrations, setRegistrations] = useState<WebinarRegistration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [showRegistrations, setShowRegistrations] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [selectedWebinar, setSelectedWebinar] = useState<Webinar | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [registrationSearchQuery, setRegistrationSearchQuery] = useState('');
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [webinarToDelete, setWebinarToDelete] = useState<string | null>(null);
  const [imageUploading, setImageUploading] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageInputType, setImageInputType] = useState<'upload' | 'url'>('upload');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const registrationSearchInputRef = useRef<HTMLInputElement>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [formData, setFormData] = useState<Omit<Webinar, 'id' | 'created_at' | 'updated_at'>>({
    title: '',
    description: '',
    date: '',
    time: '',
    duration: '',
    speaker: '',
    image_url: '',
    recording_url: '',
    webinar_link: '',
    status: 'upcoming'
  });

  // Use useCallback for fetchWebinars to properly handle dependencies
  const fetchWebinars = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Calculate pagination values
      const from = (currentPage - 1) * pageSize;
      const to = from + pageSize - 1;

      console.log(`Fetching webinars: page ${currentPage}, size ${pageSize}, from ${from}, to ${to}`);

      // Build the query with count option to get total
      let query = supabase
        .from('webinars')
        .select('*', { count: 'exact' })
        .order('date', { ascending: false })
        .range(from, to);

      // Apply search filter if provided
      if (searchQuery) {
        // Create a more comprehensive search across multiple fields
        query = query.or(
          `title.ilike.%${searchQuery}%,` +
          `description.ilike.%${searchQuery}%,` +
          `speaker.ilike.%${searchQuery}%,` +
          `date.ilike.%${searchQuery}%,` +
          `time.ilike.%${searchQuery}%,` +
          `duration.ilike.%${searchQuery}%,` +
          `status.ilike.%${searchQuery}%`
        );
      }

      // Execute the query
      const { data, error, count } = await query;

      if (error) throw error;

      console.log(`Fetched ${data?.length} webinars, total count: ${count}`);

      // Update state with the paginated data
      setWebinars(data || []);

      // Update pagination information
      const totalCount = count || 0;
      const totalPages = Math.ceil(totalCount / pageSize);
      setTotalCount(totalCount);
      setTotalPages(totalPages);

    } catch (err: unknown) {
      console.error('Error fetching webinars:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchQuery]);

  // Fetch webinars when fetchWebinars function changes (which happens when dependencies change)
  useEffect(() => {
    fetchWebinars();
  }, [fetchWebinars]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const fetchRegistrations = async (webinarId: string) => {
    try {
      setLoading(true);
      setRegistrationSearchQuery(''); // Reset search query when loading new registrations

      const { data, error } = await supabase
        .from('webinar_registrations')
        .select('*')
        .eq('webinar_id', webinarId)
        .order('registered_at', { ascending: false });

      if (error) throw error;
      setRegistrations(data || []);

      // Find the selected webinar
      const webinar = webinars.find(w => w.id === webinarId);
      setSelectedWebinar(webinar || null);
      setShowRegistrations(true);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Filter registrations based on search query
  const filteredRegistrations = registrations.filter(registration => {
    if (!registrationSearchQuery) return true;

    const searchLower = registrationSearchQuery.toLowerCase();
    return (
      registration.full_name.toLowerCase().includes(searchLower) ||
      registration.email.toLowerCase().includes(searchLower) ||
      registration.status.toLowerCase().includes(searchLower)
    );
  });

  // Validate form data before submission
  const validateForm = () => {
    // Title validation
    if (!formData.title.trim()) {
      setError('Title is required');
      return false;
    }

    // Speaker validation
    if (!formData.speaker.trim()) {
      setError('Speaker name is required');
      return false;
    }

    // Date validation
    if (!formData.date) {
      setError('Date is required');
      return false;
    }

    // For upcoming or live webinars, date should not be in the past
    if (formData.status === 'upcoming' || formData.status === 'live') {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to beginning of day for date comparison
      const selectedDate = new Date(formData.date);
      selectedDate.setHours(0, 0, 0, 0);

      if (selectedDate < today) {
        setError('Date cannot be in the past for upcoming or live webinars');
        return false;
      }

      // Time validation for upcoming or live webinars
      if (!formData.time) {
        setError('Time is required for upcoming or live webinars');
        return false;
      }
    }

    // Duration validation for recorded webinars
    if (formData.status === 'recorded' && !formData.duration) {
      setError('Duration is required for recorded webinars');
      return false;
    }

    // Recording URL validation for recorded webinars - only check if it exists
    if (formData.status === 'recorded' && !formData.recording_url) {
      setError('Recording URL is required for recorded webinars');
      return false;
    }

    // Image URL validation removed - no longer required

    // No validation for webinar link as requested

    // Description validation
    if (!formData.description.trim()) {
      setError('Description is required');
      return false;
    }

    return true;
  };

  const handleCreateWebinar = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    try {
      const { error } = await supabase
        .from('webinars')
        .insert([formData]);

      if (error) throw error;

      setShowForm(false);
      resetForm();
      fetchWebinars();
      setSuccess('Webinar created successfully!');
      setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
    } catch (err: any) {
      setError(err.message);
      setTimeout(() => setError(null), 5000); // Auto-dismiss after 5 seconds
    }
  };

  const handleUpdateWebinar = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!editingId) return;

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    try {
      const { error } = await supabase
        .from('webinars')
        .update(formData)
        .eq('id', editingId);

      if (error) throw error;

      setShowForm(false);
      setEditingId(null);
      resetForm();
      fetchWebinars();
      setSuccess('Webinar updated successfully!');
      setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
    } catch (err: any) {
      setError(err.message);
      setTimeout(() => setError(null), 5000); // Auto-dismiss after 5 seconds
    }
  };

  const handleDeleteWebinar = (id: string) => {
    setWebinarToDelete(id);
    setShowDeleteConfirmation(true);
  };

  const confirmDeleteWebinar = async () => {
    if (!webinarToDelete) return;

    try {
      const { error } = await supabase
        .from('webinars')
        .delete()
        .eq('id', webinarToDelete);

      if (error) throw error;

      fetchWebinars();
      setShowDeleteConfirmation(false);
      setWebinarToDelete(null);
      setSuccess('Webinar deleted successfully!');
      setTimeout(() => setSuccess(null), 3000); // Auto-dismiss after 3 seconds
    } catch (err: any) {
      setError(err.message);
      setTimeout(() => setError(null), 5000); // Auto-dismiss after 5 seconds
    }
  };

  const cancelDeleteWebinar = () => {
    setShowDeleteConfirmation(false);
    setWebinarToDelete(null);
  };

  const handleEditWebinar = (webinar: Webinar) => {
    setFormData({
      title: webinar.title,
      description: webinar.description,
      date: webinar.date,
      time: webinar.time || '',
      duration: webinar.duration || '',
      speaker: webinar.speaker,
      image_url: webinar.image_url,
      recording_url: webinar.recording_url || '',
      webinar_link: webinar.webinar_link || '',
      status: webinar.status
    });
    setImagePreview(webinar.image_url);
    // Set the correct image input type based on the image URL
    if (webinar.image_url && webinar.image_url.includes('webinar-images')) {
      setImageInputType('upload');
    } else if (webinar.image_url) {
      setImageInputType('url');
    } else {
      setImageInputType('upload'); // Default to upload if no image
    }
    setEditingId(webinar.id);
    setShowForm(true);
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      date: '',
      time: '',
      duration: '',
      speaker: '',
      image_url: '',
      recording_url: '',
      webinar_link: '',
      status: 'upcoming'
    });
    setImagePreview(null);
    setImageInputType('upload'); // Reset to upload by default
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setImageUploading(true);
      setError(null);

      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Please upload a valid image file (JPEG, PNG, GIF, or WEBP)');
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('File size should be less than 5MB');
      }

      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const fileName = `${Math.random().toString(36).substring(2, 9)}-${Date.now()}.${fileExt}`;
      const filePath = `webinars/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('webinar-images')
        .upload(filePath, file, { upsert: true });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('webinar-images')
        .getPublicUrl(filePath);

      // Set the image URL in the form data
      setFormData(prev => ({ ...prev, image_url: publicUrl }));
      setImagePreview(publicUrl);
      setSuccess('Image uploaded successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message);
      setTimeout(() => setError(null), 5000);
    } finally {
      setImageUploading(false);
    }
  };

  // Extract file path from public URL
  const getFilePathFromUrl = (url: string): string | null => {
    if (!url || !url.includes('webinar-images')) return null;

    try {
      // Extract the path after the bucket name
      const pathMatch = url.match(/webinar-images\/(.*)/i);
      if (pathMatch && pathMatch[1]) {
        return pathMatch[1];
      }
      return null;
    } catch (err) {
      console.error('Error extracting file path from URL:', err);
      return null;
    }
  };

  // Handle image removal from storage
  const handleRemoveImage = async (imageUrl: string) => {
    try {
      // Only attempt to remove from storage if it's an uploaded image
      if (imageUrl && imageUrl.includes('webinar-images')) {
        const filePath = getFilePathFromUrl(imageUrl);

        if (filePath) {
          setImageUploading(true);
          const { error } = await supabase.storage
            .from('webinar-images')
            .remove([filePath]);

          if (error) {
            console.error('Error removing image from storage:', error);
            setError('Failed to remove image from storage');
            setTimeout(() => setError(null), 5000);
            return;
          }

          setSuccess('Image removed successfully');
          setTimeout(() => setSuccess(null), 3000);
        }
      }

      // Clear the image URL from the form data
      setFormData(prev => ({ ...prev, image_url: '' }));
      setImagePreview(null);
    } catch (err: any) {
      console.error('Error in handleRemoveImage:', err);
      setError(err.message || 'Failed to remove image');
      setTimeout(() => setError(null), 5000);
    } finally {
      setImageUploading(false);
    }
  };

  const updateRegistrationStatus = async (registrationId: string, newStatus: 'registered' | 'cancelled') => {
    try {
      const { error } = await supabase
        .from('webinar_registrations')
        .update({ status: newStatus })
        .eq('id', registrationId);

      if (error) throw error;

      // Update local state
      setRegistrations(registrations.map(reg =>
        reg.id === registrationId ? { ...reg, status: newStatus } : reg
      ));
      setSuccess(`Registration status updated to ${newStatus}`);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const exportRegistrationsToCSV = () => {
    if (!selectedWebinar || registrations.length === 0) return;

    // Create CSV content with proper escaping and formatting
    const headers = ['ID', 'Full Name', 'Email', 'Registration Date', 'Status'];

    // Helper function to properly escape CSV fields
    const escapeCSV = (field: string) => {
      // If the field contains commas, quotes, or newlines, wrap it in quotes and escape any quotes
      if (field.includes(',') || field.includes('"') || field.includes('\n')) {
        return `"${field.replace(/"/g, '""')}"`;
      }
      return field;
    };

    const csvContent = [
      // Join headers with commas
      headers.join(','),
      // Map each registration to a CSV row
      ...registrations.map(reg => {
        // Format the date consistently
        const formattedDate = new Date(reg.registered_at).toLocaleString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: true
        });

        // Create and join the row fields with proper escaping
        return [
          escapeCSV(reg.id),
          escapeCSV(reg.full_name),
          escapeCSV(reg.email),
          escapeCSV(formattedDate),
          escapeCSV(reg.status)
        ].join(',');
      })
    ].join('\n');

    // Create download link with BOM for Excel compatibility
    const BOM = '\uFEFF'; // UTF-8 BOM for Excel to recognize UTF-8 encoding
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${selectedWebinar.title.replace(/\s+/g, '_')}_registrations.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Initial loading state is now handled within the table

  return (
    <div className="p-6">
      {!showRegistrations ? (
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Webinar Management</h1>
          <div className="flex space-x-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search webinars..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                ref={searchInputRef}
              />
              <Eye className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <button
              onClick={() => {
                setShowForm(true);
                setEditingId(null);
                resetForm();
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 flex items-center"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add Webinar
            </button>
          </div>
        </div>
      ) : (
        <div className="mb-6"></div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg mb-6 flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
          <button onClick={() => setError(null)} className="ml-auto">
            <X className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg flex items-start">
          <Check className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
          <button onClick={() => setSuccess(null)} className="ml-auto">
            <X className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Webinar List */}
      {!showRegistrations && (
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Speaker</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registrations</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="px-4 py-8 text-center">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                        <span className="text-gray-500">{searchQuery ? 'Searching...' : 'Loading webinars...'}</span>
                      </div>
                    </td>
                  </tr>
                ) : webinars.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                      {searchQuery ? 'No webinars match your search.' : 'No webinars found. Create your first webinar by clicking the "Add Webinar" button.'}
                    </td>
                  </tr>
                ) : (
                  webinars.map((webinar) => (
                    <tr key={webinar.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0 mr-3">
                            {webinar.image_url ? (
                              <img
                                src={webinar.image_url}
                                alt={webinar.title}
                                className="h-10 w-10 rounded-md object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.onerror = null;
                                  target.style.display = 'none';
                                  target.parentElement!.innerHTML = `<div class="h-10 w-10 flex items-center justify-center bg-blue-100 rounded-md">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                      <path d="M15 10l4.553-2.276A1 1 0 0 1 21 8.618v6.764a1 1 0 0 1-1.447.894L15 14M5 18h8a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2z"></path>
                                    </svg>
                                  </div>`;
                                }}
                              />
                            ) : (
                              <div className="h-10 w-10 flex items-center justify-center bg-blue-100 rounded-md">
                                <MonitorPlay className="h-5 w-5 text-blue-600" />
                              </div>
                            )}
                          </div>
                          <div className="truncate max-w-xs">
                            <div className="text-sm font-medium text-gray-900">{webinar.title}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{webinar.date}</div>
                        <div className="text-sm text-gray-500">
                          {webinar.status === 'upcoming' ? formatTimeWithAMPM(webinar.time || '') : webinar.duration}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{webinar.speaker}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          webinar.status === 'upcoming'
                            ? 'bg-green-100 text-green-800'
                            : webinar.status === 'live'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-blue-100 text-blue-800'
                        }`}>
                          {webinar.status === 'upcoming'
                            ? 'Upcoming'
                            : webinar.status === 'live'
                              ? 'Live'
                              : 'Recorded'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => fetchRegistrations(webinar.id)}
                          className="text-blue-600 hover:text-blue-800 flex items-center"
                        >
                          <Users className="h-4 w-4 mr-1" />
                          View Registrations
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-3">
                          <button
                            onClick={() => handleEditWebinar(webinar)}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            <Edit className="h-5 w-5" />
                          </button>
                          <button
                            onClick={() => handleDeleteWebinar(webinar.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination Controls */}
          {!loading && webinars.length > 0 && (
            <div className="border-t border-gray-200 bg-white px-4 py-3 mt-4">
              {/* Results info and page size selector */}
              <div className="flex flex-wrap items-center justify-between mb-4">
                <div className="text-sm text-gray-700 mb-2 sm:mb-0 flex items-center">
                  Showing <span className="font-medium mx-1">{webinars.length > 0 ? (currentPage - 1) * pageSize + 1 : 0}</span> to{' '}
                  <span className="font-medium mx-1">{Math.min(currentPage * pageSize, totalCount)}</span> of{' '}
                  <span className="font-medium mx-1">{totalCount}</span> results
                  {searchQuery && <span className="ml-2 text-blue-600">for "{searchQuery}"</span>}
                </div>

                <div className="flex items-center">
                  <span className="mr-2 text-sm text-gray-700">Rows per page:</span>
                  <div className="relative">
                    <select
                      value={pageSize}
                      onChange={(e) => {
                        const newSize = Number(e.target.value);
                        setPageSize(newSize);
                        // Adjust current page to maintain position in data as much as possible
                        const firstItemIndex = (currentPage - 1) * pageSize;
                        const newPage = Math.floor(firstItemIndex / newSize) + 1;
                        setCurrentPage(Math.min(newPage, Math.ceil(totalCount / newSize) || 1));
                      }}
                      className="py-1 text-sm bg-white border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 pl-3 pr-8 appearance-none"
                    >
                      <option value="5">5</option>
                      <option value="10">10</option>
                      <option value="25">25</option>
                      <option value="50">50</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2 text-gray-700">
                      <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Simplified Pagination controls */}
              <div className="flex items-center justify-center">
                <nav className="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  {/* Previous page button */}
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage <= 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Previous</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>

                  {/* Page number display */}
                  <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-700">
                    Page <span className="font-bold mx-1 text-blue-600">{currentPage}</span> of <span className="font-bold mx-1">{totalPages || 1}</span>
                  </span>

                  {/* Next page button */}
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages || 1))}
                    disabled={currentPage >= (totalPages || 1)}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Next</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Registrations View */}
      {showRegistrations && selectedWebinar && (
        <div>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <h1 className="text-2xl font-bold">
              Webinar Registrations: {selectedWebinar.title}
            </h1>

            <div className="flex flex-col sm:flex-row w-full md:w-auto gap-3">
              <div className="relative flex-grow sm:max-w-md">
                <input
                  type="text"
                  placeholder="Search registrations..."
                  value={registrationSearchQuery}
                  onChange={(e) => setRegistrationSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  ref={registrationSearchInputRef}
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => {
                    setShowRegistrations(false);
                    setSelectedWebinar(null);
                  }}
                  className="flex-1 sm:flex-none px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors whitespace-nowrap"
                >
                  Back to Webinars
                </button>

                <button
                  onClick={exportRegistrationsToCSV}
                  disabled={filteredRegistrations.length === 0}
                  className="flex-1 sm:flex-none px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export CSV
                </button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered</th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {filteredRegistrations.length === 0 ? (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                        {registrationSearchQuery ? 'No registrations match your search.' : 'No registrations found for this webinar.'}
                      </td>
                    </tr>
                  ) : (
                    filteredRegistrations.map((registration) => (
                      <tr key={registration.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{registration.full_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{registration.email}</div>
                        </td>

                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">
                            {new Date(registration.registered_at).toLocaleString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <select
                            value={registration.status}
                            onChange={(e) => updateRegistrationStatus(registration.id, e.target.value as 'registered' | 'cancelled')}
                            className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors border ${
                              registration.status === 'cancelled' ? 'bg-red-50 text-red-600 border-red-200' :
                              'bg-blue-50 text-blue-600 border-blue-200'
                            }`}
                          >
                            <option value="registered">Registered</option>
                            <option value="cancelled">Cancelled</option>
                          </select>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Webinar Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold">
                  {editingId ? 'Edit Webinar' : 'Add New Webinar'}
                </h3>
                <button
                  onClick={() => {
                    setShowForm(false);
                    setEditingId(null);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <form onSubmit={editingId ? handleUpdateWebinar : handleCreateWebinar}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      Title *
                    </label>
                    <input
                      id="title"
                      type="text"
                      required
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label htmlFor="speaker" className="block text-sm font-medium text-gray-700 mb-1">
                      Speaker *
                    </label>
                    <input
                      id="speaker"
                      type="text"
                      required
                      value={formData.speaker}
                      onChange={(e) => setFormData({ ...formData, speaker: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                      Date *
                    </label>
                    <input
                      id="date"
                      type="date"
                      required
                      min={formData.status === 'upcoming' || formData.status === 'live' ? new Date().toISOString().split('T')[0] : undefined}
                      value={formData.date}
                      onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                      className={`w-full px-4 py-2 border ${(formData.status === 'upcoming' || formData.status === 'live') && formData.date && new Date(formData.date) < new Date(new Date().toISOString().split('T')[0]) ? 'border-red-300 bg-red-50' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {(formData.status === 'upcoming' || formData.status === 'live')
                        ? 'Select a date in the future for upcoming or live webinars'
                        : 'Select the date of the webinar'}
                    </p>
                  </div>

                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                      Status *
                    </label>
                    <select
                      id="status"
                      required
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value as 'upcoming' | 'recorded' | 'live' })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="upcoming">Upcoming</option>
                      <option value="live">Live</option>
                      <option value="recorded">Recorded</option>
                    </select>
                  </div>

                  {formData.status === 'upcoming' || formData.status === 'live' ? (
                    <div>
                      <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-1">
                        Time *
                      </label>
                      <input
                        id="time"
                        type="time"
                        required
                        value={formData.time}
                        onChange={(e) => setFormData({ ...formData, time: e.target.value })}
                        className={`w-full px-4 py-2 border ${!formData.time ? 'border-gray-300' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                      />
                      <p className="text-xs text-gray-500 mt-1">Select the time of the webinar (24-hour format)</p>
                    </div>
                  ) : (
                    <div>
                      <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
                        Duration *
                      </label>
                      <input
                        id="duration"
                        type="text"
                        required
                        placeholder="e.g., 45 minutes, 1 hour, 1h 30m"
                        value={formData.duration}
                        onChange={(e) => setFormData({ ...formData, duration: e.target.value })}
                        className={`w-full px-4 py-2 border ${!formData.duration && formData.status === 'recorded' ? 'border-red-300 bg-red-50' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                      />
                      <p className="text-xs text-gray-500 mt-1">Enter the duration of the webinar (e.g., "45 minutes", "1 hour", "1h 30m")</p>
                    </div>
                  )}

                  <div>
                    <label htmlFor="image_url" className="block text-sm font-medium text-gray-700 mb-3">
                      Webinar Image
                    </label>
                    <div className="mb-4">
                      <div className="flex items-center space-x-6 mb-4">
                        <div className="flex items-center">
                          <input
                            type="radio"
                            id="image_upload_option"
                            name="image_source"
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            checked={imageInputType === 'upload'}
                            onChange={() => {
                              setImageInputType('upload');
                              // If there was a URL, clear it
                              if (formData.image_url && !formData.image_url.includes('webinar-images')) {
                                setFormData({ ...formData, image_url: '' });
                              }
                            }}
                          />
                          <label
                            htmlFor="image_upload_option"
                            className="ml-2 text-sm text-gray-700 cursor-pointer"
                            onClick={() => {
                              setImageInputType('upload');
                              if (formData.image_url && !formData.image_url.includes('webinar-images')) {
                                setFormData({ ...formData, image_url: '' });
                              }
                            }}
                          >
                            Upload Image
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="radio"
                            id="image_url_option"
                            name="image_source"
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            checked={imageInputType === 'url'}
                            onChange={() => {
                              setImageInputType('url');
                              // If switching to URL and there was an uploaded image, clear it
                              if (formData.image_url && formData.image_url.includes('webinar-images')) {
                                setFormData({ ...formData, image_url: '' });
                              }
                            }}
                          />
                          <label
                            htmlFor="image_url_option"
                            className="ml-2 text-sm text-gray-700 cursor-pointer"
                            onClick={() => {
                              setImageInputType('url');
                              if (formData.image_url && formData.image_url.includes('webinar-images')) {
                                setFormData({ ...formData, image_url: '' });
                              }
                            }}
                          >
                            Image URL
                          </label>
                        </div>
                      </div>

                      {/* Show the appropriate input based on selection */}
                      {imageInputType === 'upload' ? (
                        // Upload button
                        <div className="w-full">
                          <label className="flex-1">
                            <div className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 cursor-pointer transition-colors">
                              <Upload className="h-5 w-5 mr-2 text-gray-500" />
                              {imageUploading ? 'Uploading...' : 'Choose File'}
                              <input
                                type="file"
                                accept="image/*"
                                onChange={handleImageUpload}
                                className="hidden"
                                disabled={imageUploading}
                              />
                            </div>
                          </label>
                        </div>
                      ) : (
                        // URL input
                        <div className="w-full">
                          <input
                            id="image_url"
                            type="text"
                            placeholder="https://example.com/image.jpg"
                            value={formData.image_url || ''}
                            onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                          />
                        </div>
                      )}
                    </div>

                    {/* Image preview */}
                    {(formData.image_url || imagePreview) && (
                      <div className="mt-2 flex justify-center bg-[#6c6696] bg-opacity-20 p-4 rounded-lg">
                        <div className="relative max-w-xs">
                          <div className="relative">
                            <img
                              src={formData.image_url || imagePreview || ''}
                              alt="Webinar image preview"
                              className="max-h-48 object-contain rounded border border-gray-200 shadow-sm bg-white"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'https://via.placeholder.com/300x200?text=Invalid+Image+URL';
                              }}
                            />
                            {/* Remove image button */}
                            <button
                              type="button"
                              onClick={() => handleRemoveImage(formData.image_url || imagePreview || '')}
                              className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100 transition-colors"
                              title="Remove image"
                              disabled={imageUploading}
                            >
                              <XCircle className="h-5 w-5 text-red-500" />
                            </button>
                          </div>
                          <div className="text-center text-xs text-gray-500 mt-1">
                            {formData.image_url && formData.image_url.includes('webinar-images')
                              ? 'Image uploaded'
                              : formData.image_url
                                ? 'Image URL'
                                : ''}
                          </div>
                        </div>
                      </div>
                    )}
                    <p className="text-xs text-gray-500 mt-2">Upload an image or provide a URL for the webinar image</p>
                  </div>

                  {formData.status === 'recorded' && (
                    <div>
                      <label htmlFor="recording_url" className="block text-sm font-medium text-gray-700 mb-1">
                        Recording URL *
                      </label>
                      <input
                        id="recording_url"
                        type="text"
                        required={formData.status === 'recorded'}
                        placeholder="https://example.com/recording"
                        value={formData.recording_url}
                        onChange={(e) => setFormData({ ...formData, recording_url: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <p className="text-xs text-gray-500 mt-1">Enter the URL for the webinar recording</p>
                    </div>
                  )}

                  {(formData.status === 'upcoming' || formData.status === 'live') && (
                    <div>
                      <label htmlFor="webinar_link" className="block text-sm font-medium text-gray-700 mb-1">
                        Webinar Link
                      </label>
                      <input
                        id="webinar_link"
                        type="text"
                        placeholder="e.g., https://zoom.us/j/123456789"
                        value={formData.webinar_link || ''}
                        onChange={(e) => setFormData({ ...formData, webinar_link: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <p className="text-xs text-gray-500 mt-1">This link will be visible to registered users</p>
                    </div>
                  )}
                </div>

                <div className="mb-6">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    id="description"
                    rows={4}
                    required
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  ></textarea>
                </div>

                <div className="flex justify-end space-x-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false);
                      setEditingId(null);
                      resetForm();
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    {editingId ? 'Update Webinar' : 'Create Webinar'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn"
          onClick={(e) => {
            // Close modal when clicking outside
            if (e.target === e.currentTarget) {
              cancelDeleteWebinar();
            }
          }}
          onKeyDown={(e) => {
            // Close modal when pressing Escape key
            if (e.key === 'Escape') {
              cancelDeleteWebinar();
            }
          }}
          tabIndex={0}
        >
          <div className="bg-white rounded-lg shadow-xl overflow-hidden max-w-md w-full mx-4 transform transition-all duration-300 ease-in-out animate-scaleIn">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="bg-red-100 p-2 rounded-full mr-3">
                    <AlertCircle className="h-6 w-6 text-red-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Confirm Deletion</h3>
                </div>
                <button
                  onClick={cancelDeleteWebinar}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <p className="text-gray-600 mb-6">
                Are you sure you want to delete this webinar? This action cannot be undone and will also delete all registrations.
              </p>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelDeleteWebinar}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDeleteWebinar}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
