@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font Imports */
@font-face {
  font-family: 'Platform';
  src: url('./assets/font/Platform-Medium/Platform-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Zenith Trial';
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@keyframes gradient {
  0% {
    transform: translate(0, 0);
  }

  50% {
    transform: translate(-30px, 30px);
  }

  100% {
    transform: translate(0, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 3D Transforms */
.perspective-1000 {
  perspective: 1000px;
}

.transform-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

/* Smooth Scroll - disabled for ScrollToTop functionality */
/* html {
  scroll-behavior: smooth;
} */

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Slider Animations */
@keyframes float {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 6s ease-in-out infinite;
  animation-delay: 1s;
}

/* Button Glow Effects */
.glow-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.glow-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow-button:hover::before {
  opacity: 0.15;
}

.glow-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 60%);
  opacity: 0;
  transition: opacity 0.3s ease;
  transform: rotate(45deg);
}

.glow-button:hover::after {
  opacity: 0.1;
}

/* 3D Image Container */
.image-3d-container {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.image-3d {
  transform: rotateX(5deg) rotateY(-5deg);
  transition: transform 0.3s ease;
}

.image-3d:hover {
  transform: rotateX(0deg) rotateY(0deg) scale(1.05);
}

/* Cursor effects */
.cursor-glow {
  filter: blur(8px);
  mix-blend-mode: screen;
}

/* Gradient text */
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, currentColor, #60a5fa);
}

/* Slide transition */
.slide-enter {
  transform: translateX(100%);
}

.slide-enter-active {
  transform: translateX(0%);
  transition: transform 500ms ease-in-out;
}

.slide-exit {
  transform: translateX(0%);
}

.slide-exit-active {
  transform: translateX(-100%);
  transition: transform 500ms ease-in-out;
}

/* Custom Swiper pagination styles */
.custom-bullet {
  width: 44px !important;
  height: 9px !important;
  border-radius: 2px !important;
  background: rgba(255, 255, 255, 0.4) !important;
  opacity: 1 !important;
  margin: 0 0.5px !important;
}

.custom-bullet-active {
  background: #ffffff !important;
  width: 45px !important;
}

/* Button Micro-interactions */
.micro-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
}

.micro-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: skewX(-20deg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.micro-button:hover::before {
  left: 100%;
}

.micro-button:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.micro-button:active {
  transform: translateY(0);
}

.micro-button .icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.micro-button:hover .icon {
  transform: translateX(4px);
}


/* Basic Swiper Pagination Styles */
.home-banner-swiper .swiper-pagination-bullet {
  background-color: #A0A0A0;
  /* Light gray for inactive dots */
  opacity: 0.7;
  width: 10px;
  height: 10px;
  margin: 0 5px !important;
  /* Ensure spacing */
  transition: background-color 0.3s ease, opacity 0.3s ease;
}

.home-banner-swiper .swiper-pagination-bullet-active {
  background-color: #6B21A8;
  /* Purple for active dot - matches theme */
  opacity: 1;
}

/* Ensure pagination is centered if not already by Swiper's default */
.home-banner-swiper .swiper-pagination {
  position: relative;
  /* Or 'absolute' if you want to overlay it */
  bottom: auto;
  /* Adjust if using absolute positioning */
  left: 0;
  width: 100%;
  text-align: center;
  margin-top: 20px;
  /* Space above the dots */
}

/* Banner Text Animations - Fade in from top to bottom */
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Book Demo Page Animations */
.animate-fadeInDown {
  animation: fadeInDown 0.8s ease-out forwards;
  opacity: 0;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-800 {
  animation-delay: 0.8s;
}

/* Floating animation for demo page elements */
@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Gradient background animation */
@keyframes gradientShift {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
}

/* Pulse glow effect for buttons */
@keyframes pulseGlow {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(121, 214, 143, 0.3);
  }

  50% {
    box-shadow: 0 0 30px rgba(121, 214, 143, 0.6), 0 0 40px rgba(122, 142, 219, 0.4);
  }
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes fadeOutUp {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(-30px);
  }
}

.animate-fadeInDown {
  animation: fadeInDown 0.8s ease-out forwards;
}

.animate-fadeOutUp {
  animation: fadeOutUp 0.6s ease-in forwards;
}

/* Animation delay classes */
.animation-delay-200 {
  animation-delay: 0.2s;
  opacity: 0;
}

.animation-delay-400 {
  animation-delay: 0.4s;
  opacity: 0;
}

.animation-delay-600 {
  animation-delay: 0.6s;
  opacity: 0;
}

.animation-delay-800 {
  animation-delay: 0.8s;
  opacity: 0;
}