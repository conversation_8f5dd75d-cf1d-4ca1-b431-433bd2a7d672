import { serve } from 'https://deno.land/std/http/server.ts';
import nodemailer from 'npm:nodemailer';

// Interface for the request body
interface EmailRequestBody {
  to: string;
  subject: string;
  html: string;
  pdfUrl: string;
  invoiceRef: string;
}

serve(async (req: Request) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': '*',
    'Access-Control-Allow-Headers': '*',
    'Content-Type': 'application/json'
  };

  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers
    });
  }

  try {
    const body = await req.json() as EmailRequestBody;
    if (!body) {
      throw new Error('Request body is empty');
    }

    // Validate required fields
    if (!body.to || !body.subject || !body.html || !body.pdfUrl) {
      throw new Error('Missing required fields (to, subject, html, pdfUrl)');
    }

    const { to, subject, html, pdfUrl, invoiceRef } = body;

    // Create email transporter
    // Get environment variables
    const SMTP_USERNAME = Deno.env.get('SMTP_USERNAME') || '<EMAIL>';
    const SMTP_PASSWORD = Deno.env.get('SMTP_PASSWORD');

    if (!SMTP_PASSWORD) {
      throw new Error('SMTP_PASSWORD environment variable is not set');
    }

    const transporter = nodemailer.createTransport({
      host: 'smtp.office365.com',
      port: 587,
      secure: false,
      auth: {
        user: SMTP_USERNAME,
        pass: SMTP_PASSWORD
      }
    });

    // Verify SMTP connection
    const isValid = await transporter.verify();
    console.log('SMTP verified:', isValid);

    // Download the PDF from the URL
    console.log('Downloading PDF from URL:', pdfUrl);
    const pdfResponse = await fetch(pdfUrl);
    if (!pdfResponse.ok) {
      throw new Error(`Failed to download PDF: ${pdfResponse.statusText}`);
    }

    // Get the PDF as an ArrayBuffer and convert to Uint8Array (Deno's equivalent to Buffer)
    const pdfBuffer = await pdfResponse.arrayBuffer();
    const pdfUint8Array = new Uint8Array(pdfBuffer);

    // Send email with PDF attachment
    await transporter.sendMail({
      from: SMTP_USERNAME,
      to: to.trim() === '' ? SMTP_USERNAME : to,
      subject,
      html,
      attachments: [
        {
          filename: `invoice-${invoiceRef}.pdf`,
          content: pdfUint8Array,
          contentType: 'application/pdf',
          encoding: 'binary'
        }
      ]
    });

    return new Response(JSON.stringify({
      success: true
    }), {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({
      error: 'Failed to process request',
      details: error.message
    }), {
      status: 400,
      headers
    });
  }
});
