/*
  # Products and Media Management Schema

  1. New Tables
    - `products`
      - `id` (uuid, primary key)
      - `name` (text)
      - `description` (text)
      - `price` (numeric)
      - `image_url` (text)
      - `category` (text)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)
    
    - `product_discounts`
      - `id` (uuid, primary key)
      - `product_id` (uuid, references products)
      - `discount_type` (text) - percentage, fixed_amount
      - `discount_value` (numeric)
      - `start_date` (timestamptz)
      - `end_date` (timestamptz)
      - `code` (text)
      - `created_at` (timestamptz)
    
    - `product_bundles`
      - `id` (uuid, primary key)
      - `name` (text)
      - `description` (text)
      - `price` (numeric)
      - `created_at` (timestamptz)
    
    - `bundle_products`
      - `bundle_id` (uuid, references product_bundles)
      - `product_id` (uuid, references products)
    
    - `media_items`
      - `id` (uuid, primary key)
      - `title` (text)
      - `description` (text)
      - `type` (text) - guide, whitepaper, tutorial, template
      - `url` (text)
      - `thumbnail_url` (text)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users and public access where appropriate

  3. Functions
    - Add function to calculate discounted price
    - Add function to validate promotion codes
*/

-- Create products table
CREATE TABLE IF NOT EXISTS products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text NOT NULL,
  price numeric NOT NULL CHECK (price >= 0),
  price_id text,
  image_url text,
  category text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create product_discounts table
CREATE TABLE IF NOT EXISTS product_discounts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products ON DELETE CASCADE,
  discount_type text NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')),
  discount_value numeric NOT NULL CHECK (discount_value >= 0),
  start_date timestamptz NOT NULL,
  end_date timestamptz NOT NULL,
  code text UNIQUE,
  created_at timestamptz DEFAULT now(),
  CONSTRAINT valid_date_range CHECK (end_date > start_date)
);

-- Create product_bundles table
CREATE TABLE IF NOT EXISTS product_bundles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text NOT NULL,
  price numeric NOT NULL CHECK (price >= 0),
  created_at timestamptz DEFAULT now()
);

-- Create bundle_products table
CREATE TABLE IF NOT EXISTS bundle_products (
  bundle_id uuid REFERENCES product_bundles ON DELETE CASCADE,
  product_id uuid REFERENCES products ON DELETE CASCADE,
  PRIMARY KEY (bundle_id, product_id)
);

-- Create media_items table
CREATE TABLE IF NOT EXISTS media_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text NOT NULL,
  type text NOT NULL CHECK (type IN ('guide', 'whitepaper', 'tutorial', 'template')),
  url text NOT NULL,
  thumbnail_url text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_discounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_bundles ENABLE ROW LEVEL SECURITY;
ALTER TABLE bundle_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_items ENABLE ROW LEVEL SECURITY;

-- Create policies for products
CREATE POLICY "Products are viewable by everyone" 
  ON products FOR SELECT 
  TO public 
  USING (true);

CREATE POLICY "Products are insertable by authenticated users" 
  ON products FOR INSERT 
  TO authenticated 
  WITH CHECK (true);

CREATE POLICY "Products are updatable by authenticated users" 
  ON products FOR UPDATE 
  TO authenticated 
  USING (true);

-- Create policies for product_discounts
CREATE POLICY "Discounts are viewable by everyone" 
  ON product_discounts FOR SELECT 
  TO public 
  USING (true);

CREATE POLICY "Discounts are manageable by authenticated users" 
  ON product_discounts FOR ALL 
  TO authenticated 
  USING (true);

-- Create policies for product_bundles
CREATE POLICY "Bundles are viewable by everyone" 
  ON product_bundles FOR SELECT 
  TO public 
  USING (true);

CREATE POLICY "Bundles are manageable by authenticated users" 
  ON product_bundles FOR ALL 
  TO authenticated 
  USING (true);

-- Create policies for bundle_products
CREATE POLICY "Bundle products are viewable by everyone" 
  ON bundle_products FOR SELECT 
  TO public 
  USING (true);

CREATE POLICY "Bundle products are manageable by authenticated users" 
  ON bundle_products FOR ALL 
  TO authenticated 
  USING (true);

-- Create policies for media_items
CREATE POLICY "Media items are viewable by everyone" 
  ON media_items FOR SELECT 
  TO public 
  USING (true);

CREATE POLICY "Media items are manageable by authenticated users" 
  ON media_items FOR ALL 
  TO authenticated 
  USING (true);

-- Create function to calculate discounted price
CREATE OR REPLACE FUNCTION calculate_discounted_price(
  product_id uuid,
  promo_code text DEFAULT NULL
)
RETURNS numeric
LANGUAGE plpgsql
AS $$
DECLARE
  base_price numeric;
  discount record;
BEGIN
  -- Get the base price
  SELECT price INTO base_price FROM products WHERE id = product_id;
  
  -- If no base price found, return null
  IF base_price IS NULL THEN
    RETURN NULL;
  END IF;
  
  -- Get applicable discount
  SELECT * INTO discount
  FROM product_discounts
  WHERE product_id = calculate_discounted_price.product_id
    AND now() BETWEEN start_date AND end_date
    AND (promo_code IS NULL OR code = promo_code)
  ORDER BY discount_value DESC
  LIMIT 1;
  
  -- If no discount found, return base price
  IF discount IS NULL THEN
    RETURN base_price;
  END IF;
  
  -- Calculate discounted price
  RETURN CASE
    WHEN discount.discount_type = 'percentage' THEN
      base_price * (1 - discount.discount_value / 100)
    WHEN discount.discount_type = 'fixed_amount' THEN
      base_price - discount.discount_value
    ELSE base_price
  END;
END;
$$;

-- Create function to validate promotion code
CREATE OR REPLACE FUNCTION validate_promo_code(
  code text
)
RETURNS boolean
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM product_discounts
    WHERE product_discounts.code = validate_promo_code.code
      AND now() BETWEEN start_date AND end_date
  );
END;
$$;

-- Add updated_at trigger for products
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_products_updated_at
  BEFORE UPDATE ON products
  FOR EACH ROW
  EXECUTE PROCEDURE update_updated_at_column();

-- Add updated_at trigger for media_items
CREATE TRIGGER update_media_items_updated_at
  BEFORE UPDATE ON media_items
  FOR EACH ROW
  EXECUTE PROCEDURE update_updated_at_column();

ALTER TABLE products 
ADD COLUMN specifications JSONB DEFAULT '[]'::jsonb;


-- Add Stripe price_id to products table
-- ALTER TABLE products 
-- ADD COLUMN price_id text;