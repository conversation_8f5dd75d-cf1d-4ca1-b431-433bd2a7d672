/*
  # Admin User Setup

  1. Tables
    - Create admin_users table with proper structure and constraints
    - Enable RLS and set up policies

  2. Functions
    - Add admin verification functions
    - Add credential checking functions

  3. Initial Data
    - Create initial admin user with secure credentials
*/

-- Create admin_users table if not exists
CREATE TABLE IF NOT EXISTS admin_users (
  id uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  role text NOT NULL CHECK (role IN ('admin', 'editor')),
  created_at timestamptz DEFAULT now(),
  last_login timestamptz
);

-- Enable RLS
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admin users can view own data" ON admin_users;
DROP POLICY IF EXISTS "Admin users can update own data" ON admin_users;

-- Create RLS policies
CREATE POLICY "Admin users can view own data"
  ON admin_users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Admin users can update own data"
  ON admin_users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Function to verify admin status
CREATE OR REPLACE FUNCTION verify_admin(user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM admin_users
    WHERE id = user_id
    AND role = 'admin'
  );
END;
$$;

-- Function to check admin credentials
CREATE OR REPLACE FUNCTION check_admin_credentials(email text, password text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM auth.users u
    JOIN admin_users a ON u.id = a.id
    WHERE u.email = check_admin_credentials.email
    AND u.encrypted_password = crypt(check_admin_credentials.password, u.encrypted_password)
    AND a.role = 'admin'
  );
END;
$$;

-- Create initial admin user
DO $$ 
DECLARE
  new_admin_id uuid := gen_random_uuid();
BEGIN
  -- Clean up any existing admin data
  DELETE FROM admin_users WHERE email = '<EMAIL>';
  DELETE FROM auth.users WHERE email = '<EMAIL>';

  -- Insert admin user
  INSERT INTO auth.users (
    id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    role,
    aud,
    confirmation_token
  )
  VALUES (
    new_admin_id,
    '<EMAIL>',
    crypt('11111111', gen_salt('bf')),
    now(),
    now(),
    now(),
    '{"provider":"email","providers":["email"]}'::jsonb,
    '{"name":"Admin User"}'::jsonb,
    false,
    'authenticated',
    'authenticated',
    'confirmed'
  );

  -- Add admin user to admin_users table
  INSERT INTO admin_users (
    id,
    email,
    role,
    created_at
  )
  VALUES (
    new_admin_id,
    '<EMAIL>',
    'admin',
    now()
  );
END $$;