import { useState, useEffect, useCallback } from 'react';
import { FileText, Search, Download, Calendar, User, ExternalLink, Clock } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';
import LoginModal from '../components/LoginModal';

interface Whitepaper {
  id: string;
  title: string;
  description: string;
  pdf_url: string;
  image_url: string | null;
  is_public: boolean;
  download_count: number;
  type: string;
  whitepaper_link?: string;
  created_at: string;
}

export default function Whitepapers() {
  const [whitepapers, setWhitepapers] = useState<Whitepaper[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [downloadingId, setDownloadingId] = useState<string | null>(null);
  const [filteredWhitepapers, setFilteredWhitepapers] = useState<Whitepaper[]>([]);
  const [expandedDescriptions, setExpandedDescriptions] = useState<Record<string, boolean>>({});
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState<{ id: string, url: string } | null>(null);
  const user = useAuthStore((state) => state.user);

  const fetchWhitepapers = useCallback(async () => {
    try {
      let query = supabase
        .from('guides')
        .select('*')
        .eq('type', 'whitepaper')
        .order('created_at', { ascending: false });

      // Only show public whitepapers to non-authenticated users
      if (!user) {
        query = query.eq('is_public', true);
      }

      const { data, error } = await query;

      if (error) throw error;
      setWhitepapers(data || []);
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchWhitepapers();
  }, [fetchWhitepapers]);

  useEffect(() => {
    // Filter whitepapers based on search query
    const filtered = whitepapers.filter(whitepaper => {
      const query = searchQuery.toLowerCase();
      return (
        whitepaper.title.toLowerCase().includes(query) ||
        whitepaper.description.toLowerCase().includes(query)
      );
    });

    setFilteredWhitepapers(filtered);
  }, [searchQuery, whitepapers]);

  // Function to toggle description expansion
  const toggleDescription = (whitepaperID: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [whitepaperID]: !prev[whitepaperID]
    }));
  };

  const handleDownload = useCallback(async (guideId: string, pdfUrl: string) => {
    // Check if user is authenticated
    if (!user) {
      // Show login modal
      setSelectedPdf({ id: guideId, url: pdfUrl });
      // Store the URL type to determine which title to show
      sessionStorage.setItem('loginModalType', 'download');
      setShowLoginModal(true);
      return;
    }

    try {
      setDownloadingId(guideId);

      // First get current download count
      const { data: guideData, error: fetchError } = await supabase
        .from('guides')
        .select('download_count')
        .eq('id', guideId)
        .single();

      if (fetchError) throw fetchError;

      // Increment download count
      const { error: updateError } = await supabase
        .from('guides')
        .update({ download_count: (guideData.download_count || 0) + 1 })
        .eq('id', guideId);

      if (updateError) throw updateError;

      // Handle download
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = pdfUrl.split('/').pop() || 'whitepaper.pdf';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Refresh whitepapers to show updated count
      fetchWhitepapers();
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || 'Failed to download whitepaper');
    } finally {
      setDownloadingId(null);
    }
  }, [user, fetchWhitepapers]);

  // Handle closing the login modal
  const handleCloseLoginModal = useCallback(() => {
    setShowLoginModal(false);
    setSelectedPdf(null);
    // Clear the modal type from session storage
    sessionStorage.removeItem('loginModalType');
  }, []);

  // Handle continuing with download or view online after login
  const handleContinueDownload = useCallback(() => {
    if (selectedPdf && user) {
      // Check if the URL is a PDF (likely a download) or a regular URL (likely view online)
      if (selectedPdf.url.toLowerCase().endsWith('.pdf')) {
        handleDownload(selectedPdf.id, selectedPdf.url);
      } else {
        // Open in new tab for non-PDF URLs (view online)
        window.open(selectedPdf.url, '_blank', 'noopener,noreferrer');
      }
      setSelectedPdf(null);
    }
  }, [selectedPdf, user, handleDownload]);

  // Check if user changed and we have a pending download
  useEffect(() => {
    if (user && selectedPdf) {
      handleContinueDownload();
    }
  }, [user, selectedPdf, handleContinueDownload]);

  // Function to truncate text
  const truncateText = (text: string, wordCount: number = 15) => {
    if (!text) return '';
    const words = text.split(' ');
    if (words.length <= wordCount) return text;
    return words.slice(0, wordCount).join(' ') + '...';
  };

  return (
    <div className="overflow-x-hidden">
      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={handleCloseLoginModal}
        title="Download Whitepaper"
        message="Please log in to download this whitepaper. Logging in allows us to save your information for future downloads."
      />

      {/* Hero Section */}
      <section className="text-white py-[78px] relative rounded-lg overflow-hidden" style={{ background: 'radial-gradient(50% 55% at 0% 0%, #06B6D4 0%, #3B82F6 100%)', minHeight: '0' }}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Small decorative dots */}
          <div className="absolute top-32 right-20 w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-32 w-3 h-3 bg-blue-200 rounded-full opacity-60 animate-pulse delay-75"></div>
          <div className="absolute bottom-32 right-24 w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-150"></div>
          <div className="absolute bottom-20 right-40 w-1 h-1 bg-blue-100 rounded-full animate-pulse delay-300"></div>

          {/* Geometric shapes */}
          <div className="absolute top-16 right-16 w-12 h-12 border border-white/20 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-24 right-64 w-8 h-8 border border-white/10 rotate-12 animate-bounce"></div>

          {/* Additional decorative lines */}
          <div className="absolute top-1/3 right-0 w-px h-20 bg-gradient-to-b from-transparent via-white/20 to-transparent"></div>
          <div className="absolute top-2/3 right-12 w-px h-16 bg-gradient-to-b from-transparent via-white/15 to-transparent"></div>
        </div>

        {/* Creative whitepaper illustration on the right */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block">
          {/* Main illustration container */}
          <div className="relative h-full flex items-center justify-center">
            {/* Digital whitepaper visualization - Scaled up */}
            <div className="relative scale-150">
              {/* Floating document pages */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float">
                <div className="p-3">
                  <div className="w-full h-2.5 bg-gray-300/50 rounded mb-1.5"></div>
                  <div className="w-3/4 h-1.5 bg-gray-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-gray-200/50 rounded mb-1"></div>
                  <div className="w-2/3 h-1 bg-gray-100/50 rounded mb-1"></div>
                  <div className="w-full h-1 bg-gray-100/50 rounded"></div>
                  <div className="absolute top-2 right-2 w-2 h-2 bg-green-500 rounded-full"></div>
                </div>
              </div>

              <div className="absolute -top-6 right-10 w-18 h-22 bg-white/15 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed">
                <div className="p-2.5">
                  <div className="w-full h-2 bg-indigo-400/50 rounded mb-1.5"></div>
                  <div className="w-2/3 h-1.5 bg-indigo-300/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-indigo-300/50 rounded mb-1"></div>
                  <div className="w-3/4 h-1 bg-indigo-200/50 rounded mb-1"></div>
                  <div className="w-1/2 h-1 bg-indigo-200/50 rounded"></div>
                  <div className="absolute top-2 right-2 w-2 h-2 bg-blue-400 rounded-full"></div>
                </div>
              </div>

              {/* Central research hub */}
              <div className="w-40 h-40 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center relative animate-pulse-slow">
                <div className="w-28 h-28 bg-white/30 rounded-full flex items-center justify-center">
                  <FileText className="h-14 w-14 text-white" />
                </div>

                {/* Orbiting research elements */}
                <div className="absolute inset-0 animate-spin-slow">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-green-300 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -right-3 transform -translate-y-1/2 w-4 h-4 bg-indigo-200 rounded-full opacity-60"></div>
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-teal-400 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -left-3 transform -translate-y-1/2 w-4 h-4 bg-purple-100 rounded-full opacity-60"></div>
                </div>
              </div>

              {/* Floating whitepaper-related icons */}
              <div className="absolute -bottom-8 -left-8 w-16 h-16 bg-white/15 rounded-lg backdrop-blur-sm flex items-center justify-center transform -rotate-12 animate-bounce-slow">
                <Search className="h-8 w-8 text-white" />
              </div>

              <div className="absolute bottom-3 right-6 w-14 h-14 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center animate-float">
                <Download className="h-7 w-7 text-white" />
              </div>

              <div className="absolute top-10 -left-10 w-18 h-18 bg-white/10 rounded-xl backdrop-blur-sm flex items-center justify-center transform rotate-45 animate-pulse">
                <ExternalLink className="h-8 w-8 text-white transform -rotate-45" />
              </div>

              <div className="absolute top-6 right-2 w-12 h-12 bg-white/15 rounded-full backdrop-blur-sm flex items-center justify-center animate-float">
                <User className="h-6 w-6 text-white" />
              </div>

              {/* Additional research elements */}
              <div className="absolute bottom-8 left-4 w-10 h-10 bg-white/10 rounded-lg backdrop-blur-sm flex items-center justify-center animate-pulse">
                <Calendar className="h-5 w-5 text-white" />
              </div>
            </div>

            {/* Data flow lines for research connections */}
            <div className="absolute inset-0 pointer-events-none scale-125">
              <svg className="w-full h-full opacity-20" viewBox="0 0 400 400">
                <defs>
                  <linearGradient id="whitepaper-gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="rgba(255,255,255,0)" />
                    <stop offset="50%" stopColor="rgba(255,255,255,0.3)" />
                    <stop offset="100%" stopColor="rgba(255,255,255,0)" />
                  </linearGradient>
                </defs>
                <path
                  d="M50,200 Q200,100 350,200 Q200,300 50,200"
                  fill="none"
                  stroke="url(#whitepaper-gradient1)"
                  strokeWidth="3"
                  className="animate-pulse"
                />
                <path
                  d="M100,150 Q200,50 300,150"
                  fill="none"
                  stroke="url(#whitepaper-gradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-500"
                />
                <path
                  d="M100,250 Q200,350 300,250"
                  fill="none"
                  stroke="url(#whitepaper-gradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-1000"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Professional whitepaper icons on the right side */}
        <div className="absolute right-8 top-1/2 transform -translate-y-1/2 hidden xl:flex flex-col space-y-6 opacity-15 z-10">
          <div className="p-3 bg-white/10 rounded-full backdrop-blur-sm animate-float">
            <Clock className="h-6 w-6 text-white" />
          </div>
          <div className="p-3 bg-white/10 rounded-full backdrop-blur-sm animate-float-delayed">
            <User className="h-6 w-6 text-white" />
          </div>
        </div>

        {/* Content pushed to the left */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Whitepapers
              <span className="block text-3xl font-normal mt-2 text-gray-200">
                Research & Insights
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Explore our collection of whitepapers covering industry insights, best practices, and innovative approaches to public health and emergency management.
            </p>

            <div className="relative max-w-xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-400" />
              <input
                type="text"
                placeholder="Search whitepapers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 rounded-lg bg-white/20 backdrop-blur-sm border border-white/30 text-white placeholder-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300 focus:bg-white/30"
              />
            </div>
          </div>
        </div>

        {/* Add custom animations */}
        <style jsx>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(12deg); }
            50% { transform: translateY(-10px) rotate(12deg); }
          }
          @keyframes float-delayed {
            0%, 100% { transform: translateY(0px) rotate(-6deg); }
            50% { transform: translateY(-8px) rotate(-6deg); }
          }
          @keyframes spin-slow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @keyframes bounce-slow {
            0%, 100% { transform: translateY(0px) rotate(-12deg); }
            50% { transform: translateY(-5px) rotate(-12deg); }
          }
          @keyframes pulse-slow {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
          .animate-float { animation: float 3s ease-in-out infinite; }
          .animate-float-delayed { animation: float-delayed 3s ease-in-out infinite 1s; }
          .animate-spin-slow { animation: spin-slow 20s linear infinite; }
          .animate-bounce-slow { animation: bounce-slow 2s ease-in-out infinite; }
          .animate-pulse-slow { animation: pulse-slow 4s ease-in-out infinite; }
        `}</style>
      </section>

      {/* Whitepapers Grid */}
      {!loading && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl font-bold mb-8">Featured Whitepapers</h2>

            {error ? (
              <div className="bg-red-50 text-red-600 p-6 rounded-lg text-center">
                <p>Error loading whitepapers: {error}</p>
              </div>
            ) : filteredWhitepapers.length === 0 ? (
              searchQuery ? (
                <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                  <p className="text-gray-600">
                    No whitepapers match your search "{searchQuery}". Try different keywords.
                  </p>
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-600 mb-2">No whitepapers available</h3>
                  <p className="text-gray-500">Check back soon for new whitepapers.</p>
                </div>
              )
            ) : (
              <div className="grid md:grid-cols-3 gap-8">
                {filteredWhitepapers.map((whitepaper) => (
                  <div key={whitepaper.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    {whitepaper.image_url ? (
                      <img
                        src={whitepaper.image_url}
                        alt={whitepaper.title}
                        className="w-full h-48 object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.style.display = 'none';
                          target.parentElement!.innerHTML = `
                            <div class="w-full h-48 bg-gray-100 flex items-center justify-center">
                              <div class="text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </div>
                            </div>
                          `;
                        }}
                      />
                    ) : (
                      <div className="w-full h-48 bg-gray-100 flex items-center justify-center">
                        <div className="text-center">
                          <FileText className="h-12 w-12 text-gray-400 mx-auto" />
                        </div>
                      </div>
                    )}

                    <div className="p-5">
                      <h3 className="text-xl font-bold mb-2 line-clamp-2">{whitepaper.title}</h3>
                      <div className="text-gray-600 mb-4 text-sm">
                        {expandedDescriptions[whitepaper.id] ? (
                          <>
                            <p className="break-words">{whitepaper.description}</p>
                            <button
                              onClick={() => toggleDescription(whitepaper.id)}
                              className="text-blue-600 text-sm mt-1 hover:underline"
                            >
                              See Less
                            </button>
                          </>
                        ) : (
                          <>
                            <p className="break-words">{truncateText(whitepaper.description)}</p>
                            {whitepaper.description && whitepaper.description.split(' ').length > 15 && (
                              <button
                                onClick={() => toggleDescription(whitepaper.id)}
                                className="text-blue-600 text-sm mt-1 hover:underline"
                              >
                                See More
                              </button>
                            )}
                          </>
                        )}
                      </div>

                      <div className="flex items-center text-sm text-gray-500 mb-4">
                        <Calendar className="h-4 w-4 mr-1.5" />
                        <span>{new Date(whitepaper.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}</span>
                        <span className="mx-2 text-gray-300">•</span>
                        <User className="h-4 w-4 mr-1.5" />
                        <span>IRS Team</span>
                      </div>

                      <div className="flex flex-col space-y-3 mt-4 pt-3 border-t border-gray-100">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-gray-500">
                            <Download className="h-4 w-4 mr-1" />
                            <span>{whitepaper.download_count} downloads</span>
                          </div>
                          <button
                            onClick={() => handleDownload(whitepaper.id, whitepaper.pdf_url)}
                            className={`flex items-center text-blue-600 font-medium hover:text-blue-700 ${downloadingId === whitepaper.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                            disabled={downloadingId === whitepaper.id}
                          >
                            {downloadingId === whitepaper.id ? 'Downloading...' : 'Download PDF'}
                            <FileText className="h-4 w-4 ml-2" />
                          </button>
                        </div>

                        {whitepaper.whitepaper_link && (
                          <button
                            onClick={() => {
                              if (!user) {
                                // Show login modal with different title for online content
                                setSelectedPdf({ id: whitepaper.id, url: whitepaper.whitepaper_link || '' });
                                // Store the URL type to determine which title to show
                                sessionStorage.setItem('loginModalType', 'viewOnline');
                                setShowLoginModal(true);
                              } else {
                                // Open in new tab
                                window.open(whitepaper.whitepaper_link, '_blank', 'noopener,noreferrer');
                              }
                            }}
                            className="flex items-center justify-center w-full py-2 bg-blue-500 text-white rounded-md font-medium hover:bg-blue-600 transition-colors"
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            View Online
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Call to Action */}
      {!loading && (
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-6">Need More Information?</h2>
            <p className="text-lg text-gray-600 mb-8">
              Contact our team to request additional resources or to discuss how our solutions can help your organization.
            </p>
            <a
              href="/contact"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Contact Us
              <ExternalLink className="h-4 w-4 ml-2" />
            </a>
          </div>
        </section>
      )}
    </div>
  );
}
