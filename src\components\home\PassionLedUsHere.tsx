import React from "react";
import { Link } from "react-router-dom";
import bottomImage from "../../assets/images/homepage/PassionedTeam2.jpg";

// Arrow Icon for buttons
const ArrowRightIcon = ({ className = "" }: { className?: string }) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={`block ${className}`}
  >
    <line x1="5" y1="12" x2="19" y2="12"></line>
    <polyline points="12 5 19 12 12 19"></polyline>
  </svg>
);

const PassionLedUsHere: React.FC = () => {

  return (
    <>
      {/* Text Container with specified properties */}
      <div className="w-full rounded-lg px-5 py-8 md:px-22 md:pt-16 pb-10 text-center relative overflow-x-hidden gap-8 flex flex-col items-center justify-center bg-[#F9FAFB] shadow-sm mt-6 mb-0">
        <h1 className="font-platform text-black font-medium mb-2 leading-tight tracking-wide text-3xl md:text-4xl lg:text-5xl">
          Passion Led us <span className="text-[#A892F7]">here</span>
        </h1>

        <p className="text-black font-medium max-w-6xl mx-auto mb-0 leading-relaxed text-lg md:text-xl lg:text-xl">
          Our capabilities are transferable across many public and private
          sector agencies.
          <br />
          We are versed in a variety of response support.
        </p>

        <p className="text-black font-medium max-w-6xl mx-auto mb-1.5 leading-relaxed text-lg md:text-xl lg:text-xl">
          Let us help you meet your goals with significant knowledge and
          accuracy that counts.
        </p>

        <div className="flex justify-center items-center gap-6 flex-wrap">
          <Link
            to="/solutions"
            className="bg-black text-white border-2 border-black py-0.5 px-0.5 pl-3.5 rounded-[8px] text-sm md:text-base font-medium cursor-pointer flex items-center justify-between transition-all duration-200 ease-in-out tracking-wide no-underline w-56 md:w-64 hover:bg-gray-800 hover:border-gray-800"
          >
            <span className="text-sm md:text-base">Discover Our Solution</span>
            <span className="bg-white p-2 rounded inline-flex items-center justify-center leading-none">
              <ArrowRightIcon className="text-black" />
            </span>
          </Link>

          <Link
            to="/book-a-demo"
            className="bg-black text-white border-2 border-black py-0.5 px-0.5 pl-3.5 rounded-[8px] text-sm md:text-base font-medium cursor-pointer flex items-center justify-between transition-all duration-200 ease-in-out tracking-wide no-underline w-56 md:w-64 hover:bg-gray-800 hover:border-gray-800"
          >
            <span className="text-sm md:text-base">Book A Demo</span>
            <span className="bg-white p-2 rounded inline-flex items-center justify-center leading-none">
              <ArrowRightIcon className="text-black" />
            </span>
          </Link>
        </div>
      </div>

      {/* Bottom Image with specified properties */}
      <div className="w-full max-w-7xl 2xl:max-w-7xl mx-auto rounded-8 md:px-0 flex justify-center items-center">
        <img
          src={bottomImage}
          alt="Bottom decorative image"
          className="w-full h-[500px] rounded-lg object-cover"
        />
      </div>
    </>
  );
};

export default PassionLedUsHere;
