-- First check if columns exist
DO $$ 
BEGIN
  -- Only add resolution_comment if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'support_tickets' 
    AND column_name = 'resolution_comment'
  ) THEN
    ALTER TABLE support_tickets ADD COLUMN resolution_comment text;
  END IF;

  -- Only add resolved_by if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'support_tickets' 
    AND column_name = 'resolved_by'
  ) THEN
    ALTER TABLE support_tickets ADD COLUMN resolved_by uuid REFERENCES profiles(id);
  END IF;

  -- Only add resolved_at if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'support_tickets' 
    AND column_name = 'resolved_at'
  ) THEN
    ALTER TABLE support_tickets ADD COLUMN resolved_at timestamptz;
  END IF;
END $$;

-- Drop function if it exists
DROP FUNCTION IF EXISTS resolve_support_ticket(uuid, text, uuid);

-- <PERSON>reate function to resolve ticket
CREATE OR REPLACE FUNCTION resolve_support_ticket(
  ticket_id uuid,
  resolution text,
  resolver_id uuid
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE support_tickets
  SET 
    status = 'resolved',
    resolution_comment = resolution,
    resolved_by = resolver_id,
    resolved_at = now(),
    updated_at = now()
  WHERE id = ticket_id;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION resolve_support_ticket TO authenticated;