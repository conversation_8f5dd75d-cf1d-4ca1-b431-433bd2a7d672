-- First ensure we have the storage schema
CREATE SCHEMA IF NOT EXISTS storage;

-- Create storage bucket for avatars if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM storage.buckets WHERE id = 'avatars'
  ) THEN
    INSERT INTO storage.buckets (id, name, public)
    VALUES ('avatars', 'avatars', true);
  END IF;
END $$;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "authenticated_users_upload_avatar" ON storage.objects;
DROP POLICY IF EXISTS "public_view_avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view avatars" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_users_delete_avatar" ON storage.objects;
DROP POLICY IF EXISTS "allow_public_uploads" ON storage.objects;
DROP POLICY IF EXISTS "allow_public_select" ON storage.objects;
DROP POLICY IF EXISTS "allow_public_delete" ON storage.objects;

-- Create simple storage policies for avatars
CREATE POLICY "authenticated_users_upload_avatar"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'avatars' AND
  (storage.foldername(name))[1] = 'avatars' AND
  CASE 
    WHEN POSITION('.' in name) > 0 THEN
      LOWER(SUBSTRING(name FROM '\.([^\.]+)$')) IN ('jpg', 'jpeg', 'png', 'gif', 'webp')
    ELSE
      false
  END
);

CREATE POLICY "public_view_avatars"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'avatars');

CREATE POLICY "authenticated_users_delete_avatar"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'avatars' AND
  auth.uid()::text = (storage.foldername(name))[2]
);

-- Create function to validate file size
CREATE OR REPLACE FUNCTION storage.validate_size()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  IF NEW.metadata->>'size' IS NOT NULL AND (NEW.metadata->>'size')::int > 5242880 THEN
    RAISE EXCEPTION 'File size exceeds 5MB limit';
  END IF;
  RETURN NEW;
END;
$$;

-- Create trigger for size validation
DROP TRIGGER IF EXISTS validate_avatar_size ON storage.objects;
CREATE TRIGGER validate_avatar_size
  BEFORE INSERT ON storage.objects
  FOR EACH ROW
  WHEN (NEW.bucket_id = 'avatars')
  EXECUTE FUNCTION storage.validate_size();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA storage TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA storage TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA storage TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA storage TO postgres, anon, authenticated, service_role;