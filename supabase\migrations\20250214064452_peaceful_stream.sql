-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can subscribe" ON subscribers;
DROP POLICY IF EXISTS "Admins can manage subscribers" ON subscribers;

-- Create new policies for subscribers
CREATE POLICY "Public can read subscriber count"
  ON subscribers
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Anyone can subscribe"
  ON subscribers
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Admins can manage subscribers"
  ON subscribers
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));

-- Grant necessary permissions
GRANT SELECT ON subscribers TO anon;
GRANT INSERT ON subscribers TO anon;
GRANT ALL ON subscribers TO authenticated;