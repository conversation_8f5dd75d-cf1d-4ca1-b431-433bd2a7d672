-- Add role column to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS role text DEFAULT 'user' CHECK (role IN ('admin', 'user'));

-- Create admin check function
CREATE OR REPLACE FUNCTION is_admin(user_email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles
    WHERE email = user_email
    AND role = 'admin'
  );
END;
$$;

-- Create function to update last login
CREATE OR REPLACE FUNCTION update_last_login(user_email text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE profiles
  SET updated_at = now()
  WHERE email = user_email;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION is_admin TO authenticated;
GRANT EXECUTE ON FUNCTION update_last_login TO authenticated;

-- Update existing admin users
UPDATE profiles 
SET role = 'admin'
WHERE email IN ('j<PERSON><PERSON><PERSON>@gmail.com', '<EMAIL>');