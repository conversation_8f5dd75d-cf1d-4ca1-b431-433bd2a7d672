import React from "react";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  PDFDownloadLink,
  Image,
} from "@react-pdf/renderer";
import irsLogo from "../assets/images/new-irs-logo2.3.png";

// Use default fonts instead of custom fonts to avoid loading issues
// The default font families available in react-pdf are:
// - Helvetica (default)
// - Times-Roman
// - Courier

// Define styles
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 30,
    fontFamily: "Helvetica",
    fontSize: 10, // Smaller base font size
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20, // Reduced margin
  },
  logo: {
    width: 200,
    marginBottom: 10,
  },
  companyInfo: {
    fontSize: 9, // Smaller font for company info
  },
  invoiceTitle: {
    fontSize: 18, // Smaller title
    fontWeight: "bold",
    marginBottom: 5, // Reduced margin
  },
  customerInfo: {
    marginBottom: 15, // Reduced margin
    fontSize: 9, // Smaller font
  },
  table: {
    display: "flex",
    width: "auto",
    borderStyle: "solid",
    borderWidth: 1,
    borderColor: "#DDDDDD",
    marginBottom: 15, // Reduced margin
  },
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#DDDDDD",
    borderBottomStyle: "solid",
  },
  tableHeader: {
    backgroundColor: "#F2F2F2",
    fontWeight: "bold",
    fontSize: 9, // Smaller header font
  },
  tableCell: {
    padding: 6, // Reduced padding
    flex: 1,
    textAlign: "left",
    fontSize: 9, // Smaller cell font
  },
  tableCellRight: {
    padding: 6, // Reduced padding
    flex: 1,
    textAlign: "right",
    fontSize: 9, // Smaller cell font
  },
  totalRow: {
    fontWeight: "bold",
  },
  footer: {
    marginTop: 20, // Reduced margin
    textAlign: "center",
    fontSize: 8, // Smaller footer font
    color: "#777777",
  },
  paymentInfo: {
    marginTop: 15,
    marginBottom: 15,
    padding: 10,
    borderWidth: 1,
    borderColor: "#DDDDDD",
    borderStyle: "solid",
    borderRadius: 4,
    backgroundColor: "#F9F9F9",
  },
  paymentTitle: {
    fontWeight: "bold",
    marginBottom: 5,
    fontSize: 10,
  },
  paymentDetail: {
    fontSize: 9,
    marginBottom: 3,
  },
  notesSection: {
    marginTop: 10,
    marginBottom: 15,
    padding: 10,
    borderWidth: 1,
    borderColor: "#DDDDDD",
    borderStyle: "solid",
    borderRadius: 4,
    backgroundColor: "#F9F9F9",
  },
  notesTitle: {
    fontWeight: "bold",
    marginBottom: 5,
    fontSize: 10,
  },
  notesText: {
    fontSize: 9,
    fontStyle: "italic",
  },
});

// Define the invoice interface
interface InvoiceItem {
  product_name: string;
  price: number;
  quantity: number;
  billing_cycle: string;
  amount: number;
}

interface InvoiceData {
  id: string;
  customer_name: string | null;
  customer_email: string | null;
  status: string;
  amount_due: number;
  amount_paid: number;
  amount_remaining: number;
  currency: string;
  due_date: string;
  created_at: string;
  line_items: InvoiceItem[];
  is_manual?: boolean;
  payment_method?: {
    type: string;
    bank_name?: string;
    account_number?: string;
    routing_number?: string;
  };
  notes?: string;
  ref?: string; // 8-character reference code
}

// Helper function to format currency
const formatCurrency = (amount: number | null, currency: string | null) => {
  if (amount === null || currency === null) return "N/A";

  // Stripe amounts are in cents, so divide by 100 to get dollars
  const amountInDollars = amount / 100;

  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency.toUpperCase(),
  }).format(amountInDollars);
};

// Helper function to format date
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Create the PDF Document component
const InvoicePDFDocument: React.FC<{ invoice: InvoiceData }> = ({
  invoice,
}) => {
  // Add default payment method if not available and it's a manual invoice
  if (!invoice.payment_method && invoice.is_manual) {
    invoice.payment_method = {
      type: "bank_transfer",
      bank_name: "blank",
      account_number: "XXXXXXXX",
      routing_number: "blank",
    };
  }

  // Add default notes for testing if not available
  if (!invoice.notes && invoice.is_manual) {
    invoice.notes =
      "This is a sample invoice. Please pay within the due date. Thank you for your business.";
  }

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
          <View>
            <Image src={irsLogo} style={styles.logo} />
            <View style={styles.companyInfo}>
              <Text>International Responder Systems</Text>
              <Text>157 E Main Street</Text>
              <Text>Elkton, MD 21921-5977</Text>
              <Text><EMAIL></Text>
            </View>
          </View>
          <View>
            <Text style={styles.invoiceTitle}>INVOICE</Text>
            <Text>Invoice #: {invoice.ref || invoice.id.substring(0, 8)}</Text>
            <Text>Date: {formatDate(invoice.created_at)}</Text>
            <Text>
              Due Date:{" "}
              {invoice.due_date ? formatDate(invoice.due_date) : "N/A"}
            </Text>
          </View>
        </View>

        <View style={styles.customerInfo}>
          <Text style={{ fontWeight: "bold" }}>Bill To:</Text>
          <Text>{invoice.customer_name || "N/A"}</Text>
          <Text>{invoice.customer_email || "N/A"}</Text>
        </View>

        {/* Notes Section */}
        {invoice.notes && (
          <View style={styles.notesSection}>
            <Text style={styles.notesTitle}>Notes:</Text>
            <Text style={styles.notesText}>{invoice.notes}</Text>
          </View>
        )}

        <View style={styles.table}>
          <View style={[styles.tableRow, styles.tableHeader]}>
            <Text style={styles.tableCell}>Item</Text>
            <Text style={styles.tableCell}>Billing Cycle</Text>
            <Text style={styles.tableCellRight}>Price</Text>
            <Text style={styles.tableCellRight}>Quantity</Text>
            <Text style={styles.tableCellRight}>Amount</Text>
          </View>

          {invoice.line_items &&
            invoice.line_items.map((item, index) => (
              <View key={index} style={styles.tableRow}>
                <Text style={styles.tableCell}>{item.product_name}</Text>
                <Text style={styles.tableCell}>{item.billing_cycle}</Text>
                <Text style={styles.tableCellRight}>
                  {formatCurrency(item.price, invoice.currency || "usd")}
                </Text>
                <Text style={styles.tableCellRight}>{item.quantity}</Text>
                <Text style={styles.tableCellRight}>
                  {formatCurrency(item.amount, invoice.currency || "usd")}
                </Text>
              </View>
            ))}

          <View style={[styles.tableRow, styles.totalRow]}>
            <Text style={[styles.tableCell, { flex: 4, textAlign: "right" }]}>
              Total:
            </Text>
            <Text style={styles.tableCellRight}>
              {formatCurrency(
                invoice.amount_due || 0,
                invoice.currency || "usd"
              )}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={[styles.tableCell, { flex: 4, textAlign: "right" }]}>
              Amount Paid:
            </Text>
            <Text style={styles.tableCellRight}>
              {formatCurrency(
                invoice.amount_paid || 0,
                invoice.currency || "usd"
              )}
            </Text>
          </View>

          <View style={[styles.tableRow, styles.totalRow]}>
            <Text style={[styles.tableCell, { flex: 4, textAlign: "right" }]}>
              Amount Due:
            </Text>
            <Text style={styles.tableCellRight}>
              {formatCurrency(
                invoice.amount_remaining || 0,
                invoice.currency || "usd"
              )}
            </Text>
          </View>
        </View>

        {/* Payment Method Information */}
        {invoice.payment_method && (
          <View style={styles.paymentInfo}>
            <Text style={styles.paymentTitle}>Payment Information:</Text>
            <Text style={styles.paymentDetail}>
              Method: {invoice.payment_method.type}
            </Text>

            {invoice.payment_method.type === "bank_transfer" && (
              <>
                {invoice.payment_method.bank_name && (
                  <Text style={styles.paymentDetail}>
                    Bank: {invoice.payment_method.bank_name}
                  </Text>
                )}
                {invoice.payment_method.account_number && (
                  <Text style={styles.paymentDetail}>
                    Account Number: {invoice.payment_method.account_number}
                  </Text>
                )}
                {invoice.payment_method.routing_number && (
                  <Text style={styles.paymentDetail}>
                    Routing Number: {invoice.payment_method.routing_number}
                  </Text>
                )}
              </>
            )}
          </View>
        )}

        <View>
          <Text style={{ fontWeight: "bold" }}>
            Status: {invoice.status ? invoice.status.toUpperCase() : "N/A"}
          </Text>
          <Text>Payment Terms: Due within 30 days</Text>
        </View>

        <View style={styles.footer}>
          <Text>Thank you for your business!</Text>
          <Text style={{ marginTop: 5 }}>
            International Responder Systems - Public Health
            Responder Systems
          </Text>
        </View>
      </Page>
    </Document>
  );
};

// Create a download link component
interface InvoicePDFDownloadProps {
  invoice: InvoiceData;
  fileName?: string;
  className?: string;
  children: React.ReactNode;
}

export const InvoicePDFDownload: React.FC<InvoicePDFDownloadProps> = ({
  invoice,
  fileName = `invoice-${invoice.ref || invoice.id.substring(0, 8)}.pdf`,
  className,
  children,
}) => (
  <PDFDownloadLink
    document={<InvoicePDFDocument invoice={invoice} />}
    fileName={fileName}
    className={className}
  >
    {children}
  </PDFDownloadLink>
);

export default InvoicePDFDocument;
