-- First grant necessary permissions to the current user
DO $$
BEGIN
  IF EXISTS (SELECT FROM pg_roles WHERE rolname = 'postgres') THEN
    EXECUTE 'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres';
  END IF;
END $$;

-- Drop existing policies if they exist (modified to check table existence)
DO $$
BEGIN
  -- Temporarily disable <PERSON><PERSON> for all tables
  ALTER TABLE IF EXISTS campaign_logs DISABLE ROW LEVEL SECURITY;
  ALTER TABLE IF EXISTS subscribers DISABLE ROW LEVEL SECURITY;
  ALTER TABLE IF EXISTS email_campaigns DISABLE ROW LEVEL SECURITY;

  -- Drop policies
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'campaign_logs') THEN
    EXECUTE 'DROP POLICY IF EXISTS "Ad<PERSON> can view campaign logs" ON campaign_logs';
  END IF;
  
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'subscribers') THEN
    EXECUTE 'DROP POLICY IF EXISTS "Admins can manage subscribers" ON subscribers';
    EXECUTE 'DROP POLICY IF EXISTS "Users can manage own subscriptions" ON subscribers';
    EXECUTE 'DROP POLICY IF EXISTS "Public read access to subscribers" ON subscribers';
  END IF;
  
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'email_campaigns') THEN
    EXECUTE 'DROP POLICY IF EXISTS "Admins can manage campaigns" ON email_campaigns';
  END IF;
END $$;

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Drop existing tables if they exist (cascade to remove dependencies)
DROP TABLE IF EXISTS campaign_logs CASCADE;
DROP TABLE IF EXISTS subscribers CASCADE;
DROP TABLE IF EXISTS email_campaigns CASCADE;

-- Create email_campaigns table
CREATE TABLE email_campaigns (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  subject text NOT NULL,
  content text NOT NULL,
  status text NOT NULL CHECK (status IN ('draft', 'scheduled', 'sent')),
  scheduled_for timestamptz,
  sent boolean DEFAULT false,
  sent_at timestamptz,
  opens integer DEFAULT 0,
  clicks integer DEFAULT 0,
  recipients integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create subscribers table
CREATE TABLE subscribers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  full_name text,
  status text NOT NULL CHECK (status IN ('active', 'unsubscribed')) DEFAULT 'active',
  subscribed_at timestamptz DEFAULT now(),
  last_email_sent timestamptz,
  CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Create campaign_logs table for tracking email events
CREATE TABLE campaign_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid REFERENCES email_campaigns(id) ON DELETE CASCADE,
  subscriber_id uuid REFERENCES subscribers(id) ON DELETE CASCADE,
  event_type text NOT NULL CHECK (event_type IN ('sent', 'opened', 'clicked', 'bounced', 'complained', 'unsubscribed')),
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE email_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for email_campaigns
CREATE POLICY "Public read access to campaigns" ON email_campaigns
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Public insert access to campaigns" ON email_campaigns
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Public update access to campaigns" ON email_campaigns
  FOR UPDATE
  TO public
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Public delete access to campaigns" ON email_campaigns
  FOR DELETE
  TO public
  USING (true);

-- Create policies for subscribers
CREATE POLICY "Public read access" ON subscribers
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Public insert access" ON subscribers
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Public update own" ON subscribers
  FOR UPDATE
  TO public
  USING (email = current_setting('request.jwt.claims', true)::json->>'email')
  WITH CHECK (email = current_setting('request.jwt.claims', true)::json->>'email');

CREATE POLICY "Public delete own" ON subscribers
  FOR DELETE
  TO public
  USING (email = current_setting('request.jwt.claims', true)::json->>'email');

CREATE POLICY "Authenticated full access" ON subscribers
  FOR ALL
  TO authenticated
  USING (email = auth.jwt() ->> 'email')
  WITH CHECK (email = auth.jwt() ->> 'email');

CREATE POLICY "Admin full access" ON subscribers
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  ));

-- Create policies for campaign_logs
CREATE POLICY "Admins can view campaign logs"
  ON campaign_logs
  FOR SELECT
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_email_campaigns_updated_at
  BEFORE UPDATE ON email_campaigns
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data
INSERT INTO subscribers (email, full_name, status) VALUES
  ('<EMAIL>', 'John Doe', 'active'),
  ('<EMAIL>', 'Jane Smith', 'active'),
  ('<EMAIL>', 'Bob Wilson', 'unsubscribed');

INSERT INTO email_campaigns (name, subject, content, status, opens, clicks, recipients) VALUES
  (
    'March Newsletter',
    'Latest Updates from IRS',
    'Here are the latest updates from International Responder Systems...',
    'sent',
    245,
    89,
    1000
  ),
  (
    'Product Launch',
    'Introducing New Features',
    'We''re excited to announce new features in our products...',
    'draft',
    0,
    0,
    0
  );

-- Grant necessary permissions
-- Update grants for subscribers table
GRANT SELECT, INSERT, UPDATE, DELETE ON subscribers TO public;
GRANT SELECT, INSERT, UPDATE, DELETE ON subscribers TO anon;
GRANT ALL ON subscribers TO authenticated;
GRANT ALL ON subscribers TO service_role;
GRANT ALL ON email_campaigns TO public;
GRANT ALL ON email_campaigns TO anon;
GRANT ALL ON campaign_logs TO authenticated;

-- Add these to your migration file
CREATE OR REPLACE FUNCTION get_active_subscriber_count()
RETURNS integer AS $$
BEGIN
  RETURN (SELECT COUNT(*) FROM subscribers WHERE status = 'active');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION subscribe_to_newsletter(
  p_email text,
  p_full_name text
) RETURNS void AS $$
BEGIN
  -- Check if already subscribed
  IF EXISTS (SELECT 1 FROM subscribers WHERE email = p_email AND status = 'active') THEN
    RAISE EXCEPTION 'This email is already subscribed';
  END IF;
  
  -- Insert new subscriber
  INSERT INTO subscribers (email, full_name, status, subscribed_at)
  VALUES (p_email, p_full_name, 'active', NOW());
  
  -- You can add email sending logic here if needed
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;