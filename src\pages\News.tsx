import React, { useState, useEffect } from 'react';
import { Calendar, ArrowRight, Tag, Search, ArrowUpRight, Timer as TimerIcon, Rss, Edit } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';

interface NewsPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  status: string;
  featured_image: string;
  created_at: string;
  type: string;
}

export default function News() {
  const navigate = useNavigate();
  const [newsPosts, setNewsPosts] = useState<NewsPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<NewsPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const fetchNewsPosts = async () => {
    try {
      const { data: posts, error: postsError } = await supabase
        .from('blog_posts')
        .select(`*`)
        .eq('status', 'published')
        .eq('type', 'news')
        .order('created_at', { ascending: false });

      if (postsError) throw postsError;
      return posts || [];
    } catch (err: any) {
      setError(err.message);
      return [];
    }
  };

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        const posts = await fetchNewsPosts();
        if (isMounted) {
          setNewsPosts(posts);
          setFilteredPosts(posts);
        }
      } catch (err: any) {
        if (isMounted) setError(err.message);
      } finally {
        if (isMounted) setLoading(false);
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, []);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredPosts(newsPosts);
    } else {
      const filtered = newsPosts.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredPosts(filtered);
    }
  }, [searchQuery, newsPosts]);

  // if (loading) return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  // if (error) return <div className="min-h-screen flex items-center justify-center text-red-500">Error: {error}</div>;

  return (
    <div className="">
      {/* Hero Section */}
      <section className="relative text-white py-24 rounded-lg" style={{ background: 'radial-gradient(50% 55% at 0% 0%, #F59E0B 0%, #D97706 100%)' }}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Small decorative dots */}
          <div className="absolute top-32 right-20 w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-32 w-3 h-3 bg-blue-200 rounded-full opacity-60 animate-pulse delay-75"></div>
          <div className="absolute bottom-32 right-24 w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-150"></div>
          <div className="absolute bottom-20 right-40 w-1 h-1 bg-blue-100 rounded-full animate-pulse delay-300"></div>

          {/* Geometric shapes */}
          <div className="absolute top-16 right-16 w-12 h-12 border border-white/20 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-24 right-64 w-8 h-8 border border-white/10 rotate-12 animate-bounce"></div>

          {/* Additional decorative lines */}
          <div className="absolute top-1/3 right-0 w-px h-20 bg-gradient-to-b from-transparent via-white/20 to-transparent"></div>
          <div className="absolute top-2/3 right-12 w-px h-16 bg-gradient-to-b from-transparent via-white/15 to-transparent"></div>
        </div>

        {/* Creative illustration on the right - Made bigger */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block overflow-hidden">
          {/* Main illustration container */}
          <div className="relative h-full flex items-center justify-center">
            {/* Digital news visualization - Scaled up */}
            <div className="relative scale-150">
              {/* Floating news cards - Made bigger */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float">
                <div className="p-3">
                  <div className="w-full h-2.5 bg-blue-300/50 rounded mb-1.5"></div>
                  <div className="w-3/4 h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-200/50 rounded mb-1"></div>
                  <div className="w-2/3 h-1 bg-blue-100/50 rounded"></div>
                </div>
              </div>

              <div className="absolute -top-6 right-10 w-18 h-22 bg-white/15 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed">
                <div className="p-2.5">
                  <div className="w-full h-2 bg-blue-400/50 rounded mb-1.5"></div>
                  <div className="w-2/3 h-1.5 bg-blue-300/50 rounded mb-1"></div>
                  <div className="w-full h-1.5 bg-blue-300/50 rounded mb-1"></div>
                  <div className="w-3/4 h-1 bg-blue-200/50 rounded"></div>
                </div>
              </div>

              {/* Central news icon - Made bigger */}
              <div className="w-40 h-40 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center relative animate-pulse-slow">
                <div className="w-28 h-28 bg-white/30 rounded-full flex items-center justify-center">
                  <Rss className="h-14 w-14 text-white" /> {/* Using Rss icon for news */}
                </div>

                {/* Orbiting elements - Made bigger */}
                <div className="absolute inset-0 animate-spin-slow">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-300 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -right-3 transform -translate-y-1/2 w-4 h-4 bg-blue-200 rounded-full opacity-60"></div>
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-400 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -left-3 transform -translate-y-1/2 w-4 h-4 bg-blue-100 rounded-full opacity-60"></div>
                </div>
              </div>

              {/* Floating news icons - Made bigger */}
              <div className="absolute -bottom-8 -left-8 w-16 h-16 bg-white/15 rounded-lg backdrop-blur-sm flex items-center justify-center transform -rotate-12 animate-bounce-slow">
                <Search className="h-8 w-8 text-white" />
              </div>

              <div className="absolute bottom-3 right-6 w-14 h-14 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center animate-float">
                <Calendar className="h-7 w-7 text-white" /> {/* Using Calendar icon */}
              </div>

              <div className="absolute top-10 -left-10 w-18 h-18 bg-white/10 rounded-xl backdrop-blur-sm flex items-center justify-center transform rotate-45 animate-pulse">
                <Edit className="h-8 w-8 text-white transform -rotate-45" /> {/* Using Edit icon */}
              </div>
            </div>

            {/* Data flow lines - Made bigger */}
            <div className="absolute inset-0 pointer-events-none scale-125">
              <svg className="w-full h-full opacity-20" viewBox="0 0 400 400">
                <defs>
                  <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="rgba(255,255,255,0)" />
                    <stop offset="50%" stopColor="rgba(255,255,255,0.3)" />
                    <stop offset="100%" stopColor="rgba(255,255,255,0)" />
                  </linearGradient>
                </defs>
                <path
                  d="M50,200 Q200,100 350,200 Q200,300 50,200"
                  fill="none"
                  stroke="url(#gradient1)"
                  strokeWidth="3"
                  className="animate-pulse"
                />
                <path
                  d="M100,150 Q200,50 300,150"
                  fill="none"
                  stroke="url(#gradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-500"
                />
                <path
                  d="M100,250 Q200,350 300,250"
                  fill="none"
                  stroke="url(#gradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-1000"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Content pushed to the left */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Latest News
              <span className="block text-3xl font-normal mt-2 text-blue-200">
                Stay Updated
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Stay updated with the latest developments and announcements from International Responder Systems
            </p>

            {/* Search Bar */}
            <div className="relative w-full max-w-xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-400 z-50" />
              <input
                type="text"
                placeholder="Search news..."
                className="w-full pl-12 pr-4 py-4 rounded-lg bg-white/20 backdrop-blur-sm border border-white/30 text-white placeholder-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300 focus:bg-white/30"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Add custom animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(12deg); }
          50% { transform: translateY(-10px) rotate(12deg); }
        }
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0px) rotate(-6deg); }
          50% { transform: translateY(-8px) rotate(-6deg); }
        }
        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        @keyframes bounce-slow {
          0%, 100% { transform: translateY(0px) rotate(-12deg); }
          50% { transform: translateY(-8px) rotate(-12deg); }
        }
        @keyframes pulse-slow {
          0%, 100% { opacity: 1; }
          50% { opacity: .7; }
        }
      `}</style>

      {/* Featured News - Only show when not searching */}
      {filteredPosts.length > 0 && searchQuery.trim() === '' && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl font-bold mb-8">Featured News</h2>
            <div className="bg-white rounded-lg shadow-xl overflow-hidden">
              <div className="md:flex">
                {filteredPosts[0].featured_image && (
                  <div className="md:w-1/2">
                    <img
                      src={filteredPosts[0].featured_image}
                      alt={filteredPosts[0].title}
                      className="h-full w-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </div>
                )}
                <div className="md:w-1/2 p-8">
                  <span className="inline-block px-4 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mb-4">
                    News
                  </span>
                  <h2 className="text-3xl font-bold mb-4">{filteredPosts[0].title}</h2>
                  <p className="text-gray-600 mb-6">
                    {filteredPosts[0].excerpt || filteredPosts[0].content.substring(0, 200) + '...'}
                  </p>
                  <div className="flex items-center mb-6">
                    <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="text-gray-600">
                      {new Date(filteredPosts[0].created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                  <button
                    onClick={() => navigate(`/news/${filteredPosts[0].id}`)}
                    className="flex items-center gap-1 bg-blue-400 hover:bg-blue-300 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
                  >
                    Read Full Story
                    <ArrowUpRight className="h-5 w-5 ml-2" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* News Grid - Show all filtered posts when searching */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold mb-8">
            {searchQuery.trim() ? 'Search Results' : 'Recent News'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.length > 0 ? (
              // Skip the first post when showing all posts and not searching (it's already shown in featured section)
              (searchQuery.trim() ? filteredPosts : filteredPosts.slice(1)).map((post) => (
                <div key={post.id} className="relative bg-white rounded-lg shadow-md overflow-hidden flex flex-col h-full hover:shadow-xl transition-shadow">
                  {post.featured_image && (
                    <img
                      src={post.featured_image}
                      alt={post.title}
                      className="w-full h-48 object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  )}
                  <span className="absolute top-2 right-2 flex items-center gap-2 text-xs rounded-full bg-gray-500 px-2 py-1 text-white">
                    <TimerIcon className='w-3 h-3' />
                    {new Date(post.created_at).toLocaleDateString()}
                  </span>
                  <div className="p-4 flex-grow">
                    <div className="flex items-center space-x-2 mb-4">
                      <Tag className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-blue-600 font-medium">News</span>
                    </div>
                    <h3 className="text-xl font-bold mb-2 line-clamp-2">{post.title}</h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {post.excerpt || post.content.substring(0, 150) + '...'}
                    </p>
                  </div>
                  <div className="p-4 border-t">
                    <button
                      onClick={() => navigate(`/news/${post.id}`)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center justify-center transition-colors duration-200"
                    >
                      Read Full Story
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-500 text-lg">
                  {searchQuery ? 'No news articles match your search.' : 'No news articles found.'}
                </p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
}