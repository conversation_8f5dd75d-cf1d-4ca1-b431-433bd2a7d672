import React from 'react';
import { Link } from 'react-router-dom';
import YahooBackground from '../../assets/images/yahoo-background.svg';
import JimMullikinImage from '../../assets/images/jim-mullikin.png';
import YahooFinance from '../../assets/images/yahoo-finance.svg';

const NewsBannerCard: React.FC = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center font-inter overflow-hidden">
      {/* Background with SVG */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${YahooBackground})`,
        }}
      ></div>

      {/* Content */}
      <div className="relative z-10 flex flex-col items-center gap-3 sm:gap-4 md:gap-5 lg:gap-6 px-4 py-6 sm:py-8 md:py-10 lg:py-12">
        {/* Header */}
        <div className="flex flex-col items-center gap-4 lg:gap-6">
          <div className="bg-white text-[#7C3AED] text-[10px] sm:text-[12.44px] font-normal tracking-[4.83%] px-[6px] sm:px-[7.78px] py-[3px] sm:py-[4.67px] rounded-[3px] animate-fadeInDown">
            News
          </div>

          {/* Yahoo Finance Logo */}
          <div className="flex flex-col items-center gap-2 animate-fadeInDown animation-delay-200">
            <img
              src={YahooFinance}
              alt="Yahoo Finance Logo"
              className="w-auto h-12 sm:h-16 lg:h-18"
            />
          </div>
        </div>

        {/* Description with Jim Mullikin */}
        <div className="flex flex-col items-center gap-2 animate-fadeInDown animation-delay-400">
          <p className="text-lg sm:text-xl lg:text-[24px] font-medium text-white text-center max-w-[600px] leading-[1.37]">
            Check out <span className="font-semibold">Jim Mullikin's</span>
            <span className="inline-flex items-center mx-2">
              <img
                src={JimMullikinImage}
                alt="Jim Mullikin"
                className="w-8 h-8 rounded-full border-2 border-white"
              />
            </span>
          </p>
          <p className="text-lg sm:text-xl lg:text-[24px] font-medium text-white text-center max-w-[600px] leading-[1.37]">
            recent interview with yahoo finance
          </p>
        </div>

        {/* Button */}
        <Link
          to="/news"
          className="bg-white text-black rounded-lg flex items-center gap-2 micro-button animate-fadeInDown animation-delay-600"
          style={{
            padding: '13.81px 21.71px',
            fontFamily: 'Inter, sans-serif',
            fontWeight: 700,
            fontSize: '15.66px',
            lineHeight: '23.68px',
            letterSpacing: '0%',
            verticalAlign: 'middle',
          }}
        >
          Go to article
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="icon">
            <path d="M4.16667 10H15.8333M10 4.16667L15.8333 10L10 15.8333" stroke="currentColor" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </Link>
      </div>
    </div>
  );
};

export default NewsBannerCard;
