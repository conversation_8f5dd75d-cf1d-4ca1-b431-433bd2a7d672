import React from 'react';
import { Link } from 'react-router-dom';
import grantreadyHalfBgVideo from '../../assets/video/grantready-half-bgbanner.mp4';
import grantreadyDecorationVideo from '../../assets/video/grantready-decoration.mp4';
import unionIcon from '../../assets/images/grantready/union-icon.svg';

const Banner: React.FC = () => {
    return (
        <div className="relative w-full overflow-hidden rounded-lg">
            {/* Full-width background container for 2xl+ screens */}
            <div className="relative w-full h-[500px] sm:h-[380px] md:h-[460px] lg:h-[520px] xl:h-[580px] 2xl:h-[620px] rounded-lg md:rounded-[20px] 2xl:rounded-none shadow-2xl 2xl:shadow-none" style={{ backgroundColor: '#000020' }}>
                {/* Content container with max-width constraint */}
                <div className="relative max-w-[1900px] mx-auto h-full overflow-hidden  shadow-2xl 2xl:shadow-2xl">
                    {/* Split Layout Container */}
                    <div className="absolute inset-0 w-full h-full flex">
                        {/* Left Side - Video Background */}
                        <div className="w-full lg:w-1/2 h-full overflow-hidden relative">
                            <video
                                autoPlay
                                loop
                                muted
                                playsInline
                                className="banner-video w-full h-full object-cover"
                            >
                                <source src={grantreadyHalfBgVideo} type="video/mp4" />
                            </video>
                            {/* Overlay for better text readability */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent to-[#020826]"></div>
                        </div>

                        {/* Right Side - Video */}
                        <div className="hidden lg:w-1/2 h-full overflow-hidden relative lg:flex items-center justify-center">
                            {/* Enhanced background glow effects for better video blending */}
                            <div className="absolute inset-0 bg-gradient-radial from-[#4A90E2]/25 via-[#22C55E]/15 to-transparent opacity-60"></div>
                            <div className="absolute inset-0 bg-gradient-radial from-[#22C55E]/20 via-[#4A90E2]/10 to-transparent opacity-50"></div>

                            {/* Central luminous sphere effect to enhance the video blend */}
                            <div className="absolute inset-0 flex items-center justify-center">
                                <div className="w-80 h-80 bg-gradient-radial from-[#4A90E2]/30 via-[#22C55E]/20 to-transparent rounded-full blur-xl opacity-70"></div>
                            </div>

                            {/* Subtle glow orbs behind the video for depth */}
                            <div className="absolute top-1/3 right-1/3 w-20 h-20 bg-[#4A90E2]/20 rounded-full blur-lg opacity-70"></div>
                            <div className="absolute bottom-1/3 left-1/3 w-16 h-16 bg-[#22C55E]/25 rounded-full blur-md opacity-60"></div>
                            <div className="absolute top-1/2 right-1/2 w-12 h-12 bg-[#4A90E2]/25 rounded-full blur-sm opacity-50"></div>

                            {/* Main video element */}
                            <video
                                autoPlay
                                loop
                                muted
                                playsInline
                                className="w-[390%] h-[390%] object-contain relative z-10"
                                style={{
                                    mixBlendMode: 'screen',
                                    opacity: 0.7,
                                    filter: 'brightness(1.4) contrast(1.5) ',
                                    transform: 'scale(1.6)'
                                }}
                            >
                                <source src={grantreadyDecorationVideo} type="video/mp4" />
                            </video>

                            {/* Subtle overlay that doesn't interfere with blend mode */}
                            {/* <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-[#020407]/20 z-20"></div> */}
                        </div>
                    </div>

                    {/* Dynamic Floating Network Lines */}
                    <div className="absolute inset-0 overflow-hidden opacity-30">
                        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 1200 800">
                            <defs>
                                <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stopColor="#797EEC" stopOpacity="0.6" />
                                    <stop offset="50%" stopColor="#22C55E" stopOpacity="0.4" />
                                    <stop offset="100%" stopColor="#797EEC" stopOpacity="0.2" />
                                </linearGradient>
                            </defs>
                            <path d="M100,200 Q300,100 500,200 T900,150" stroke="url(#lineGradient)" strokeWidth="2" fill="none" />
                            <path d="M200,400 Q400,300 600,400 T1000,350" stroke="url(#lineGradient)" strokeWidth="1.5" fill="none" />
                            <path d="M50,600 Q250,500 450,600 T850,550" stroke="url(#lineGradient)" strokeWidth="1" fill="none" />
                        </svg>
                    </div>



                    {/* Content Overlay */}
                    <div className="relative z-10 flex items-center justify-center lg:justify-start h-full px-6 sm:px-8 md:px-10 lg:px-12 xl:px-16 2xl:px-20">
                        <div className="max-w-4xl text-center lg:text-left">

                                {/* Hero Title with Staggered Animation */}
                                <div className="mb-6 md:mb-8">
                                    <h1
                                        className="animate-fadeInUp text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-[60px]"
                                        style={{
                                            fontFamily: 'Platform-Medium, Platform, Inter, sans-serif',
                                            fontWeight: 500,
                                            lineHeight: '1.1',
                                            letterSpacing: '0%',
                                            animationDelay: '0.2s'
                                        }}
                                    >
                                        <span className="text-white drop-shadow-2xl">Smarter </span>
                                        <span
                                            className="bg-clip-text text-transparent"
                                            style={{
                                                background: 'linear-gradient(89.67deg, #38B382 18.41%, #797EEC 92.9%)',
                                                WebkitBackgroundClip: 'text',
                                                WebkitTextFillColor: 'transparent',
                                                fontFamily: 'Platform-Medium, Platform, Inter, sans-serif',
                                                fontWeight: 500,
                                                lineHeight: '1.1',
                                                letterSpacing: '0%'
                                            }}
                                        >
                                            Grant Management
                                        </span>
                                        <br />
                                        <span className="text-white drop-shadow-2xl">
                                            for Health & Emergency <br /> Response
                                        </span>
                                    </h1>
                                </div>

                                {/* Brand Attribution under title */}
                                <div className="mb-6 md:mb-8">
                                    <div className="flex items-center gap-3">
                                        <div className="relative">
                                            <img
                                                src={unionIcon}
                                                alt="GrantReady Icon"
                                                className="w-6 h-6 md:w-8 md:h-8 filter brightness-125"
                                                style={{
                                                    filter: 'drop-shadow(0 0 8px rgba(121, 126, 236, 0.6)) brightness(1.3)'
                                                }}
                                            />
                                        </div>
                                        <div className="flex items-center gap-1 flex-wrap">
                                            <span
                                                className="bg-clip-text text-transparent text-lg sm:text-xl md:text-2xl font-bold tracking-wide"
                                                style={{
                                                    background: 'linear-gradient(89.67deg, #38B382 18.41%, #797EEC 92.9%)',
                                                    WebkitBackgroundClip: 'text',
                                                    WebkitTextFillColor: 'transparent',
                                                }}
                                            >
                                                GrantReady™
                                            </span>
                                            <span className='text-white text-base sm:text-lg md:text-xl font-normal tracking-wide'>
                                                 by International Responders Systems
                                            </span>
                                            {/* <span className="text-white font-normal text-lg sm:text-xl md:text-2xl"> by  */}
                                            {/* International Responders Systems */}
                                            {/* </span> */}
                                        </div>
                                    </div>
                                </div>
                                {/* Enhanced Description */}
                                <div className="mb-8 md:mb-10 max-w-3xl">
                                    <p className="text-white/90 text-base sm:text-lg md:text-xl leading-relaxed font-light animate-fadeInUp" style={{ animationDelay: '0.8s' }}>
                                        Helps local health jurisdictions and states plan, implement, track, and report federal grant funding. all in one seamless platform.
                                    </p>
                                </div>

                                {/* Action Buttons with Enhanced Design */}
                                <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 animate-fadeInUp" style={{ animationDelay: '1s' }}>
                                    <Link
                                        to="/whitepapers"
                                        className="group relative bg-gradient-to-r from-white to-gray-100 text-black px-6 sm:px-8 md:px-10 py-3 sm:py-4 md:py-3 lg:py-3 rounded-lg font-normal text-base md:text-lg flex items-center justify-center gap-2 sm:gap-3 md:gap-4 hover:scale-105 hover:shadow-2xl transition-all duration-500 overflow-hidden"
                                    >
                                        <div className="absolute inset-0 bg-gradient-to-r from-[#797EEC]/20 to-[#22C55E]/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                        <span className="relative z-10 font-bold">Read Whitepaper</span>
                                        <svg
                                            width="20"
                                            height="20"
                                            viewBox="0 0 20 20"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="relative z-10 group-hover:translate-x-2 transition-transform duration-500 sm:w-6 sm:h-6"
                                        >
                                            <path
                                                d="M4.16667 10H15.8333M10 4.16667L15.8333 10L10 15.8333"
                                                stroke="currentColor"
                                                strokeWidth="2.5"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            />
                                        </svg>
                                    </Link>

                                    <Link
                                        to="/support"
                                        className="group relative border-2 border-[#797EEC]/60 text-white px-6 sm:px-8 md:px-10 py-2 md:py-2 lg:py-2 rounded-lg font-bold text-base md:text-lg hover:scale-105 transition-all duration-500 text-center backdrop-blur-md bg-white/10 hover:bg-[#797EEC]/20 hover:border-[#797EEC] shadow-xl hover:shadow-[#797EEC]/30"
                                    >
                                        <span className="relative z-10 font-bold">Support Center</span>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Custom CSS for animations and glow effects */}
                    <style dangerouslySetInnerHTML={{
                        __html: `
                        @font-face {
                            font-family: "Platform-Medium";
                            src: url("src/assets/font/Platform-Medium/web-fonts/f58919c8d17a7e8e170c3ba632f93bd3.eot");
                            src: url("src/assets/font/Platform-Medium/web-fonts/f58919c8d17a7e8e170c3ba632f93bd3.eot?#iefix") format("embedded-opentype"),
                                url("src/assets/font/Platform-Medium/web-fonts/f58919c8d17a7e8e170c3ba632f93bd3.woff") format("woff"),
                                url("src/assets/font/Platform-Medium/web-fonts/f58919c8d17a7e8e170c3ba632f93bd3.woff2") format("woff2"),
                                url("src/assets/font/Platform-Medium/web-fonts/f58919c8d17a7e8e170c3ba632f93bd3.ttf") format("truetype"),
                                url("src/assets/font/Platform-Medium/web-fonts/f58919c8d17a7e8e170c3ba632f93bd3.svg#Platform-Medium") format("svg");
                            font-weight: 500;
                            font-style: normal;
                            font-display: swap;
                        }

                        @keyframes gradient-shift {
                            0%, 100% { background-position: 0% 50%; }
                            50% { background-position: 100% 50%; }
                        }

                        @keyframes fadeInUp {
                            0% {
                                opacity: 0;
                                transform: translateY(30px);
                            }
                            100% {
                                opacity: 1;
                                transform: translateY(0);
                            }
                        }

                        .animate-fadeInUp {
                            animation: fadeInUp 0.8s ease-out forwards;
                            opacity: 0;
                        }

                        .bg-gradient-radial {
                            background: radial-gradient(circle, var(--tw-gradient-stops));
                        }

                        .banner-video {
                            object-position: left center;
                            object-fit: cover;
                        }

                        @media (min-width: 1536px) {
                            .banner-video {
                                width: 120%;
                                transform-origin: left center;
                                object-position: left center;
                            }
                        }

                        @media (min-width: 1920px) {
                            .banner-video {
                                width: 140%;
                                transform-origin: left center;
                                object-position: left center;
                            }
                        }

                        @media (min-width: 2560px) {
                            .banner-video {
                                width: 160%;
                                transform-origin: left center;
                                object-position: left center;
                            }
                        }
                    `
                    }} />
                </div>
            </div>
    );
};

export default Banner;