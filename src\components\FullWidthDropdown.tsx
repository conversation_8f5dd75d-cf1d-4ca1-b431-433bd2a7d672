import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, LucideIcon } from 'lucide-react';

export interface DropdownCard {
  name: string;
  path?: string;
  href?: string;
  description: string;
  icon: LucideIcon | React.ComponentType<any>;
  // bgColor and textColor are no longer used for light dropdowns
}

interface FullWidthDropdownProps {
  cards: DropdownCard[];
  isActive: boolean;
  activeDropdown: string | null;
  onClose: () => void;
  filterFunction?: (card: DropdownCard) => boolean;
}

export default function FullWidthDropdown({
  cards,
  isActive,
  activeDropdown,
  onClose,
  filterFunction
}: FullWidthDropdownProps) {
  const filteredCards = filterFunction ? cards.filter(filterFunction) : cards;

  // Split into first row (up to 4), second row (rest)
  const firstRow = filteredCards.slice(0, 4);
  const secondRow = filteredCards.slice(4, 8); // up to 4 in second row

  const CARD_MIN_HEIGHT = 'h-[120px] ';

  const renderCard = (card: DropdownCard, idx: number, keyPrefix = '') => {
    const IconComponent = card.icon;
    // Use #C42E0E for hover on icon, arrow, and title
    const cardClass = `rounded-lg p-4 transition-all duration-200 group/card w-full flex flex-col justify-between relative overflow-hidden ${CARD_MIN_HEIGHT} bg-white hover:bg-gray-100`;
    const iconClass = 'w-5 h-5 text-black transition-colors duration-200 group-hover/card:text-[#C42E0E]';
    const textClass = 'text-base font-extrabold text-black transition-colors duration-200 group-hover/card:text-[#C42E0E]';
    const descClass = 'text-sm text-black opacity-90 leading-relaxed';
    const arrowClass = 'h-4 w-4 text-gray-800 transition-colors duration-200 group-hover/card:text-[#C42E0E]';
    if (card.href) {
      return (
        <a
          key={keyPrefix + (card.href || idx)}
          href={card.href}
          className={cardClass}
          onClick={onClose}
        >
          <div className="absolute top-0 left-0 w-full h-full pointer-events-none"></div>
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className="w-5 h-5">
                {typeof IconComponent === 'function' && IconComponent.name === 'CustomIcon' ? (
                  <IconComponent />
                ) : (
                  <IconComponent className={iconClass} />
                )}
              </div>
              <h3 className={textClass}>{card.name}</h3>
            </div>
            <div className="bg-gray-100 rounded-md p-2 group-hover/card:bg-[#F7E3DF] group-hover/card:translate-x-1 transition-all duration-200 flex-shrink-0">
              <ArrowRight className={arrowClass} />
            </div>
          </div>
          <p className={descClass}>{card.description}</p>
        </a>
      );
    }
    return (
      <Link
        key={keyPrefix + (card.path || idx)}
        to={card.path || '/'}
        className={cardClass}
        onClick={onClose}
      >
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none"></div>
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5">
              {typeof IconComponent === 'function' && IconComponent.name === 'CustomIcon' ? (
                <IconComponent />
              ) : (
                <IconComponent className={iconClass} />
              )}
            </div>
            <h3 className={textClass}>{card.name}</h3>
          </div>
          <div className="bg-gray-100 rounded-md p-2 group-hover/card:bg-[#F7E3DF] group-hover/card:translate-x-1 transition-all duration-200 flex-shrink-0">
            <ArrowRight className={arrowClass} />
          </div>
        </div>
        <p className={descClass}>{card.description}</p>
      </Link>
    );
  };

  return (
    <div
      className={`fixed left-0 px-0 bg-white rounded-lg w-[99%] mx-auto right-0 bg-transparent shadow-xl transition-all duration-200 ${isActive ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible transform -translate-y-2'} ${activeDropdown === null ? 'group-hover:opacity-100 group-hover:visible group-hover:translate-y-0' : ''}`}
      style={{
        top: '7.2rem',
        zIndex: 50
      }}
    >
      <div className="max-w-[1900px] w-full rounded-lg mx-auto px-2 py-2">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
          {firstRow.map((card, idx) => renderCard(card, idx, 'row1-'))}
        </div>
        {secondRow.length > 0 && (
          <div className="flex gap-2 mt-2">
            {secondRow.map((card, idx) => (
              <div className="flex-1 min-w-0" key={card.path || card.href || idx}>
                {renderCard(card, idx, 'row2-')}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
