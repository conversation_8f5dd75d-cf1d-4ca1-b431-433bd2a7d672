import React, { useState, useEffect } from 'react';
import { Check, ShoppingCart } from 'lucide-react';
import { useCartStore } from '../../store/cartStore';
import { useAuthStore } from '../../store/authStore';
import { Link } from 'react-router-dom';
import { supabase } from '../../lib/supabase';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  pages?: string[];
  specifications: string[];
  theme_color?: string;
  monthly_discount?: number;
  yearly_discount?: number;
  created_at: string;
  updated_at?: string;
}

const PricingSection: React.FC = () => {
  const { addItem } = useCartStore();
  const user = useAuthStore((state) => state.user);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');
  const [expandedDescriptions, setExpandedDescriptions] = useState<{ [key: string]: boolean }>({});
  const [addedProducts, setAddedProducts] = useState<{ [key: string]: boolean }>({});
  const [maxYearlyDiscount, setMaxYearlyDiscount] = useState<number>(0);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      console.log('Fetching products for ELENOR page...');

      const { data: allProducts, error } = await supabase
        .from('products')
        .select('*')
        .order('price', { ascending: true });

      if (error) {
        console.error('Error fetching products:', error);
        setProducts([]);
        return;
      }

      console.log('All products from database:', allProducts);

      // Filter products client-side for ELENOR page
      const elenorProducts = allProducts.filter((product) => {
        // Check if pages exists and is an array
        if (Array.isArray(product.pages)) {
          return product.pages.includes('Elenor');
        }
        // If pages is a string (JSON string), try to parse it
        if (typeof product.pages === 'string') {
          try {
            const pagesArray = JSON.parse(product.pages);
            return Array.isArray(pagesArray) && pagesArray.includes('Elenor');
          } catch (e) {
            console.error('Error parsing pages JSON:', e);
            return false;
          }
        }
        return false;
      });

      console.log('Filtered ELENOR products:', elenorProducts);

      // If we have products for ELENOR page
      if (elenorProducts && elenorProducts.length > 0) {
        // Sort by price to ensure consistent order
        const sortedProducts = [...elenorProducts].sort((a, b) => (a.price || 0) - (b.price || 0));

        // Find the maximum yearly discount among all products
        const highestDiscount = sortedProducts.reduce((max, product) => {
          const discount = product.yearly_discount || 0;
          return discount > max ? discount : max;
        }, 0);

        setMaxYearlyDiscount(highestDiscount);
        setProducts(sortedProducts);
      } else {
        // If there are no products for ELENOR, set empty array
        console.log('No products found for ELENOR page');
        setProducts([]);
      }
    } catch (error) {
      console.error('Error in fetchProducts:', error);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (product: Product) => {
    const cartItem = {
      id: product.id,
      name: product.name,
      price: billingInterval === 'yearly'
        ? product.yearly_discount
          ? ((product.price * 12) * (1 - product.yearly_discount / 100))
          : (product.price * 12)
        : product.monthly_discount
          ? (product.price * (1 - product.monthly_discount / 100))
          : product.price,
      image: product.image_url,
      quantity: 1,
      billingInterval,
      originalPrice: billingInterval === 'yearly' ? product.price * 12 : product.price,
      discount: billingInterval === 'yearly' ? product.yearly_discount : product.monthly_discount
    };

    addItem(cartItem);
    setAddedProducts(prev => ({ ...prev, [product.id]: true }));

    // Reset the "added" state after 2 seconds
    setTimeout(() => {
      setAddedProducts(prev => ({ ...prev, [product.id]: false }));
    }, 2000);
  };

  const toggleDescription = (productId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  if (loading) {
    return (
      <section className="py-8 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading pricing...</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-8 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">What are you saving for?</h2>
          <p className="text-xl text-gray-600 mb-8">Choose the perfect plan for your organization</p>

          <div className="bg-gray-100 p-1 rounded-lg inline-flex mb-8 shadow-sm">
            <button
              onClick={() => setBillingInterval('monthly')}
              className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'monthly'
                ? 'bg-black text-white shadow-sm'
                : 'text-gray-700 hover:bg-gray-200'
                }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingInterval('yearly')}
              className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'yearly'
                ? 'bg-black text-white shadow-sm'
                : 'text-gray-700 hover:bg-gray-200'
                }`}
            >
              Yearly
              {maxYearlyDiscount > 0 && (
                <span className="ml-2 text-sm bg-blue-500 text-white px-2 py-1 rounded-full">
                  Save up to {maxYearlyDiscount}%
                </span>
              )}
            </button>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {products.length > 0 ? (
            // If we have products from the database, display them directly
            products.map((product) => (
              <div key={product.id} className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow flex flex-col h-full">
                {/* Price Section */}
                <div className="mb-6">
                  <div className="flex items-baseline mb-1">
                    <span className="text-3xl font-bold text-gray-900">
                      ${billingInterval === 'yearly'
                        ? product.yearly_discount
                          ? ((product.price * 12) * (1 - product.yearly_discount / 100)).toFixed(2)
                          : (product.price * 12).toFixed(2)
                        : product.monthly_discount
                          ? (product.price * (1 - product.monthly_discount / 100)).toFixed(2)
                          : product.price.toFixed(2)
                      }
                    </span>
                    <span className="text-gray-500 ml-1">
                      /{billingInterval === 'yearly' ? 'year' : 'month'}
                    </span>
                  </div>
                  {(() => {
                    const hasDiscount = (billingInterval === 'yearly' && product.yearly_discount && product.yearly_discount > 0) ||
                      (billingInterval === 'monthly' && product.monthly_discount && product.monthly_discount > 0);

                    if (!hasDiscount) return null;

                    return (
                      <div className="text-sm text-gray-500">
                        <span className="line-through mr-2">
                          ${billingInterval === 'yearly'
                            ? (product.price * 12).toFixed(2)
                            : product.price.toFixed(2)
                          }
                        </span>
                        <span className="text-blue-600 font-medium">
                          Save {billingInterval === 'yearly' ? product.yearly_discount : product.monthly_discount}%
                        </span>
                      </div>
                    );
                  })()}
                </div>

                {/* Product Name */}
                <h3 className="text-xl font-bold mb-3">{product.name}</h3>

                {/* Product Description */}
                <div className="mb-6 flex-grow">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {expandedDescriptions[product.id] || product.description.length <= 100
                      ? product.description
                      : `${product.description.substring(0, 100)}...`}
                  </p>
                  {product.description.length > 100 && (
                    <button
                      onClick={() => toggleDescription(product.id)}
                      className="text-blue-600 text-sm mt-2 hover:underline"
                    >
                      {expandedDescriptions[product.id] ? 'Show Less' : 'Read More'}
                    </button>
                  )}
                </div>

                {/* Specifications */}
                {product.specifications && product.specifications.length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-semibold mb-2 text-sm">Key Features:</h4>
                    <ul className="space-y-1">
                      {product.specifications.slice(0, 5).map((spec, index) => (
                        <li key={index} className="flex items-start text-sm text-gray-600">
                          <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span>{spec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Add to Cart Button */}
                <div className="mt-auto">
                  {user ? (
                    <button
                      onClick={() => handleAddToCart(product)}
                      className={`w-full ${addedProducts[product.id]
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-white hover:bg-gray-50 text-black border border-gray-400'
                        } py-3 px-6 rounded-lg font-bold transition duration-300 text-sm`}
                      disabled={addedProducts[product.id]}
                    >
                      {addedProducts[product.id] ? (
                        <>
                          <Check className="inline-block h-4 w-4 mr-2" />
                          Added to Cart
                        </>
                      ) : (
                        'Add to Cart'
                      )}
                    </button>
                  ) : (
                    <Link
                      to="/signup"
                      className="block w-full text-center bg-white hover:bg-gray-50 text-black border border-gray-400 py-3 px-6 rounded-lg font-bold transition duration-300 text-sm"
                    >
                      Sign Up to Subscribe
                    </Link>
                  )}
                </div>
              </div>
            ))
          ) : (
            // If no products found, display a message
            <div className="col-span-3 text-center py-12">
              <div className="bg-gray-50 rounded-lg p-8 max-w-md mx-auto">
                <h3 className="text-xl font-bold mb-4 text-gray-700">No Products Available</h3>
                <p className="text-gray-600 mb-6">
                  There are currently no Elenor products available. Please check back later or contact our sales team for more information.
                </p>
                <Link
                  to="/contact"
                  className="inline-block bg-white text-black border border-gray-400 py-2 px-6 rounded-lg font-bold hover:bg-gray-50 transition duration-300"
                >
                  Contact Sales
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
