-- Create consulting_services table
CREATE TABLE IF NOT EXISTS consulting_services (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text NOT NULL,
  image_url text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE consulting_services ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read consulting services"
  ON consulting_services
  FOR SELECT
  USING (true);

CREATE POLICY "<PERSON><PERSON> can manage consulting services"
  ON consulting_services
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  ));

-- Create indexes for better performance
CREATE INDEX consulting_services_created_at_idx ON consulting_services(created_at);

-- Insert some sample data
INSERT INTO consulting_services (title, description, image_url) VALUES
  ('Technology Consulting', 'Empowers strategic and investment banking advisors by combining advanced AI with human insights, delivering tailored analysis and predictive intelligence to enhance competitiveness and client outcomes.', null),
  ('Policy Advisory and Implementation', 'Comprehensive policy advisory services to help organizations navigate complex regulatory environments and implement effective strategies.', null),
  ('Strategic Planning', 'Strategic planning services to help organizations define their vision, mission, and strategic objectives for long-term success.', null);
