import { createClient } from '@supabase/supabase-js';
 
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
 
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}
 
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    storageKey: 'supabase.auth.token',
    storage: window.localStorage
  },
  global: {
    headers: {
      'x-application-name': 'irs-website'
    }
  }
});
 
// Add auth state change listener
supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'SIGNED_OUT') {
    // Clear any user-specific data from localStorage
    localStorage.removeItem('user-data');
  } else if (event === 'SIGNED_IN') {
    // You can handle sign-in events here
    console.log('User signed in:', session?.user.email);
  }
});
 
// Enhanced connection check
export const checkConnection = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    const { data, error } = await supabase.from('profiles').select('count');
   
    if (error) throw error;
    return { connected: true, authenticated: !!user };
  } catch (err) {
    console.error('Supabase connection error:', err);
    return { connected: false, authenticated: false };
  }
};
 
// OAuth providers configuration with additional security
export const oAuthProviders = {
  github: {
    provider: 'github',
    options: {
      redirectTo: `${window.location.origin}/profile`,
      scopes: 'read:user user:email',
      skipBrowserRedirect: false
    }
  },
  google: {
    provider: 'google',
    options: {
      redirectTo: `${window.location.origin}/profile`,
      scopes: 'email profile',
      skipBrowserRedirect: false
    }
  },
  facebook: {
    provider: 'facebook',
    options: {
      redirectTo: `${window.location.origin}/profile`,
      scopes: 'email,public_profile',
      skipBrowserRedirect: false
    }
  }
};
 
// Add authentication helper functions
export const auth = {
  signUp: async (email: string, password: string, metadata = {}) => {
    return supabase.auth.signUp({
      email,
      password,
      options: { data: metadata }
    });
  },
 
  signIn: async (email: string, password: string) => {
    return supabase.auth.signInWithPassword({ email, password });
  },
 
  signOut: async () => {
    return supabase.auth.signOut();
  },
 
  resetPassword: async (email: string) => {
    return supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/update-password`
    });
  },
 
  updatePassword: async (newPassword: string) => {
    return supabase.auth.updateUser({ password: newPassword });
  }
};