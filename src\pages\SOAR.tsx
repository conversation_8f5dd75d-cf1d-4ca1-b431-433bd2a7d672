import DataModeling from '../components/soar/DataModeling';
import CooperativePartnerships from '../components/soar/CooperativePartnerships';
import PricingSection from '../components/soar/PricingSection';
import { Colors } from '../constants/Colors';
import ClientTestimonial from '../components/grantready/ClientTestemonial';
import ActSection from '../components/soar/ActSection';
import TrustedBy from '../components/grantready/TrustedBy';
import Banner from '../components/soar/Banner';
import OurAproches from '../components/soar/OurAproches';
import EmergencyManagementBanner from '../components/elenor/EmergencyManagementBanner';
import soarCtaVideoPink from '../assets/video/elenor-cta-bg-pink.mov';
import SoarStatistics from '../components/soar/SoarStatistics';
import ChangedText from "../components/ui/ChangedText.tsx";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  pages?: string[];
  specifications: string[];
  theme_color?: string;
  monthly_discount?: number;
  yearly_discount?: number;
  created_at: string;
  updated_at?: string;
}

export default function SOAR() {
  const metabaseDashboardUrl = "https://metabase.chriszhu.me/public/dashboard/************************************?all_options=&location=";



  return (
    <div className="px-0">
      <Banner />
      <SoarStatistics />
      <TrustedBy
        title="Trusted by Health Departments Nationwide"
        description="Leading organizations rely on SOAR to save lives and stop outbreaks using advanced forecasting, innovative partnerships, and real-time analytics."
      />


      <DataModeling />

      <CooperativePartnerships />
      <OurAproches />
      <ClientTestimonial
        color={Colors.SOAR}
        title="See how clients are transforming outbreak response with SOAR."
        description="Organizations nationwide are modernizing public health and outbreak management with SOAR."
      />

      {/* Pricing Section */}
      <PricingSection />
    </div>
  );
}