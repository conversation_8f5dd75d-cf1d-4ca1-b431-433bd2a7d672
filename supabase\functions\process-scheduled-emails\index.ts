import { serve } from 'https://deno.land/std/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js';
import nodemailer from 'npm:nodemailer';
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': '*',
  'Access-Control-Allow-Headers': '*',
  'Content-Type': 'application/json'
};
const supabase = createClient(Deno.env.get('SUPABASE_URL'), Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'), {
  global: {
    headers: {
      Authorization: `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
    }
  }
});
const transporter = nodemailer.createTransport({
  host: 'smtp.office365.com',
  port: 587,
  secure: false,
  auth: {
    user: Deno.env.get('SMTP_USERNAME'),
    pass: Deno.env.get('SMTP_PASSWORD')
  }
});
serve(async (req)=>{
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers
    });
  }
  try {
    console.log('📨 Starting email cron job...');
    // 1. Get scheduled campaigns
    const { data: campaigns, error: campaignErr } = await supabase.from('email_campaigns').select('*').eq('status', 'scheduled').eq('sent', false).lte('scheduled_for', new Date().toISOString());
    if (campaignErr) {
      console.error('❌ Failed to fetch campaigns:', campaignErr);
      throw campaignErr;
    }
    if (!campaigns || campaigns.length === 0) {
      console.log('✅ No scheduled campaigns to process.');
      return new Response(JSON.stringify({
        message: 'No campaigns to send'
      }), {
        status: 200,
        headers
      });
    }
    console.log(`📦 Found ${campaigns.length} campaign(s) ready to send.`);
    // 2. Get active subscribers
    const { data: subscribers, error: subsErr } = await supabase.from('subscribers').select('id, email').eq('status', 'active');
    if (subsErr) {
      console.error('❌ Failed to fetch subscribers:', subsErr);
      throw subsErr;
    }
    if (!subscribers || subscribers.length === 0) {
      console.log('⚠️ No active subscribers found.');
      return new Response(JSON.stringify({
        message: 'No active subscribers'
      }), {
        status: 200,
        headers
      });
    }
    console.log(`👥 Found ${subscribers.length} active subscriber(s).`);
    // 3. Verify SMTP
    const smtpValid = await transporter.verify();
    console.log('📡 SMTP connection verified:', smtpValid);
    let totalSent = 0;
    for (const campaign of campaigns){
      console.log(`🚀 Sending campaign "${campaign.name}" (${campaign.subject})`);
      for (const subscriber of subscribers){
        try {
          const result = await transporter.sendMail({
            from: Deno.env.get('SMTP_USERNAME'),
            to: subscriber.email,
            subject: campaign.subject,
            html: campaign.content
          });
          console.log(`✅ Email sent to ${subscriber.email}: ${result.messageId}`);
          await supabase.from('subscribers').update({
            last_email_sent: new Date().toISOString()
          }).eq('id', subscriber.id);
          totalSent++;
        } catch (sendErr) {
          console.error(`❌ Failed to send to ${subscriber.email}:`, sendErr.message);
        }
      }
      // 4. Update campaign as sent
      await supabase.from('email_campaigns').update({
        sent: true,
        sent_at: new Date().toISOString(),
        recipients: subscribers.length,
        status: 'sent',
        updated_at: new Date().toISOString()
      }).eq('id', campaign.id);
      console.log(`📬 Campaign "${campaign.name}" marked as sent.`);
    }
    console.log(`✅ Done. Total emails sent: ${totalSent}`);
    return new Response(JSON.stringify({
      success: true,
      campaignsSent: campaigns.length,
      totalEmailsSent: totalSent
    }), {
      status: 200,
      headers
    });
  } catch (err) {
    console.error('🔥 ERROR in email cron job:', err.message);
    return new Response(JSON.stringify({
      error: 'Failed to send campaign emails',
      details: err.message
    }), {
      status: 500,
      headers
    });
  }
});
