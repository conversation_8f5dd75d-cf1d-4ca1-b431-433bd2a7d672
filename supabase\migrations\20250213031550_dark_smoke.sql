-- Add new admin user to standalone_admins
INSERT INTO standalone_admins (username, role)
VALUES (
  '<EMAIL>',
  'admin'
)
ON CONFLICT (username) 
DO UPDATE SET
  role = 'admin',
  last_login = NULL;

-- Function to check admin status
CREATE OR REPLACE FUNCTION is_admin(email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM standalone_admins
    WHERE username = email
    AND role = 'admin'
  );
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION is_admin TO authenticated;