import React, { useState, useEffect } from 'react';
import { Linkedin, Mail, Users } from 'lucide-react';
import { supabase } from '../lib/supabase';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

// Bio component with see more/less functionality
function BioContent({ bio }) {
  const [expanded, setExpanded] = useState(false);
  const maxLength = 150; // Character limit before showing "See More"
  const isLong = bio && bio.length > maxLength;

  if (!bio) return null;

  return (
    <div className="text-gray-600">
      <div className={expanded ? '' : 'line-clamp-3'}>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={{
            h1: ({ children }) => <h1 className="text-3xl font-bold mt-10 mb-6">{children}</h1>,
            h2: ({ children }) => (
              <h2 className="text-2xl font-bold mt-8 mb-4 border-b pb-2">{children}</h2>
            ),
            h3: ({ children }) => <h3 className="text-xl font-semibold mt-6 mb-3">{children}</h3>,
            p: ({ children }) => <p className="mb-6 text-gray-700 leading-relaxed">{children}</p>,
            ul: ({ children }) => <ul className="list-disc pl-8 mb-6 space-y-2">{children}</ul>,
            ol: ({ children }) => <ol className="list-decimal pl-8 mb-6 space-y-2">{children}</ol>,
            li: ({ children }) => <li className="mb-2">{children}</li>,
            blockquote: ({ children }) => (
              <blockquote className="border-l-4 border-blue-500 pl-4 italic bg-gray-50 py-2 mb-6 text-gray-600">
                {children}
              </blockquote>
            ),
            a: ({ children, href }) => (
              <a href={href} className="text-blue-600 hover:text-blue-800 underline">
                {children}
              </a>
            ),
            img: ({ src, alt }) => (
              <img src={src} alt={alt} className="my-6 rounded-lg shadow-md max-w-full h-auto" />
            ),
            table: ({ children }) => (
              <table className="w-full mb-6 border-collapse">{children}</table>
            ),
            th: ({ children }) => (
              <th className="py-2 px-4 border bg-gray-100 font-semibold text-left">{children}</th>
            ),
            td: ({ children }) => <td className="py-2 px-4 border">{children}</td>,
            code: ({ inline, className, children }) => {
              const match = /language-(\w+)/.exec(className || '');
              return !inline ? (
                <SyntaxHighlighter
                  style={atomDark}
                  language={match?.[1] || 'javascript'}
                  className="rounded-lg mb-6 text-sm"
                  showLineNumbers
                  wrapLines
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              ) : (
                <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                  {children}
                </code>
              );
            },
            u: ({ children }) => <u className="underline">{children}</u>,
          }}
        >
          {bio.replace(/\+\+([^\+]+)\+\+/g, '<u>$1</u>')}
        </ReactMarkdown>
      </div>
      {isLong && (
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-blue-600 text-sm mt-2 hover:underline"
        >
          {expanded ? 'See Less' : 'See More'}
        </button>
      )}
    </div>
  );
}

export default function Leadership() {
  const [teamMembers, setTeamMembers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const { data, error } = await supabase
          .from('teams')
          .select('*')
          .order('created_at', { ascending: true });

        if (error) throw error;
        setTeamMembers(data);
      } catch (err) {
        console.error('Error fetching team members:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  // if (loading) {
  //   return (
  //     <div className=" flex justify-center items-center h-screen">
  //       <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  //     </div>
  //   );
  // }

  return (
    <div className="">
      {/* Hero Section */}
      <section className="relative text-white py-24 rounded-lg" style={{ background: 'radial-gradient(50% 55% at 0% 0%, #06B6D4 0%, #72b0fb 100%)' }}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Large circles */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/5 rounded-full"></div>
          <div className="absolute top-20 -right-20 w-60 h-60 bg-white/3 rounded-full"></div>
          <div className="absolute -bottom-20 -right-32 w-80 h-80 bg-white/5 rounded-full"></div>

          {/* Small decorative dots */}
          <div className="absolute top-32 right-20 w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-32 w-3 h-3 bg-blue-200 rounded-full opacity-60 animate-pulse delay-75"></div>
          <div className="absolute bottom-32 right-24 w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-150"></div>
          <div className="absolute bottom-20 right-40 w-1 h-1 bg-blue-100 rounded-full animate-pulse delay-300"></div>

          {/* Geometric shapes */}
          <div className="absolute top-16 right-16 w-12 h-12 border border-white/20 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-24 right-64 w-8 h-8 border border-white/10 rotate-12 animate-bounce"></div>

          {/* Additional decorative lines */}
          <div className="absolute top-1/3 right-0 w-px h-20 bg-gradient-to-b from-transparent via-white/20 to-transparent"></div>
          <div className="absolute top-2/3 right-12 w-px h-16 bg-gradient-to-b from-transparent via-white/15 to-transparent"></div>
        </div>

        {/* Creative illustration on the right - Leadership themed */}
        <div className="absolute right-0 top-0 bottom-0 w-3/5 hidden lg:block overflow-hidden">
          <div className="relative h-full flex items-center justify-center">
            <div className="relative scale-150">
              {/* Floating leadership icons */}
              <div className="absolute -top-12 -right-6 w-20 h-24 bg-white/10 rounded-lg backdrop-blur-sm transform rotate-12 animate-float">
                <div className="p-3 flex items-center justify-center h-full">
                  <Linkedin className="h-8 w-8 text-white" />
                </div>
              </div>

              <div className="absolute -top-6 right-10 w-18 h-22 bg-white/15 rounded-lg backdrop-blur-sm transform -rotate-6 animate-float-delayed">
                <div className="p-2.5 flex items-center justify-center h-full">
                  <Mail className="h-7 w-7 text-white" />
                </div>
              </div>

              {/* Central leadership hub */}
              <div className="w-40 h-40 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center relative animate-pulse-slow">
                <div className="w-28 h-28 bg-white/30 rounded-full flex items-center justify-center">
                  <div className="w-16 h-16 bg-white/40 rounded-full flex items-center justify-center">
                    <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold text-xl">
                        <Users className="h-6 w-6" />
                      </span>
                    </div>
                  </div>
                </div>

                {/* Orbiting elements */}
                <div className="absolute inset-0 animate-spin-slow">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-300 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -right-3 transform -translate-y-1/2 w-4 h-4 bg-blue-200 rounded-full opacity-60"></div>
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-400 rounded-full opacity-80"></div>
                  <div className="absolute top-1/2 -left-3 transform -translate-y-1/2 w-4 h-4 bg-blue-100 rounded-full opacity-60"></div>
                </div>
              </div>

              {/* Floating team icons */}
              <div className="absolute -bottom-8 -left-8 w-16 h-16 bg-white/15 rounded-lg backdrop-blur-sm flex items-center justify-center transform -rotate-12 animate-bounce-slow">
                <div className="flex -space-x-1">
                  <div className="w-6 h-6 bg-blue-300 rounded-full border-2 border-white"></div>
                  <div className="w-6 h-6 bg-blue-400 rounded-full border-2 border-white"></div>
                  <div className="w-6 h-6 bg-blue-200 rounded-full border-2 border-white"></div>
                </div>
              </div>

              <div className="absolute bottom-3 right-6 w-14 h-14 bg-white/20 rounded-full backdrop-blur-sm flex items-center justify-center animate-float">
                <div className="w-8 h-8 bg-white/30 rounded-full"></div>
              </div>

              <div className="absolute top-10 -left-10 w-18 h-18 bg-white/10 rounded-xl backdrop-blur-sm flex items-center justify-center transform rotate-45 animate-pulse">
                <div className="w-8 h-8 bg-white/40 rounded transform -rotate-45"></div>
              </div>
            </div>

            {/* Network connection lines */}
            <div className="absolute inset-0 pointer-events-none scale-125">
              <svg className="w-full h-full opacity-20" viewBox="0 0 400 400">
                <defs>
                  <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="rgba(255,255,255,0)" />
                    <stop offset="50%" stopColor="rgba(255,255,255,0.3)" />
                    <stop offset="100%" stopColor="rgba(255,255,255,0)" />
                  </linearGradient>
                </defs>
                <path
                  d="M50,200 Q200,100 350,200 Q200,300 50,200"
                  fill="none"
                  stroke="url(#gradient1)"
                  strokeWidth="3"
                  className="animate-pulse"
                />
                <path
                  d="M100,150 Q200,50 300,150"
                  fill="none"
                  stroke="url(#gradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-500"
                />
                <path
                  d="M100,250 Q200,350 300,250"
                  fill="none"
                  stroke="url(#gradient1)"
                  strokeWidth="2"
                  className="animate-pulse delay-1000"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Leadership Team
              <span className="block text-3xl font-normal mt-2 text-blue-200">
                Visionary Leaders
              </span>
            </h1>
            <p className="text-xl mb-10 opacity-90 leading-relaxed max-w-2xl">
              Meet the visionaries driving innovation in public health emergency response
            </p>
          </div>
        </div>
      </section>

      {/* Add custom animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(12deg); }
          50% { transform: translateY(-10px) rotate(12deg); }
        }
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0px) rotate(-6deg); }
          50% { transform: translateY(-8px) rotate(-6deg); }
        }
        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        @keyframes bounce-slow {
          0%, 100% { transform: translateY(0px) rotate(-12deg); }
          50% { transform: translateY(-5px) rotate(-12deg); }
        }
        @keyframes pulse-slow {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }
        .animate-float { animation: float 3s ease-in-out infinite; }
        .animate-float-delayed { animation: float-delayed 3s ease-in-out infinite 1s; }
        .animate-spin-slow { animation: spin-slow 20s linear infinite; }
        .animate-bounce-slow { animation: bounce-slow 2s ease-in-out infinite; }
        .animate-pulse-slow { animation: pulse-slow 4s ease-in-out infinite; }
      `}</style>

      {/* Team Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member) => (
              <div key={member.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <img
                  src={member.image_url}
                  alt={member.name}
                  className="w-full h-64 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2">{member.name}</h3>
                  <p className="text-blue-600 font-medium mb-4">{member.role}</p>
                  <div className="mb-6">
                    <BioContent bio={member.bio} />
                  </div>
                  <div className="flex space-x-4">
                    {member.linkedin_url && (
                      <a
                        href={member.linkedin_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-gray-600 hover:text-blue-600"
                      >
                        <Linkedin className="h-5 w-5" />
                      </a>
                    )}
                    {member.email && (
                      <a
                        href={`mailto:${member.email}`}
                        className="p-2 text-gray-600 hover:text-blue-600"
                      >
                        <Mail className="h-5 w-5" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}