/*
  # Fix admin authentication

  1. Changes
    - Drop and recreate admin user with proper password hashing
    - Add proper RLS policies for admin authentication
    - Add function to verify admin status

  2. Security
    - Enable RLS on admin_users table
    - Add policies for admin access
*/

-- First clean up any existing admin data
DELETE FROM auth.users WHERE email = '<EMAIL>';
DELETE FROM admin_users WHERE email = '<EMAIL>';

-- Create the admin user in auth.users
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role
)
VALUES (
  '00000000-0000-0000-0000-000000000000'::uuid,
  '00000000-0000-0000-0000-000000000000'::uuid,
  '<EMAIL>',
  -- Using proper password hashing for '11111111'
  crypt('11111111', gen_salt('bf')),
  now(),
  now(),
  now(),
  '{"provider":"email","providers":["email"]}'::jsonb,
  '{"name":"Admin User"}'::jsonb,
  false,
  'authenticated'
);

-- Add admin user to admin_users table
INSERT INTO admin_users (
  id,
  email,
  role
)
VALUES (
  '00000000-0000-0000-0000-000000000000'::uuid,
  '<EMAIL>',
  'admin'
);

-- Ensure proper RLS policies
DROP POLICY IF EXISTS "Admin users can view own data" ON admin_users;
DROP POLICY IF EXISTS "Admin users can update own data" ON admin_users;

CREATE POLICY "Admin users can view own data"
  ON admin_users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Admin users can update own data"
  ON admin_users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Function to verify admin status
CREATE OR REPLACE FUNCTION verify_admin(user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM admin_users
    WHERE id = user_id
    AND role = 'admin'
  );
END;
$$;