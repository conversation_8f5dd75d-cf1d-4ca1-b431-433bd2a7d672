-- First ensure the logos bucket exists
INSERT INTO storage.buckets (id, name, public)
VALUES ('logos', 'logos', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for logos bucket
DROP POLICY IF EXISTS "Public can view logos" ON storage.objects;
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage logos" ON storage.objects;

CREATE POLICY "Public can view logos"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'logos');

CREATE POLICY "Admins can manage logos"
ON storage.objects FOR ALL 
TO authenticated
USING (
  bucket_id = 'logos' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);

-- Create site_settings if not exists
CREATE TABLE IF NOT EXISTS site_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  settings jsonb NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Update site settings with logo URL
INSERT INTO site_settings (settings)
VALUES (jsonb_build_object(
  'logo', 'https://ycurijjvdnkshadnazud.supabase.co/storage/v1/object/public/logos/irs-logo.png'
))
ON CONFLICT (id) DO UPDATE
SET settings = jsonb_set(
  site_settings.settings,
  '{logo}',
  '"https://ycurijjvdnkshadnazud.supabase.co/storage/v1/object/public/logos/irs-logo.png"'
);

-- Grant necessary permissions
GRANT ALL ON SCHEMA storage TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA storage TO postgres, anon, authenticated, service_role;