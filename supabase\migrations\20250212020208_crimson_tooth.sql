/*
  # Admin User Setup Migration

  1. Tables
    - Create admin_users table with proper structure
    - Set up RLS policies
    - Add admin verification functions

  2. Security
    - Enable RLS
    - Set up secure policies
*/

-- Create admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
  id uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  role text NOT NULL CHECK (role IN ('admin', 'editor')),
  created_at timestamptz DEFAULT now(),
  last_login timestamptz
);

-- Enable RLS
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Function to verify admin status
CREATE OR REPLACE FUNCTION verify_admin(user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM admin_users
    WHERE id = user_id
    AND role = 'admin'
  );
END;
$$;

-- Function to check admin credentials
CREATE OR REPLACE FUNCTION check_admin_credentials(email text, password text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM auth.users u
    JOIN admin_users a ON u.id = a.id
    WHERE u.email = check_admin_credentials.email
    AND u.encrypted_password = crypt(check_admin_credentials.password, u.encrypted_password)
    AND a.role = 'admin'
  );
END;
$$;

-- Clean up any existing admin data
DO $$ 
DECLARE
  new_admin_id uuid;
BEGIN
  -- Delete existing admin records if they exist
  DELETE FROM admin_users WHERE email = '<EMAIL>';
  DELETE FROM auth.users WHERE email = '<EMAIL>';

  -- Create new admin user
  INSERT INTO auth.users (
    id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    role,
    aud,
    confirmation_token
  )
  VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    crypt('11111111', gen_salt('bf')),
    now(),
    now(),
    now(),
    '{"provider":"email","providers":["email"]}'::jsonb,
    '{"name":"Admin User"}'::jsonb,
    false,
    'authenticated',
    'authenticated',
    'confirmed'
  )
  RETURNING id INTO new_admin_id;

  -- Add admin user to admin_users table
  INSERT INTO admin_users (
    id,
    email,
    role,
    created_at
  )
  VALUES (
    new_admin_id,
    '<EMAIL>',
    'admin',
    now()
  );
END $$;