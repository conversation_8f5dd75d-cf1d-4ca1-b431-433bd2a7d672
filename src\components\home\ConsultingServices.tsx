import React, { useState, useEffect } from "react";
import { supabase } from "../../lib/supabase";

interface ConsultingService {
  id: string;
  title: string;
  description: string;
  image_url: string | null;
  created_at: string;
  updated_at: string;
}

// Icon components for each service
const TechnologyIcon = () => (
  <svg
    width="36"
    height="37"
    viewBox="0 0 36 37"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M11.1092 3.13306H13.3311V7.57682H15.553V3.13306H17.7748V7.57682H19.9967V3.13306H22.2186V7.57682H24.4405L26.6624 9.7987V12.0206H31.1061V14.2425H26.6624V16.4643H31.1061V18.6862H26.6624V20.9081H31.1061V23.13H26.6624V25.3519L24.4405 27.5737H22.2186V32.0175H19.9967V27.5737H17.7748V32.0175H15.553V27.5737H13.3311V32.0175H11.1092V27.5737H8.88732L6.66544 25.3519V23.13H2.22168V20.9081H6.66544V18.6862H2.22168V16.4643H6.66544V14.2425H2.22168V12.0206H6.66544V9.7987L8.88732 7.57682H11.1092V3.13306ZM8.88732 25.3519H24.4405V9.7987H8.88732V25.3519Z"
      fill="url(#paint0_linear_1310_22481)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_1310_22481"
        x1="16.6639"
        y1="3.13306"
        x2="16.6639"
        y2="32.0175"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#4F63D9" />
        <stop offset="1" stop-color="#2A3473" />
      </linearGradient>
    </defs>
  </svg>
);

const PolicyIcon = () => (
  <svg
    width="31"
    height="33"
    viewBox="0 0 31 33"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24.2278 10.805L24.9127 10.1186C25.4581 9.57307 26.1979 9.2665 26.9693 9.26636C27.7406 9.26622 28.4805 9.57252 29.0261 10.1179C29.5716 10.6632 29.8782 11.403 29.8783 12.1744C29.8785 12.9458 29.5722 13.6857 29.0268 14.2312L28.3419 14.9176M24.2278 10.805C24.2278 10.805 24.3136 12.2607 25.5992 13.5463C26.8847 14.8318 28.3419 14.9176 28.3419 14.9176M24.2278 10.805L17.9258 17.1071C17.4968 17.5331 17.2837 17.7476 17.1003 17.9828C16.8843 18.261 16.6994 18.5598 16.547 18.8793C16.4183 19.1486 16.3236 19.4341 16.1328 20.0066L15.5218 21.841L15.3236 22.4342M28.3419 14.9176L22.0398 21.2197C21.6108 21.6487 21.3978 21.8617 21.1626 22.0451C20.8845 22.2611 20.5856 22.446 20.2661 22.5984C19.9969 22.7271 19.7114 22.8218 19.1388 23.0126L17.3045 23.6236L16.7112 23.8218M15.3236 22.4342L15.1269 23.0289C15.081 23.1669 15.0745 23.315 15.108 23.4565C15.1415 23.5981 15.2137 23.7275 15.3166 23.8304C15.4194 23.9332 15.5488 24.0054 15.6904 24.0389C15.8319 24.0724 15.98 24.0659 16.118 24.0201L16.7112 23.8218M15.3236 22.4342L16.7112 23.8218"
      stroke="url(#paint0_linear_1310_22486)"
      stroke-width="2.21902"
    />
    <path
      d="M9.16797 18.1427H12.8663M9.16797 12.2253H18.7837M9.16797 24.0601H11.387"
      stroke="url(#paint1_linear_1310_22486)"
      stroke-width="2.21902"
      stroke-linecap="round"
    />
    <path
      opacity="0.5"
      d="M1.77148 13.7047C1.77148 8.12604 1.77148 5.33599 3.50528 3.60367C5.2376 1.86987 8.02765 1.86987 13.6063 1.86987H16.565C22.1436 1.86987 24.9336 1.86987 26.666 3.60367C28.3998 5.33599 28.3998 8.12604 28.3998 13.7047V19.6221C28.3998 25.2007 28.3998 27.9907 26.666 29.723C24.9336 31.4568 22.1436 31.4568 16.565 31.4568H13.6063C8.02765 31.4568 5.2376 31.4568 3.50528 29.723C1.77148 27.9907 1.77148 25.2007 1.77148 19.6221V13.7047Z"
      stroke="url(#paint2_linear_1310_22486)"
      stroke-width="2.21902"
    />
    <defs>
      <linearGradient
        id="paint0_linear_1310_22486"
        x1="22.4826"
        y1="9.26636"
        x2="22.4826"
        y2="24.06"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#4F63D9" />
        <stop offset="1" stop-color="#2A3473" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_1310_22486"
        x1="13.9759"
        y1="12.2253"
        x2="13.9759"
        y2="24.0601"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#4F63D9" />
        <stop offset="1" stop-color="#2A3473" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_1310_22486"
        x1="15.0856"
        y1="1.86987"
        x2="15.0856"
        y2="31.4568"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#4F63D9" />
        <stop offset="1" stop-color="#2A3473" />
      </linearGradient>
    </defs>
  </svg>
);

const ProgramIcon = () => (
  <svg
    width="28"
    height="31"
    viewBox="0 0 33 31"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M31.0555 19.2581H1.61133M31.0555 19.2581V28.8274H1.61133V19.2581M31.0555 19.2581L27.3897 1.59082H5.31982L1.61133 19.2581"
      stroke="url(#paint0_linear_1310_22492)"
      stroke-width="2.94442"
      stroke-linejoin="round"
    />
    <path
      d="M12.6532 6.74438C11.677 6.74438 10.7409 7.13215 10.0507 7.82238C9.36042 8.51262 8.97266 9.44877 8.97266 10.4249C8.97266 11.401 9.36042 12.3372 10.0507 13.0274C10.7409 13.7177 11.677 14.1054 12.6532 14.1054M20.0142 14.1054C20.9904 14.1054 21.9265 13.7177 22.6167 13.0274C23.307 12.3372 23.6947 11.401 23.6947 10.4249C23.6947 9.44877 23.307 8.51262 22.6167 7.82238C21.9265 7.13215 20.9904 6.74438 20.0142 6.74438M13.3893 10.4249H19.2781"
      stroke="url(#paint1_linear_1310_22492)"
      stroke-width="2.94442"
      stroke-linecap="round"
    />
    <defs>
      <linearGradient
        id="paint0_linear_1310_22492"
        x1="16.3334"
        y1="1.59082"
        x2="16.3334"
        y2="28.8274"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#4F63D9" />
        <stop offset="1" stop-color="#2A3473" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_1310_22492"
        x1="16.3337"
        y1="6.74438"
        x2="16.3337"
        y2="14.1054"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#4F63D9" />
        <stop offset="1" stop-color="#2A3473" />
      </linearGradient>
    </defs>
  </svg>
);

const DisasterIcon = () => (
  <svg
    width="32"
    height="36"
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.7522 0.415527C7.98848 0.415527 0 8.40401 0 18.1677C0 27.9314 7.98848 35.9199 17.7522 35.9199C27.5159 35.9199 35.5044 27.9314 35.5044 18.1677C35.5044 8.40401 27.5159 0.415527 17.7522 0.415527ZM8.87609 18.1677C8.87609 13.2859 12.8703 9.29162 17.7522 9.29162C22.634 9.29162 26.6283 13.2859 26.6283 18.1677C26.6283 23.0496 22.634 27.0438 17.7522 27.0438C12.8703 27.0438 8.87609 23.0496 8.87609 18.1677ZM27.9597 22.1619C28.6254 21.0524 28.8473 19.4991 28.8473 18.1677C28.8473 16.8363 28.6254 15.283 27.9597 14.1735L31.2882 10.8449C32.6196 13.064 33.2853 15.5049 33.2853 18.1677C33.2853 20.8305 32.6196 23.2715 31.5101 25.4905L27.9597 22.1619ZM25.075 4.40977L21.7464 7.9602C20.6369 7.2945 19.0836 7.07259 17.7522 7.07259C16.4208 7.07259 14.8675 7.2945 13.7579 7.9602L10.4294 4.40977C12.6484 3.30026 15.0894 2.63455 17.7522 2.63455C20.415 2.63455 22.8559 3.30026 25.075 4.40977ZM3.99424 10.8449L7.32277 14.1735C6.87897 15.283 6.65707 16.8363 6.65707 18.1677C6.65707 19.4991 6.87897 21.0524 7.54468 22.1619L4.21614 25.4905C2.88473 23.2715 2.21902 20.8305 2.21902 18.1677C2.21902 15.5049 2.88473 13.064 3.99424 10.8449ZM10.4294 31.9256L13.7579 28.5971C14.8675 29.0409 16.4208 29.2628 17.7522 29.2628C19.0836 29.2628 20.6369 29.0409 21.7464 28.3752L25.075 31.7037C22.8559 33.0352 20.415 33.7009 17.7522 33.7009C15.0894 33.7009 12.6484 33.0352 10.4294 31.9256Z"
      fill="url(#paint0_linear_1310_22500)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_1310_22500"
        x1="17.7522"
        y1="0.415527"
        x2="17.7522"
        y2="35.9199"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#4F63D9" />
        <stop offset="1" stop-color="#2A3473" />
      </linearGradient>
    </defs>
  </svg>
);

const GrantsIcon = () => (
  <svg
    width="32"
    height="31"
    viewBox="0 0 32 31"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.29199 29.2216H30.879M24.9616 20.3455H24.9749M24.9616 24.7836H24.9749M7.20938 24.7836H7.2227M7.20938 20.3455H7.2227M7.20938 15.9075H7.2227M7.20938 11.4695H7.2227M9.85742 5.39229L22.3254 11.6026M10.1681 4.07271C10.1681 4.85741 9.85636 5.60996 9.3015 6.16482C8.74664 6.71969 7.99408 7.03141 7.20938 7.03141C6.42469 7.03141 5.67213 6.71969 5.11727 6.16482C4.56241 5.60996 4.25069 4.85741 4.25069 4.07271C4.25069 3.28802 4.56241 2.53546 5.11727 1.9806C5.67213 1.42573 6.42469 1.11401 7.20938 1.11401C7.99408 1.11401 8.74664 1.42573 9.3015 1.9806C9.85636 2.53546 10.1681 3.28802 10.1681 4.07271ZM27.9203 12.9488C27.9203 13.7335 27.6085 14.4861 27.0537 15.0409C26.4988 15.5958 25.7463 15.9075 24.9616 15.9075C24.1769 15.9075 23.4243 15.5958 22.8694 15.0409C22.3146 14.4861 22.0029 13.7335 22.0029 12.9488C22.0029 12.1641 22.3146 11.4115 22.8694 10.8567C23.4243 10.3018 24.1769 9.9901 24.9616 9.9901C25.7463 9.9901 26.4988 10.3018 27.0537 10.8567C27.6085 11.4115 27.9203 12.1641 27.9203 12.9488Z"
      stroke="url(#paint0_linear_1310_22503)"
      stroke-width="2.21902"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <defs>
      <linearGradient
        id="paint0_linear_1310_22503"
        x1="16.0855"
        y1="1.11401"
        x2="16.0855"
        y2="29.2216"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#4F63D9" />
        <stop offset="1" stop-color="#2A3473" />
      </linearGradient>
    </defs>
  </svg>
);

const TrainingIcon = () => (
  <svg
    width="30"
    height="33"
    viewBox="0 0 36 33"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.02848 9.19314C10.384 9.19314 12.4451 9.78203 14.2118 10.3709M8.02848 15.082C10.384 15.082 12.4451 15.6709 14.2118 16.2597M8.02848 20.9708C10.384 20.9708 12.4451 21.5597 14.2118 22.1486M28.9338 9.19314C26.5783 9.19314 24.5172 9.78203 22.7506 10.3709M28.9338 15.082C26.5783 15.082 24.5172 15.6709 22.7506 16.2597M28.9338 20.9708C26.5783 20.9708 24.5172 21.5597 22.7506 22.1486M18.9228 4.18763C20.9839 3.30431 26.5783 0.948776 33.3505 2.12654C33.9394 2.12654 34.5282 3.00987 34.5282 3.59875V27.4485C34.5282 28.3319 33.6449 28.9207 32.7616 28.9207C26.2839 27.743 20.6895 30.0985 18.9228 30.9818C18.6284 31.2763 18.0395 31.2763 17.7451 30.9818C15.9784 30.0985 10.384 27.743 3.9063 28.9207C3.02297 28.9207 2.13965 28.3319 2.13965 27.4485V3.59875C2.13965 3.00987 2.72853 2.42098 3.31742 2.12654C10.0896 0.948776 15.684 3.30431 17.7451 4.18763C18.0395 4.48208 18.6284 4.48208 18.9228 4.18763Z"
      stroke="url(#paint0_linear_1310_22506)"
      stroke-width="2.94442"
      stroke-linecap="round"
    />
    <defs>
      <linearGradient
        id="paint0_linear_1310_22506"
        x1="18.3339"
        y1="1.80347"
        x2="18.3339"
        y2="31.2027"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#4F63D9" />
        <stop offset="1" stop-color="#2A3473" />
      </linearGradient>
    </defs>
  </svg>
);

const ConsultingServices: React.FC = () => {
  const [services, setServices] = useState<ConsultingService[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Default icons mapping for fallback
  const defaultIcons = [
    <TechnologyIcon />,
    <PolicyIcon />,
    <ProgramIcon />,
    <DisasterIcon />,
    <GrantsIcon />,
    <TrainingIcon />,
  ];

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('consulting_services')
        .select('*')
        .order('created_at', { ascending: true });

      if (error) throw error;

      setServices(data || []);
    } catch (err) {
      console.error('Error fetching consulting services:', err);
      setError('Failed to load consulting services');
    } finally {
      setLoading(false);
    }
  };

  const getServiceIcon = (index: number) => {
    return defaultIcons[index % defaultIcons.length];
  };

  // Loading state
  if (loading) {
    return (
      <div className="bg-[#F9FAFB] py-[60px] px-[4px] md:px-[10px] my-[66px] w-full rounded-[8px]">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">
            Consulting Services
          </h2>
          <p className="text-lg md:text-xl lg:text-xl text-black font-medium font-inter max-w-2xl mx-auto mb-15 leading-relaxed">
            Comprehensive solutions designed for public health and emergency
            management excellence
          </p>
          <div className="flex justify-center items-center mt-10">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-[#F9FAFB] py-[60px] px-[4px] md:px-[10px] my-[66px] w-full rounded-[8px]">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">
            Consulting Services
          </h2>
          <p className="text-lg md:text-xl lg:text-xl text-black font-medium font-inter max-w-2xl mx-auto mb-15 leading-relaxed">
            Comprehensive solutions designed for public health and emergency
            management excellence
          </p>
          <div className="mt-10 p-8 bg-red-50 rounded-xl border border-red-200">
            <div className="text-red-600 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-red-800 mb-2">Unable to Load Services</h3>
            <p className="text-red-600">We're having trouble loading our consulting services. Please try again later.</p>
          </div>
        </div>
      </div>
    );
  }

  // Empty state
  if (services.length === 0) {
    return (
      <div className="bg-[#F9FAFB] py-[60px] px-[4px] md:px-[10px] my-[66px] w-full rounded-[8px]">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">
            Consulting Services
          </h2>
          <p className="text-lg md:text-xl lg:text-xl text-black font-medium font-inter max-w-2xl mx-auto mb-15 leading-relaxed">
            Comprehensive solutions designed for public health and emergency
            management excellence
          </p>
          <div className="mt-10 p-12 bg-white rounded-xl border border-gray-200">
            <div className="text-gray-400 mb-6">
              <svg className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-gray-800 mb-4">No Consulting Services Available</h3>
            <p className="text-gray-600 max-w-md mx-auto leading-relaxed">
              We're currently updating our consulting services offerings. Please check back soon for our comprehensive solutions designed for public health and emergency management excellence.
            </p>
            <div className="mt-8">
              <button
                onClick={fetchServices}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Refresh Services
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Normal state with services
  return (
    <div className="bg-[#F9FAFB] py-[60px] px-[4px] md:px-[10px] my-[66px] w-full rounded-[8px]">
      <div className="max-w-7xl mx-auto text-center">
        {/* Header Section */}
        <h2 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">
          Consulting Services
        </h2>

        <p className="text-lg md:text-xl lg:text-xl text-black font-medium font-inter max-w-2xl mx-auto mb-15 leading-relaxed">
          Comprehensive solutions designed for public health and emergency
          management excellence
        </p>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 mt-10 gap-7">
          {services.map((service, index) => (
            <div
              key={service.id}
              className="bg-white rounded-xl p-8 text-left transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border border-gray-200"
            >
              {/* Service Image or Default Icon */}
              <div className="mb-5">
                {service.image_url ? (
                  <img
                    src={service.image_url}
                    alt={service.title}
                    className="h-9 w-9 object-contain"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                ) : null}
                <div className={service.image_url ? 'hidden' : ''}>
                  {getServiceIcon(index)}
                </div>
              </div>

              <h3 className="text-lg font-semibold text-black mb-4 font-inter">
                {service.title}
              </h3>

              <p className="text-sm text-gray-500 leading-relaxed">
                {service.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ConsultingServices;
