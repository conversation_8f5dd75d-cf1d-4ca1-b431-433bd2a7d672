import React from 'react';
import { Link } from 'react-router-dom';
import mainImage from '../../assets/images/grantready/main-image.png';
import unionIcon from '../../assets/images/grantready/union-icon.svg';

const Banner: React.FC = () => {

    return (
        <div className="relative max-w-[99%] mx-auto overflow-hidden md:rounded-[20px] shadow-2xl">
            {/* Background Container */}
            <div className="relative w-full h-[500px] md:h-[600px] lg:h-[650px] xl:h-[700px] 2xl:h-[750px]">
                {/* Animated Background with CSS */}
                <div
                    className="absolute inset-0 w-full h-full"
                    style={{
                        background: `
                            radial-gradient(ellipse 800px 600px at 20% 30%, rgba(0, 255, 255, 0.25) 0%, transparent 60%),
                            radial-gradient(ellipse 600px 800px at 80% 70%, rgba(0, 200, 255, 0.2) 0%, transparent 60%),
                            radial-gradient(ellipse 400px 400px at 40% 80%, rgba(0, 150, 255, 0.15) 0%, transparent 60%),
                            radial-gradient(ellipse 1000px 400px at 60% 20%, rgba(79, 172, 254, 0.1) 0%, transparent 70%),
                            linear-gradient(135deg, #010406 0%, #020407 30%, #010406 60%, #020407 100%)
                        `
                    }}
                />

                {/* Dynamic Floating Network Lines */}
                <div className="absolute inset-0 overflow-hidden opacity-30">
                    <svg className="absolute inset-0 w-full h-full" viewBox="0 0 1200 800">
                        <defs>
                            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stopColor="#00ffff" stopOpacity="0.6" />
                                <stop offset="50%" stopColor="#0ea5e9" stopOpacity="0.4" />
                                <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.2" />
                            </linearGradient>
                        </defs>
                        <path d="M100,200 Q300,100 500,200 T900,150" stroke="url(#lineGradient)" strokeWidth="2" fill="none" className="animate-pulse" />
                        <path d="M200,400 Q400,300 600,400 T1000,350" stroke="url(#lineGradient)" strokeWidth="1.5" fill="none" className="animate-pulse" style={{ animationDelay: '1s' }} />
                        <path d="M50,600 Q250,500 450,600 T850,550" stroke="url(#lineGradient)" strokeWidth="1" fill="none" className="animate-pulse" style={{ animationDelay: '2s' }} />
                    </svg>
                </div>

                {/* Enhanced Floating Particles */}
                <div className="absolute inset-0 overflow-hidden">
                    <div className="absolute top-20 left-20 w-3 h-3 bg-cyan-400 rounded-full animate-pulse opacity-80 shadow-lg shadow-cyan-400/50"></div>
                    <div className="absolute top-40 left-60 w-2 h-2 bg-blue-400 rounded-full animate-ping opacity-60" style={{ animationDelay: '0.5s' }}></div>
                    <div className="absolute top-80 left-40 w-4 h-4 bg-teal-400 rounded-full animate-bounce opacity-70 shadow-lg shadow-teal-400/40" style={{ animationDelay: '1s' }}></div>
                    <div className="absolute bottom-40 left-80 w-2 h-2 bg-cyan-300 rounded-full animate-pulse opacity-50" style={{ animationDelay: '1.5s' }}></div>
                    <div className="absolute bottom-80 left-20 w-1.5 h-1.5 bg-blue-300 rounded-full animate-ping opacity-60" style={{ animationDelay: '2s' }}></div>

                    {/* Right side particles */}
                    <div className="absolute top-32 right-32 w-3 h-3 bg-cyan-400 rounded-full animate-pulse opacity-70 shadow-lg shadow-cyan-400/50 hidden lg:block"></div>
                    <div className="absolute top-60 right-60 w-2 h-2 bg-blue-400 rounded-full animate-ping opacity-50 hidden lg:block" style={{ animationDelay: '0.8s' }}></div>
                    <div className="absolute bottom-40 right-40 w-4 h-4 bg-teal-400 rounded-full animate-bounce opacity-60 shadow-lg shadow-teal-400/40 hidden lg:block" style={{ animationDelay: '1.2s' }}></div>
                    <div className="absolute top-1/2 right-1/4 w-1.5 h-1.5 bg-cyan-300 rounded-full animate-pulse opacity-40 hidden xl:block" style={{ animationDelay: '1.8s' }}></div>
                </div>

                {/* Centered Content Container */}
                <div className="absolute inset-0 flex justify-center max-w-7xl mx-auto">
                    <div className="relative w-full mx-auto">
                        {/* Right Side Image with Enhanced Glow */}
                        <div className="absolute right-0 top-0 w-4/5 md:w-3/5 lg:w-2/5 xl:w-2/5 2xl:w-2/5 h-full hidden sm:block opacity-90">
                            {/* Glow Background Layers */}
                            <div className="absolute inset-0 opacity-60">
                                <div className="absolute inset-0 bg-gradient-radial from-cyan-400/30 via-cyan-400/10 to-transparent blur-3xl transform scale-110"></div>
                                <div className="absolute inset-0 bg-gradient-radial from-blue-400/20 via-blue-400/5 to-transparent blur-2xl transform scale-105"></div>
                                <div className="absolute inset-0 bg-gradient-radial from-teal-400/15 via-transparent to-transparent blur-xl"></div>
                            </div>

                            {/* Main Image with Enhanced Effects */}
                            <img
                                src={mainImage}
                                alt="GrantReady Network"
                                className="relative z-10 w-full h-full object-contain object-center"
                                style={{
                                    filter: `
                                        brightness(1.3)
                                        contrast(1.2)
                                        saturate(1.1)
                                        drop-shadow(0 0 40px rgba(0, 255, 255, 0.5))
                                        drop-shadow(0 0 80px rgba(0, 200, 255, 0.3))
                                        drop-shadow(0 0 120px rgba(79, 172, 254, 0.2))
                                    `,
                                }}
                            />

                            {/* Animated Glow Pulses */}
                            <div className="absolute inset-0 opacity-40">
                                <div className="absolute inset-0 bg-gradient-radial from-cyan-300/20 to-transparent blur-2xl animate-pulse" style={{ animationDuration: '3s' }}></div>
                                <div className="absolute inset-0 bg-gradient-radial from-blue-300/15 to-transparent blur-xl animate-pulse" style={{ animationDuration: '4s', animationDelay: '1s' }}></div>
                            </div>
                        </div>

                        {/* Content Overlay - Left Side */}
                        <div className="absolute inset-0 flex items-center">
                            <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20 relative z-10">
                                <div className="relative">
                                    {/* Main Content - Left Side Only */}
                                    <div className="max-w-full sm:max-w-2/5 md:max-w-1/2 lg:max-w-3/5 xl:max-w-3/5 2xl:max-w-3/5 sm:pr-8 md:pr-12 lg:pr-16 xl:pr-20 2xl:pr-24">
                                        {/* Floating Brand Badge */}
                                        <div className="mb-8 md:mb-10">
                                            <div className="inline-flex items-center gap-3 md:gap-4 px-4 md:px-6 py-3 md:py-4 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 shadow-2xl hover:scale-105 transition-all duration-500">
                                                <div className="relative">
                                                    <img
                                                        src={unionIcon}
                                                        alt="GrantReady Icon"
                                                        className="w-8 h-8 md:w-10 md:h-10 filter brightness-125"
                                                        style={{
                                                            filter: 'drop-shadow(0 0 12px rgba(0, 255, 255, 0.6)) brightness(1.3)'
                                                        }}
                                                    />
                                                    <div className="absolute inset-0 w-8 h-8 md:w-10 md:h-10 bg-cyan-400/40 rounded-full animate-ping"></div>
                                                    <div className="absolute inset-0 w-8 h-8 md:w-10 md:h-10 bg-cyan-400/20 rounded-full animate-pulse"></div>
                                                </div>
                                                <span className="text-white text-base sm:text-lg md:text-xl lg:text-2xl font-bold tracking-wide">
                                                    GrantReady™ <span className="text-cyan-300 font-normal">by International Responders Systems</span>
                                                </span>
                                            </div>
                                        </div>

                                        {/* Hero Title with Staggered Animation */}
                                        <div className="mb-6 md:mb-8">
                                            <h1 className="text-3xl font-black leading-tight tracking-tight">
                                                <div className="overflow-hidden">
                                                    <span className="block text-white drop-shadow-2xl animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
                                                        Smarter
                                                    </span>
                                                </div>
                                                <div className="overflow-hidden">
                                                    <span
                                                        className="block bg-gradient-to-r from-[#4ade80] via-[#06b6d4] to-[#3b82f6] bg-clip-text text-transparent animate-fadeInUp"
                                                        style={{
                                                            backgroundSize: '200% 200%',
                                                            animation: 'gradient-shift 4s ease-in-out infinite, fadeInUp 0.8s ease-out 0.4s both',
                                                        }}
                                                    >
                                                        Grant Management
                                                    </span>
                                                </div>
                                                <div className="overflow-hidden">
                                                    <span className="block text-white/95 drop-shadow-2xl animate-fadeInUp text-xl sm:text-2xl md:text-3xl lg:text-3xl" style={{ animationDelay: '0.6s' }}>
                                                        for Health & Emergency Response
                                                    </span>
                                                </div>
                                            </h1>
                                        </div>

                                        {/* Enhanced Description */}
                                        <div className="mb-8 md:mb-10 max-w-3xl">
                                            <p className="text-white/90 text-base sm:text-lg md:text-xl leading-relaxed font-light animate-fadeInUp" style={{ animationDelay: '0.8s' }}>
                                                Helps local health jurisdictions and states{' '}
                                                <span className="text-cyan-300 font-semibold italic hover:text-cyan-200 transition-colors duration-300">plan</span>,{' '}
                                                <span className="text-blue-300 font-semibold italic hover:text-blue-200 transition-colors duration-300">implement</span>,{' '}
                                                <span className="text-teal-300 font-semibold italic hover:text-teal-200 transition-colors duration-300">track</span>, and{' '}
                                                <span className="text-green-300 font-semibold italic hover:text-green-200 transition-colors duration-300">report</span>{' '}
                                                federal grant funding. All in one seamless platform.
                                            </p>
                                        </div>

                                        {/* Action Buttons with Enhanced Design */}
                                        <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 animate-fadeInUp" style={{ animationDelay: '1s' }}>
                                            <Link
                                                to="/whitepapers"
                                                className="group relative bg-gradient-to-r from-white to-gray-100 text-black px-6 sm:px-8 md:px-10 py-3 sm:py-4 md:py-3 lg:py-3 rounded-lg font-normal text-base md:text-lg flex items-center justify-center gap-2 sm:gap-3 md:gap-4 hover:scale-105 hover:shadow-2xl transition-all duration-500 overflow-hidden"
                                            >
                                                <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-blue-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                                <span className="relative z-10 font-black">Read Whitepaper</span>
                                                <svg
                                                    width="20"
                                                    height="20"
                                                    viewBox="0 0 20 20"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className="relative z-10 group-hover:translate-x-2 transition-transform duration-500 sm:w-6 sm:h-6"
                                                >
                                                    <path
                                                        d="M4.16667 10H15.8333M10 4.16667L15.8333 10L10 15.8333"
                                                        stroke="currentColor"
                                                        strokeWidth="2.5"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                </svg>
                                            </Link>

                                            <Link
                                                to="/support"
                                                className="group relative border-2 border-cyan-400/60 text-white px-6 sm:px-8 md:px-10 py-2 md:py-2 lg:py-2 rounded-lg font-normal text-base md:text-lg hover:scale-105 transition-all duration-500 text-center backdrop-blur-md bg-white/10 hover:bg-cyan-400/20 hover:border-cyan-400 shadow-xl hover:shadow-cyan-400/30"
                                            >
                                                <span className="relative z-10 font-black">Support Center</span>
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Custom CSS for animations and glow effects */}
                <style dangerouslySetInnerHTML={{
                    __html: `
                        @keyframes gradient-shift {
                            0%, 100% { background-position: 0% 50%; }
                            50% { background-position: 100% 50%; }
                        }

                        @keyframes fadeInUp {
                            0% {
                                opacity: 0;
                                transform: translateY(30px);
                            }
                            100% {
                                opacity: 1;
                                transform: translateY(0);
                            }
                        }

                        .animate-fadeInUp {
                            animation: fadeInUp 0.8s ease-out forwards;
                            opacity: 0;
                        }

                        .bg-gradient-radial {
                            background: radial-gradient(circle, var(--tw-gradient-stops));
                        }
                    `
                }} />
            </div>
        </div>
    );
};

export default Banner;