import { useState, useEffect } from "react";
import {
  Share2,
  Eye,
  UserCheck,
  Bell,
  Map,
  AlertTriangle,
  Calendar,
  Monitor,
} from "lucide-react";
import { supabase } from "../lib/supabase";
import Accelerate from "../components/elenor/Accelerate";
import HomeBanner from "../components/elenor/HomeBanner";
import TrustedIncidentManagement from "../components/elenor/TrustedIncidentManagement";
import TrustedBy from "../components/grantready/TrustedBy";
import EmpowerYourTeamElenor, {
  CardData,
} from "../components/elenor/EmpowerYourTeamElenor"; // Updated import
import ClientTestimonial from "../components/grantready/ClientTestemonial";
import PricingSection from "../components/elenor/PricingSection";
import EmergencyManagementBanner from "../components/elenor/EmergencyManagementBanner";
import elenorCtaVideoBlue from "../assets/video/elenor-cta-bg-blue.mov";
import { Colors } from "../constants/Colors";
import SeamlessCordination from "../components/elenor/SeamlessCordination";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  pages?: string[];
  specifications: string[];
  theme_color?: string;
  monthly_discount?: number;
  yearly_discount?: number;
  created_at: string;
  updated_at?: string;
}

export default function ELENOR() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [billingInterval, setBillingInterval] = useState<"monthly" | "yearly">(
    "monthly"
  );
  const [maxYearlyDiscount, setMaxYearlyDiscount] = useState<number>(0);

  useEffect(() => {
    fetchProducts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchProducts = async () => {
    try {
      console.log("Fetching products for the ELENOR page");

      // First, let's get all products to examine their structure
      const { data: allProducts, error: fetchError } = await supabase
        .from("products")
        .select("*");

      if (fetchError) {
        console.error("Error fetching products:", fetchError);
        setProducts([]);
        setError(
          `Error fetching products: ${fetchError.message || fetchError}`
        );
        setLoading(false);
        return;
      }

      console.log("All products from database:", allProducts);

      // Filter products client-side for ELENOR page
      const elenorProducts = allProducts.filter((product) => {
        // Check if pages exists and is an array
        if (Array.isArray(product.pages)) {
          return product.pages.includes("Elenor");
        }
        // If pages is a string (JSON string), try to parse it
        if (typeof product.pages === "string") {
          try {
            const pagesArray = JSON.parse(product.pages);
            return Array.isArray(pagesArray) && pagesArray.includes("Elenor");
          } catch (e) {
            console.error("Error parsing pages JSON:", e);
            return false;
          }
        }
        return false;
      });

      console.log("Filtered ELENOR products:", elenorProducts);

      // If we have products for ELENOR page
      if (elenorProducts && elenorProducts.length > 0) {
        // Sort by price to ensure consistent order
        const sortedProducts = [...elenorProducts].sort(
          (a, b) => (a.price || 0) - (b.price || 0)
        );

        // Log the sorted products with formatted prices for debugging
        console.log(
          "Sorted ELENOR products:",
          sortedProducts.map((p) => ({
            id: p.id,
            name: p.name,
            price: p.price.toFixed(2),
            monthly_discount: p.monthly_discount || 0,
            yearly_discount: p.yearly_discount || 0,
          }))
        );

        // Find the maximum yearly discount among all products
        const highestDiscount = sortedProducts.reduce((max, product) => {
          const discount = product.yearly_discount || 0;
          return discount > max ? discount : max;
        }, 0);

        setMaxYearlyDiscount(highestDiscount);
        setProducts(sortedProducts);
      } else {
        // If there are no products for ELENOR page, set empty array
        console.log("No products found for ELENOR page");
        setProducts([]);
      }
    } catch (err: unknown) {
      console.error("Error fetching products:", err);
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
    } finally {
      setLoading(false);
    }
  };

  // ELENOR specific card data from Figma design (all 8 cards with appropriate icons)
  const elenorCardsData: CardData[] = [
    {
      id: 1,
      title: "Real-Time Information Sharing",
      description:
        "Stay connected with instant updates across all teams, ensuring everyone works from the same, current data.",
      icon: Share2,
      gradientFrom: "#F59E0B",
      gradientTo: "#EF4444",
    },
    {
      id: 2,
      title: "Common Operating Picture",
      description:
        "Visualize the full scope of an incident with an easy-to-understand, unified map and status overview.",
      icon: Eye,
      gradientFrom: "#DC2626",
      gradientTo: "#B91C1C",
    },
    {
      id: 3,
      title: "Resource Management",
      description:
        "Visualize the full scope of an incident with an easy-to-understand, unified map and status overview.",
      icon: UserCheck,
      gradientFrom: "#F97316",
      gradientTo: "#EA580C",
    },
    {
      id: 4,
      title: "Alerting and Notifications",
      description:
        "Receive automated alerts so your team never misses vital developments.",
      icon: Bell,
      gradientFrom: "#D97706",
      gradientTo: "#B45309",
    },
    {
      id: 5,
      title: "GIS Integration",
      description:
        "Leverage powerful geographic mapping tools for better situational awareness.",
      icon: Map,
      gradientFrom: "#F59E0B",
      gradientTo: "#D97706",
    },
    {
      id: 6,
      title: "Incident Management",
      description:
        "Manage incidents from start to finish with streamlined workflows and clear documentation.",
      icon: AlertTriangle,
      gradientFrom: "#EF4444",
      gradientTo: "#DC2626",
    },
    {
      id: 7,
      title: "Daily Operations Support",
      description:
        "Beyond emergencies, Eleno supports your everyday operations and continuity plans.",
      icon: Calendar,
      gradientFrom: "#F59E0B",
      gradientTo: "#EF4444",
    },
    {
      id: 8,
      title: "User-Friendly Interface",
      description:
        "Designed with users in mind — simple, intuitive, and easy to navigate, so your team can focus on action, not technology.",
      icon: Monitor,
      gradientFrom: "#DC2626",
      gradientTo: "#B91C1C",
    },
  ];

  return (
    <div className="px-0">
      <HomeBanner />
      <TrustedIncidentManagement />
      <EmpowerYourTeamElenor
        cardsData={elenorCardsData}
        gridCols="grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
        title="Powerful Features That Keep You Ahead"
        description="Every feature is designed to enhance your emergency response capabilities and streamline operations."
        showTrademark={false}
      />
      <SeamlessCordination />
      <TrustedBy
        title="Trusted by Health Departments Nationwide"
        description="Leading health organizations rely on Elenor to streamline communication, coordinate resources, and ensure effective response during critical incidents."
      // description="Leading health organizations rely on Elenor to automate and enhance their incident response and cybersecurity resilience."
      />
      <PricingSection />
      <ClientTestimonial
        color={Colors.ELENOR}
        title="See how clients strengthen emergency response"
        description="Health jurisdictions across the country are transforming their incident response with Elenor."
      />
    </div>
  );
}
