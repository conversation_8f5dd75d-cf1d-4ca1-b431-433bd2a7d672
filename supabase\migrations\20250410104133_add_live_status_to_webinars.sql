-- Migration: Add 'live' status option to webinars table and update registration status options
-- Purpose:
-- 1. Allow webinars to have 'live' status in addition to 'upcoming' and 'recorded'
-- 2. Remove 'attended' and 'no_show' from webinar registration status options

-- First, drop the existing constraint for webinars table
ALTER TABLE webinars
DROP CONSTRAINT webinars_status_check;

-- Add the new constraint with 'live' included
ALTER TABLE webinars
ADD CONSTRAINT webinars_status_check CHECK (status IN ('upcoming', 'recorded', 'live'));

-- Add comment to explain the webinar status column
COMMENT ON COLUMN webinars.status IS 'Webinar status: upcoming, live, recorded';

-- Now update the webinar_registrations table to remove 'attended' and 'no_show' status options
-- First, drop the existing constraint
ALTER TABLE webinar_registrations
DROP CONSTRAINT webinar_registrations_status_check;

-- Add the new constraint with only 'registered' and 'cancelled' options
ALTER TABLE webinar_registrations
ADD CONSTRAINT webinar_registrations_status_check CHECK (status IN ('registered', 'cancelled'));

-- Update any existing records with 'attended' or 'no_show' status to 'registered'
UPDATE webinar_registrations
SET status = 'registered'
WHERE status IN ('attended', 'no_show');

-- Add comment to explain the registration status column
COMMENT ON COLUMN webinar_registrations.status IS 'Registration status: registered, cancelled';


-- Migration: Add webinar_link column to webinars table
-- Purpose: Allow admins to add a link to the webinar that will be visible to registered users

-- Add the webinar_link column to the webinars table
ALTER TABLE webinars
ADD COLUMN webinar_link TEXT;

-- Add comment to explain the webinar_link column
COMMENT ON COLUMN webinars.webinar_link IS 'Link to join the webinar, visible to registered users';
