import React from 'react';
import { FileText, Mail, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const sections = [
  {
    title: "1. Acceptance of Terms",
    content: 'By accessing or using the services provided by International Responder Systems LLC ("the Company"), you agree to be bound by these Terms and Conditions. If you do not agree with any part of these terms, you may not use our services.'
  },
  {
    title: "2. Definitions",
    content: [
      'a. "Services" refers to the products, software, and any other offerings provided by International Responder Systems LLC.',
      'b. "User" refers to any individual, organization, or entity using the Company\'s services.'
    ]
  },
  {
    title: "3. Use of Services",
    content: [
      "a. Users must comply with all applicable laws and regulations while using our services.",
      "b. Users are responsible for maintaining the confidentiality of their account information and are liable for any activities conducted through their accounts."
    ]
  },
  {
    title: "4. Intellectual Property",
    content: [
      "a. All content, trademarks, and intellectual property associated with International Responder Systems LLC are the exclusive property of the Company.",
      "b. Users may not reproduce, distribute, or use any content from our services without prior written consent from International Responder Systems LLC."
    ]
  },
  {
    title: "5. Privacy Policy",
    content: "a. Our Privacy Policy outlines how we collect, use, and protect user information. By using our services, you agree to the terms outlined in our Privacy Policy."
  },
  {
    title: "6. Limitation of Liability",
    content: [
      "a. International Responder Systems LLC is not liable for any direct, indirect, incidental, consequential, or punitive damages arising from the use of our services.",
      "b. The Company is not responsible for any loss or damage to user data."
    ]
  },
  {
    title: "7. Termination of Services",
    content: "a. International Responder Systems LLC reserves the right to terminate or suspend services to any user at its discretion, without prior notice."
  },
  {
    title: "8. Governing Law",
    content: "a. These Terms and Conditions are governed by the laws of the state in which International Responder Systems LLC is registered."
  },
  {
    title: "9. Modifications to Terms",
    content: "a. The Company reserves the right to modify or update these Terms and Conditions at any time. Users are encouraged to review this document periodically."
  },
  {
    title: "10. Contact Information",
    content: "For any questions or concerns regarding these Terms and Conditions, please contact International Responder Systems <NAME_EMAIL>."
  }
];

export default function Terms() {
  return (
    <div className="">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-4 mb-6">
            <FileText className="h-10 w-10" />
            <h1 className="text-4xl font-bold">Terms & Conditions</h1>
          </div>
          <p className="text-xl text-blue-100 max-w-3xl">
            Please read these terms and conditions carefully before using our services.
          </p>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-xl p-8">
            {sections.map((section, index) => (
              <div key={index} className="mb-12 last:mb-0">
                <h2 className="text-2xl font-bold mb-4">{section.title}</h2>
                {Array.isArray(section.content) ? (
                  <div className="space-y-4">
                    {section.content.map((item, i) => (
                      <p key={i} className="text-gray-600 leading-relaxed">{item}</p>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-600 leading-relaxed">{section.content}</p>
                )}
              </div>
            ))}

            {/* Contact Section */}
            <div className="mt-12 bg-gray-50 p-8 rounded-lg">
              <h2 className="text-2xl font-bold mb-4">Questions?</h2>
              <p className="text-gray-600 mb-6">
                If you have any questions about these Terms & Conditions, please contact us.
              </p>
              <div className="flex items-center space-x-6">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
                >
                  <Mail className="mr-2 h-5 w-5" />
                  Email Us
                </a>
                <Link
                  to="/contact"
                  className="inline-flex items-center text-blue-600 font-medium hover:text-blue-700"
                >
                  Contact Page
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}