import { useState, useRef, useEffect } from 'react';
import { X, User, Mail, AlertCircle, CheckCircle, LogIn } from 'lucide-react';
import { supabase } from '../lib/supabase';
import HCaptcha from '@hcaptcha/react-hcaptcha';
import { useAuthStore } from '../store/authStore';
import { Link } from 'react-router-dom';

interface EventRegistrationModalProps {
  eventId: string;
  eventTitle: string;
  onClose: () => void;
  onSuccess: () => void;
}

export default function EventRegistrationModal({
  eventId,
  eventTitle,
  onClose,
  onSuccess
}: EventRegistrationModalProps) {
  const user = useAuthStore((state) => state.user);
  const [formData, setFormData] = useState({
    full_name: user?.user_metadata?.full_name || '',
    email: user?.email || ''
  });
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [isAlreadyRegistered, setIsAlreadyRegistered] = useState(false);
  const captchaRef = useRef<HCaptcha>(null);

  useEffect(() => {
    // Check if user is already registered for this event
    const checkRegistration = async () => {
      if (user) {
        try {
          const { data, error } = await supabase
            .from('event_registrations')
            .select('id, status')
            .eq('event_id', eventId)
            .eq('user_id', user.id);

          if (!error && data && data.length > 0) {
            // Only consider as registered if status is not 'cancelled'
            const activeRegistration = data.find(reg => reg.status !== 'cancelled');
            if (activeRegistration) {
              setIsAlreadyRegistered(true);
            }
          }
        } catch (err) {
          console.error('Error checking registration:', err);
        }
      }
    };

    checkRegistration();

    // Update form data when user changes
    if (user) {
      setFormData(prev => ({
        ...prev,
        full_name: user.user_metadata?.full_name || prev.full_name,
        email: user.email || prev.email
      }));
    }
  }, [user, eventId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (isAlreadyRegistered) {
      onClose();
      return;
    }

    // Validate form
    if (!formData.full_name.trim()) {
      setError('Please enter your full name');
      return;
    }

    if (!formData.email.trim()) {
      setError('Please enter your email address');
      return;
    }

    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return;
    }

    // Check if captcha is required (based on environment variable)
    const captchaRequired = import.meta.env.VITE_HCAPTCHA_SITE_KEY && import.meta.env.VITE_HCAPTCHA_SITE_KEY !== 'false';

    if (captchaRequired && !captchaToken) {
      setError('Please complete the captcha verification');
      return;
    }

    setIsSubmitting(true);

    try {
      // Check if already registered by email (for non-logged in users)
      if (!user) {
        const { data: existingRegistrations, error: checkError } = await supabase
          .from('event_registrations')
          .select('id, status')
          .eq('event_id', eventId)
          .eq('email', formData.email);

        if (checkError) throw checkError;

        // Check if there's an active (non-cancelled) registration
        const activeRegistration = existingRegistrations?.find(reg => reg.status !== 'cancelled');
        if (activeRegistration) {
          setError('You have already registered for this event');
          setIsSubmitting(false);
          return;
        }

        // Check if there's a cancelled registration that we can reactivate
        const cancelledRegistration = existingRegistrations?.find(reg => reg.status === 'cancelled');
        if (cancelledRegistration) {
          // Update the existing registration instead of creating a new one
          const { error: updateError } = await supabase
            .from('event_registrations')
            .update({ status: 'registered' })
            .eq('id', cancelledRegistration.id);

          if (updateError) throw updateError;

          // Success
          onSuccess();
          return;
        }
      }

      // Insert registration
      const registrationData: any = {
        event_id: eventId,
        user_id: user?.id,
        full_name: formData.full_name,
        email: formData.email
      };

      // Only add captcha_token if captcha is required and token exists
      if (captchaRequired && captchaToken) {
        registrationData.captcha_token = captchaToken;
      }

      const { error: insertError } = await supabase
        .from('event_registrations')
        .insert([registrationData]);

      if (insertError) throw insertError;

      // Send confirmation email
      try {
        // Get event details to include in the email
        const { data: eventData, error: eventError } = await supabase
          .from('events')
          .select('title, date, time, location, address')
          .eq('id', eventId)
          .single();

        if (eventError) throw eventError;

        // Send email using the send-contact-email edge function
        const { error: emailError } = await supabase.functions.invoke('send-contact-email', {
          body: {
            to: formData.email,
            subject: `Event Registration Confirmation: ${eventTitle}`,
            html: `
              <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <h2 style="color: #2c3e50;">Registration Confirmation</h2>
                <p>Hello ${formData.full_name},</p>
                <p>Thank you for registering for <strong>${eventTitle}</strong>. Your registration has been confirmed.</p>

                <div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="margin-top: 0; color: #3498db;">Event Details</h3>
                  <p><strong>Date:</strong> ${eventData.date}</p>
                  <p><strong>Time:</strong> ${eventData.time}</p>
                  <p><strong>Location:</strong> ${eventData.location}</p>
                  ${eventData.address ? `<p><strong>Address:</strong> ${eventData.address}</p>` : ''}
                </div>

                <p>We look forward to seeing you at the event. If you have any questions, please don't hesitate to contact us.</p>
                <p>Best regards,<br>International Responder Systems</p>
              </div>
            `
          }
        });

        if (emailError) {
          console.error('Error sending confirmation email:', emailError);
          // Continue with success even if email fails
        }
      } catch (emailErr) {
        console.error('Error in email process:', emailErr);
        // Continue with success even if email fails
      }

      // Success
      onSuccess();
    } catch (err: any) {
      console.error('Registration error:', err);
      setError(err.message || 'Failed to register. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-bold">Register for Event</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {!user ? (
            <div className="text-center py-6">
              <div className="bg-blue-50 p-4 rounded-lg mb-6">
                <div className="flex flex-col items-center">
                  <LogIn className="h-12 w-12 text-blue-500 mb-4" />
                  <h4 className="text-lg font-semibold mb-2">Login Required</h4>
                  <p className="text-gray-600 mb-4">
                    Please log in to register for this event. Logging in allows us to save your information for future events.
                  </p>
                  <Link
                    to="/login"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 inline-flex items-center"
                  >
                    <LogIn className="h-4 w-4 mr-2" />
                    Log In
                  </Link>
                  <p className="mt-4 text-sm text-gray-500">
                    Don't have an account? <Link to="/signup" className="text-blue-600 hover:underline">Sign up</Link>
                  </p>
                </div>
              </div>
            </div>
          ) : isAlreadyRegistered ? (
            <div className="text-center py-6">
              <div className="bg-green-50 p-4 rounded-lg mb-6">
                <div className="flex flex-col items-center">
                  <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
                  <h4 className="text-lg font-semibold mb-2">Already Registered</h4>
                  <p className="text-gray-600 mb-4">
                    You have already registered for this event. We'll send you a reminder before it starts.
                  </p>
                  <button
                    onClick={onClose}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <>
              <p className="text-gray-600 mb-6">
                Complete the form below to register for "{eventTitle}"
              </p>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                  <div className="flex">
                    <AlertCircle className="h-5 w-5 text-red-400" />
                    <div className="ml-3">
                      <p className="text-sm text-red-600">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      id="full_name"
                      type="text"
                      required
                      value={formData.full_name}
                      onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="John Doe"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      id="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                {import.meta.env.VITE_HCAPTCHA_SITE_KEY && import.meta.env.VITE_HCAPTCHA_SITE_KEY !== 'false' && (
                  <div className="mt-4">
                    <HCaptcha
                      ref={captchaRef}
                      sitekey={import.meta.env.VITE_HCAPTCHA_SITE_KEY}
                      onVerify={(token) => setCaptchaToken(token)}
                      onExpire={() => {
                        setCaptchaToken(null);
                        captchaRef.current?.resetCaptcha();
                      }}
                      onError={(err) => {
                        setError('Captcha verification failed');
                        console.error('hCaptcha Error:', err);
                      }}
                    />
                  </div>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center disabled:opacity-50"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Registering...
                      </>
                    ) : (
                      'Register Now'
                    )}
                  </button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
