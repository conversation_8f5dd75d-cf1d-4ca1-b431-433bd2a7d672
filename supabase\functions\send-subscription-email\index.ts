import { serve } from 'https://deno.land/std/http/server.ts';
import nodemailer from 'npm:nodemailer';
serve(async (req)=>{
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': '*',
    'Access-Control-Allow-Headers': '*',
    'Content-Type': 'application/json'
  };
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers
    });
  }
  try {
    // Alternative body reading method
    const body = await req.json();
    if (!body) {
      throw new Error('Request body is empty');
    }
    // Validate required fields
    if (!body.to || !body.subject || !body.text) {
      throw new Error('Missing required fields (to, subject, text)');
    }
    const { to, subject, text } = body;
    const transporter = nodemailer.createTransport({
      // service: 'gmail',
      // auth: {
      //   user: "<EMAIL>",
      //   pass: "",
      host: 'smtp.office365.com',
      port: 587,
      secure: false,
      auth: {
        // user: Deno.env.get('SMTP_USERNAME'),
        // pass: Deno.env.get('SMTP_PASSWORD')
        user:"<EMAIL>",
        pass:"mdfhnxgrfcnlbxjl"
      }
    });
    const isValid = await transporter.verify();
    console.log('SMTP verified:', isValid);
    await transporter.sendMail({
      // from: Deno.env.get('SMTP_USERNAME'),
      from: "<EMAIL>",
      to,
      subject,
      text
    });
    return new Response(JSON.stringify({
      success: true
    }), {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({
      error: 'Failed to process request',
      details: error.message
    }), {
      status: 400,
      headers
    });
  }
});
