import React, { useEffect, useState } from 'react';
import { Mail, Phone, MapPin, Linkedin, Facebook, Send } from 'lucide-react';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';
import irsLogo from '../assets/images/footer-irs-logo.png';
import newsLetterBG from '../assets/images/news-letter-bg.mp4';
import { useSiteSettings } from '../hooks/useSiteSettings';


export default function Footer() {
  const user = useAuthStore((state) => state.user);
  const [email, setEmail] = useState('');
  const [subscribeStatus, setSubscribeStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');
  const [subscriberCount, setSubscriberCount] = useState(0);
  const { settings, loading } = useSiteSettings();

  // Fetch subscriber count on component mount
  useEffect(() => {
    const fetchSubscriberCount = async () => {
      try {
        // First try with exact column names
        const { data, count, error } = await supabase
          .from('subscribers')
          .select('*', { count: 'exact' })
          .eq('status', 'active');

        if (error || count === null) {

          setSubscriberCount(altCount || 0);
        } else {
          setSubscriberCount(count);
        }
      } catch (err) {
        console.error('Error fetching subscriber count:', err);
        // Fallback to showing the count from SQL query
        setSubscriberCount(2);
      }
    };

    fetchSubscriberCount();
  }, []);

  // Format subscriber count for display
  const formattedSubscriberCount = subscriberCount >= 1000
    ? `${(subscriberCount / 1000).toFixed(1)}k+`
    : `${subscriberCount}+`;

  const solutions = [
    { name: "Products", path: "/products" },
    { name: 'GrantReady™', path: '/solutions/grantready' },
    { name: 'SOAR', path: '/solutions/soar' },
    { name: 'ELENOR', path: '/solutions/elenor' }
  ];

  const resources = [
    { name: 'Blog', path: '/blog' },
    { name: 'Case Studies', path: '/case-studies' },
    { name: 'Webinars', path: '/webinars' },
    { name: 'Whitepapers', path: '/whitepapers' },
    { name: 'Guides', path: '/guides' },
    { name: 'Events', path: '/events' }
  ];

  const company = [
    { name: 'About Us', path: '/about' },
    { name: 'Leadership Team', path: '/leadership' },
    { name: 'News', path: '/news' },
    { name: 'Careers', path: '/careers' }
  ];

  const support = [
    { name: 'Contact', path: '/contact' },
    { name: 'FAQ', path: '/faq' }
  ];

  const legal = [
    { name: 'Terms & Conditions', path: '/terms' },
    { name: 'Privacy Policy', path: '/privacy' },
    { name: 'Accessibility', path: '/accessibility' }
  ];

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubscribeStatus('loading');
    setStatusMessage('');

    const emailToCheck = user ? user.email : email;
    const name = user ? user.user_metadata?.full_name || 'Guest' : 'Guest';

    // Check if email is empty
    if (!emailToCheck || emailToCheck.trim() === '') {
      setSubscribeStatus('error');
      setStatusMessage('Please enter your email address.');
      return;
    }

    try {
      // if(emailToCheck.trim === '')

      // First check if email is already subscribed and active
      const { data: existingSubscriber } = await supabase
        .from('subscribers')
        .select('status')
        .eq('email', emailToCheck)
        .eq('status', 'active')
        .maybeSingle();

      if (existingSubscriber) {
        throw new Error('This email is already subscribed');
      }

      // Add to database
      const { error: dbError } = await supabase
        .from('subscribers')
        .insert({
          email: emailToCheck,
          full_name: name,
          status: 'active',
          subscribed_at: new Date().toISOString()
        });

      if (dbError) {
        throw new Error(dbError.message || 'Failed to save subscription');
      }

      // Call the Supabase function to send email
      const { error: emailError } = await supabase.functions.invoke('send-subscription-email', {
        body: {
          to: emailToCheck,
          subject: "Welcome to Our Newsletter!",
          text: `Hello ${name || "Subscriber"},
      
      Thank you for subscribing to International Responder Systems' newsletter!
      Stay tuned for updates on public health emergency response and grant management.
      
      Best regards,
      International Responder Systems`
        }
      });


      if (emailError) {
        throw new Error(`Failed to send confirmation email: ${emailError.message}`);
      }

      setSubscribeStatus('success');
      setStatusMessage('Thank you for subscribing! Check your inbox.');
      setEmail('');
      setSubscriberCount(prev => prev + 1); // Update subscriber count
    } catch (err: any) {
      console.error('Subscription error:', err);
      setSubscribeStatus('error');
      setStatusMessage(err.message || 'Subscription failed. Please try again.');
    }
  };

  if (loading) return null; // Or loading spinner

  return (
    <footer className="bg-white text-gray-700">
      {/* Newsletter Section */}
      <div className="border-b border-gray-200">
        <div className="max-w-full py-0">
          <div className="h-[400px] flex items-center justify-center rounded-md relative overflow-hidden">
            {/* Background Video */}
            <video
              autoPlay
              loop
              muted
              playsInline
              className="absolute inset-0 w-full h-full object-cover"
            >
              <source src={newsLetterBG} type="video/mp4" />
            </video>
            {/* Newsletter Form */}
            <div className="relative w-[600px] z-10 px-4">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-100/30 to-purple-100/30 rounded-lg blur-xl"></div>
              <div className="relative bg-white py-4 px-2 rounded-lg border border-gray-200">
                <h3 className="font-platform text-black font-medium mb-2 leading-tight tracking-normal text-2xl md:text-3xl lg:text-4xl text-center">Stay Updated</h3>
                <p className="text-gray-500 mb-6 text-center">
                  Get the latest updates on public health emergency response and grant management.
                </p>
                <form onSubmit={handleSubscribe} className="w-[99%] md:w-5/6 mx-auto">
                  <div className="relative">
                    <input
                      type="email"
                      placeholder="Enter your email"
                      value={user ? user.email : email}
                      onChange={(e) => {
                        if (!user) {
                          setEmail(e.target.value);
                        }
                      }}
                      className="w-full px-4 py-3 bg-gray-100 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-400"
                      disabled={!!user}
                    />
                    <button
                      type="submit"
                      disabled={subscribeStatus === 'loading'}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center"
                    >
                      {subscribeStatus === 'loading' ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Subscribe
                        </>
                      )}
                    </button>
                  </div>
                  {subscribeStatus !== 'idle' && (
                    <p className={`text-sm ${subscribeStatus === 'success' ? 'text-green-600' : 'text-red-600'
                      }`}>
                      {statusMessage}
                    </p>
                  )}
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="max-w-7xl mx-auto py-12 text-base">
        <div className="grid grid-cols-2 md:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="col-span-2 md:pr-12">
            <div className="flex items-center mb-6">
              <img src={irsLogo} alt="IRS Logo" className="w-full" />
            </div>
            <p className="text-gray-500 mb-6">
              {settings?.general?.siteDescription || 'Public Health Emergency Response Solutions'}
            </p>
            <div className="space-y-3 flex flex-col items-start">
              <a
                href={`mailto:${settings?.general?.contactEmail}`}
                className="flex items-center text-gray-500 hover:text-gray-900 transition-colors"
              >
                <Mail className="h-5 w-5 min-w-5 min-h-5 mr-2" />
                {settings?.general?.contactEmail}
              </a>
              <div className="flex items-center text-gray-500 hover:text-gray-900 transition-colors cursor-pointer">
                <MapPin className="h-5 w-5 min-w-5 min-h-5 mr-2" />
                {settings?.general?.address || '157 E Main Street, Elkton, MD 21921-5977'}
              </div>
              <div className='flex items-center text-gray-500 hover:text-gray-900 transition-colors cursor-pointer'>
                <Phone className="h-5 w-5 min-w-5 min-h-5 mr-2" />
                {settings?.general?.phone}
              </div>
            </div>
          </div>

          {/* Solutions */}
          <div>
            <h3 className="text-gray-900 font-semibold mb-4 text-base">Solutions</h3>
            <ul className="space-y-3">
              {solutions.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-500 hover:text-gray-900">
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-gray-900 font-semibold mb-4 text-base">Resources</h3>
            <ul className="space-y-3">
              {resources.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-500 hover:text-gray-900">
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-gray-900 font-semibold mb-4 text-base">Company</h3>
            <ul className="space-y-3">
              {company.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-500 hover:text-gray-900">
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support & Legal */}
          <div>
            <h3 className="text-gray-900 font-semibold mb-4 text-base">Support</h3>
            <ul className="space-y-3">
              {support.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-500 hover:text-gray-900">
                    {item.name}
                  </Link>
                </li>
              ))}
              {legal.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-500 hover:text-gray-900">
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Social Links & Copyright */}
      <div className="border-t border-gray-200 text-base">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div
            className="flex flex-col items-center justify-center w-full"
            style={{
              fontFamily: 'Inter, sans-serif',
              fontWeight: 500,
              fontSize: '16px',
              lineHeight: '100%',
              letterSpacing: 0,
              textAlign: 'center',
            }}
          >
            <div className="flex items-center justify-center gap-4 mb-4">
              <a
                href={settings?.general?.socialLinks?.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-gray-900 flex items-center justify-center"
                style={{ lineHeight: 0 }}
              >
                <Linkedin className="h-5 w-5" stroke="#6B7280" />
              </a>
              <a
                href={settings?.general?.socialLinks?.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-gray-900 flex items-center justify-center"
                style={{ lineHeight: 0 }}
              >
                <svg viewBox="0 0 22 22" width="20" height="20" fill="none" stroke="#6B7280" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="inline-block align-middle">
                  <g>
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
                  </g>
                </svg>
              </a>
              <a
                href={settings?.general?.socialLinks?.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-gray-900 flex items-center justify-center"
                style={{ lineHeight: 0 }}
              >
                <Facebook className="h-5 w-5" stroke="#6B7280" />
              </a>
            </div>
            <div className="flex flex-wrap gap-x-4 gap-y-2 justify-center mb-2">
              <Link to="/terms" className="text-gray-500 hover:text-gray-900">Terms & Conditions</Link>
              <span className="text-gray-300">|</span>
              <Link to="/privacy" className="text-gray-500 hover:text-gray-900">Privacy Policy</Link>
              <span className="text-gray-300">|</span>
              <Link to="/accessibility" className="text-gray-500 hover:text-gray-900">Accessibility</Link>
            </div>
            <p className="text-gray-500 text-center">
              © {new Date().getFullYear()} International Responder Systems. All Rights Reserved
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}