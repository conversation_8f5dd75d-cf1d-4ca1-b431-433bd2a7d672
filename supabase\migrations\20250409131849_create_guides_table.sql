-- Drop table if exists
DROP TABLE IF EXISTS guides;

-- Create guides table
CREATE TABLE guides (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  category TEXT NOT NULL,
  description TEXT NOT NULL,
  download_count INTEGER DEFAULT 0,
  image_url TEXT,
  pdf_url TEXT NOT NULL,
  is_public BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grant permissions to appropriate roles
GRANT ALL PRIVILEGES ON TABLE guides TO postgres;
GRANT ALL PRIVILEGES ON TABLE guides TO authenticated;
GRANT SELECT ON TABLE guides TO anon;

-- Enable RLS
ALTER TABLE guides ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Public guides are visible to everyone"
ON guides FOR SELECT
USING (is_public = TRUE);

CREATE POLICY "All guides are visible to all users"
ON guides FOR SELECT
TO anon, authenticated
USING (TRUE);

-- Remove the redundant delete policy and modify the main admin policy
CREATE POLICY "Admins can manage all guides"
ON guides FOR ALL
TO authenticated
USING (auth.role() = 'admin');

-- Remove this policy as it's redundant with the one above
-- CREATE POLICY "Allow admin deletes"
-- ON guides FOR DELETE
-- TO authenticated
-- USING (auth.role() = 'admin');

-- Add public delete policy
CREATE POLICY "Public can delete guides"
ON guides FOR DELETE
TO anon, authenticated
USING (TRUE);

-- Add public delete policy for storage
CREATE POLICY "Public can delete from guides bucket"
ON storage.objects FOR DELETE
TO anon, authenticated
USING (bucket_id = 'guides');

-- Allow public access to bucket
CREATE POLICY "Public access to guides bucket"
ON storage.objects FOR SELECT
USING (bucket_id = 'guides');

-- Allow authenticated users to insert guides
CREATE POLICY "Allow authenticated inserts"
ON guides FOR INSERT
TO authenticated
WITH CHECK (true);

-- Allow authenticated users to update their own guides
CREATE POLICY "Allow authenticated updates"
ON guides FOR UPDATE
TO authenticated
USING (true) WITH CHECK (true);


CREATE POLICY "Allow download count updates"
ON guides FOR UPDATE
TO authenticated
USING (true)
WITH CHECK (
  -- Only allow updating download_count
  (SELECT COUNT(*) FROM jsonb_each(to_jsonb(guides))
   WHERE key != 'download_count') = 0
);

CREATE OR REPLACE FUNCTION increment()
RETURNS INTEGER AS $$
BEGIN
  RETURN COALESCE(download_count, 0) + 1;
END;
$$ LANGUAGE plpgsql;

-- Allow authenticated users to upload files
CREATE POLICY "Authenticated upload access"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'guides');

-- Allow admins full access
CREATE POLICY "Admin full access to guides bucket"
ON storage.objects FOR ALL
TO authenticated
USING (bucket_id = 'guides' AND auth.role() = 'admin');

CREATE POLICY "Allow public downloads"
ON storage.objects FOR SELECT
USING (bucket_id = 'guides');

CREATE POLICY "Allow authenticated downloads"
ON storage.objects FOR SELECT
TO authenticated
USING (bucket_id = 'guides');

-- Add type column to guides table to support both guides and whitepapers
ALTER TABLE guides
ADD COLUMN type TEXT NOT NULL DEFAULT 'guide'
CHECK (type IN ('guide', 'whitepaper'));

-- Add whitepaper_link column to guides table for external whitepaper links
ALTER TABLE guides
ADD COLUMN whitepaper_link TEXT;
