import React, { useState, useEffect, useCallback } from "react";
import { supabase } from "../lib/supabase";
import { useParams, useNavigate } from "react-router-dom";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as Syntax<PERSON><PERSON>lighter } from "react-syntax-highlighter";
import { atomDark } from "react-syntax-highlighter/dist/cjs/styles/prism";
import rehypeRaw from "rehype-raw";
import {
  ArrowLeft,
  CircleOff,
  MessageSquare,
  Pen,
  TimerIcon,
} from "lucide-react";
import defaultImage from "../assets/images/default-image.jpg";

// Types
interface UserProfile {
  email: string;
  full_name: string;
}

interface BlogComment {
  id: string;
  content: string;
  status: "pending" | "approved" | "rejected";
  user: UserProfile;
  created_at: string;
}

interface BlogPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  status: "draft" | "published" | "archived";
  featured_image: string;
  created_at: string;
  comments: BlogComment[];
  type?: string;
}

// Component
const BlogDetails: React.FC = ({ site_settings }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [commentContent, setCommentContent] = useState<string>("");
  const [submittingComment, setSubmittingComment] = useState<boolean>(false);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  useEffect(() => {
    const checkAuth = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setIsAuthenticated(!!user);
    };
    checkAuth();
  }, []);

  /**
   * Fetch blog post with comments from Supabase
   */
  const fetchPost = useCallback(async () => {
    try {
      if (!id) throw new Error("Invalid post ID");

      const { data, error } = await supabase
        .from("blog_posts")
        .select(
          `
          *,
          comments:blog_comments(
            id,
            content,
            status,
            created_at,
            user:profiles(email, full_name)
          )
        `
        )
        .eq("id", id)
        .filter("comments.status", "in", '("approved","pending")')
        .single();

      if (error) throw error;
      if (!data) throw new Error("Post not found");

      setPost(data);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while fetching the post"
      );
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchPost();
  }, [fetchPost]);

  /**
   * Handle comment submission
   */
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmittingComment(true);
    setError(null);

    try {
      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser();
      const { data: userRole } = await supabase
        .from("profiles")
        .select("role")
        .eq("id", user?.id)
        .single();

      if (authError || !user || !userRole)
        throw new Error("Authentication required to comment");

      const { error: insertError } = await supabase
        .from("blog_comments")
        .insert({
          content: commentContent,
          post_id: id,
          user_id: user.id,
          status: userRole.role === "admin" ? "approved" : "pending",
        });

      if (insertError) throw insertError;

      // Refresh comments
      await fetchPost();
      setCommentContent("");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to submit comment");
    } finally {
      setSubmittingComment(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-50 text-red-600 p-4 rounded-lg">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  // Not found state
  if (!post) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-yellow-50 text-yellow-600 p-4 rounded-lg">
          <p className="font-medium">Not Found</p>
          <p>The requested blog post could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className=" bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <button
          onClick={() => {
            // Navigate back to the appropriate section based on post type
            if (post.type === "news") {
              navigate("/news");
            } else {
              navigate("/blog");
            }
          }}
          className="flex items-center text-blue-600 hover:text-blue-800 transition-colors mb-8"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          <span className="font-medium">
            Back to {post.type === "news" ? "News" : "Blog"}
          </span>
        </button>

        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {post.featured_image ? (
            <div className="relative h-96 w-full">
              <img
                src={post.featured_image}
                alt={post.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            </div>
          ) : (
            <div className="relative h-96 w-full">
              <img
                src={defaultImage}
                alt="Default Case Study"
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <div className="p-8 md:p-10 relative">
            <div className="flex items-center mb-4 absolute right-3">
              <span className="flex items-center gap-2 text-sm rounded-full bg-gray-500 px-2 py-1 text-white">
                <TimerIcon className="w-4 h-4" />
                {new Date(post.created_at).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </span>
            </div>

            <h1 className="text-4xl font-bold mt-4 mb-6 text-gray-900">
              {post.title}
            </h1>

            <div className="prose max-w-none">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
                components={{
                  h1: ({ children }) => (
                    <h1 className="text-3xl font-bold mt-10 mb-6">
                      {children}
                    </h1>
                  ),
                  h2: ({ children }) => (
                    <h2 className="text-2xl font-bold mt-8 mb-4 border-b pb-2">
                      {children}
                    </h2>
                  ),
                  h3: ({ children }) => (
                    <h3 className="text-xl font-semibold mt-6 mb-3">
                      {children}
                    </h3>
                  ),
                  p: ({ children }) => (
                    <p className="mb-6 text-gray-700 leading-relaxed">
                      {children}
                    </p>
                  ),
                  ul: ({ children }) => (
                    <ul className="list-disc pl-8 mb-6 space-y-2">
                      {children}
                    </ul>
                  ),
                  ol: ({ children }) => (
                    <ol className="list-decimal pl-8 mb-6 space-y-2">
                      {children}
                    </ol>
                  ),
                  li: ({ children }) => <li className="mb-2">{children}</li>,
                  blockquote: ({ children }) => (
                    <blockquote className="border-l-4 border-blue-500 pl-4 italic bg-gray-50 py-2 mb-6 text-gray-600">
                      {children}
                    </blockquote>
                  ),
                  a: ({ children, href }) => (
                    <a
                      href={href}
                      className="text-blue-600 hover:text-blue-800 underline"
                    >
                      {children}
                    </a>
                  ),
                  img: ({ src, alt }) => (
                    <img
                      src={src}
                      alt={alt}
                      className="my-6 rounded-lg shadow-md max-w-full h-auto"
                    />
                  ),
                  table: ({ children }) => (
                    <table className="w-full mb-6 border-collapse">
                      {children}
                    </table>
                  ),
                  th: ({ children }) => (
                    <th className="py-2 px-4 border bg-gray-100 font-semibold text-left">
                      {children}
                    </th>
                  ),
                  td: ({ children }) => (
                    <td className="py-2 px-4 border">{children}</td>
                  ),
                  code: ({ inline, className, children }) => {
                    const match = /language-(\w+)/.exec(className || "");
                    return !inline ? (
                      <SyntaxHighlighter
                        style={atomDark}
                        language={match?.[1] || "javascript"}
                        className="rounded-lg mb-6 text-sm"
                        showLineNumbers
                        wrapLines
                      >
                        {String(children).replace(/\n$/, "")}
                      </SyntaxHighlighter>
                    ) : (
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                        {children}
                      </code>
                    );
                  },
                  u: ({ children }) => <u className="underline">{children}</u>,
                }}
              >
                {
                  // Preprocess to convert ++text++ to <u>text</u>
                  post.content.replace(/\+\+([^\+]+)\+\+/g, "<u>$1</u>")
                }
              </ReactMarkdown>
            </div>

            {/* Comments Section */}
            <section className="mt-16 border-t pt-10">
              <div className="flex items-center mb-8">
                <MessageSquare className="h-6 w-6 mr-3 text-gray-700" />
                <h2 className="text-2xl font-semibold text-gray-900">
                  Comments ({post.comments.length})
                </h2>
              </div>

              <div className="space-y-6 mb-10">
                {post.comments.map((comment) => (
                  <div key={comment.id} className="bg-gray-50 p-6 rounded-lg">
                    <div className="flex justify-between items-start mb-3">
                      <div className="font-medium text-gray-900">
                        {comment.user.full_name || comment.user.email}
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(comment.created_at).toLocaleDateString()}
                      </div>
                    </div>
                    <p className="text-gray-700">{comment.content}</p>
                  </div>
                ))}
              </div>
              {site_settings?.features.commentsEnabled ? (
                isAuthenticated ? (
                  <form onSubmit={handleSubmitComment} className="space-y-6">
                    <div>
                      <label className="block text-lg font-medium text-gray-900 mb-3">
                        Leave a comment
                      </label>
                      <textarea
                        rows={5}
                        value={commentContent}
                        onChange={(e) => setCommentContent(e.target.value)}
                        className="w-full px-5 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700"
                        placeholder="Share your thoughts..."
                        required
                      />
                    </div>
                    <button
                      type="submit"
                      disabled={submittingComment}
                      className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {submittingComment ? "Submitting..." : "Post Comment"}
                    </button>
                  </form>
                ) : (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
                    <p className="text-blue-800 font-medium mb-2">
                      Want to leave a comment?
                    </p>
                    <button
                      onClick={() => navigate("/login")}
                      className="text-blue-600 hover:text-blue-800 font-semibold"
                    >
                      Sign in to comment
                    </button>
                  </div>
                )
              ) : (
                <>
                  <span className="w-full flex items-center justify-center gap-2 bg-red-300 text-white py-2 font-semibold rounded-md">
                    <CircleOff className="w-5 h-5" />
                    Comments Are Disabled
                  </span>
                </>
              )}
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogDetails;
