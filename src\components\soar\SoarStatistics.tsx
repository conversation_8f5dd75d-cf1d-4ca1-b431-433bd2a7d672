import React from 'react'

const SoarStatistics = () => {
  return (
        <div className="max-w-[2000px] mx-auto flex flex-col sm:flex-row gap-4 sm:gap-6 py-5">
          {/* Prediction Accuracy */}
          <div className="flex-1 bg-gradient-to-br from-[#1a1a2e] from-0% via-[#1a1a2e] via-45% to-orange-400 to-100% rounded-8 py-6 px-4 text-white">
            <div
              className="text-white w-full mb-3 text-xl md:text-[44px]"
              style={{
                fontFamily: 'Zenith Trial',
                fontWeight: 400,
                fontStyle: 'italic',
                letterSpacing: '0%',
                verticalAlign: 'middle'
              }}
            >
              94.8%
            </div>
            <div
              className="text-white text-base sm:text-lg md:text-xl"
              style={{
                fontFamily: 'Inter, system-ui, sans-serif',
                fontWeight: 700,
                lineHeight: '1.21em',
                letterSpacing: '-1%'
              }}
            >
              Prediction
            </div>
          </div>

          {/* Data Points */}
          <div className="flex-1 bg-gradient-to-br from-[#1a1a2e] from-0% via-[#1a1a2e] via-45% to-orange-400 to-100% rounded-8 py-6 px-4  text-white">
            <div
              className="text-white w-full mb-3 text-xl md:text-[44px]"
              style={{
                fontFamily: 'Zenith Trial',
                fontWeight: 400,
                fontStyle: 'italic',
                letterSpacing: '0%',
                verticalAlign: 'middle'
              }}
            >
              1.2M+
            </div>
            <div
              className="text-white text-base sm:text-lg md:text-xl"
              style={{
                fontFamily: 'Inter, system-ui, sans-serif',
                fontWeight: 700,
                lineHeight: '1.21em',
                letterSpacing: '-1%'
              }}
            >
              Data Points
            </div>
          </div>

          {/* Real-time Response */}
          <div className="flex-1 bg-gradient-to-br from-[#1a1a2e] from-0% via-[#1a1a2e] via-45% to-orange-400 to-100% rounded-8 py-6 px-4 text-white">
            <div
              className="text-white w-full mb-3 text-xl md:text-[44px]"
              style={{
                fontFamily: 'Zenith Trial',
                fontWeight: 400,
                fontStyle: 'italic',
                letterSpacing: '0%',
                verticalAlign: 'middle'
              }}
            >
              Real-time
            </div>
            <div
              className="text-white text-base sm:text-lg md:text-xl"
              style={{
                fontFamily: 'Inter, system-ui, sans-serif',
                fontWeight: 700,
                lineHeight: '1.21em',
                letterSpacing: '-1%'
              }}
            >
              Response
            </div>
          </div>
        </div>
  )
}

export default SoarStatistics