import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { FileText, Trash2, Edit, Plus, MessageSquare, Check, X, Image as ImageIcon, AlertCircle, Eye } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import MdEditor from 'react-markdown-editor-lite';
import 'react-markdown-editor-lite/lib/index.css';
import { useNavigate } from 'react-router-dom';

interface BlogPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  status: 'draft' | 'published' | 'archived';
  featured_image: string;
  created_at: string;
  comments: BlogComment[];
  type: string;
}

interface BlogComment {
  id: string;
  content: string;
  status: 'pending' | 'approved' | 'rejected';
  user: {
    email: string;
    full_name: string;
  };
  created_at: string;
}

interface StatusBadgeProps {
  status: 'draft' | 'published' | 'archived';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const statusStyles = {
    draft: 'bg-yellow-100 text-yellow-800',
    published: 'bg-green-100 text-green-800',
    archived: 'bg-gray-100 text-gray-800'
  };

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusStyles[status]}`}>
      {status}
    </span>
  );
};

interface TypeBadgeProps {
  type: string;
}

const TypeBadge: React.FC<TypeBadgeProps> = ({ type }) => {
  const typeStyles = {
    blog: 'bg-purple-100 text-purple-800',
    news: 'bg-blue-100 text-blue-800',
    default: 'bg-gray-100 text-gray-800'
  };

  const style = typeStyles[type as keyof typeof typeStyles] || typeStyles.default;

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full ${style}`}>
      {type || 'blog'}
    </span>
  );
};

interface CommentButtonProps {
  count: number;
  onClick: () => void;
}

const CommentButton: React.FC<CommentButtonProps> = ({ count, onClick }) => (
  <button
    onClick={onClick}
    className="flex items-center text-gray-600 hover:text-gray-900"
  >
    <MessageSquare className="h-5 w-5 mr-2" />
    {count}
  </button>
);

interface ActionButtonProps {
  icon: React.ReactNode;
  onClick: () => void;
  title: string;
  color: 'gray' | 'blue' | 'red';
}

const ActionButton: React.FC<ActionButtonProps> = ({ icon, onClick, title, color }) => {
  const colorClasses = {
    gray: 'text-gray-600 hover:text-gray-900',
    blue: 'text-blue-600 hover:text-blue-900',
    red: 'text-red-600 hover:text-red-900'
  };

  return (
    <button
      onClick={onClick}
      className={colorClasses[color]}
      title={title}
    >
      {icon}
    </button>
  );
};

interface CommentItemProps {
  comment: BlogComment;
  onApprove: () => void;
  onReject: () => void;
}

const CommentItem: React.FC<CommentItemProps> = ({ comment, onApprove, onReject }) => (
  <div className="flex items-start justify-between bg-white p-4 rounded-lg shadow-sm">
    <div className="flex-1">
      <div className="flex justify-between items-start mb-2">
        <div className="font-medium text-sm">
          {comment.user.full_name || comment.user.email}
        </div>
        <div className="text-xs text-gray-500">
          {new Date(comment.created_at).toLocaleString()}
        </div>
      </div>
      <p className="text-gray-600 text-sm">{comment.content}</p>
    </div>
    <div className="ml-4 flex items-center space-x-2">
      {comment.status === 'pending' && (
        <>
          <button
            onClick={onApprove}
            className="text-green-600 hover:text-green-700"
            title="Approve"
          >
            <Check className="h-5 w-5" />
          </button>
          <button
            onClick={onReject}
            className="text-red-600 hover:text-red-700"
            title="Reject"
          >
            <X className="h-5 w-5" />
          </button>
        </>
      )}
      <span className={`px-2 py-1 text-xs rounded-full ${comment.status === 'approved'
        ? 'bg-green-100 text-green-800'
        : comment.status === 'rejected'
          ? 'bg-red-100 text-red-800'
          : 'bg-yellow-100 text-yellow-800'
        }`}>
        {comment.status}
      </span>
    </div>
  </div>
);

const BlogManagement: React.FC = () => {
  const navigate = useNavigate();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [selectedPost, setSelectedPost] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    featured_image: '',
    status: 'draft' as const,
    type: 'blog' as string
  });
  const [editingId, setEditingId] = useState<string | null>(null);
  const [imageUploading, setImageUploading] = useState(false);

  // Update pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [postsPerPage, setPostsPerPage] = useState(10);
  const [totalPosts, setTotalPosts] = useState(0);

  useEffect(() => {
    fetchPosts();
  }, [currentPage, postsPerPage]); // Add postsPerPage as dependency

  const fetchPosts = async () => {
    try {
      const { data: posts, error: postsError, count } = await supabase
        .from('blog_posts')
        .select(`
          *,
          comments:blog_comments(
            id,
            content,
            status,
            created_at,
            user:profiles(email, full_name)
          )
        `, { count: 'exact' })
        .order('created_at', { ascending: false })
        .range((currentPage - 1) * postsPerPage, currentPage * postsPerPage - 1);

      if (postsError) throw postsError;

      const postsWithImages = posts?.map(post => ({
        ...post,
        featured_image: post.featured_image || null
      })) || [];

      setPosts(postsWithImages);
      setTotalPosts(count || 0);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setImageUploading(true);
      setError(null);

      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Please upload a valid image file (JPG, PNG, GIF, or WebP)');
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('Image size should be less than 5MB');
      }

      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const fileName = `${Math.random().toString(36).substring(2, 9)}-${Date.now()}.${fileExt}`;

      // Make sure the bucket name matches your Supabase storage bucket
      const bucketName = 'blog-images';
      const filePath = `posts/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) throw uploadError;

      // Get public URL with proper cache busting
      const { data: { publicUrl } } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath, {
          download: false
        });

      setFormData(prev => ({
        ...prev,
        featured_image: publicUrl
      }));
    } catch (err: any) {
      setError(err.message);
    } finally {
      setImageUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      const { title, content, excerpt, featured_image, status, type } = formData;
      const { data: { user } } = await supabase.auth.getUser();

      if (!title.trim() || !content.trim()) {
        throw new Error('Title and content are required');
      }

      const postData = {
        title: title.trim(),
        content: content.trim(),
        excerpt: excerpt.trim() || content.trim().substring(0, 200) + '...',
        featured_image: featured_image || null,
        status,
        type: type || 'blog',
        author_id: user?.id
      };

      if (editingId) {
        const { error } = await supabase
          .from('blog_posts')
          .update(postData)
          .eq('id', editingId);

        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('blog_posts')
          .insert([postData]);

        if (error) throw error;
      }

      setFormData({
        title: '',
        content: '',
        excerpt: '',
        featured_image: '',
        status: 'draft',
        type: 'blog'
      });
      setShowForm(false);
      setEditingId(null);
      fetchPosts();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleEdit = (post: BlogPost) => {
    setFormData({
      title: post.title,
      content: post.content,
      excerpt: post.excerpt || '',
      featured_image: post.featured_image || '',
      status: post.status,
      type: post.type || 'blog'
    });
    setEditingId(post.id);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this post?')) return;

    try {
      const post = posts.find(p => p.id === id);
      if (post?.featured_image) {
        const urlParts = post.featured_image.split('/');
        const bucketName = 'blog-images';
        const filePath = urlParts.slice(urlParts.indexOf('posts')).join('/');

        const { error: storageError } = await supabase.storage
          .from(bucketName)
          .remove([filePath]);

        if (storageError) console.error('Error deleting image:', storageError);
      }

      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', id)
        .select();

      if (error) throw error;

      fetchPosts();
    } catch (err: any) {
      setError('You do not have permission to delete this post or another error occurred');
      console.error('Delete error:', err);
    }
  };

  const handleCommentStatus = async (commentId: string, status: 'approved' | 'rejected') => {
    try {
      const { error } = await supabase
        .from('blog_comments')
        .update({ status })
        .eq('id', commentId);

      if (error) throw error;
      fetchPosts();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Blog & News</h2>
        <button
          onClick={() => {
            setShowForm(true);
            setEditingId(null);
            setFormData({
              title: '',
              content: '',
              excerpt: '',
              featured_image: '',
              status: 'draft',
              type: 'blog'
            });
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          New Article
        </button>
      </div>

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}

      {showForm && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-4">
            {editingId ? 'Edit Article' : 'Add New Article'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title
              </label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Content
              </label>
              <MdEditor
                name="content"
                value={formData.content}
                onChange={({ text }) => setFormData({ ...formData, content: text })}
                style={{ height: '400px' }}
                className="w-full border rounded-lg"
                renderHTML={(text) => (
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    className="prose max-w-none p-4"
                  >
                    {text}
                  </ReactMarkdown>
                )}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Excerpt
              </label>
              <textarea
                rows={3}
                value={formData.excerpt}
                onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Optional brief summary of the post"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Featured Image
              </label>
              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="flex items-center px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50"
                >
                  <ImageIcon className="h-5 w-5 mr-2 text-gray-500" />
                  <span>{imageUploading ? 'Uploading...' : 'Upload Image'}</span>
                </label>
                {formData.featured_image && (
                  <img
                    src={formData.featured_image}
                    alt="Preview"
                    className="h-20 w-20 object-cover rounded-lg"
                  />
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as 'draft' | 'published' | 'archived' })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="blog">Blog</option>
                  <option value="news">News</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setEditingId(null);
                }}
                className="text-gray-600 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700"
              >
                {editingId ? 'Update Article' : 'Create Article'}
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-6(py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5">
                Post
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                Status
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                Type
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                Comments
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                Created
              </th>
              <th className="px-6 py-3 bg-gray-50 w-1/12"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {posts.length > 0 ? (
              posts.map((post) => (
                <React.Fragment key={post.id}>
                  <tr className="hover:bg-gray-50 transition-colors duration-150">
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-4">
                        {post.featured_image ? (
                          <div className="flex-shrink-0">
                            <img
                              src={post.featured_image}
                              alt={post.title}
                              className="h-12 w-12 rounded-lg object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.onerror = null;
                                target.src = '';
                                target.classList.add('hidden');
                              }}
                            />
                          </div>
                        ) : (
                          <div className="flex-shrink-0">
                            <div className="h-12 w-12 rounded-lg bg-gray-100 flex items-center justify-center">
                              <FileText className="h-6 w-6 text-gray-400" />
                            </div>
                          </div>
                        )}
                        <div className="min-w-0 flex-1">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {post.title}
                          </h3>
                          <p className="text-sm text-gray-500 mt-1">
                            {post.excerpt || truncateText(post.content, 60)}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge status={post.status} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <TypeBadge type={post.type} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <CommentButton
                        count={post.comments.length}
                        onClick={() => setSelectedPost(selectedPost === post.id ? null : post.id)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(post.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end space-x-3">
                        <ActionButton
                          icon={<Eye className="h-5 w-5" />}
                          onClick={() => navigate(`/${post.type === 'news' ? 'news' : 'blog'}/${post.id}`)}
                          title="Preview"
                          color="gray"
                        />
                        <ActionButton
                          icon={<Edit className="h-5 w-5" />}
                          onClick={() => handleEdit(post)}
                          title="Edit"
                          color="blue"
                        />
                        <ActionButton
                          icon={<Trash2 className="h-5 w-5" />}
                          onClick={() => handleDelete(post.id)}
                          title="Delete"
                          color="red"
                        />
                      </div>
                    </td>
                  </tr>
                  {selectedPost === post.id && post.comments.length > 0 && (
                    <tr>
                      <td colSpan={6} className="px-6 py-4">
                        <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">
                            Comments ({post.comments.length})
                          </h4>
                          {post.comments.map((comment) => (
                            <CommentItem
                              key={comment.id}
                              comment={comment}
                              onApprove={() => handleCommentStatus(comment.id, 'approved')}
                              onReject={() => handleCommentStatus(comment.id, 'rejected')}
                            />
                          ))}
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="px-6 py-8 text-center">
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <FileText className="h-12 w-12 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900">
                      No posts found
                    </h3>
                    <p className="text-sm text-gray-500">
                      Get started by creating a new blog post
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Update Pagination Controls with Items per Page Selector */}
      <div className="flex items-center justify-between px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Showing {(currentPage - 1) * postsPerPage + 1} to {Math.min(currentPage * postsPerPage, totalPosts)} of {totalPosts} posts
          </div>
          <div className="flex items-center">
            <label className="text-sm text-gray-500 mr-2">Items per page:</label>
            <select
              value={postsPerPage}
              onChange={(e) => {
                setPostsPerPage(Number(e.target.value));
                setCurrentPage(1); // Reset to first page when changing items per page
              }}
              className="px-2 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>

            </select>
          </div>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => {
              if (currentPage > 1) {
                setCurrentPage(currentPage - 1);
              }
            }}
            disabled={currentPage === 1}
            className={`px-4 py-2 border rounded-lg ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'}`}
          >
            Previous
          </button>
          <button
            onClick={() => {
              if (currentPage * postsPerPage < totalPosts) {
                setCurrentPage(currentPage + 1);
              }
            }}
            disabled={currentPage * postsPerPage >= totalPosts}
            className={`px-4 py-2 border rounded-lg ${currentPage * postsPerPage >= totalPosts ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'}`}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default BlogManagement;