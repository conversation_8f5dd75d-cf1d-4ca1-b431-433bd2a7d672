import 'jsr:@supabase/functions-js/edge-runtime.d.ts';
import Stripe from 'npm:stripe@17.7.0';
import { createClient } from 'npm:@supabase/supabase-js@2.49.1';
import nodemailer from 'npm:nodemailer';
// Stripe and Supabase Setup
const stripeSecret = Deno.env.get('STRIPE_SECRET_KEY');
const stripeWebhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET');
if (!stripeWebhookSecret) {
  throw new Error('STRIPE_WEBHOOK_SECRET environment variable not set');
}
const stripe = new Stripe(stripeSecret, {
  appInfo: {
    name: 'IRS Integration',
    version: '1.0.0'
  }
});
const supabase = createClient(Deno.env.get('SUPABASE_URL'), Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'), {
  global: {
    headers: {
      Authorization: `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
    }
  }
});
// Main Handler
Deno.serve(async (req)=>{
  try {
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Stripe-Signature'
        }
      });
    }
    if (req.method !== 'POST') {
      return new Response('Method not allowed', {
        status: 405
      });
    }
    const signature = req.headers.get('stripe-signature');
    if (!signature) {
      return new Response('No signature found', {
        status: 400
      });
    }
    const body = await req.text();
    let event;
    try {
      event = await stripe.webhooks.constructEventAsync(body, signature, stripeWebhookSecret);
    } catch (error) {
      console.error(`Webhook signature verification failed: ${error.message}`);
      return new Response(`Webhook error: ${error.message}`, {
        status: 403,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    EdgeRuntime.waitUntil(handleEvent(event));
    return Response.json({
      received: true
    });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return Response.json({
      error: error.message
    }, {
      status: 500
    });
  }
});

// Handle Stripe Event
async function handleEvent(event: Stripe.Event) { // Add type annotation for event
  console.log('Received Stripe event type:', event.type);

  // --- Handle Checkout Session Completion ---
  if (event.type === 'checkout.session.completed') {
    const session = event.data.object as Stripe.Checkout.Session; // Type assertion
    const userId = session.metadata?.user_id;
    if (!userId) {
      console.error('Missing user_id in session metadata');
      return;
    }
    try {
      if (session.mode === 'subscription' && session.subscription) { // Ensure subscription exists
        const subscriptionId = typeof session.subscription === 'string' ? session.subscription : session.subscription.id;
        const subscription = await stripe.subscriptions.retrieve(subscriptionId);
        const lineItems = await stripe.checkout.sessions.listLineItems(session.id, {
          expand: [
            'data.price.product'
          ]
        });

        // --- Map Stripe Price IDs to your Product IDs ---
        // Create a map for easier lookup later
        const priceIdToProductIdMap = new Map<string, string>();
        const productIdsForEmail: string[] = []; 

        for (const item of lineItems.data) {
          if (item.price?.id) {
            const productId = await getProductIdByPriceId(item.price.id);
            if (productId !== 'unknown') {
              priceIdToProductIdMap.set(item.price.id, productId);
              productIdsForEmail.push(productId); 
            } else {
              console.error(`Could not map Stripe price ID ${item.price.id} to a database product ID.`);
              throw new Error(`Could not map Stripe price ID ${item.price.id} to a database product ID.`);
            }
          }
        }

        if (priceIdToProductIdMap.size === 0 && lineItems.data.length > 0) {
           console.error(`Failed to map any product IDs for Stripe subscription ${subscription.id}`);
           throw new Error('Could not determine any valid product IDs for the subscription.');
        }
        // --- End Mapping ---


        // --- Derive Timestamps ---
        const now = new Date();
        const derivedCurrentPeriodStart = now;
        const derivedCreatedAt = now;
        let derivedCurrentPeriodEnd = new Date(now); // Start with now

        const interval = subscription.items.data[0]?.price?.recurring?.interval;
        const intervalCount = subscription.items.data[0]?.price?.recurring?.interval_count ?? 1;

        if (interval === 'month') {
          derivedCurrentPeriodEnd.setMonth(derivedCurrentPeriodEnd.getMonth() + intervalCount);
        } else if (interval === 'year') {
          derivedCurrentPeriodEnd.setFullYear(derivedCurrentPeriodEnd.getFullYear() + intervalCount);
        } else {
          // Fallback or error if interval is unknown or not set
          console.warn(`Subscription ${subscription.id} has an unknown or missing interval: ${interval}. Cannot accurately calculate period end.`);
          // Set a default fallback, e.g., one month, or handle as an error
          derivedCurrentPeriodEnd.setMonth(derivedCurrentPeriodEnd.getMonth() + 1);
        }
        // --- End Derive Timestamps ---


        // Prepare subscription data using derived timestamps
        const subscriptionData: {
          stripe_subscription_id: string;
          user_id: string;
          status: string;
          period: string;
          current_period_start: string; // Use derived value
          current_period_end: string;   // Use derived value
          // cancel_at_period_end: boolean; // Still use value from Stripe object
          created_at: string;           // Use derived value
          ref: string;
          quantity: number;
        } = {
          stripe_subscription_id: subscription.id,
          user_id: userId,
          status: subscription.status.toLowerCase(),
          period: interval ?? 'unknown', // Use the determined interval
          // cancel_at_period_end: subscription.cancel_at_period_end, // Get this from Stripe
          ref: crypto.randomUUID().split('-')[0],
          quantity: subscription.items.data[0]?.quantity ?? 1,
          // Assign derived ISO strings
          current_period_start: derivedCurrentPeriodStart.toISOString(),
          current_period_end: derivedCurrentPeriodEnd.toISOString(),
          created_at: derivedCreatedAt.toISOString(),
        };


        if (subscriptionData.period === 'unknown') {
            // This check might be redundant now if we handle the interval above, but keep for safety
            console.error(`Could not determine subscription period for Stripe ID: ${subscription.id}`);
            throw new Error(`Could not determine subscription period for Stripe ID: ${subscription.id}`);
        }

        console.log(`Upserting subscription data for Stripe ID: ${subscription.id}`, subscriptionData);

        // Use the prepared subscriptionData object for upsert
        const { data: upsertedSubscription, error: upsertError } = await supabase
          .from('subscriptions')
          .upsert(subscriptionData, { // Use the data with derived timestamps
            onConflict: 'stripe_subscription_id',
          })
          .select('id')
          .single();

        if (upsertError || !upsertedSubscription) {
          console.error(`Subscription upsert failed for Stripe ID ${subscription.id}:`, upsertError);
          throw new Error(`Failed to save subscription: ${upsertError?.message || 'Upsert returned no data'}`);
        }

        const dbSubscriptionId = upsertedSubscription.id;
        console.log(`Successfully upserted subscription ${dbSubscriptionId} for Stripe ID: ${subscription.id}`);

        // --- Insert Line Items ---
        // First, delete existing items for this subscription ID to handle updates/changes
        // It's simpler to delete and re-insert than to diff and update/delete/insert individually
        console.log(`Deleting existing items for subscription ${dbSubscriptionId} before inserting new ones.`);
        const { error: deleteError } = await supabase
            .from('subscription_items')
            .delete()
            .eq('subscription_id', dbSubscriptionId);

        if (deleteError) {
            console.error(`Failed to delete existing items for subscription ${dbSubscriptionId}:`, deleteError);
            // Decide if this is critical. Maybe log and continue?
        }

        const itemsToInsert = lineItems.data.map(item => {
          const productId = priceIdToProductIdMap.get(item.price!.id);
          if (!productId) {
            console.error(`Consistency error: Product ID for price ${item.price!.id} not found in map.`);
            return null; 
          }
          return {
            subscription_id: dbSubscriptionId, 
            product_id: productId,             
            stripe_price_id: item.price!.id,   
            stripe_subscription_item_id: item.id,
            status: 'active',
            quantity: item.quantity ?? 1,      
            amount: item.amount_total ? item.amount_total / 100 : item.price!.unit_amount / 100 * (item.quantity ?? 1), // Add amount field
          };
        }).filter(item => item !== null); 

        if (itemsToInsert.length > 0) {
          console.log(`Inserting ${itemsToInsert.length} items for subscription ${dbSubscriptionId}:`, itemsToInsert);
          const { error: itemInsertError } = await supabase
            .from('subscription_items')
            .insert(itemsToInsert as any); 

          if (itemInsertError) {
            console.error(`Failed to insert subscription items for subscription ${dbSubscriptionId}:`, itemInsertError);
          } else {
            console.log(`Successfully inserted items for subscription ${dbSubscriptionId}`);
          }
        }
        // --- End Insert Line Items ---

        // Update profile (remains the same)
        const { error: profileError } = await supabase.from('profiles').update({
          stripe_customer_id: typeof session.customer === 'string' ? session.customer : session.customer?.id // Handle customer object or string ID
        }).eq('id', userId);
        if (profileError) console.error('Customer ID update failed:', profileError);
        
        console.log(`Clearing cart for user: ${userId}`);
        const { error: cartError } = await supabase.from('user_carts').delete().eq('user_id', userId);
        if (cartError) {
          console.error('Cart clearance failed:', cartError);
        }
        
        const email = session.customer_details?.email || session.customer_email;
        if (email) {
          // Pass the original subscription object, but the email function will need to handle potentially missing dates
          await sendEmailToClient(email, subscription, productIdsForEmail, {
              start: derivedCurrentPeriodStart,
              end: derivedCurrentPeriodEnd
          });
        }
      } else if (session.mode === 'payment') {
          // Handle one-time payments if necessary
          console.log('Checkout session completed for a one-time payment.');
          // Add logic for one-time payments here
      }
    } catch (err) {
      // Add specific check for RangeError here too
       if (err instanceof RangeError && err.message.includes('Invalid time value')) {
          console.error(`Error in checkout.session.completed handler (Invalid time value likely from subscription timestamps):`, err);
      } else {
          console.error('Error in checkout.session.completed handler:', err);
      }
    }
  }
  // --- Handle Subscription Updates (Cancellation Pending, Reactivation, etc.) ---
  else if (event.type === 'customer.subscription.updated') {
    const subscription = event.data.object as Stripe.Subscription; // Type assertion
    console.log(`Handling subscription update for Stripe ID: ${subscription.id}`);

    // Determine the subscription status based on Stripe data
    let newStatus = subscription.status.toLowerCase();
    if (subscription.cancel_at_period_end && newStatus !== 'canceled') {
      newStatus = 'pending cancellation';
      console.log(`Subscription ${subscription.id} marked for cancellation at period end. Setting status to 'pending cancellation'.`);
    } else if (!subscription.cancel_at_period_end && newStatus === 'active') {
      console.log(`Subscription ${subscription.id} potentially reactivated. Setting status to 'active'.`);
      newStatus = 'active';
    } else if (newStatus === 'past_due') {
      newStatus = 'past due';
    }
    // Add other status mappings if needed

    try {
      // 1. Get the DB subscription ID
      const { data: dbSubscription, error: dbSubError } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('stripe_subscription_id', subscription.id)
        .single();

      if (dbSubError || !dbSubscription) {
        console.error(`Failed to find subscription in DB for Stripe ID ${subscription.id}:`, dbSubError);
        return; // Exit if we can't find the subscription
      }
      const dbSubscriptionId = dbSubscription.id;

      // --- NEW: Handle scheduled updates (pending_update) ---
      if (subscription.pending_update) {
        // Scheduled change exists, create or upsert a scheduled item for display
        const scheduledItems = subscription.pending_update.items.data;
        for (const item of scheduledItems) {
          const productId = await getProductIdByPriceId(item.price.id);
          if (productId === 'unknown') continue;
          // Upsert scheduled item by stripe_subscription_item_id
          await supabase
            .from('subscription_items')
            .upsert({
              subscription_id: dbSubscriptionId,
              product_id: productId,
              stripe_price_id: item.price.id,
              quantity: item.quantity ?? 1,
              status: 'scheduled',
              stripe_subscription_item_id: item.id,
            }, { onConflict: 'stripe_subscription_item_id' });
        }
      } else {
        // Remove scheduled items without matching active items
        const activeItemIds = new Set(subscription.items.data.map(item => item.id));
        
        const { data: scheduledItems } = await supabase
          .from('subscription_items')
          .select('id, stripe_subscription_item_id')
          .eq('subscription_id', dbSubscriptionId)
          .eq('status', 'scheduled');
        
        if (scheduledItems?.length) {
          for (const scheduledItem of scheduledItems) {
            if (!scheduledItem.stripe_subscription_item_id || 
                !activeItemIds.has(scheduledItem.stripe_subscription_item_id)) {
              await supabase
                .from('subscription_items')
                .delete()
                .eq('id', scheduledItem.id);
            }
          }
        }
      }

      // 2. Get current ACTIVE items from DB
      const { data: currentDbItems, error: dbItemsError } = await supabase
        .from('subscription_items')
        .select('id, stripe_price_id, quantity, product_id, stripe_subscription_item_id') // Select necessary fields including the item's own ID
        .eq('subscription_id', dbSubscriptionId)
        .eq('status', 'active'); // Only fetch active items

      if (dbItemsError) {
        console.error(`Failed to fetch current items for subscription ${dbSubscriptionId}:`, dbItemsError);
        return; // Exit if we can't get current state
      }

      // Create a map for easy lookup and tracking of processed items
      const dbItemsMap = new Map(currentDbItems.map(item => [item.stripe_price_id, item]));

      // 3. Prepare lists for DB operations
      const itemsToInsert: any[] = []; // Items to add with status 'active'
      const itemsToDeactivateIds: string[] = []; // IDs of items to mark 'inactive'
      let calculatedTotalQuantity = 0; // Recalculate based on incoming items

      // 4. Process incoming items from Stripe event
      for (const stripeItem of subscription.items.data) {
        const stripePriceId = stripeItem.price.id;
        const stripeQuantity = stripeItem.quantity ?? 1;
        calculatedTotalQuantity += stripeQuantity; // Sum quantity from incoming active items

        const existingDbItem = dbItemsMap.get(stripePriceId);

        if (existingDbItem) {
          // Item with this price ID exists and is active in DB
          if (existingDbItem.quantity !== stripeQuantity) {
            // Quantity changed: Deactivate old, create new
            console.log(`Quantity changed for item ${stripePriceId} (DB ID: ${existingDbItem.id}). Deactivating old, creating new.`);
            itemsToDeactivateIds.push(existingDbItem.id);
            // Prepare new item (product_id should be the same)
            itemsToInsert.push({
              subscription_id: dbSubscriptionId,
              product_id: existingDbItem.product_id, // Use existing product ID
              stripe_price_id: stripePriceId,
              quantity: stripeQuantity,
              status: 'active', // New item is active
              stripe_subscription_item_id: stripeItem.id, // Use the item's own ID
            });
          }
          // If quantity is the same, do nothing - it remains active.
          // Mark this item as processed by removing it from the map
          dbItemsMap.delete(stripePriceId);
        } else {
          // New item price ID (either a new item added or plan changed for an existing slot)
          console.log(`New item or plan change detected for price ${stripePriceId}. Creating new active item.`);
          // Look up product ID for the new price ID
          const productId = await getProductIdByPriceId(stripePriceId);
          if (productId === 'unknown') {
            console.error(`Could not find product ID for new Stripe Price ID: ${stripePriceId}. Skipping item insertion.`);
            // Potentially throw an error or handle differently
            continue; // Skip this item if product mapping fails
          }
          // For new items in customer.subscription.updated event
          itemsToInsert.push({
            subscription_id: dbSubscriptionId,
            product_id: productId,
            stripe_price_id: stripePriceId,
            quantity: stripeQuantity,
            status: 'active',
            stripe_subscription_item_id: stripeItem.id,
            amount: stripeItem.price.unit_amount / 100 * stripeQuantity, // Add amount field
          });
        }
      }

      // 5. Handle items removed from Stripe subscription
      // Any items left in dbItemsMap were active in DB but not in the incoming Stripe data
      for (const leftoverDbItem of dbItemsMap.values()) {
        console.log(`Item ${leftoverDbItem.stripe_price_id} (DB ID: ${leftoverDbItem.id}) removed from subscription. Deactivating.`);
        itemsToDeactivateIds.push(leftoverDbItem.id);
      }

      // 6. Perform DB Updates for items

      // Deactivate items
      if (itemsToDeactivateIds.length > 0) {
        console.log(`Deactivating ${itemsToDeactivateIds.length} subscription items:`, itemsToDeactivateIds);
        const { error: deactivateError } = await supabase
          .from('subscription_items')
          .update({ status: 'inactive' })
          .in('id', itemsToDeactivateIds); // Use the primary ID of the subscription_items row

        if (deactivateError) {
          console.error(`Failed to deactivate items for subscription ${dbSubscriptionId}:`, deactivateError);
          // Consider implications - might lead to duplicate active items if insert succeeds
        }
      }

      // Insert new items
      if (itemsToInsert.length > 0) {
        console.log(`Inserting ${itemsToInsert.length} new subscription items:`, itemsToInsert);
        const { error: insertError } = await supabase
          .from('subscription_items')
          .insert(itemsToInsert);

        if (insertError) {
          console.error(`Failed to insert new items for subscription ${dbSubscriptionId}:`, insertError);
          // Consider implications - state might be inconsistent
        }
      }

      // 7. Prepare update data for the main subscriptions table
      const updateData: {
        status: string;
        cancel_at_period_end: boolean;
        current_period_end?: string | null;
        quantity: number; // Use the calculated total quantity from incoming items
        period?: string; // Also update period if it can change
      } = {
        status: newStatus,
        cancel_at_period_end: subscription.cancel_at_period_end,
        quantity: calculatedTotalQuantity, // Update with the sum of current Stripe item quantities
        period: subscription.items.data[0]?.price?.recurring?.interval ?? undefined, // Update period
      };

      // Only add current_period_end if it's a valid number from Stripe event
      if (typeof subscription.current_period_end === 'number') {
        updateData.current_period_end = new Date(subscription.current_period_end * 1000).toISOString();
      } else {
        console.warn(`Subscription ${subscription.id} update event received with invalid current_period_end: ${subscription.current_period_end}. Skipping update for this field.`);
      }

      console.log(`Attempting to update main subscription ${subscription.id} (DB ID: ${dbSubscriptionId}) in DB with data:`, updateData);

      // 8. Update the main subscriptions table
      const { error: mainSubUpdateError } = await supabase
        .from('subscriptions')
        .update(updateData)
        .eq('id', dbSubscriptionId); // Use the primary DB ID for the update

      if (mainSubUpdateError) {
        console.error(`Failed to update main subscription details for ${subscription.id} (DB ID: ${dbSubscriptionId}):`, mainSubUpdateError);
      } else {
        console.log(`Successfully updated main subscription ${subscription.id} (DB ID: ${dbSubscriptionId}).`);
      }
    } catch (err) {
      // ... existing error handling ...
      if (err instanceof RangeError && err.message.includes('Invalid time value')) {
          console.error(`Error updating subscription in database (Invalid time value likely from current_period_end: ${subscription.current_period_end}):`, err);
      } else {
          console.error('Error updating subscription in database:', err);
      }
    }
  }
  // --- Handle Subscription Deletion (Final Cancellation) ---
  else if (event.type === 'customer.subscription.deleted') {
    const subscription = event.data.object as Stripe.Subscription;
    try {
        // First get the DB subscription ID
        const { data: dbSubscription, error: dbSubError } = await supabase
            .from('subscriptions')
            .select('id')
            .eq('stripe_subscription_id', subscription.id)
            .single();

        if (!dbSubError && dbSubscription) {
            const dbSubscriptionId = dbSubscription.id;
            // Deactivate all items associated with this subscription
            const { error: itemDeactivateError } = await supabase
                .from('subscription_items')
                .update({ status: 'canceled' })
                .eq('subscription_id', dbSubscriptionId);

            if (itemDeactivateError) {
                console.error(`Failed to deactivate items during subscription deletion for ${dbSubscriptionId}:`, itemDeactivateError);
            } else {
                 console.log(`Deactivated items for deleted subscription ${dbSubscriptionId}.`);
            }
        } else {
             console.warn(`Could not find DB subscription ${subscription.id} to deactivate items during deletion.`);
        }

        // Now update the main subscription status
        const { error: mainSubUpdateError } = await supabase
            .from('subscriptions')
            .update({
                status: 'canceled',
                cancel_at_period_end: true,
                current_period_end: new Date().toISOString(),
            })
            .eq('stripe_subscription_id', subscription.id);

        if (mainSubUpdateError) {
            console.error(`Failed to mark subscription ${subscription.id} as canceled:`, mainSubUpdateError);
        } else {
            console.log(`Successfully marked subscription ${subscription.id} as 'canceled'.`);
        }
    } catch (err) {
        console.error('Error processing subscription deletion:', err);
    }
  }
  // --- Handle Subscription Creation ---
  else if (event.type === 'customer.subscription.created') {
    const subscription = event.data.object as Stripe.Subscription;
    console.log(`Handling subscription creation for Stripe ID: ${subscription.id}`);
    
    try {
      // First check if this subscription already exists in our database
      const { data: existingSubscription } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('stripe_subscription_id', subscription.id)
        .maybeSingle();
      
      if (existingSubscription) {
        console.log(`Subscription ${subscription.id} already exists in database, skipping creation`);
        return;
      }
      
      // Get customer ID and find the associated user
      const customerId = subscription.customer as string;
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', customerId)
        .maybeSingle();
      
      if (!userProfile) {
        console.error(`No user found with Stripe customer ID: ${customerId}`);
        return;
      }
      
      const userId = userProfile.id;
      
      // Map Stripe Price IDs to your Product IDs
      const priceIdToProductIdMap = new Map<string, string>();
      
      // Get line items for this subscription
      for (const item of subscription.items.data) {
        if (item.price?.id) {
          const productId = await getProductIdByPriceId(item.price.id);
          if (productId !== 'unknown') {
            priceIdToProductIdMap.set(item.price.id, productId);
          }
        }
      }
      
      // Derive period information
      const interval = subscription.items.data[0]?.price?.recurring?.interval || 'unknown';
      const now = new Date();
      const currentPeriodStart = subscription.current_period_start 
        ? new Date(subscription.current_period_start * 1000) 
        : now;
      const currentPeriodEnd = subscription.current_period_end
        ? new Date(subscription.current_period_end * 1000)
        : new Date(now.setMonth(now.getMonth() + 1)); // Default to 1 month
      
      // Create subscription record
      const subscriptionData = {
        stripe_subscription_id: subscription.id,
        user_id: userId,
        status: subscription.status.toLowerCase(),
        period: interval,
        current_period_start: currentPeriodStart.toISOString(),
        current_period_end: currentPeriodEnd.toISOString(),
        cancel_at_period_end: subscription.cancel_at_period_end,
        created_at: new Date().toISOString(),
        ref: crypto.randomUUID().split('-')[0],
        quantity: subscription.items.data[0]?.quantity ?? 1,
      };
      
      // Insert subscription into database
      const { data: insertedSubscription, error } = await supabase
        .from('subscriptions')
        .insert(subscriptionData)
        .select('id')
        .single();
      
      if (error) {
        throw error;
      }
      
      // Insert subscription items
      if (insertedSubscription && priceIdToProductIdMap.size > 0) {
        const itemsToInsert = subscription.items.data.map(item => {
          const productId = priceIdToProductIdMap.get(item.price.id);
          if (!productId) return null;
          
          return {
            subscription_id: insertedSubscription.id,
            product_id: productId,
            stripe_price_id: item.price.id,
            stripe_subscription_item_id: item.id,
            status: 'active',
            quantity: item.quantity ?? 1,
            amount: (item.price.unit_amount / 100) * (item.quantity ?? 1),
          };
        }).filter(item => item !== null);
        
        if (itemsToInsert.length > 0) {
          const { error: itemsError } = await supabase
            .from('subscription_items')
            .insert(itemsToInsert);
            
          if (itemsError) {
            console.error('Error inserting subscription items:', itemsError);
          }
        }
      }
      
      console.log(`Successfully created subscription ${subscription.id} in database`);
    } catch (err) {
      console.error('Error handling subscription creation:', err);
    }
  }
  else {
    console.log(`Unhandled event type: ${event.type}`);
  }
}

// Map Stripe price_id to local product_id
async function getProductIdByPriceId(priceId: string): Promise<string> {
  // --- Updated Query ---
  const { data: product, error } = await supabase
    .from('products')
    .select('id')
    .or(`stripe_monthly_price_id.eq.${priceId},stripe_yearly_price_id.eq.${priceId}`) 
    .limit(1) 
    .single(); 
  // --- End Updated Query ---

  if (error || !product) {
    console.error(`Product lookup failed for Stripe Price ID: ${priceId}. Error: ${error?.message || 'Product not found'}`);
    return 'unknown'; 
  }

  return product.id; 
}

// Send confirmation email - Modified to accept and use derived dates if originals are missing
async function sendEmailToClient(
    email: string,
    subscription: Stripe.Subscription,
    productIds: string[],
    derivedDates?: { start: Date; end: Date } // Add optional derived dates
) {
  const productNames = await Promise.all(productIds.map(async (id)=>{
    const { data } = await supabase.from('products').select('name').eq('id', id).single();
    return data?.name || 'Unknown Product';
  }));

  // Use derived dates if provided and original dates are invalid, otherwise use originals
  const startDate = (typeof subscription.current_period_start === 'number')
    ? new Date(subscription.current_period_start * 1000)
    : derivedDates?.start;
  const endDate = (typeof subscription.current_period_end === 'number')
    ? new Date(subscription.current_period_end * 1000)
    : derivedDates?.end;

  const transporter = nodemailer.createTransport({
    host: 'smtp.office365.com',
    port: 587,
    secure: false,
    auth: {
      user: Deno.env.get('SMTP_USERNAME'),
      pass: Deno.env.get('SMTP_PASSWORD')
    }
  });

  const message = `
Hi there,

Thanks for subscribing! Here are the details of your subscription:

🛍️ Plan:
${productNames.map((name, index)=>`  ${index + 1}. ${name}`).join('\n')}
🔁 Status: ${subscription.status}
🔁 Period: ${subscription.items.data[0]?.price.recurring?.interval ?? 'N/A'}
📅 Starts on: ${startDate ? startDate.toLocaleDateString() : 'N/A'}
📅 Ends on: ${endDate ? endDate.toLocaleDateString() : 'N/A'}

If you have any questions, feel free to reach out.

Best regards,
Your Team
`;
  await transporter.sendMail({
    from: Deno.env.get('SMTP_USERNAME'),
    to: email,
    subject: 'Your Subscription Confirmation',
    text: message
  });
  console.log(`Confirmation email sent to ${email}`);
}
