import React from 'react';
import {
    Sparkles
} from 'lucide-react';

// Import the TM image
import tmImage from '../../assets/images/grantready/TM.png';
// Import the dollar icon image
import dollarIcon from '../../assets/images/grantready/grantready-dollar-icon.png';
// Import the timeline icon image
import timelineIcon from '../../assets/images/grantready/timeline-icon.png';
// Import the notifications icon image
import notificationsIcon from '../../assets/images/grantready/notifications-icon.png';

export interface CardData {
    id: number;
    title: string;
    description: string;
    icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
    gradientFrom: string;
    gradientTo: string;
}

interface EmpowerYourTeamProps {
    cardsData: CardData[];
    gridCols?: string; // Optional grid configuration, defaults to 3-column
    title?: string; // Optional custom title
    description?: string; // Optional custom description
    showTrademark?: boolean; // Optional to show/hide GrantReady trademark
}

const EmpowerYourTeam: React.FC<EmpowerYourTeamProps> = ({
    cardsData,
    gridCols = "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    title,
    description,
    showTrademark = true
}) => {

    return (
      <div className="py-20 mt-20 px-4 sm:px-6 lg:px-8 bg-[#edeffe]">
        {/* Container */}
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-16">
            {title ? (
              // Custom title (for ELENOR)
              <>
                <h2 className="font-platform text-black font-normal mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl">
                  {title}
                </h2>
                {description && (
                  <p className="text-xl text-gray-600 max-w-4xl mx-auto">
                    {description}
                  </p>
                )}
              </>
            ) : (
              // Default GrantReady title
              <h2 className="font-platform text-black mb-2 leading-tight tracking-normal text-3xl md:text-4xl lg:text-5xl max-w-7xl mx-auto">
                Empower your team with{" "}
                {showTrademark && (
                  <span
                    className="relative bg-clip-text text-transparent inline-flex items-center gap-1"
                    style={{
                      background:
                        "linear-gradient(266.64deg, #797EEC 13.28%, #22C55E 58.62%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                    }}
                  >
                    GrantReady™
                  </span>
                )}
                <br />
                the all-in-one platform built to simplify oversight, boost
                compliance, and accelerate outcomes.
              </h2>
            )}
          </div>

          {/* Cards Grid */}
          <div className={`grid ${gridCols} gap-4 lg:gap-6`}>
            {cardsData.map((card) => (
              <FeatureCard key={card.id} card={card} />
            ))}
          </div>
        </div>
      </div>
    );
};

interface FeatureCardProps {
    card: CardData;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ card }) => {
    const IconComponent = card.icon;

    return (
        <div className="relative h-72 rounded-xl overflow-hidden bg-white shadow-lg hover:shadow-xl transition-shadow duration-300">
            {/* Background Image with Opacity */}
            <div
                className="absolute inset-0 opacity-30 rounded-xl"
                style={{
                    backgroundImage: 'url("data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3Cpattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"%3E%3Ccircle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100" height="100" fill="url(%23grain)"/%3E%3C/svg%3E")',
                    backgroundSize: '100px 100px'
                }}
            />

            {/* Static Card Content - Always visible */}
            <div className="absolute inset-0 p-4 rounded-xl">
                {/* Title at top */}
                <h3
                    className="text-left mb-3 font-platform font-medium text-2xl"
                    style={{
                        lineHeight: '32px',
                        letterSpacing: '0%',
                        color: '#000000'
                    }}
                >
                    {card.title}
                </h3>

                {/* Description */}
                <p
                    className="mb-4"
                    style={{
                        fontFamily: 'Inter',
                        fontWeight: 400,
                        fontSize: '18px',
                        lineHeight: '22px',
                        letterSpacing: '0%',
                        color: '#000000'
                    }}
                >
                    {card.description}
                </p>

                {/* Icon in bottom-right corner */}
                <div className="absolute bottom-4 right-4">
                    {card.title === "Comprehensive Planning" ? (
                        <div className="w-20 h-20">
                            <svg width="80" height="80" viewBox="0 0 124 124" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g filter="url(#filter0_d_899_4388)">
                                    <path d="M23.5127 90.5017C27.5629 90.707 30.9747 93.3231 32.3477 96.9431C32.6987 96.7675 33.0465 96.5935 33.3916 96.4216L33.8379 97.3171L34.2832 98.2117C33.8177 98.4435 33.3475 98.6786 32.8721 98.9167C32.9528 99.4283 32.9971 99.9528 32.9971 100.487L32.9834 101.002C32.7154 106.284 28.3473 110.486 22.998 110.486L22.4834 110.472C17.3713 110.213 13.272 106.114 13.0127 101.002L13 100.487C13.0002 94.9654 17.4763 90.4892 22.998 90.489L23.5127 90.5017ZM22.998 93.822C19.317 93.8222 16.3332 96.806 16.333 100.487C16.3333 104.168 19.3171 107.153 22.998 107.153C26.6792 107.153 29.6638 104.168 29.6641 100.487C29.6639 96.8059 26.6792 93.822 22.998 93.822ZM41.8369 93.2576L42.3076 94.1394C41.012 94.8318 39.6773 95.5152 38.3008 96.2078L37.8516 95.3152L37.4023 94.4216C38.7715 93.7328 40.09 93.0572 41.3652 92.3757L41.8369 93.2576ZM49.4971 88.6052L50.0771 89.4197C48.8387 90.3007 47.5667 91.1234 46.2568 91.9109L45.7412 91.0544L45.2266 90.197C46.4995 89.4317 47.7276 88.6376 48.918 87.7908L49.4971 88.6052ZM56.2529 82.7292L56.9824 83.4138C55.9056 84.5617 54.8058 85.6142 53.6787 86.5925L52.3682 85.0828C53.4446 84.1484 54.4944 83.1426 55.5234 82.0457L56.2529 82.7292ZM62.4102 76.3718C62.1579 76.8055 61.8885 77.2352 61.6016 77.6609L61.3086 78.0857C60.8589 78.7219 60.4058 79.3347 59.9492 79.9255L59.1582 79.3142L58.3662 78.7029C58.805 78.1351 59.2412 77.5449 59.6748 76.9314L59.9434 76.5437C60.2057 76.1546 60.4513 75.7618 60.6816 75.366L62.4102 76.3718ZM64.2412 68.5935L65.2217 68.7878C64.9609 70.1027 64.6068 71.4052 64.1367 72.6873L62.2598 71.9998C62.6907 70.8244 63.0172 69.6225 63.2598 68.3992L64.2412 68.5935ZM65.8555 60.825C65.8738 62.1537 65.8449 63.4834 65.748 64.8064L63.7539 64.6599C63.8457 63.4055 63.8732 62.135 63.8555 60.8523L65.8555 60.825ZM65.3672 52.9714C65.4839 54.2645 65.5964 55.5705 65.6846 56.8787L64.6875 56.946L63.6895 57.0125C63.6029 55.7278 63.4923 54.4405 63.376 53.1511L64.3711 53.0613L65.3672 52.9714ZM64.7715 45.2498C64.8241 46.5114 64.9149 47.7945 65.0225 49.0964L64.0254 49.1785L63.0293 49.2605C62.9206 47.9443 62.8277 46.6318 62.7734 45.3328L63.7725 45.2908L64.7715 45.2498ZM65.126 37.6882C64.9189 38.8976 64.8022 40.1477 64.752 41.4333L62.7539 41.3552C62.8069 40 62.9308 38.6613 63.1553 37.3503L65.126 37.6882ZM66.876 30.2859L67.7129 30.8328C67.0309 31.8764 66.4916 32.9685 66.0703 34.1052L65.1328 33.7585L64.1953 33.4109C64.6653 32.1429 65.2701 30.9146 66.0391 29.738L66.876 30.2859ZM72.4785 24.7517L73.0576 25.5671C72.7898 25.7576 72.5214 25.9536 72.2529 26.156L71.4473 26.781C70.9478 27.1794 70.4842 27.5876 70.0547 28.0046L69.3584 27.2878L68.6611 26.5701C69.1386 26.1064 69.6513 25.6553 70.2002 25.2175L71.0498 24.5593C71.333 24.3459 71.6162 24.1386 71.8994 23.9373L72.4785 24.7517ZM110.483 18.8337L100.444 24.5349L100.479 19.6023C100.358 19.5987 100.238 19.5951 100.118 19.5916L100.147 18.5925L100.177 17.5925C100.282 17.5956 100.387 17.6001 100.493 17.6033L100.526 12.99L110.483 18.8337ZM79.7402 20.8484L80.0938 21.7839C78.8784 22.2439 77.6677 22.7922 76.458 23.4451L75.5088 21.6853C76.7979 20.9896 78.0894 20.4048 79.3867 19.9138L79.7402 20.8484ZM87.8838 19.908C86.5447 20.0752 85.2265 20.3054 83.9229 20.615L83.6914 19.6423L83.4609 18.6687C84.8432 18.3405 86.2335 18.0987 87.6357 17.9236L87.8838 19.908ZM96.0195 17.5105L96.0137 18.5105L96.0068 19.5105C94.6184 19.502 93.2599 19.519 91.9248 19.5779L91.8369 17.5798C93.214 17.5191 94.6073 17.5019 96.0195 17.5105Z" fill="url(#paint0_linear_899_4388)"/>
                                </g>
                                <path d="M42 41.9901L25.0001 58.9899M25 41.99L42 58.9899" stroke="#393737" strokeWidth="3.77546" strokeLinecap="round"/>
                                <path d="M99 74.9901L82.0001 91.9899M82 74.99L99 91.9899" stroke="#393737" strokeWidth="3.77546" strokeLinecap="round"/>
                                <defs>
                                    <filter id="filter0_d_899_4388" x="0.4" y="0.38999" width="122.684" height="122.696" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                                        <feFlood floodOpacity="0" result="BackgroundImageFix"/>
                                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                        <feOffset/>
                                        <feGaussianBlur stdDeviation="6.3"/>
                                        <feComposite in2="hardAlpha" operator="out"/>
                                        <feColorMatrix type="matrix" values="0 0 0 0 0.309804 0 0 0 0 0.388235 0 0 0 0 0.85098 0 0 0 1 0"/>
                                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_899_4388"/>
                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_899_4388" result="shape"/>
                                    </filter>
                                    <linearGradient id="paint0_linear_899_4388" x1="94.5" y1="12.99" x2="35.5" y2="104.99" gradientUnits="userSpaceOnUse">
                                        <stop stopColor="#6583FF"/>
                                        <stop offset="1" stopColor="#46A19D"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                    ) : card.title === "Financial Management" ? (
                        <div className="w-20 h-20">
                            <img
                                src={dollarIcon}
                                alt="Financial Management"
                                className="w-full h-full object-contain"
                            />
                        </div>
                    ) : card.title === "Smart Reports & Dashboards" ? (
                        <div className="w-20 h-20">
                            <svg width="80" height="50" viewBox="0 0 129 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clipPath="url(#clip0_897_4334)">
                                    <path d="M0.330078 72.0134L2.23378 53.611L7.94489 70.1097L11.435 25.3727L15.877 79.9455L19.3671 26.9591L25.3955 67.8888L29.5202 45.9962L34.5967 61.5431L38.4042 41.5542L42.5288 66.6196L46.019 47.5826L53.6338 77.7246L59.3449 16.1715L61.8832 53.611L67.277 36.4776L71.0844 59.3221L76.1609 39.9678L80.2856 53.611L85.3622 -0.0100098L88.535 56.4665L92.977 32.9875L97.7363 53.611L101.544 28.5455L107.255 53.611L110.11 7.60481L114.87 47.5826L119.312 25.3727L124.705 45.9962L128.196 9.19123" stroke="#111111" strokeOpacity="0.2" strokeWidth="1.26914"/>
                                    <g filter="url(#filter0_d_897_4334)">
                                        <path d="M0.646484 72.0131L128.829 9.82544" stroke="url(#paint0_linear_897_4334)" strokeWidth="2"/>
                                    </g>
                                </g>
                                <defs>
                                    <filter id="filter0_d_897_4334" x="-8.73647" y="-0.0216265" width="146.95" height="81.8819" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                                        <feFlood floodOpacity="0" result="BackgroundImageFix"/>
                                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                        <feOffset/>
                                        <feGaussianBlur stdDeviation="4.4737"/>
                                        <feComposite in2="hardAlpha" operator="out"/>
                                        <feColorMatrix type="matrix" values="0 0 0 0 0.164706 0 0 0 0 0.517647 0 0 0 0 0.968627 0 0 0 1 0"/>
                                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_897_4334"/>
                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_897_4334" result="shape"/>
                                    </filter>
                                    <linearGradient id="paint0_linear_897_4334" x1="132.637" y1="4.4316" x2="4.77117" y2="69.1575" gradientUnits="userSpaceOnUse">
                                        <stop stopColor="#47A0A0"/>
                                        <stop offset="1" stopColor="#6583FF"/>
                                    </linearGradient>
                                    <clipPath id="clip0_897_4334">
                                        <rect width="128.5" height="79.9556" fill="white" transform="translate(0.330078 -0.0100098)"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                    ) : card.title === "Timeline Management" ? (
                        <div className="w-20 h-20">
                            <img
                                src={timelineIcon}
                                alt="Timeline Management"
                                className="w-full h-full object-contain"
                            />
                        </div>
                    ) : card.title === "Smart Notifications" ? (
                        <div className="w-20 h-20">
                            <img
                                src={notificationsIcon}
                                alt="Smart Notifications"
                                className="w-full h-full object-contain"
                            />
                        </div>
                    ) : card.title === "Document Repository" ? (
                        <div className="w-20 h-20">
                            <svg width="80" height="86" viewBox="0 0 135 146" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <line x1="64.1389" y1="31.7042" x2="93.4294" y2="21.4866" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="62.8871" y1="35.3758" x2="72.4235" y2="49.6805" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="96.3728" y1="22.8984" x2="93.6481" y2="44.0148" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="92.2915" y1="80.03" x2="92.2915" y2="50.7396" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="75.8579" y1="55.1781" x2="90.1626" y2="81.0627" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="106.34" y1="73.7504" x2="96.122" y2="81.9245" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="96.3526" y1="22.6254" x2="109.295" y2="68.2641" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="96.3343" y1="23.0525" x2="69.7685" y2="93.2134" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="73.896" y1="54.8987" x2="69.809" y2="93.0445" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="73.896" y1="54.8987" x2="69.809" y2="93.0445" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="71.2129" y1="52.1018" x2="33.7483" y2="54.1453" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="57.2392" y1="68.3741" x2="33.3982" y2="56.1129" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="36.4687" y1="77.5129" x2="30.3381" y2="58.44" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="37.0102" y1="83.801" x2="22.7056" y2="106.28" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="42.2312" y1="79.436" x2="57.8982" y2="70.5808" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="42.2312" y1="79.436" x2="57.8982" y2="70.5808" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="22.3354" y1="106.791" x2="58.4376" y2="71.3699" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="44.6818" y1="102.203" x2="59.6676" y2="72.2313" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="44.6818" y1="102.203" x2="59.6676" y2="72.2313" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="46.4295" y1="103.227" x2="64.14" y2="97.0966" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="71.2915" y1="96.3881" x2="91.0456" y2="99.7939" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="93.6189" y1="104.086" x2="87.4883" y2="122.478" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="112.438" y1="107.152" x2="89.2778" y2="123.5" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="114.781" y1="101.948" x2="110.012" y2="75.3822" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="46.6149" y1="105.118" x2="90.8691" y2="101.148" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <line x1="45.6364" y1="106.575" x2="83.047" y2="124.372" stroke="#3D3D3D" strokeOpacity="0.28" strokeWidth="1.36235"/>
                                <g filter="url(#filter0_d_897_4278)">
                                    <path d="M86.3711 121.587C88.5308 121.696 90.248 123.482 90.248 125.669L90.2432 125.879C90.1339 128.038 88.348 129.756 86.1611 129.756L85.9512 129.751C83.8611 129.645 82.1848 127.969 82.0791 125.879L82.0742 125.669C82.0743 123.412 83.904 121.582 86.1611 121.582L86.3711 121.587ZM86.1611 122.944C84.6564 122.944 83.4366 124.164 83.4365 125.669C83.4365 127.173 84.6564 128.393 86.1611 128.393C87.6659 128.393 88.8857 127.174 88.8857 125.669C88.8857 124.164 87.6659 122.944 86.1611 122.944ZM20.2969 105.238C22.4565 105.347 24.1738 107.133 24.1738 109.32L24.1689 109.53C24.0597 111.69 22.2738 113.407 20.0869 113.407L19.877 113.402C17.7869 113.296 16.1106 111.62 16.0049 109.53L16 109.32C16.0001 107.063 17.8298 105.233 20.0869 105.233L20.2969 105.238ZM20.0869 106.595C18.5822 106.596 17.3624 107.815 17.3623 109.32C17.3623 110.825 18.5822 112.045 20.0869 112.045C21.5917 112.045 22.8115 110.825 22.8115 109.32C22.8115 107.815 21.5917 106.595 20.0869 106.595ZM43.457 101.151C45.6167 101.26 47.3339 103.046 47.334 105.233L47.3291 105.443C47.2199 107.603 45.4339 109.32 43.2471 109.32L43.0371 109.315C40.947 109.209 39.2707 107.533 39.165 105.443L39.1602 105.233C39.1602 102.976 40.99 101.146 43.2471 101.146L43.457 101.151ZM115.662 101.151C117.822 101.26 119.539 103.046 119.539 105.233L119.534 105.443C119.425 107.603 117.639 109.32 115.452 109.32L115.242 109.315C113.152 109.209 111.476 107.533 111.37 105.443L111.365 105.233C111.365 102.976 113.195 101.146 115.452 101.146L115.662 101.151ZM43.2471 102.509C41.7424 102.509 40.5225 103.728 40.5225 105.233C40.5225 106.738 41.7423 107.958 43.2471 107.958C44.7519 107.958 45.9717 106.738 45.9717 105.233C45.9716 103.728 44.7518 102.509 43.2471 102.509ZM115.452 102.509C113.947 102.509 112.728 103.728 112.728 105.233C112.728 106.738 113.947 107.958 115.452 107.958C116.957 107.958 118.177 106.738 118.177 105.233C118.177 103.728 116.957 102.509 115.452 102.509ZM94.5449 96.3826C96.7045 96.4919 98.4218 98.2779 98.4219 100.465L98.417 100.675C98.3078 102.834 96.5217 104.551 94.335 104.552L94.125 104.547C92.0349 104.441 90.3586 102.765 90.2529 100.675L90.248 100.465C90.2482 98.2075 92.0779 96.3778 94.335 96.3777L94.5449 96.3826ZM94.335 97.74C92.8303 97.7401 91.6105 98.9599 91.6104 100.465C91.6104 101.969 92.8302 103.189 94.335 103.189C95.8397 103.189 97.0596 101.969 97.0596 100.465C97.0594 98.96 95.8396 97.7401 94.335 97.74ZM67.9785 92.2957C70.1382 92.4049 71.8554 94.1909 71.8555 96.3777L71.8506 96.5876C71.7414 98.7473 69.9554 100.465 67.7686 100.465L67.5586 100.46C65.4685 100.354 63.7922 98.6778 63.6865 96.5876L63.6816 96.3777C63.6817 94.1206 65.5114 92.2908 67.7686 92.2908L67.9785 92.2957ZM67.7686 93.6531C66.2638 93.6531 65.044 94.873 65.0439 96.3777C65.0439 97.8824 66.2638 99.1022 67.7686 99.1023C69.2734 99.1023 70.4932 97.8825 70.4932 96.3777C70.4931 94.8729 69.2733 93.6531 67.7686 93.6531ZM92.502 79.3542C94.6616 79.4635 96.3788 81.2495 96.3789 83.4363L96.374 83.6462C96.2648 85.8059 94.4788 87.5232 92.292 87.5232L92.082 87.5183C89.9919 87.4125 88.3157 85.7364 88.21 83.6462L88.2051 83.4363C88.2051 81.1792 90.0349 79.3494 92.292 79.3494L92.502 79.3542ZM92.292 80.7117C90.7873 80.7117 89.5675 81.9316 89.5674 83.4363C89.5674 84.941 90.7872 86.1608 92.292 86.1609C93.7968 86.1609 95.0166 84.9411 95.0166 83.4363C95.0165 81.9315 93.7968 80.7117 92.292 80.7117ZM39.3691 76.6296C41.5288 76.7388 43.246 78.5249 43.2461 80.7117L43.2412 80.9216C43.132 83.0813 41.346 84.7986 39.1592 84.7986L38.9492 84.7937C36.8591 84.6879 35.1828 83.0118 35.0771 80.9216L35.0723 80.7117C35.0723 78.4546 36.9021 76.6248 39.1592 76.6248L39.3691 76.6296ZM39.1592 77.9871C37.6545 77.9871 36.4346 79.207 36.4346 80.7117C36.4346 82.2164 37.6544 83.4362 39.1592 83.4363C40.664 83.4363 41.8838 82.2165 41.8838 80.7117C41.8837 79.2069 40.6639 77.9871 39.1592 77.9871ZM109.531 67.7732C111.691 67.8824 113.408 69.6684 113.408 71.8552L113.403 72.0652C113.294 74.2249 111.508 75.9421 109.321 75.9421L109.111 75.9373C107.021 75.8314 105.345 74.1553 105.239 72.0652L105.234 71.8552C105.234 69.5981 107.064 67.7684 109.321 67.7683L109.531 67.7732ZM109.321 69.1306C107.817 69.1307 106.597 70.3505 106.597 71.8552C106.597 73.36 107.817 74.5798 109.321 74.5798C110.826 74.5798 112.046 73.36 112.046 71.8552C112.046 70.3505 110.826 69.1306 109.321 69.1306ZM61.168 65.0486C63.3276 65.1578 65.0449 66.9438 65.0449 69.1306L65.04 69.3406C64.9308 71.5003 63.1448 73.2175 60.958 73.2175L60.748 73.2126C58.658 73.1068 56.9817 71.4307 56.876 69.3406L56.8711 69.1306C56.8712 66.8735 58.7009 65.0438 60.958 65.0437L61.168 65.0486ZM60.958 66.406C59.4533 66.4061 58.2335 67.6259 58.2334 69.1306C58.2334 70.6354 59.4533 71.8552 60.958 71.8552C62.4628 71.8552 63.6826 70.6354 63.6826 69.1306C63.6825 67.6259 62.4628 66.406 60.958 66.406ZM30.5137 50.7449C32.6733 50.8541 34.3906 52.6401 34.3906 54.8269L34.3857 55.0369C34.2765 57.1966 32.4905 58.9138 30.3037 58.9138L30.0938 58.9089C28.0037 58.8031 26.3274 57.127 26.2217 55.0369L26.2168 54.8269C26.2169 52.5698 28.0466 50.7401 30.3037 50.74L30.5137 50.7449ZM30.3037 52.1023C28.799 52.1024 27.5792 53.3222 27.5791 54.8269C27.5791 56.3317 28.799 57.5514 30.3037 57.5515C31.8085 57.5515 33.0283 56.3317 33.0283 54.8269C33.0283 53.3222 31.8085 52.1023 30.3037 52.1023ZM74.791 48.0203C76.9507 48.1295 78.6679 49.9155 78.668 52.1023L78.6631 52.3123C78.5539 54.472 76.7679 56.1892 74.5811 56.1892L74.3711 56.1843C72.281 56.0785 70.6047 54.4024 70.499 52.3123L70.4941 52.1023C70.4942 49.8452 72.3239 48.0154 74.5811 48.0154L74.791 48.0203ZM74.5811 49.3777C73.0763 49.3778 71.8565 50.5976 71.8564 52.1023C71.8564 53.6071 73.0763 54.8268 74.5811 54.8269C76.0859 54.8269 77.3057 53.6071 77.3057 52.1023C77.3056 50.5975 76.0858 49.3777 74.5811 49.3777ZM93.1816 43.2507C95.3413 43.3599 97.0585 45.146 97.0586 47.3328L97.0537 47.5427C96.9445 49.7024 95.1585 51.4197 92.9717 51.4197L92.7617 51.4148C90.6716 51.309 88.9953 49.6328 88.8896 47.5427L88.8848 47.3328C88.8848 45.0757 90.7146 43.2459 92.9717 43.2458L93.1816 43.2507ZM92.9717 44.6082C91.467 44.6082 90.2471 45.8281 90.2471 47.3328C90.2471 48.8375 91.4669 50.0573 92.9717 50.0574C94.4765 50.0574 95.6963 48.8376 95.6963 47.3328C95.6962 45.828 94.4764 44.6082 92.9717 44.6082ZM61.168 28.2654C63.3276 28.3746 65.0449 30.1606 65.0449 32.3474L65.04 32.5574C64.9308 34.7171 63.1448 36.4343 60.958 36.4343L60.748 36.4294C58.658 36.3236 56.9817 34.6475 56.876 32.5574L56.8711 32.3474C56.8712 30.0903 58.7009 28.2606 60.958 28.2605L61.168 28.2654ZM60.958 29.6228C59.4533 29.6229 58.2335 30.8427 58.2334 32.3474C58.2334 33.8522 59.4533 35.072 60.958 35.072C62.4628 35.072 63.6826 33.8522 63.6826 32.3474C63.6825 30.8427 62.4628 29.6228 60.958 29.6228ZM97.2695 16.0046C99.4291 16.1139 101.146 17.8999 101.146 20.0867L101.142 20.2966C101.032 22.4563 99.2463 24.1735 97.0596 24.1736L96.8496 24.1687C94.7595 24.0629 93.0833 22.3867 92.9775 20.2966L92.9727 20.0867C92.9727 17.8296 94.8025 15.9998 97.0596 15.9998L97.2695 16.0046ZM97.0596 17.3621C95.5549 17.3621 94.335 18.582 94.335 20.0867C94.335 21.5914 95.5548 22.8112 97.0596 22.8113C98.5643 22.8112 99.7841 21.5914 99.7842 20.0867C99.7841 18.582 98.5643 17.3621 97.0596 17.3621Z" fill="url(#paint0_linear_897_4278)"/>
                                </g>
                                <defs>
                                    <filter id="filter0_d_897_4278" x="0.701599" y="0.701355" width="134.136" height="144.353" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                                        <feFlood floodOpacity="0" result="BackgroundImageFix"/>
                                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                        <feMorphology radius="0.681174" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_897_4278"/>
                                        <feOffset/>
                                        <feGaussianBlur stdDeviation="7.30861"/>
                                        <feComposite in2="hardAlpha" operator="out"/>
                                        <feColorMatrix type="matrix" values="0 0 0 0 0.330719 0 0 0 0 0.770583 0 0 0 0 0.732745 0 0 0 1 0"/>
                                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_897_4278"/>
                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_897_4278" result="shape"/>
                                    </filter>
                                    <linearGradient id="paint0_linear_897_4278" x1="99.3981" y1="15.7648" x2="79.9089" y2="130.265" gradientUnits="userSpaceOnUse">
                                        <stop stopColor="#4F69D4"/>
                                        <stop offset="1" stopColor="#5CF0FE"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                    ) : (
                        <div
                            className="w-10 h-10 rounded-full flex items-center justify-center shadow-lg"
                            style={{ backgroundColor: '#6490cc' }}
                        >
                            <IconComponent className="w-5 h-5 text-white" />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default EmpowerYourTeam;
